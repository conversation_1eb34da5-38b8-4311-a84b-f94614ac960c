# Revisionary - Low-Cost Production Checklist (≤$150/month)

## Infrastructure Configuration

### ✅ Single VM Setup (Primary Cost: ~$55/month)
- **Instance Type**: `e2-standard-4` (4 vCPU, 16GB RAM)
- **Storage**: 30GB SSD only (no additional persistent disks)
- **Preemptible**: Use preemptible instances for worker VMs when possible
- **Region**: `us-central1` (lowest cost region)

**Terraform Configuration:**
```hcl
resource "google_compute_instance" "main" {
  name         = "revisionary-main"
  machine_type = "e2-standard-4"
  zone         = "us-central1-a"
  
  boot_disk {
    initialize_params {
      image = "ubuntu-os-cloud/ubuntu-2204-lts"
      size  = 30
      type  = "pd-ssd"
    }
  }
  
  # Use preemptible for cost savings
  scheduling {
    preemptible = true
    automatic_restart = false
  }
}
```

### ✅ Local Redis Configuration (Included in VM cost)
- **Memory Allocation**: 2GB (within VM's 16GB)
- **Configuration**: `/etc/redis/redis.conf`
```bash
maxmemory 2gb
maxmemory-policy allkeys-lfu
appendonly yes
appendfsync everysec
notify-keyspace-events Ex
```

### ✅ Supabase Launch Tier (~$25/month)
- **Tier**: Launch tier (2GB storage)
- **Features**: 
  - PostgreSQL with Row Level Security
  - Real-time subscriptions
  - Point-in-time recovery (7 days)
- **Monitoring**: Watch disk usage at 1.5GB → upgrade warning

### ✅ Worker Autoscaling (Cost-optimized)

**Cloud Run Configuration:**
```yaml
workers:
  grammar:
    min_instances: 0  # Scale to zero when idle
    max_instances: 5
    cpu: 0.5
    memory: 512Mi
    concurrency: 10
    
  style:
    min_instances: 0
    max_instances: 3
    cpu: 0.5
    memory: 512Mi
    concurrency: 8
    
  structure:
    min_instances: 0
    max_instances: 2
    cpu: 1.0
    memory: 1Gi
    concurrency: 5
    
  content:
    min_instances: 0
    max_instances: 2
    cpu: 1.0
    memory: 2Gi
    concurrency: 3
```

**Scaling Trigger:**
```python
# Scale workers when Cloud Tasks queue depth > 50
if queue_depth > 50:
    scale_workers(queue_depth // 25)  # 1 worker per 25 jobs
```

## Cost Monitoring & Guardrails

### ✅ Token Budget Controls (~$50/month max)
```python
# Environment variables
MAX_TOKEN_COST_MONTH = 50  # USD
ALERT_THRESHOLD_5MIN = 1   # Alert if >$1 in 5 minutes

# Redis token tracking
async def track_token_usage(user_id: str, tokens: int, model: str):
    # Update monthly counter
    monthly_key = f"tokens:cost:monthly:{datetime.now().strftime('%Y%m')}"
    cost = calculate_token_cost(tokens, model)
    new_total = await redis.incrbyfloat(monthly_key, cost)
    
    # Alert if approaching limit
    if new_total > MAX_TOKEN_COST_MONTH * 0.9:
        await send_alert("Token budget 90% consumed")
    
    return new_total
```

### ✅ Prometheus Cost Alerts
```yaml
groups:
- name: cost-alerts
  rules:
  - alert: HighTokenCost5Min
    expr: increase(llm_cost_usd[5m]) > 1
    for: 0s
    labels:
      severity: critical
    annotations:
      summary: "Token spending >$1 in 5 minutes"
      
  - alert: MonthlyBudgetWarning
    expr: monthly_token_cost_usd > 45
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Monthly token budget 90% consumed"
      
  - alert: VMHighCPU
    expr: cpu_usage_percent > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "VM CPU usage high - consider scaling"
```

### ✅ Storage Lifecycle Management (~$10/month)
```hcl
resource "google_storage_bucket" "documents" {
  name = "revisionary-documents"
  
  lifecycle_rule {
    condition {
      age = 30
    }
    action {
      type = "SetStorageClass"
      storage_class = "NEARLINE"  # Cheaper for old data
    }
  }
  
  lifecycle_rule {
    condition {
      age = 365
    }
    action {
      type = "SetStorageClass"  
      storage_class = "COLDLINE"  # Very cheap for archives
    }
  }
}
```

## Monthly Cost Breakdown

| Service | Configuration | Monthly Cost |
|---------|---------------|--------------|
| **GCE VM** | e2-standard-4 preemptible | ~$55 |
| **OpenAI + Gemini** | Token budget with caching | ≤$50 |
| **Supabase** | Launch tier (2GB) | $25 |
| **Cloud Storage** | Nearline + lifecycle | ~$10 |
| **Networking** | Load balancer + egress | ~$5 |
| **Monitoring** | Basic Prometheus | ~$5 |
| **Total** | | **≤$150** |

## Operational Checklist

### Daily Tasks (Automated)
- ✅ Check Redis memory usage: `redis-cli info memory`
- ✅ Monitor token spend: Query `llm_cost_5m` metric
- ✅ Verify worker scaling: Check Cloud Tasks queue depth
- ✅ Backup Redis AOF to GCS (every 15 minutes)

### Weekly Tasks
- ✅ Review cost dashboard in GCP Console
- ✅ Check Supabase storage usage (alert at 1.5GB)
- ✅ Verify worker cold-start performance (<3s)
- ✅ Review top token-consuming users

### Monthly Tasks (First business day)
- ✅ **GCP Billing Export**: Download detailed usage CSV
- ✅ **Supabase Credit Burn**: Check project usage dashboard  
- ✅ **OpenAI API Spend**: Review usage.json from OpenAI dashboard
- ✅ **Cost Optimization**: Identify opportunities to reduce spend
- ✅ **Capacity Planning**: Project next month's scaling needs

### Cost Optimization Scripts

**Monthly Cost Report:**
```bash
#!/bin/bash
# monthly-cost-report.sh

echo "=== Revisionary Monthly Cost Report ==="
echo "Date: $(date)"
echo

# GCP costs
gcloud billing accounts list
gcloud billing projects describe $GCP_PROJECT

# Token costs from Redis
redis-cli --csv get "tokens:cost:monthly:$(date +%Y%m)"

# Supabase usage
curl -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
     "$SUPABASE_URL/rest/v1/usage"

echo "=== Action Items ==="
echo "1. Review GCP billing dashboard"
echo "2. Check for unused resources"  
echo "3. Optimize worker scaling if needed"
echo "4. Consider preemptible instances for workers"
```

**Resource Cleanup:**
```bash
#!/bin/bash
# cleanup-resources.sh

# Remove old export files (>7 days)
gsutil -m rm gs://revisionary-exports/**/*$(date -d '7 days ago' +%Y%m%d)*

# Clean Redis expired keys
redis-cli eval "return redis.call('del', unpack(redis.call('keys', 'expired:*')))" 0

# Archive old document versions
psql $DATABASE_URL -c "
UPDATE versions 
SET archived=true 
WHERE created_at < NOW() - INTERVAL '90 days'
"
```

## Emergency Cost Controls

**If monthly costs exceed $140 (alert threshold):**

1. **Immediate Actions:**
   ```bash
   # Reduce worker scaling limits
   gcloud run services update worker-grammar --max-instances=2
   gcloud run services update worker-style --max-instances=1
   gcloud run services update worker-structure --max-instances=1
   gcloud run services update worker-content --max-instances=1
   
   # Ensure budget mode is enabled (Redis Streams, not Cloud Tasks)
   export QUEUE_STRATEGY=redis_streams
   export DISABLE_CLOUD_TASKS=true
   
   # Enable aggressive token caching
   redis-cli config set timeout 7200  # 2 hour cache TTL
   ```

2. **Rate Limiting:**
   ```python
   # Reduce free tier limits temporarily
   TEMP_LIMITS = {
       'free': 5000,        # Reduced from 10,000
       'professional': 150000,  # Reduced from 225,000
   }
   ```

3. **Model Downgrading:**
   ```yaml
   # Force nano/mini models only
   force_model_downgrade: true
   disable_gemini_flash: true  # Use only OpenAI for cost control
   ```

## Scaling Upgrade Path

**When approaching capacity limits:**

1. **$150-300/month**: Add Redis Sentinel (3-node HA)
2. **$300-500/month**: Upgrade to standard (non-preemptible) VMs  
3. **$500-1000/month**: Dedicated worker nodes + load balancer
4. **$1000+/month**: Multi-region deployment

## Success Metrics

**Cost Efficiency Targets:**
- **Cost per active user**: <$2/month
- **Token cost per 1000 words**: <$0.05
- **Infrastructure cost per document**: <$0.01
- **Compute utilization**: >70% during business hours

**Performance under budget:**
- Response time: <2s (99th percentile)
- Uptime: >99.5%
- Error rate: <1%
- Cache hit rate: >80%

This checklist ensures Revisionary operates profitably within the $150/month budget while maintaining enterprise-grade reliability and performance.