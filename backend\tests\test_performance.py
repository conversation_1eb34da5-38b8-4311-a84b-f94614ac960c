"""
Pytest tests for performance monitoring.
"""
import asyncio
import time
import httpx
import psutil
import pytest
from datetime import datetime
from typing import Dict

class PerformanceMonitor:
    """Monitor system performance and API response times"""

    def __init__(self, base_url: str = "http://127.0.0.1:8001"):
        self.base_url = base_url
        self.stats = {
            "cpu_usage": [],
            "memory_usage": [],
            "response_times": {},
            "errors": []
        }

    async def test_endpoint(self, endpoint: str, method: str = "GET", timeout: int = 10) -> Dict:
        """Test a single endpoint and measure response time"""
        url = f"{self.base_url}{endpoint}"
        try:
            async with httpx.AsyncClient(timeout=timeout) as client:
                start_time = time.time()
                if method.upper() == "GET":
                    response = await client.get(url)
                elif method.upper() == "POST":
                    response = await client.post(url, json={})
                else:
                    raise ValueError(f"Unsupported method: {method}")
                response_time = (time.time() - start_time) * 1000  # Convert to ms
                return {
                    "endpoint": endpoint,
                    "method": method,
                    "status_code": response.status_code,
                    "response_time_ms": round(response_time, 2),
                    "success": response.status_code < 400,
                    "response_size": len(response.content),
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            return {
                "endpoint": endpoint,
                "method": method,
                "error": str(e),
                "success": False,
                "timestamp": datetime.now().isoformat()
            }

@pytest.fixture(scope="module")
def monitor():
    return PerformanceMonitor()

@pytest.mark.asyncio
async def test_performance(monitor):
    """Run comprehensive performance tests"""
    test_endpoints = [
        ("/", "GET"),
        ("/health", "GET"),
        ("/test", "GET"),
    ]
    results = []
    for endpoint, method in test_endpoints:
        result = await monitor.test_endpoint(endpoint, method)
        results.append(result)
        assert result.get("success"), f"Endpoint {endpoint} failed with error: {result.get('error')}"

    successful_tests = [r for r in results if r.get("success")]
    assert len(successful_tests) == len(test_endpoints)
