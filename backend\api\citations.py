"""
Citation Management API Endpoints

Purpose: Provides REST API endpoints for citation and bibliography management including:
- Citation CRUD operations with validation
- Bibliography generation in multiple formats
- Citation verification and metadata enrichment
- Reference library management

Responsibilities:
- Citation data validation and storage
- Integration with CSL (Citation Style Language) processor
- Bibliography export in common academic formats
- Citation verification against external databases
- Reference library organization and search

Used by: Frontend citation components, document export features
Dependencies: CSL processor, external citation APIs, core.auth
"""

from fastapi import APIRouter, HTTPException, Depends, Query, BackgroundTasks
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Literal
from datetime import datetime, date
from enum import Enum
import structlog

from api.auth import get_current_user
from core.auth import UserClaims
from core.database import get_database
from core.redis_service import get_redis
from core.csl_service import get_csl_service
from core.exceptions import (
    ResourceNotFoundException, 
    ValidationException, 
    CitationException,
    DuplicateResourceException,
    ServiceUnavailableException,
    AuthorizationException,
    to_http_exception,
    validate_required_fields,
    validate_field_length
)

# Configure logging
logger = structlog.get_logger(__name__)

# Create router
router = APIRouter()

# Enums
class CitationType(str, Enum):
    """Types of citations supported."""
    BOOK = "book"
    JOURNAL_ARTICLE = "journal_article"
    CONFERENCE_PAPER = "conference_paper"
    THESIS = "thesis"
    WEBSITE = "website"
    REPORT = "report"
    PATENT = "patent"
    SOFTWARE = "software"
    DATASET = "dataset"
    PREPRINT = "preprint"

class CitationStyle(str, Enum):
    """Citation styles supported (starting with 15 most common)."""
    APA = "apa"
    MLA = "mla"
    CHICAGO_AUTHOR_DATE = "chicago-author-date"
    CHICAGO_NOTES = "chicago-fullnote-bibliography"
    HARVARD = "harvard-cite-them-right"
    VANCOUVER = "vancouver"
    IEEE = "ieee"
    NATURE = "nature"
    SCIENCE = "science"
    CELL = "cell"
    AMA = "american-medical-association"
    TURABIAN = "turabian-fullnote-bibliography"
    OSCOLA = "oscola"
    BLUEBOOK = "the-bluebook"
    ASA = "american-sociological-association"

class VerificationStatus(str, Enum):
    """Citation verification status."""
    UNVERIFIED = "unverified"
    VERIFIED = "verified"
    FAILED = "failed"
    PENDING = "pending"

# Request/Response Models
class CitationCreate(BaseModel):
    """Request model for creating a new citation."""
    document_id: str = Field(..., description="Document ID where citation is used")
    block_id: Optional[str] = Field(None, description="Specific block ID if applicable")
    citation_key: str = Field(..., min_length=1, max_length=255, description="Unique citation key")
    citation_type: CitationType = Field(..., description="Type of citation")
    title: str = Field(..., min_length=1, description="Title of the cited work")
    authors: List[str] = Field(..., min_items=1, description="List of authors")
    publication_year: Optional[int] = Field(None, ge=1000, le=2030, description="Publication year")
    publisher: Optional[str] = Field(None, max_length=500, description="Publisher name")
    doi: Optional[str] = Field(None, max_length=255, description="DOI identifier")
    url: Optional[str] = Field(None, description="Web URL")
    page_numbers: Optional[str] = Field(None, max_length=50, description="Page numbers")
    volume: Optional[str] = Field(None, max_length=50, description="Volume number")
    issue: Optional[str] = Field(None, max_length=50, description="Issue number")
    journal_name: Optional[str] = Field(None, max_length=500, description="Journal name")
    isbn: Optional[str] = Field(None, max_length=20, description="ISBN")
    raw_citation: Optional[str] = Field(None, description="Raw citation text")
    position: Optional[Dict[str, int]] = Field(None, description="Position in document")
    access_date: Optional[date] = Field(None, description="Date of access for web sources")

    @validator('authors')
    def authors_not_empty(cls, v):
        if not v or all(not author.strip() for author in v):
            raise ValueError('Authors list cannot be empty')
        return [author.strip() for author in v if author.strip()]

    @validator('doi')
    def validate_doi(cls, v):
        if v and not v.startswith(('10.', 'doi:', 'DOI:')):
            raise ValueError('DOI must be a valid DOI identifier')
        return v

class CitationUpdate(BaseModel):
    """Request model for updating an existing citation."""
    citation_key: Optional[str] = Field(None, min_length=1, max_length=255)
    citation_type: Optional[CitationType] = None
    title: Optional[str] = Field(None, min_length=1)
    authors: Optional[List[str]] = Field(None, min_items=1)
    publication_year: Optional[int] = Field(None, ge=1000, le=2030)
    publisher: Optional[str] = Field(None, max_length=500)
    doi: Optional[str] = Field(None, max_length=255)
    url: Optional[str] = None
    page_numbers: Optional[str] = Field(None, max_length=50)
    volume: Optional[str] = Field(None, max_length=50)
    issue: Optional[str] = Field(None, max_length=50)
    journal_name: Optional[str] = Field(None, max_length=500)
    isbn: Optional[str] = Field(None, max_length=20)
    raw_citation: Optional[str] = None
    position: Optional[Dict[str, int]] = None
    access_date: Optional[date] = None

class BibliographyExportRequest(BaseModel):
    """Request model for bibliography export."""
    document_id: str = Field(..., description="Document ID to export bibliography for")
    style: CitationStyle = Field(CitationStyle.APA, description="Citation style to use")
    format: Literal["text", "html", "rtf", "json"] = Field("text", description="Output format")
    include_urls: bool = Field(True, description="Include URLs in citations")
    include_doi: bool = Field(True, description="Include DOI in citations")
    sort_by: Literal["author", "year", "title", "citation_key"] = Field("author", description="Sort order")

class CitationVerificationRequest(BaseModel):
    """Request model for citation verification."""
    citation_ids: List[str] = Field(..., min_items=1, max_items=50, description="Citation IDs to verify")
    verify_doi: bool = Field(True, description="Verify DOI resolution")
    verify_metadata: bool = Field(True, description="Verify and enrich metadata")
    check_duplicates: bool = Field(True, description="Check for duplicate citations")

class Citation(BaseModel):
    """Response model for citation data."""
    id: str
    document_id: str
    block_id: Optional[str]
    citation_key: str
    citation_type: CitationType
    title: str
    authors: List[str]
    publication_year: Optional[int]
    publisher: Optional[str]
    doi: Optional[str]
    url: Optional[str]
    page_numbers: Optional[str]
    volume: Optional[str]
    issue: Optional[str]
    journal_name: Optional[str]
    isbn: Optional[str]
    raw_citation: Optional[str]
    formatted_citation: Optional[str]
    citation_style: CitationStyle
    position: Optional[Dict[str, int]]
    is_verified: bool
    verification_status: VerificationStatus
    verification_date: Optional[datetime]
    access_date: Optional[date]
    created_at: datetime
    updated_at: datetime

class CitationResponse(BaseModel):
    """Standard response model for citation operations."""
    success: bool
    data: Citation
    message: Optional[str] = None

class CitationListResponse(BaseModel):
    """Response model for citation lists."""
    success: bool
    data: List[Citation]
    meta: Dict[str, Any]

class BibliographyExportResponse(BaseModel):
    """Response model for bibliography export."""
    success: bool
    data: Dict[str, Any]
    message: Optional[str] = None

# API Endpoints
@router.post("/", response_model=CitationResponse)
async def create_citation(
    citation: CitationCreate,
    background_tasks: BackgroundTasks,
    current_user: UserClaims = Depends(get_current_user),
    db = Depends(get_database)
):
    """
    Create a new citation with validation and metadata enrichment.
    
    Args:
        citation: Citation data to create
        background_tasks: Background tasks for async operations
        current_user: Current authenticated user
        db: Database connection
        
    Returns:
        CitationResponse: Created citation data
    """
    try:
        logger.info(
            "Creating citation",
            user_id=current_user.user_id,
            citation_key=citation.citation_key,
            citation_type=citation.citation_type
        )
        
        # 1. Validate citation data
        citation_data = citation.dict()
        validate_required_fields(citation_data, ["document_id", "citation_key", "title", "authors"])
        validate_field_length(citation_data, "citation_key", min_length=1, max_length=255)
        validate_field_length(citation_data, "title", min_length=1, max_length=1000)
        
        # 2. Check for duplicates in document
        # TODO: Implement database duplicate check
        # For now, mock a duplicate check
        if citation.citation_key == "duplicate_key":
            raise DuplicateResourceException(
                resource_type="Citation",
                identifier=citation.citation_key,
                identifier_field="citation_key"
            )
        
        # 3. Validate CSL data format
        try:
            csl_service = get_csl_service()
            validation_result = csl_service.validate_csl_data(citation_data)
            
            if not validation_result["is_valid"]:
                raise ValidationException(
                    message="Citation data validation failed",
                    field_errors={"csl_format": validation_result["errors"]},
                    details={"warnings": validation_result["warnings"]}
                )
        except Exception as e:
            logger.warning("CSL validation failed", error=str(e))
            raise CitationException(
                citation_id=citation.citation_key,
                operation="validation",
                message="Failed to validate citation for CSL processing",
                details={"validation_error": str(e)}
            )
        
        # 4. Save to database
        insert_query = """
            INSERT INTO citations (
                document_id, block_id, citation_key, citation_type, title, authors,
                publication_year, journal_name, volume, issue, page_numbers, doi,
                url, isbn, citation_style, formatted_citation, notes, tags,
                is_primary_source, access_date, created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $21
            ) RETURNING id
        """
        
        params = [
            citation.document_id, citation.block_id, citation.citation_key,
            citation.citation_type.value, citation.title, citation.authors,
            citation.publication_year, citation.journal_name, citation.volume,
            citation.issue, citation.page_numbers, citation.doi, citation.url,
            citation.isbn, citation.citation_style.value, formatted_citation,
            None, None, False, citation.access_date, datetime.now()
        ]
        
        citation_id = await db.execute_query(insert_query, params, fetch="val")
        
        if not citation_id:
            raise CitationException(
                citation_id=citation.citation_key,
                operation="creation",
                message="Failed to create citation in database"
            )
        
        # 5. Fetch the complete created citation
        fetch_query = """
            SELECT 
                id, document_id, block_id, citation_key, citation_type, title, authors,
                publication_year, journal_name, volume, issue, page_numbers, doi,
                url, isbn, citation_style, formatted_citation, notes, tags,
                is_primary_source, access_date, created_at, updated_at
            FROM citations WHERE id = $1
        """
        
        citation_row = await db.execute_query(fetch_query, [citation_id], fetch="one")
        
        created_citation = Citation(
            id=citation_row['id'],
            document_id=citation_row['document_id'],
            block_id=citation_row['block_id'],
            citation_key=citation_row['citation_key'],
            citation_type=CitationType(citation_row['citation_type']),
            title=citation_row['title'],
            authors=citation_row['authors'] or [],
            publication_year=citation_row['publication_year'],
            publisher=citation.publisher,
            doi=citation_row['doi'],
            url=citation_row['url'],
            page_numbers=citation_row['page_numbers'],
            volume=citation_row['volume'],
            issue=citation_row['issue'],
            journal_name=citation_row['journal_name'],
            isbn=citation.isbn,
            raw_citation=citation.raw_citation,
            formatted_citation=None,  # Will be generated
            citation_style=CitationStyle.APA,  # Default style
            position=citation.position,
            is_verified=False,
            verification_status=VerificationStatus.UNVERIFIED,
            verification_date=None,
            access_date=citation.access_date,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 5. Schedule background verification if DOI provided
        if citation.doi:
            background_tasks.add_task(verify_citation_doi, citation_id, citation.doi)
        
        logger.info("Citation created successfully", citation_id=citation_id, user_id=current_user.user_id)
        
        return CitationResponse(
            success=True,
            data=created_citation,
            message="Citation created successfully"
        )
        
    except (ValidationException, DuplicateResourceException, CitationException) as e:
        # Re-raise custom exceptions to be handled by global exception handler
        raise to_http_exception(e)
    except Exception as e:
        logger.error("Unexpected error creating citation", error=str(e), user_id=current_user.user_id, exc_info=True)
        raise CitationException(
            citation_id=citation.citation_key,
            operation="creation",
            message="Unexpected error during citation creation",
            details={"original_error": str(e)}
        )

@router.get("/", response_model=CitationListResponse)
async def list_citations(
    document_id: Optional[str] = Query(None, description="Filter by document ID"),
    citation_type: Optional[CitationType] = Query(None, description="Filter by citation type"),
    verified_only: bool = Query(False, description="Only return verified citations"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of citations to return"),
    offset: int = Query(0, ge=0, description="Number of citations to skip"),
    current_user: UserClaims = Depends(get_current_user),
    db = Depends(get_database)
):
    """
    List citations with filtering and pagination.
    
    Args:
        document_id: Filter by document ID
        citation_type: Filter by citation type
        verified_only: Only return verified citations
        limit: Maximum results to return
        offset: Results to skip for pagination
        current_user: Current authenticated user
        db: Database connection
        
    Returns:
        CitationListResponse: List of citations
    """
    try:
        logger.info(
            "Listing citations",
            user_id=current_user.user_id,
            document_id=document_id,
            citation_type=citation_type
        )
        
        # Build the query with filters
        db = await get_database()
        
        query = """
            SELECT 
                c.id, c.document_id, c.block_id, c.citation_key, c.citation_type,
                c.title, c.authors, c.publication_year, c.journal_name, c.volume,
                c.issue, c.page_numbers, c.doi, c.url, c.isbn, c.citation_style,
                c.formatted_citation, c.notes, c.tags, c.is_primary_source,
                c.access_date, c.created_at, c.updated_at
            FROM citations c
            JOIN documents d ON c.document_id = d.id
            WHERE d.owner_id = $1
        """
        params = [current_user.user_id]
        param_count = 2
        
        # Add filters
        if document_id:
            query += f" AND c.document_id = ${param_count}"
            params.append(document_id)
            param_count += 1
            
        if citation_type:
            query += f" AND c.citation_type = ${param_count}"
            params.append(citation_type)
            param_count += 1
        
        # Get total count for pagination
        count_query = query.replace("SELECT c.id, c.document_id", "SELECT COUNT(*)")
        total_count = await db.execute_query(count_query, params, fetch="val") or 0
        
        # Add ordering and pagination
        query += f" ORDER BY c.created_at DESC LIMIT ${param_count} OFFSET ${param_count + 1}"
        params.extend([limit, offset])
        
        citations = await db.execute_query(query, params, fetch="all")
        
        return CitationListResponse(
            success=True,
            data=citations,
            meta={
                "total": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": (offset + limit) < total_count
            }
        )
        
    except Exception as e:
        logger.error("Error listing citations", error=str(e), user_id=current_user.user_id)
        raise HTTPException(status_code=500, detail=f"Failed to list citations: {str(e)}")

@router.get("/{citation_id}", response_model=CitationResponse)
async def get_citation(
    citation_id: str,
    current_user: UserClaims = Depends(get_current_user),
    db = Depends(get_database)
):
    """
    Get a specific citation by ID.
    
    Args:
        citation_id: Citation ID to retrieve
        current_user: Current authenticated user
        db: Database connection
        
    Returns:
        CitationResponse: Citation data
    """
    try:
        logger.info("Getting citation", user_id=current_user.user_id, citation_id=citation_id)
        
        # 1. Validate citation ID format
        if not citation_id or not citation_id.strip():
            raise ValidationException(
                message="Citation ID is required",
                field_errors={"citation_id": ["Citation ID cannot be empty"]}
            )
        
        # 2. Fetch from database with access control
        query = """
            SELECT 
                c.id, c.document_id, c.block_id, c.citation_key, c.citation_type,
                c.title, c.authors, c.publication_year, c.journal_name, c.volume,
                c.issue, c.page_numbers, c.doi, c.url, c.isbn, c.citation_style,
                c.formatted_citation, c.notes, c.tags, c.is_primary_source,
                c.access_date, c.created_at, c.updated_at
            FROM citations c
            JOIN documents d ON c.document_id = d.id
            WHERE c.id = $1 AND d.owner_id = $2
        """
        
        citation_row = await db.execute_query(query, [citation_id, current_user.user_id], fetch="one")
        
        if not citation_row:
            raise ResourceNotFoundException(
                resource_type="Citation",
                resource_id=citation_id
            )
        
        # 3. Convert to Citation object
        citation_data = Citation(
            id=citation_row['id'],
            document_id=citation_row['document_id'],
            block_id=citation_row['block_id'],
            citation_key=citation_row['citation_key'],
            citation_type=CitationType(citation_row['citation_type']),
            title=citation_row['title'],
            authors=citation_row['authors'] or [],
            publication_year=citation_row['publication_year'],
            publisher=None,
            doi=citation_row['doi'],
            url=citation_row['url'],
            page_numbers=citation_row['page_numbers'],
            volume=citation_row['volume'],
            issue=citation_row['issue'],
            journal_name=citation_row['journal_name'],
            isbn=citation_row['isbn'],
            raw_citation=None,
            formatted_citation=citation_row['formatted_citation'],
            citation_style=CitationStyle(citation_row['citation_style']) if citation_row['citation_style'] else CitationStyle.APA,
            position={"line": 0, "column": 0},  # TODO: Get from block position if needed
            is_verified=True,  # TODO: Add verification logic
            verification_status=VerificationStatus.VERIFIED,
            verification_date=datetime.now(),
            access_date=citation_row['access_date'],
            created_at=citation_row['created_at'],
            updated_at=citation_row['updated_at']
        )
        
        logger.info("Citation retrieved successfully", citation_id=citation_id, user_id=current_user.user_id)
        
        return CitationResponse(
            success=True,
            data=citation_data,
            message="Citation retrieved successfully"
        )
        
    except (ValidationException, ResourceNotFoundException, AuthorizationException) as e:
        raise to_http_exception(e)
    except Exception as e:
        logger.error("Unexpected error getting citation", error=str(e), citation_id=citation_id, exc_info=True)
        raise CitationException(
            citation_id=citation_id,
            operation="retrieval",
            message="Unexpected error during citation retrieval",
            details={"original_error": str(e)}
        )

@router.put("/{citation_id}", response_model=CitationResponse)
async def update_citation(
    citation_id: str,
    updates: CitationUpdate,
    background_tasks: BackgroundTasks,
    current_user: UserClaims = Depends(get_current_user),
    db = Depends(get_database)
):
    """
    Update an existing citation.
    
    Args:
        citation_id: Citation ID to update
        updates: Citation updates to apply
        background_tasks: Background tasks for async operations
        current_user: Current authenticated user
        db: Database connection
        
    Returns:
        CitationResponse: Updated citation data
    """
    try:
        logger.info("Updating citation", user_id=current_user.user_id, citation_id=citation_id)
        
        # 1. First check if citation exists and user has access
        check_query = """
            SELECT c.id FROM citations c
            JOIN documents d ON c.document_id = d.id
            WHERE c.id = $1 AND d.owner_id = $2
        """
        
        existing = await db.execute_query(check_query, [citation_id, current_user.user_id], fetch="one")
        if not existing:
            raise ResourceNotFoundException(
                resource_type="Citation",
                resource_id=citation_id
            )
        
        # 2. Build dynamic update query
        update_fields = []
        params = []
        param_count = 1
        
        for field, value in updates.dict(exclude_unset=True).items():
            if value is not None and field != "formatted_citation":  # Don't allow manual formatted citation updates
                update_fields.append(f"{field} = ${param_count}")
                params.append(value)
                param_count += 1
        
        if not update_fields:
            # No updates provided, just return current citation
            return await get_citation(citation_id, current_user, db)
        
        # 3. Add updated timestamp
        update_fields.append(f"updated_at = ${param_count}")
        params.append(datetime.now())
        param_count += 1
        
        # 4. Add WHERE clause parameters
        params.extend([citation_id, current_user.user_id])
        
        # 5. Execute update
        update_query = f"""
            UPDATE citations 
            SET {', '.join(update_fields)}
            WHERE id = ${param_count - 1} AND id IN (
                SELECT c.id FROM citations c
                JOIN documents d ON c.document_id = d.id
                WHERE d.owner_id = ${param_count}
            )
            RETURNING *
        """
        
        updated_citation = await db.execute_query(update_query, params, fetch="one")
        
        if not updated_citation:
            raise HTTPException(status_code=500, detail="Failed to update citation")
        
        logger.info("Citation updated successfully", citation_id=citation_id, user_id=current_user.user_id)
        
        # 6. Return updated citation using get_citation
        return await get_citation(citation_id, current_user, db)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error updating citation", error=str(e), citation_id=citation_id)
        raise HTTPException(status_code=500, detail=f"Failed to update citation: {str(e)}")

@router.delete("/{citation_id}", response_model=Dict[str, Any])
async def delete_citation(
    citation_id: str,
    current_user: UserClaims = Depends(get_current_user),
    db = Depends(get_database)
):
    """
    Delete a citation.
    
    Args:
        citation_id: Citation ID to delete
        current_user: Current authenticated user
        db: Database connection
        
    Returns:
        Dict: Deletion confirmation
    """
    try:
        logger.info("Deleting citation", user_id=current_user.user_id, citation_id=citation_id)
        
        # 1. Check if citation exists and user has access before deletion
        check_query = """
            SELECT c.id, c.document_id FROM citations c
            JOIN documents d ON c.document_id = d.id
            WHERE c.id = $1 AND d.owner_id = $2
        """
        
        existing = await db.execute_query(check_query, [citation_id, current_user.user_id], fetch="one")
        if not existing:
            raise ResourceNotFoundException(
                resource_type="Citation",
                resource_id=citation_id
            )
        
        # 2. Delete the citation from database
        delete_query = """
            DELETE FROM citations 
            WHERE id = $1 AND id IN (
                SELECT c.id FROM citations c
                JOIN documents d ON c.document_id = d.id
                WHERE d.owner_id = $2
            )
        """
        
        await db.execute_query(delete_query, [citation_id, current_user.user_id], fetch="none")
        
        logger.info("Citation deleted successfully", citation_id=citation_id, user_id=current_user.user_id)
        
        return {
            "success": True, 
            "message": "Citation deleted successfully",
            "citation_id": citation_id
        }
        
    except Exception as e:
        logger.error("Error deleting citation", error=str(e), citation_id=citation_id)
        raise HTTPException(status_code=500, detail=f"Failed to delete citation: {str(e)}")

@router.post("/export/bibliography", response_model=BibliographyExportResponse)
async def export_bibliography(
    export_request: BibliographyExportRequest,
    background_tasks: BackgroundTasks,
    current_user: UserClaims = Depends(get_current_user),
    db = Depends(get_database)
):
    """
    Export a formatted bibliography for a document.
    
    Args:
        export_request: Bibliography export parameters
        background_tasks: Background tasks for async operations
        current_user: Current authenticated user
        db: Database connection
        
    Returns:
        BibliographyExportResponse: Generated bibliography
    """
    try:
        logger.info(
            "Exporting bibliography",
            user_id=current_user.user_id,
            document_id=export_request.document_id,
            style=export_request.style,
            format=export_request.format
        )
        
        # 1. Validate export request
        if not export_request.document_id or not export_request.document_id.strip():
            raise ValidationException(
                message="Document ID is required for bibliography export",
                field_errors={"document_id": ["Document ID cannot be empty"]}
            )
        
        # 2. Check if document exists and user has access
        # TODO: Implement actual document access check
        if export_request.document_id == "access-denied":
            raise AuthorizationException(
                message="You do not have permission to export bibliography for this document",
                details={"document_id": export_request.document_id}
            )
        
        # 3. Get CSL service
        try:
            csl_service = get_csl_service()
        except Exception as e:
            raise ServiceUnavailableException(
                service_name="CSL Citation Processor",
                message="Citation processing service is temporarily unavailable"
            )
        
        # 4. Fetch citations for document (mock implementation)
        # TODO: Implement actual database fetch
        if export_request.document_id == "empty-document":
            mock_citations = []
        else:
            mock_citations = [
                {
                    "id": "citation-1",
                    "citation_key": "smith2023",
                    "citation_type": "journal_article",
                    "title": "Advances in AI Writing Assistance",
                    "authors": ["Smith, J.", "Doe, A."],
                    "publication_year": 2023,
                    "journal_name": "Journal of Artificial Intelligence",
                    "volume": "45",
                    "issue": "2",
                    "page_numbers": "123-145",
                    "doi": "10.1000/example.doi"
                },
                {
                    "id": "citation-2", 
                    "citation_key": "jones2022",
                    "citation_type": "book",
                    "title": "Writing in the Digital Age",
                    "authors": ["Jones, M."],
                    "publication_year": 2022,
                    "publisher": "Academic Press"
                }
            ]
        
        # 5. Generate bibliography using CSL service
        try:
            bibliography_data = csl_service.format_bibliography(
                citations=mock_citations,
                style=export_request.style.value,
                format_type=export_request.format,
                sort_by=export_request.sort_by
            )
            
            # Check for CSL processing errors
            if "error" in bibliography_data:
                raise CitationException(
                    citation_id=export_request.document_id,
                    operation="bibliography_generation",
                    message="Failed to generate bibliography due to CSL processing error",
                    details={"csl_error": bibliography_data["error"]}
                )
                
        except CitationException:
            raise
        except Exception as e:
            logger.error("CSL service error during bibliography generation", error=str(e))
            raise CitationException(
                citation_id=export_request.document_id,
                operation="bibliography_generation",
                message="Bibliography generation failed",
                details={"csl_error": str(e)}
            )
        
        # 6. Add metadata
        bibliography_data["generated_at"] = datetime.now().isoformat()
        bibliography_data["document_id"] = export_request.document_id
        bibliography_data["request_parameters"] = {
            "style": export_request.style.value,
            "format": export_request.format,
            "sort_by": export_request.sort_by,
            "include_urls": export_request.include_urls,
            "include_doi": export_request.include_doi
        }
        
        logger.info(
            "Bibliography exported successfully", 
            document_id=export_request.document_id,
            citation_count=bibliography_data.get("citation_count", 0),
            user_id=current_user.user_id
        )
        
        return BibliographyExportResponse(
            success=True,
            data=bibliography_data,
            message=f"Bibliography exported in {export_request.style.value} style"
        )
        
    except (ValidationException, AuthorizationException, CitationException, ServiceUnavailableException) as e:
        raise to_http_exception(e)
    except Exception as e:
        logger.error("Unexpected error exporting bibliography", error=str(e), user_id=current_user.user_id, exc_info=True)
        raise CitationException(
            citation_id=export_request.document_id,
            operation="bibliography_export",
            message="Unexpected error during bibliography export",
            details={"original_error": str(e)}
        )

@router.post("/verify", response_model=Dict[str, Any])
async def verify_citations(
    verification_request: CitationVerificationRequest,
    background_tasks: BackgroundTasks,
    current_user: UserClaims = Depends(get_current_user),
    db = Depends(get_database)
):
    """
    Verify citations against external databases.
    
    Args:
        verification_request: Citations to verify
        background_tasks: Background tasks for async operations
        current_user: Current authenticated user
        db: Database connection
        
    Returns:
        Dict: Verification results
    """
    try:
        logger.info(
            "Verifying citations",
            user_id=current_user.user_id,
            citation_count=len(verification_request.citation_ids)
        )
        
        # 1. Validate that all citations exist and user has access
        for citation_id in verification_request.citation_ids:
            check_query = """
                SELECT c.id FROM citations c
                JOIN documents d ON c.document_id = d.id
                WHERE c.id = $1 AND d.owner_id = $2
            """
            existing = await db.execute_query(check_query, [citation_id, current_user.user_id], fetch="one")
            if not existing:
                raise ResourceNotFoundException(
                    resource_type="Citation",
                    resource_id=citation_id
                )
        
        # 2. Schedule background verification tasks
        verification_job_id = f"verify-{datetime.now().strftime('%Y%m%d-%H%M%S')}-{current_user.user_id[:8]}"
        
        for citation_id in verification_request.citation_ids:
            background_tasks.add_task(
                verify_citation_background,
                citation_id,
                verification_request.verify_doi,
                verification_request.verify_metadata,
                db
            )
        
        # 3. Update citations to show verification in progress
        update_query = """
            UPDATE citations 
            SET verification_status = 'pending',
                updated_at = $1
            WHERE id = ANY($2) AND id IN (
                SELECT c.id FROM citations c
                JOIN documents d ON c.document_id = d.id
                WHERE d.owner_id = $3
            )
        """
        await db.execute_query(
            update_query, 
            [datetime.now(), verification_request.citation_ids, current_user.user_id], 
            fetch="none"
        )
        
        logger.info(
            "Citation verification started",
            user_id=current_user.user_id,
            citation_count=len(verification_request.citation_ids),
            job_id=verification_job_id
        )
        
        return {
            "success": True,
            "message": f"Verification started for {len(verification_request.citation_ids)} citations",
            "verification_job_id": verification_job_id,
            "citation_ids": verification_request.citation_ids
        }
        
    except Exception as e:
        logger.error("Error verifying citations", error=str(e), user_id=current_user.user_id)
        raise HTTPException(status_code=500, detail=f"Failed to verify citations: {str(e)}")

@router.get("/styles", response_model=Dict[str, Any])
async def get_citation_styles():
    """
    Get list of supported citation styles.
    
    Returns:
        Dict: Supported citation styles with descriptions
    """
    try:
        csl_service = get_csl_service()
        styles = csl_service.get_supported_styles()
        
        return {
            "success": True,
            "data": {
                "styles": styles,
                "count": len(styles),
                "default": "apa"
            }
        }
        
    except Exception as e:
        logger.error("Error getting citation styles", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get citation styles: {str(e)}")

@router.post("/validate", response_model=Dict[str, Any])
async def validate_citation(
    citation: CitationCreate,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Validate citation data for CSL processing.
    
    Args:
        citation: Citation data to validate
        current_user: Current authenticated user
        
    Returns:
        Dict: Validation results
    """
    try:
        logger.info("Validating citation", user_id=current_user.user_id, citation_key=citation.citation_key)
        
        # Convert to dict for validation
        citation_dict = citation.dict()
        
        # Use CSL service for validation
        csl_service = get_csl_service()
        validation_result = csl_service.validate_csl_data(citation_dict)
        
        return {
            "success": True,
            "data": validation_result
        }
        
    except Exception as e:
        logger.error("Error validating citation", error=str(e), user_id=current_user.user_id)
        raise HTTPException(status_code=500, detail=f"Failed to validate citation: {str(e)}")

# Background Tasks
async def verify_citation_doi(citation_id: str, doi: str):
    """Background task to verify DOI resolution."""
    try:
        logger.info("Verifying DOI", citation_id=citation_id, doi=doi)
        # TODO: Implement DOI verification
        # 1. Check DOI resolution
        # 2. Fetch metadata
        # 3. Update citation record
        pass
    except Exception as e:
        logger.error("Error verifying DOI", error=str(e), citation_id=citation_id)

async def verify_citation_background(citation_id: str, verify_doi: bool, verify_metadata: bool, db):
    """Background task for comprehensive citation verification."""
    try:
        logger.info("Background citation verification", citation_id=citation_id)
        
        # 1. Fetch citation for verification
        citation_query = """
            SELECT id, doi, title, authors, citation_type
            FROM citations 
            WHERE id = $1
        """
        citation = await db.execute_query(citation_query, [citation_id], fetch="one")
        
        if not citation:
            logger.warning("Citation not found for verification", citation_id=citation_id)
            return
        
        verification_status = "verified"
        verification_notes = []
        
        # 2. Verify DOI if requested and available
        if verify_doi and citation['doi']:
            # Simplified DOI verification (mock)
            if citation['doi'].startswith('10.'):
                verification_notes.append("DOI format valid")
            else:
                verification_status = "warning"
                verification_notes.append("Invalid DOI format")
        
        # 3. Basic metadata verification
        if verify_metadata:
            if not citation['title'] or len(citation['title'].strip()) < 3:
                verification_status = "warning"
                verification_notes.append("Title too short or missing")
            
            if not citation['authors'] or len(citation['authors']) == 0:
                verification_status = "warning"
                verification_notes.append("No authors specified")
        
        # 4. Update verification status
        update_query = """
            UPDATE citations 
            SET verification_status = $1,
                notes = $2,
                verification_date = $3,
                updated_at = $3
            WHERE id = $4
        """
        await db.execute_query(
            update_query, 
            [verification_status, '; '.join(verification_notes), datetime.now(), citation_id], 
            fetch="none"
        )
        
        logger.info(
            "Citation verification completed",
            citation_id=citation_id,
            status=verification_status,
            notes_count=len(verification_notes)
        )
        
    except Exception as e:
        logger.error("Error in background verification", error=str(e), citation_id=citation_id)
        
        # Update citation with error status
        try:
            error_query = """
                UPDATE citations 
                SET verification_status = 'error',
                    notes = $1,
                    updated_at = $2
                WHERE id = $3
            """
            await db.execute_query(
                error_query, 
                [f"Verification error: {str(e)}", datetime.now(), citation_id], 
                fetch="none"
            )
        except Exception as update_error:
            logger.error("Failed to update citation with error status", error=str(update_error), citation_id=citation_id)