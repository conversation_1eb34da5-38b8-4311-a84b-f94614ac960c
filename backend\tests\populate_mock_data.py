#!/usr/bin/env python3
"""
Revisionary Mock Data Population Script

This script populates the Supabase database with realistic mock data
for testing and development purposes. It follows proper dependency
order and provides progress tracking.

Usage:
    python populate_mock_data.py --env development
    python populate_mock_data.py --env development --dry-run
    python populate_mock_data.py --clear-first  # Clear existing mock data first
"""

import asyncio
import os
import sys
import argparse
from pathlib import Path
from typing import List, Dict
import asyncpg
import structlog
from dotenv import load_dotenv

# Add backend to path for imports
sys.path.append(str(Path(__file__).parent))

logger = structlog.get_logger(__name__)


class MockDataPopulator:
    """Mock data population runner for Revisionary."""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.mock_data_dir = Path(__file__).parent / "mock_data"
        
        # Define execution order based on dependencies
        self.mock_data_files = [
            {
                "file": "001_core_data.sql",
                "description": "Core users, documents, blocks, and versions",
                "estimated_records": 200
            },
            {
                "file": "002_ai_collaboration.sql", 
                "description": "AI suggestions, summaries, collaborations, and comments",
                "estimated_records": 150
            },
            {
                "file": "003_analytics_scoring.sql",
                "description": "Document scores, user statistics, achievements, and challenges", 
                "estimated_records": 100
            },
            {
                "file": "004_advanced_features.sql",
                "description": "Personas, custom agents, style rules, and usage events",
                "estimated_records": 120
            },
            {
                "file": "005_scoring_algorithms.sql",
                "description": "Additional scoring algorithm versions",
                "estimated_records": 5
            },
            {
                "file": "006_health_metrics.sql",
                "description": "Document health and quality analysis",
                "estimated_records": 15
            },
            {
                "file": "007_health_issues.sql",
                "description": "Specific problems identified in documents",
                "estimated_records": 20
            },
            {
                "file": "008_citations.sql",
                "description": "Academic and web citations in documents",
                "estimated_records": 15
            },
            {
                "file": "009_audience_analysis.sql",
                "description": "Document audience feedback analysis",
                "estimated_records": 10
            },
            {
                "file": "010_persona_feedback.sql",
                "description": "Effectiveness scoring for user personas",
                "estimated_records": 15
            },
            {
                "file": "011_export_jobs.sql",
                "description": "Document export/download requests",
                "estimated_records": 10
            },
            {
                "file": "012_templates.sql",
                "description": "Reusable document templates",
                "estimated_records": 10
            },
            {
                "file": "013_token_usage.sql",
                "description": "Daily API token consumption aggregations",
                "estimated_records": 20
            },
            {
                "file": "014_agent_usage.sql",
                "description": "AI agent performance metrics",
                "estimated_records": 15
            }
        ]
    
    async def get_connection(self) -> asyncpg.Connection:
        """Get database connection with proper settings for Supabase."""
        return await asyncpg.connect(self.database_url, statement_cache_size=0)
    
    async def check_database_ready(self, conn: asyncpg.Connection) -> bool:
        """Verify database schema is ready for mock data using an existing connection."""
        print("🔍 Checking database readiness...")
        try:
            # Check if essential tables exist
            essential_tables = [
                'users', 'documents', 'blocks', 'versions', 'suggestions',
                'summaries', 'collaborations', 'comments', 'document_scores',
                'personas', 'custom_agents', 'agent_style_rules', 'usage_events'
            ]
            
            print(f"📋 Checking {len(essential_tables)} essential tables...")
            
            for table in essential_tables:
                print(f"  Checking table: {table}")
                exists = await conn.fetchval("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = $1
                    )
                """, table)
                print(f"  Table {table}: {'exists' if exists else 'missing'}")
                
                if not exists:
                    print(f"❌ Required table missing: {table}")
                    return False
            
            print("✅ Database schema verification passed")
            return True
            
        except Exception as e:
            print(f"❌ Database verification failed: {e}")
            return False

    async def clear_mock_data(self, conn: asyncpg.Connection) -> bool:
        """Clear existing mock data (for development/testing) using an existing connection."""
        print("🧹 Clearing existing mock data...")
        
        try:
            # Get all user-data tables (excluding system tables)
            tables_query = """
                SELECT tablename FROM pg_tables 
                WHERE schemaname = 'public' 
                AND tablename NOT IN ('schema_migrations')
                ORDER BY tablename
            """
            tables = await conn.fetch(tables_query)
            table_names = [row['tablename'] for row in tables]
            
            print(f"📋 Found {len(table_names)} tables to clear")
            
            # TRUNCATE all tables with CASCADE to handle foreign keys
            if table_names:
                table_list = ', '.join(table_names)
                await conn.execute(f"TRUNCATE TABLE {table_list} RESTART IDENTITY CASCADE")
                print(f"🗑️  Truncated {len(table_names)} tables with CASCADE")
                
                # Validate that tables are actually empty
                validation_failed = False
                for table in table_names:
                    try:
                        count = await conn.fetchval(f'SELECT COUNT(*) FROM "{table}"')
                        if count > 0:
                            print(f"⚠️  Warning: {table} still has {count} rows after truncation")
                            validation_failed = True
                    except Exception as e:
                        print(f"⚠️  Could not validate {table}: {e}")
                
                if validation_failed:
                    print("❌ Some tables still contain data after clearing")
                    return False
            
            print("✅ Mock data cleared successfully - all tables validated empty")
            return True
            
        except Exception as e:
            print(f"❌ Failed to clear mock data: {e}")
            return False

    async def count_existing_records(self, conn: asyncpg.Connection) -> Dict[str, int]:
        """Count existing records in key tables using an existing connection."""
        try:
            tables = ['users', 'documents', 'blocks', 'suggestions', 'personas', 'custom_agents']
            counts = {}
            
            for table in tables:
                exists = await conn.fetchval("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' AND table_name = $1
                    )
                """, table)
                
                if exists:
                    count = await conn.fetchval(f'SELECT COUNT(*) FROM "{table}"')
                    counts[table] = count
                else:
                    counts[table] = 0
            
            return counts
            
        except Exception as e:
            print(f"❌ Failed to count existing records: {e}")
            return {}

    async def run_mock_data_file(self, conn: asyncpg.Connection, file_info: Dict) -> bool:
        """Run a single mock data file."""
        file_path = self.mock_data_dir / file_info["file"]
        
        print(f"📝 Processing: {file_info['file']} - {file_info['description']}")
        
        if not file_path.exists():
            print(f"❌ Mock data file not found: {file_path}")
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                mock_data_sql = f.read()
            
            logger.info("Mock data file loaded", file=file_info["file"], size_chars=len(mock_data_sql))
            
            async with conn.transaction():
                await conn.execute(mock_data_sql)
            
            logger.info("Mock data file completed successfully", file=file_info["file"], estimated_records=file_info["estimated_records"])
            return True
            
        except Exception as e:
            logger.error("Mock data file failed", file=file_info["file"], error=str(e))
            return False

    async def populate_mock_data(self, conn: asyncpg.Connection, dry_run: bool = False) -> bool:
        """Populate database with all mock data files using an existing connection."""
        logger.info("Starting mock data population", dry_run=dry_run)
        
        existing_counts = await self.count_existing_records(conn)
        logger.info("Current database state", existing_records=existing_counts)
        
        if dry_run:
            logger.info("DRY RUN - Would populate the following files:")
            for file_info in self.mock_data_files:
                logger.info("Would process", file=file_info["file"], description=file_info["description"], estimated_records=file_info["estimated_records"])
            return True
        
        total_files = len(self.mock_data_files)
        successful_files = 0
        
        for i, file_info in enumerate(self.mock_data_files, 1):
            logger.info("Processing file", progress=f"{i}/{total_files}", file=file_info["file"])
            
            success = await self.run_mock_data_file(conn, file_info)
            if success:
                successful_files += 1
                logger.info("File completed", progress=f"{successful_files}/{total_files}", file=file_info["file"])
            else:
                logger.error("File failed, stopping population", file=file_info["file"])
                return False
        
        if successful_files == total_files:
            final_counts = await self.count_existing_records(conn)
            logger.info("Mock data population completed successfully", files_processed=successful_files, final_records=final_counts)
            return True
        else:
            logger.error("Mock data population incomplete", successful_files=successful_files, total_files=total_files)
            return False

    async def verify_population(self, conn: asyncpg.Connection) -> bool:
        """Verify mock data was populated correctly using an existing connection."""
        logger.info("Verifying mock data population...")
        
        try:
            # Use broader queries to check if mock data exists
            users = await conn.fetchval("SELECT COUNT(*) FROM users")
            documents = await conn.fetchval("SELECT COUNT(*) FROM documents")
            suggestions = await conn.fetchval("SELECT COUNT(*) FROM suggestions")
            personas = await conn.fetchval("SELECT COUNT(*) FROM personas")
            blocks = await conn.fetchval("SELECT COUNT(*) FROM blocks")
            
            verification_results = {
                "users": users, "documents": documents, 
                "suggestions": suggestions, "personas": personas, "blocks": blocks
            }
            logger.info("Verification completed", results=verification_results)
            
            # More reasonable thresholds based on our actual mock data
            if (users >= 5 and documents >= 10 and suggestions >= 10 and personas >= 5 and blocks >= 15):
                logger.info("✅ Mock data verification passed")
                print("✅ All verification checks passed!")
                return True
            else:
                logger.warning("⚠️ Mock data verification showed low counts")
                print(f"⚠️ Verification failed - Users: {users}/5, Documents: {documents}/10, Suggestions: {suggestions}/10, Personas: {personas}/5, Blocks: {blocks}/15")
                return False
                
        except Exception as e:
            logger.error("Verification failed", error=str(e))
            return False

    async def run(self, dry_run: bool, clear_first: bool, verify_only: bool):
        """Run the entire mock data population process with a single connection."""
        conn = None
        try:
            conn = await self.get_connection()
            logger.info("Database connection established.")

            if verify_only:
                await self.verify_population(conn)
                return

            if not await self.check_database_ready(conn):
                logger.error("Database not ready for mock data population")
                return

            if clear_first and not dry_run:
                if not await self.clear_mock_data(conn):
                    return

            success = await self.populate_mock_data(conn, dry_run=dry_run)
            
            if success and not dry_run:
                if await self.verify_population(conn):
                    print("\n" + "="*60)
                    print("✅ MOCK DATA POPULATION SUCCESSFUL!")
                    print("="*60)
                    print("Your Supabase database now contains realistic mock data.")
                    print("="*60)
                else:
                    logger.warning("Population completed but verification had issues")

        except Exception as e:
            logger.error("An error occurred during the population process", error=str(e))
        finally:
            if conn:
                await conn.close()
                logger.info("Database connection closed.")


async def main():
    """Main mock data population runner."""
    parser = argparse.ArgumentParser(description="Populate Revisionary mock data")
    parser.add_argument("--env", choices=["development", "staging"], default="development", help="Environment to populate mock data in")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be populated without executing")
    parser.add_argument("--clear-first", action="store_true", help="Clear existing mock data before populating new data")
    parser.add_argument("--verify-only", action="store_true", help="Only verify existing mock data without populating")
    parser.add_argument("--database-url", help="Override database URL from environment")
    
    args = parser.parse_args()
    
    if args.env == "development":
        load_dotenv("../.env")
    elif args.env == "staging":
        load_dotenv("../.env.staging")
    
    database_url = args.database_url or os.getenv("DATABASE_URL")
    
    if not database_url:
        logger.error("No database URL provided")
        sys.exit(1)
    
    if "production" in database_url.lower():
        logger.error("Mock data population not allowed on production database")
        sys.exit(1)
    
    populator = MockDataPopulator(database_url)
    
    try:
        await populator.run(
            dry_run=args.dry_run,
            clear_first=args.clear_first,
            verify_only=args.verify_only
        )
    except Exception as e:
        logger.error("Mock data population process failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    asyncio.run(main())