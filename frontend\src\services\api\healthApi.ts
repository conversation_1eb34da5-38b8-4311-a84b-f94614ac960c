/**
 * Health Analysis API Service
 * 
 * Handles all writing health analysis API calls including:
 * - Real-time health analysis
 * - Health metrics and scoring
 * - Issue detection and resolution
 * - Health trends and insights
 */

import { baseApi, ApiResponse } from './baseApi';

export interface HealthMetrics {
  id: string;
  block_id: string;
  user_id: string;
  readability_score: number;
  clarity_score: number;
  voice_consistency_score: number;
  brand_alignment_score?: number;
  overall_score: number;
  analysis_details: {
    readability: {
      flesch_kincaid: number;
      gunning_fog: number;
      avg_sentence_length: number;
      complex_words: number;
    };
    clarity: {
      passive_voice_percentage: number;
      sentence_variety: number;
      transition_usage: number;
      clarity_issues: string[];
    };
    voice: {
      tone_consistency: number;
      style_consistency: number;
      voice_issues: string[];
    };
    brand?: {
      brand_voice_match: number;
      terminology_consistency: number;
      brand_issues: string[];
    };
  };
  suggestions: Array<{
    type: 'readability' | 'clarity' | 'voice' | 'brand';
    priority: 'low' | 'medium' | 'high';
    title: string;
    description: string;
    suggested_fix?: string;
  }>;
  created_at: string;
}

export interface HealthAnalysisRequest {
  text: string;
  context?: {
    document_type?: string;
    target_audience?: string;
    brand_guidelines?: Record<string, any>;
  };
  options?: {
    include_suggestions?: boolean;
    analysis_depth?: 'quick' | 'detailed';
    focus_areas?: Array<'readability' | 'clarity' | 'voice' | 'brand'>;
  };
}

export interface HealthIssue {
  id: string;
  block_id: string;
  user_id: string;
  issue_type: 'readability' | 'clarity' | 'voice' | 'brand' | 'grammar' | 'style';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  location: {
    start: number;
    end: number;
    text: string;
  };
  suggested_fix?: string;
  auto_fixable: boolean;
  status: 'open' | 'resolved' | 'ignored';
  resolved_at?: string;
  created_at: string;
}

export interface HealthTrends {
  user_id: string;
  time_period: {
    start: string;
    end: string;
  };
  overall_trend: {
    current_score: number;
    previous_score: number;
    change_percentage: number;
    trend_direction: 'improving' | 'declining' | 'stable';
  };
  metric_trends: {
    readability: { scores: number[]; dates: string[] };
    clarity: { scores: number[]; dates: string[] };
    voice_consistency: { scores: number[]; dates: string[] };
    brand_alignment: { scores: number[]; dates: string[] };
  };
  insights: Array<{
    type: 'improvement' | 'concern' | 'achievement';
    title: string;
    description: string;
    metric: string;
    value: number;
  }>;
}

export interface HealthPreferences {
  auto_analysis: boolean;
  real_time_feedback: boolean;
  analysis_depth: 'quick' | 'detailed';
  focus_areas: Array<'readability' | 'clarity' | 'voice' | 'brand'>;
  target_scores: {
    readability: number;
    clarity: number;
    voice_consistency: number;
    brand_alignment?: number;
  };
  notification_settings: {
    score_improvements: boolean;
    issues_detected: boolean;
    weekly_reports: boolean;
  };
  brand_guidelines?: {
    tone: string;
    voice_characteristics: string[];
    terminology: Record<string, string>;
    style_preferences: Record<string, any>;
  };
}

class HealthApiService {
  private analysisCache = new Map<string, { result: HealthMetrics; timestamp: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Analyze text health in real-time
   */
  async analyzeText(request: HealthAnalysisRequest): Promise<HealthMetrics> {
    const cacheKey = this.generateCacheKey(request.text);
    const cached = this.analysisCache.get(cacheKey);
    
    // Return cached result if still valid
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      console.log('[Health API] Returning cached analysis result');
      return cached.result;
    }

    const response = await baseApi.post<HealthMetrics>('/health/analyze', request);
    
    // Cache the result
    this.analysisCache.set(cacheKey, {
      result: response.data,
      timestamp: Date.now()
    });

    return response.data;
  }

  /**
   * Analyze text health with debouncing (for real-time analysis)
   */
  async analyzeTextDebounced(request: HealthAnalysisRequest): Promise<HealthMetrics> {
    const response = await baseApi.post<HealthMetrics>('/health/analyze-debounced', request);
    return response.data;
  }

  /**
   * Batch analyze multiple text blocks
   */
  async batchAnalyze(requests: HealthAnalysisRequest[]): Promise<HealthMetrics[]> {
    const response = await baseApi.post<HealthMetrics[]>('/health/batch-analyze', {
      requests
    });
    return response.data;
  }

  /**
   * Get health trends for user
   */
  async getHealthTrends(params?: {
    time_range?: 'week' | 'month' | 'quarter' | 'year';
    metric_focus?: 'overall' | 'readability' | 'clarity' | 'voice' | 'brand';
  }): Promise<HealthTrends> {
    const response = await baseApi.get<HealthTrends>('/health/trends', { params });
    return response.data;
  }

  /**
   * Get health issues for a specific block
   */
  async getBlockHealthIssues(blockId: string): Promise<HealthIssue[]> {
    const response = await baseApi.get<HealthIssue[]>(`/health/issues/${blockId}`);
    return response.data;
  }

  /**
   * Get all health issues for a document
   */
  async getDocumentHealthIssues(documentId: string, filters?: {
    severity?: HealthIssue['severity'];
    status?: HealthIssue['status'];
    issue_type?: HealthIssue['issue_type'];
  }): Promise<HealthIssue[]> {
    const response = await baseApi.get<HealthIssue[]>(`/health/document/${documentId}/issues`, {
      params: filters
    });
    return response.data;
  }

  /**
   * Resolve a health issue
   */
  async resolveHealthIssue(issueId: string, resolution?: {
    action: 'fixed' | 'ignored' | 'false_positive';
    comment?: string;
  }): Promise<HealthIssue> {
    const response = await baseApi.post<HealthIssue>('/health/resolve-issue', {
      issue_id: issueId,
      resolution
    });
    return response.data;
  }

  /**
   * Auto-fix health issues where possible
   */
  async autoFixIssues(issueIds: string[]): Promise<{
    fixed_issues: string[];
    failed_fixes: Array<{ issue_id: string; reason: string }>;
    updated_content: Record<string, string>; // block_id -> new_content
  }> {
    const response = await baseApi.post('/health/auto-fix', {
      issue_ids: issueIds
    });
    return response.data;
  }

  /**
   * Get health preferences
   */
  async getHealthPreferences(): Promise<HealthPreferences> {
    const response = await baseApi.get<HealthPreferences>('/health/preferences');
    return response.data;
  }

  /**
   * Update health preferences
   */
  async updateHealthPreferences(preferences: Partial<HealthPreferences>): Promise<HealthPreferences> {
    const response = await baseApi.put<HealthPreferences>('/health/preferences', preferences);
    return response.data;
  }

  /**
   * Get health progress/improvement over time
   */
  async getHealthProgress(params?: {
    document_id?: string;
    time_range?: 'week' | 'month' | 'quarter';
  }): Promise<{
    current_period: {
      avg_score: number;
      total_issues: number;
      resolved_issues: number;
      documents_analyzed: number;
    };
    previous_period: {
      avg_score: number;
      total_issues: number;
      resolved_issues: number;
      documents_analyzed: number;
    };
    improvement_percentage: number;
    key_improvements: string[];
    recommendations: string[];
  }> {
    const response = await baseApi.get('/health/progress', { params });
    return response.data;
  }

  /**
   * Get health insights and recommendations
   */
  async getHealthInsights(documentId?: string): Promise<{
    overall_assessment: {
      score: number;
      grade: 'A' | 'B' | 'C' | 'D' | 'F';
      summary: string;
    };
    strengths: string[];
    areas_for_improvement: string[];
    priority_actions: Array<{
      action: string;
      impact: 'high' | 'medium' | 'low';
      effort: 'easy' | 'moderate' | 'difficult';
    }>;
    writing_patterns: {
      common_issues: string[];
      improvement_trends: string[];
      consistency_score: number;
    };
  }> {
    const params = documentId ? { document_id: documentId } : undefined;
    const response = await baseApi.get('/health/insights', { params });
    return response.data;
  }

  /**
   * Generate health report
   */
  async generateHealthReport(params: {
    document_id?: string;
    time_range?: 'week' | 'month' | 'quarter';
    format?: 'summary' | 'detailed';
  }): Promise<{
    report_url: string;
    expires_at: string;
  }> {
    const response = await baseApi.post('/health/report', params);
    return response.data;
  }

  // Utility methods
  private generateCacheKey(text: string): string {
    // Create a simple hash of the text for caching
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `health_${Math.abs(hash)}`;
  }

  /**
   * Clear analysis cache
   */
  clearCache(): void {
    this.analysisCache.clear();
    console.log('[Health API] Analysis cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number;
    entries: Array<{ key: string; age: number }>;
  } {
    const now = Date.now();
    const entries = Array.from(this.analysisCache.entries()).map(([key, value]) => ({
      key,
      age: now - value.timestamp
    }));

    return {
      size: this.analysisCache.size,
      entries
    };
  }
}

// Export singleton instance
export const healthApi = new HealthApiService();
export default healthApi;