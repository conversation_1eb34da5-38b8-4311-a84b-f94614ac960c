-- Revisionary Mock Data - Part 14: Agent Usage Stats
-- AI agent performance metrics

DO $$
DECLARE
    -- Get user IDs from firebase_uid (from 001_core_data.sql)
    sarah_id UUID;
    alex_id UUID;
    kim_id UUID;
    maya_id UUID;
    james_id UUID;
    
BEGIN
    -- Get user IDs from existing users
    SELECT id INTO sarah_id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1;
    SELECT id INTO alex_id FROM users WHERE firebase_uid = 'firebase_user_002' LIMIT 1;
    SELECT id INTO kim_id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1;
    SELECT id INTO maya_id FROM users WHERE firebase_uid = 'firebase_user_004' LIMIT 1;
    SELECT id INTO james_id FROM users WHERE firebase_uid = 'firebase_user_005' LIMIT 1;

    -- =================================================================
    -- AGENT USAGE STATS - AI agent performance metrics
    -- =================================================================
    
    INSERT INTO agent_usage_stats (agent_id, user_id, date, invocations, total_processing_time_ms, suggestions_generated, suggestions_accepted, suggestions_rejected, avg_confidence, error_count, tokens_used) 
    VALUES
    ((SELECT id FROM custom_agents WHERE name = 'Medical Terminology Validator' AND user_id = kim_id LIMIT 1), kim_id, CURRENT_DATE, 156, 444600000, 148, 135, 13, 0.91, 8, 12850),
    
    ((SELECT id FROM custom_agents WHERE name = 'Research Structure Optimizer' AND user_id = kim_id LIMIT 1), kim_id, CURRENT_DATE - INTERVAL '1 day', 89, 373800000, 84, 73, 11, 0.87, 5, 8950),
    
    ((SELECT id FROM custom_agents WHERE name = 'World-Building Consistency Checker' AND user_id = maya_id LIMIT 1), maya_id, CURRENT_DATE, 234, 853100000, 221, 208, 13, 0.94, 13, 18750),
    
    ((SELECT id FROM custom_agents WHERE name = 'Dialogue Enhancement Specialist' AND user_id = maya_id LIMIT 1), maya_id, CURRENT_DATE - INTERVAL '1 day', 178, 530440000, 165, 145, 20, 0.88, 13, 14200),
    
    ((SELECT id FROM custom_agents WHERE name = 'Executive Summary Generator' AND user_id = james_id LIMIT 1), james_id, CURRENT_DATE, 67, 361800000, 64, 61, 3, 0.96, 3, 11450),
    
    ((SELECT id FROM custom_agents WHERE name = 'Data Storytelling Assistant' AND user_id = james_id LIMIT 1), james_id, CURRENT_DATE - INTERVAL '2 days', 92, 441600000, 88, 79, 9, 0.89, 4, 13200),
    
    ((SELECT id FROM custom_agents WHERE name = 'Climate Data Interpreter' AND user_id = sarah_id LIMIT 1), sarah_id, CURRENT_DATE - INTERVAL '1 day', 43, 137600000, 39, 33, 6, 0.84, 4, 5680),
    
    ((SELECT id FROM custom_agents WHERE name = 'Atmospheric Enhancement Agent' AND user_id = alex_id LIMIT 1), alex_id, CURRENT_DATE, 78, 206700000, 72, 62, 10, 0.86, 6, 7850);

END $$;