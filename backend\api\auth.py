"""
Authentication API Endpoints

Purpose: Provides REST API endpoints for user authentication including:
- Firebase ID token exchange for API access tokens
- Token refresh and validation
- User profile management
- Session management and logout

Responsibilities:
- Firebase authentication integration
- JWT token generation and validation
- User session management
- Authentication middleware integration
- Security headers and rate limiting

Used by: Frontend authentication components, protected API endpoints
Dependencies: core.auth, Firebase Admin SDK
"""

from fastapi import APIRouter, HTTPException, Depends, Header
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
from typing import Optional
import structlog

from core.auth import AuthService, UserClaims
from core.database import get_database, DatabaseService

# Configure logging
logger = structlog.get_logger(__name__)

# Create router
router = APIRouter()

# Security scheme
security = HTTPBearer(auto_error=False)

# Request/Response Models
class TokenExchangeRequest(BaseModel):
    """Request model for Firebase token exchange."""
    firebase_token: str

class TokenExchangeResponse(BaseModel):
    """Response model for token exchange."""
    success: bool
    data: dict

class RefreshTokenRequest(BaseModel):
    """Request model for token refresh."""
    refresh_token: str

class UserProfileResponse(BaseModel):
    """Response model for user profile."""
    success: bool
    data: dict

# Authentication dependency
async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> UserClaims:
    """
    Authentication dependency to extract current user from JWT token.
    
    Args:
        credentials: HTTP Bearer token credentials
        
    Returns:
        UserClaims: Validated user claims from token
        
    Raises:
        HTTPException: If token is invalid or expired
    """
    # Check if we're in mock mode first
    if not AuthService._initialized:
        await AuthService.initialize()
    
    logger.info("Authentication check", mock_mode=AuthService.is_mock_mode(), has_credentials=bool(credentials))
    
    if AuthService.is_mock_mode():
        logger.info("Using mock authentication mode - bypassing token validation")
        from datetime import datetime, timedelta
        return UserClaims(
            user_id="14285842-26d6-4a48-8b7c-1ac76fa5c488",
            email="<EMAIL>",
            display_name="Mock User",
            subscription_tier="professional",
            subscription_status="active",
            expires_at=datetime.utcnow() + timedelta(hours=1)
        )
    
    # Production mode - require valid token
    if not credentials:
        raise HTTPException(
            status_code=401,
            detail="Authorization header required"
        )
    
    try:
        token = credentials.credentials
        return await AuthService.verify_token(token)
    except Exception as e:
        logger.warning("Token validation failed", error=str(e))
        raise HTTPException(
            status_code=401,
            detail="Invalid or expired token"
        )

@router.post("/token", response_model=TokenExchangeResponse)
async def exchange_firebase_token(request: TokenExchangeRequest):
    """
    Exchange Firebase ID token for API access token.
    
    This endpoint validates the Firebase ID token and returns
    an API access token that can be used for authenticated requests.
    
    Args:
        request: Token exchange request with Firebase ID token
        
    Returns:
        TokenExchangeResponse: API access token and user information
        
    Raises:
        HTTPException: If Firebase token is invalid
    """
    try:
        logger.info("Token exchange requested")
        
        # Validate Firebase token and get user claims
        user_claims = await AuthService.verify_firebase_token(request.firebase_token)
        
        # Generate API access token
        access_token = await AuthService.generate_access_token(user_claims)
        refresh_token = await AuthService.generate_refresh_token(user_claims)
        
        # Get or create user profile
        user_profile = await AuthService.get_or_create_user(user_claims)
        
        logger.info("Token exchange successful", user_id=user_claims.user_id)
        
        return TokenExchangeResponse(
            success=True,
            data={
                "access_token": access_token,
                "refresh_token": refresh_token,
                "expires_in": 3600,
                "token_type": "Bearer",
                "user": {
                    "id": user_profile.id,
                    "email": user_profile.email,
                    "display_name": user_profile.display_name,
                    "subscription_tier": user_profile.subscription_tier,
                    "subscription_status": user_profile.subscription_status,
                }
            }
        )
        
    except Exception as e:
        logger.error("Token exchange failed", error=str(e))
        raise HTTPException(
            status_code=401,
            detail="Invalid Firebase token"
        )

@router.post("/refresh", response_model=TokenExchangeResponse)
async def refresh_access_token(request: RefreshTokenRequest):
    """
    Refresh an expired access token using refresh token.
    
    Args:
        request: Refresh token request
        
    Returns:
        TokenExchangeResponse: New access token
        
    Raises:
        HTTPException: If refresh token is invalid
    """
    try:
        logger.info("Token refresh requested")
        
        # Validate refresh token and get user claims
        user_claims = await AuthService.verify_refresh_token(request.refresh_token)
        
        # Generate new access token
        new_access_token = await AuthService.generate_access_token(user_claims)
        
        logger.info("Token refresh successful", user_id=user_claims.user_id)
        
        return TokenExchangeResponse(
            success=True,
            data={
                "access_token": new_access_token,
                "expires_in": 3600,
                "token_type": "Bearer"
            }
        )
        
    except Exception as e:
        logger.error("Token refresh failed", error=str(e))
        raise HTTPException(
            status_code=401,
            detail="Invalid refresh token"
        )

@router.get("/me", response_model=UserProfileResponse)
async def get_current_user_profile(current_user: UserClaims = Depends(get_current_user)):
    """
    Get current authenticated user's profile information.
    
    Args:
        current_user: Current authenticated user from token
        
    Returns:
        UserProfileResponse: User profile information
    """
    try:
        logger.info("User profile requested", user_id=current_user.user_id)
        
        # Get user profile from database
        user_profile = await AuthService.get_user_profile(current_user.user_id)
        
        return UserProfileResponse(
            success=True,
            data={
                "id": user_profile.id,
                "email": user_profile.email,
                "display_name": user_profile.display_name,
                "avatar_url": user_profile.avatar_url,
                "subscription_tier": user_profile.subscription_tier,
                "subscription_status": user_profile.subscription_status,
                "created_at": user_profile.created_at.isoformat(),
                "last_active_at": user_profile.last_active_at.isoformat() if user_profile.last_active_at else None,
                "preferences": user_profile.preferences,
            }
        )
        
    except Exception as e:
        logger.error("Failed to get user profile", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve user profile"
        )

@router.post("/logout")
async def logout_user(current_user: UserClaims = Depends(get_current_user)):
    """
    Logout current user and invalidate tokens.
    
    Args:
        current_user: Current authenticated user from token
        
    Returns:
        dict: Success response
    """
    try:
        logger.info("User logout requested", user_id=current_user.user_id)
        
        # Invalidate user tokens
        await AuthService.invalidate_user_tokens(current_user.user_id)
        
        logger.info("User logout successful", user_id=current_user.user_id)
        
        return {"success": True, "message": "Logged out successfully"}
        
    except Exception as e:
        logger.error("Logout failed", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Logout failed"
        )

@router.get("/validate")
async def validate_token(current_user: UserClaims = Depends(get_current_user)):
    """
    Validate current access token.
    
    Args:
        current_user: Current authenticated user from token
        
    Returns:
        dict: Token validation response
    """
    return {
        "success": True,
        "valid": True,
        "user_id": current_user.user_id,
        "expires_at": current_user.expires_at
    }