-- Revisionary Mock Data - Part 11: Export Jobs
-- Document export/download requests

DO $$
DECLARE
    -- Get user IDs from firebase_uid (from 001_core_data.sql)
    sarah_id UUID;
    alex_id UUID;
    kim_id UUID;
    maya_id UUID;
    james_id UUID;
    
    -- Get document IDs
    sarah_climate_doc_id UUID;
    alex_cafe_doc_id UUID;
    kim_neural_doc_id UUID;
    maya_chap1_doc_id UUID;
    maya_world_doc_id UUID;
    james_q4_doc_id UUID;
    
BEGIN
    -- Get user IDs from existing users
    SELECT id INTO sarah_id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1;
    SELECT id INTO alex_id FROM users WHERE firebase_uid = 'firebase_user_002' LIMIT 1;
    SELECT id INTO kim_id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1;
    SELECT id INTO maya_id FROM users WHERE firebase_uid = 'firebase_user_004' LIMIT 1;
    SELECT id INTO james_id FROM users WHERE firebase_uid = 'firebase_user_005' LIMIT 1;
    
    -- Get document IDs from existing documents
    SELECT id INTO sarah_climate_doc_id FROM documents WHERE title = 'Climate Change Research Paper' LIMIT 1;
    SELECT id INTO alex_cafe_doc_id FROM documents WHERE title = 'The Midnight Café' LIMIT 1;
    SELECT id INTO kim_neural_doc_id FROM documents WHERE title = 'Neural Networks in Medical Diagnosis' LIMIT 1;
    SELECT id INTO maya_chap1_doc_id FROM documents WHERE title = 'Chronicles of Aetheria: Chapter 1' LIMIT 1;
    SELECT id INTO maya_world_doc_id FROM documents WHERE title = 'World Building Bible' LIMIT 1;
    SELECT id INTO james_q4_doc_id FROM documents WHERE title = 'Q4 Marketing Strategy Report' LIMIT 1;

    -- =================================================================
    -- EXPORT JOBS - Document export/download requests
    -- =================================================================
    
    INSERT INTO export_jobs (user_id, document_id, format, status, file_size, file_url, options, processing_started_at, processing_completed_at, expires_at) 
    VALUES
    (kim_id, kim_neural_doc_id, 'pdf', 'completed', 2847392, 
     'https://storage.supabase.co/exports/neural_networks_medical_diagnosis_20240625.pdf', 
     '{"page_count": 24, "include_citations": true, "formatting": "academic", "header_footer": true, "table_of_contents": true}'::jsonb, 
     NOW() - INTERVAL '2 hours', NOW() - INTERVAL '1 hour 45 minutes', NOW() + INTERVAL '7 days'),
    
    (james_id, james_q4_doc_id, 'docx', 'completed', 1543288, 
     'https://storage.supabase.co/exports/q4_marketing_strategy_20240625.docx', 
     '{"page_count": 18, "include_charts": true, "formatting": "business", "executive_summary": true, "appendices": true}'::jsonb, 
     NOW() - INTERVAL '3 hours', NOW() - INTERVAL '2 hours 30 minutes', NOW() + INTERVAL '7 days'),
    
    (maya_id, maya_chap1_doc_id, 'epub', 'completed', 892156, 
     'https://storage.supabase.co/exports/chronicles_aetheria_chapter1_20240625.epub', 
     '{"word_count": 3200, "include_metadata": true, "formatting": "ebook", "chapter_breaks": true, "font_embedding": true}'::jsonb, 
     NOW() - INTERVAL '1 day', NOW() - INTERVAL '23 hours', NOW() + INTERVAL '14 days'),
    
    (sarah_id, sarah_climate_doc_id, 'latex', 'processing', NULL, NULL, 
     '{"target_journal": "Climate Science Quarterly", "citation_style": "apa", "include_figures": true, "bibliography_separate": true}'::jsonb, 
     NOW() - INTERVAL '30 minutes', NULL, NOW() + INTERVAL '3 days'),
    
    (alex_id, alex_cafe_doc_id, 'html', 'completed', 156789, 
     'https://storage.supabase.co/exports/midnight_cafe_20240625.html', 
     '{"word_count": 2100, "include_styling": true, "formatting": "web", "responsive": true, "social_meta": true}'::jsonb, 
     NOW() - INTERVAL '4 hours', NOW() - INTERVAL '3 hours 45 minutes', NOW() + INTERVAL '7 days'),
    
    (maya_id, maya_world_doc_id, 'markdown', 'failed', NULL, NULL, 
     '{"target_size": 15728640, "max_size": 10485760, "retry_count": 2}'::jsonb, 
     NOW() - INTERVAL '6 hours', NULL, NOW() + INTERVAL '1 day');

END $$;