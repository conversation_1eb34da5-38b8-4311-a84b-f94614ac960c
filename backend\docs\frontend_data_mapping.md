
# Frontend Data Mapping

This document outlines the database tables and columns that the frontend will need to interact with.

## Core Tables

### `users`

| Column | Data Type | Description | Frontend Usage |
|---|---|---|---|
| `id` | UUID | Primary key for the user. | Used for identifying the current user. |
| `firebase_uid` | VARCHAR(128) | Firebase unique identifier. | Used for authentication. |
| `email` | VARCHAR(255) | User's email address. | Displayed in user profile. |
| `display_name` | VARCHAR(255) | User's display name. | Displayed in user profile and comments. |
| `avatar_url` | TEXT | URL for the user's avatar. | Displayed in user profile and comments. |
| `subscription_tier` | subscription_tier_enum | User's subscription plan. | Determines access to features. |
| `subscription_status` | subscription_status_enum | Status of the user's subscription. | Displayed in billing/settings. |
| `onboarding_completed` | BOOLEAN | Flag for whether the user has completed the onboarding process. | Used to trigger onboarding flow. |
| `preferences` | JSONB | User-specific preferences. | Used to customize the user experience. |

### `documents`

| Column | Data Type | Description | Frontend Usage |
|---|---|---|---|
| `id` | UUID | Primary key for the document. | Used for fetching and updating documents. |
| `owner_id` | UUID | Foreign key to the `users` table. | Used to identify the document owner. |
| `title` | VARCHAR(500) | The title of the document. | Displayed in the document list and editor. |
| `type` | document_type_enum | The type of document (e.g., creative, academic). | Used for filtering and applying specific logic. |
| `status` | document_status_enum | The current status of the document. | Displayed in the document list. |
| `word_count` | INTEGER | The total number of words in the document. | Displayed in the document list and editor. |
| `version` | INTEGER | The current version of the document. | Displayed in the editor. |
| `is_public` | BOOLEAN | Flag for whether the document is publicly accessible. | Used to control access. |
| `updated_at` | TIMESTAMPTZ | The timestamp of the last update. | Displayed in the document list. |

### `blocks`

| Column | Data Type | Description | Frontend Usage |
|---|---|---|---|
| `id` | UUID | Primary key for the block. | Used for fetching and updating individual blocks of content. |
| `document_id` | UUID | Foreign key to the `documents` table. | Used to associate blocks with a document. |
| `parent_id` | UUID | Foreign key to another block (for nesting). | Used to build the document hierarchy. |
| `type` | block_type_enum | The type of block (e.g., paragraph, heading). | Used to render the block with the correct styling. |
| `position` | INTEGER | The order of the block within its parent. | Used to maintain the document structure. |
| `content` | TEXT | The text content of the block. | The main content displayed in the editor. |

### `collaborations`

| Column | Data Type | Description | Frontend Usage |
|---|---|---|---|
| `id` | UUID | Primary key for the collaboration. | |
| `document_id` | UUID | Foreign key to the `documents` table. | Identifies the document being collaborated on. |
| `user_id` | UUID | Foreign key to the `users` table. | Identifies the collaborator. |
| `role` | collaboration_role_enum | The role of the collaborator. | Determines the user's permissions. |
| `status` | collaboration_status_enum | The status of the collaboration invitation. | Displayed in the collaboration management UI. |

### `comments`

| Column | Data Type | Description | Frontend Usage |
|---|---|---|---|
| `id` | UUID | Primary key for the comment. | |
| `block_id` | UUID | Foreign key to the `blocks` table. | Associates the comment with a specific block of content. |
| `author_id` | UUID | Foreign key to the `users` table. | Identifies the author of the comment. |
| `content` | TEXT | The text of the comment. | Displayed in the comments panel. |
| `status` | comment_status_enum | The status of the comment. | Used to filter and manage comments. |

## Analytics Tables

### `document_scores`

| Column | Data Type | Description | Frontend Usage |
|---|---|---|---|
| `document_id` | UUID | Foreign key to the `documents` table. | |
| `total_score` | DECIMAL(5,2) | The overall score for the document. | Displayed in the analytics dashboard. |
| `grammar_score` | DECIMAL(5,2) | The grammar score for the document. | Displayed in the analytics dashboard. |
| `style_score` | DECIMAL(5,2) | The style score for the document. | Displayed in the analytics dashboard. |
| `structure_score` | DECIMAL(5,2) | The structure score for the document. | Displayed in the analytics dashboard. |
| `content_score` | DECIMAL(5,2) | The content score for the document. | Displayed in the analytics dashboard. |
| `improvement_suggestions` | TEXT[] | A list of suggestions for improving the document. | Displayed in the analytics dashboard. |

### `user_writing_stats`

| Column | Data Type | Description | Frontend Usage |
|---|---|---|---|
| `user_id` | UUID | Foreign key to the `users` table. | |
| `date` | DATE | The date of the writing activity. | Used for time-series charts. |
| `average_score` | DECIMAL(5,2) | The user's average score for the day. | Displayed in the analytics dashboard. |
| `total_words` | INTEGER | The total number of words written by the user for the day. | Displayed in the analytics dashboard. |
| `streak_days` | INTEGER | The user's current writing streak. | Displayed in the analytics dashboard. |

### `writing_achievements`

| Column | Data Type | Description | Frontend Usage |
|---|---|---|---|
| `user_id` | UUID | Foreign key to the `users` table. | |
| `achievement_name` | VARCHAR(100) | The name of the achievement. | Displayed in the user's profile and notifications. |
| `achievement_level` | achievement_level_enum | The level of the achievement. | Displayed with the achievement name. |
| `earned_at` | TIMESTAMPTZ | The timestamp when the achievement was earned. | |

## Persona and Agent Tables

### `personas`

| Column | Data Type | Description | Frontend Usage |
|---|---|---|---|
| `id` | UUID | Primary key for the persona. | |
| `user_id` | UUID | Foreign key to the `users` table. | |
| `name` | VARCHAR(255) | The name of the persona. | Displayed in the persona management UI. |
| `description` | TEXT | A description of the persona. | Displayed in the persona management UI. |
| `type` | persona_type_enum | The type of persona. | Used for filtering and organization. |

### `custom_agents`

| Column | Data Type | Description | Frontend Usage |
|---|---|---|---|
| `id` | UUID | Primary key for the custom agent. | |
| `user_id` | UUID | Foreign key to the `users` table. | |
| `name` | VARCHAR(255) | The name of the agent. | Displayed in the agent management UI. |
| `description` | TEXT | A description of the agent. | Displayed in the agent management UI. |
| `type` | agent_type_enum | The type of agent. | Used for filtering and organization. |
