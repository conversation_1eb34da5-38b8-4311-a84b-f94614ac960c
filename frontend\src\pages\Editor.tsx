import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { SparklesIcon, ChevronLeftIcon, CloudArrowUpIcon, BookOpenIcon, ClockIcon, UserGroupIcon } from '@heroicons/react/24/outline';
import { shouldUseMobileEditor } from '../utils/deviceDetection';
import MobileEditor from '../components/editor/MobileEditor';
import EditorHeader from '../components/editor/EditorHeader';
import EditorToolbar from '../components/editor/EditorToolbar';
import EnhancedAIPanel from '../components/editor/EnhancedAIPanel';
import CommandPalette from '../components/editor/CommandPalette';
import StatusBar from '../components/editor/StatusBar';
import LeftToolbar from '../components/editor/LeftToolbar';
import WritingHealthBadge from '../components/editor/WritingHealthBadge';
import WritingHealthPanel from '../components/editor/WritingHealthPanel';
import SmartCitationSearch from '../components/editor/SmartCitationSearch';
import CitationManager from '../components/editor/CitationManager';
import VersionControl from '../components/editor/VersionControl';
import ImportWizard from '../components/editor/ImportWizard';
import LivePresence from '../components/editor/LivePresence';
import { healthApi, HealthMetrics, HealthIssue as ApiHealthIssue } from '../services/api/healthApi';
import { useAnalyticsService } from '../services/analytics';
import { useAnalyticsStore } from '../stores/analyticsStore';
import DocumentAnalyticsPanel from '../components/editor/DocumentAnalyticsPanel';
import PersonaFeedbackPanel from '../components/persona/PersonaFeedbackPanel';
import { usePersonaStore } from '../stores/personaStore';
import { documentsApi, Document as ApiDocument, DocumentCollaboration } from '../services/api/documentsApi';
import { blocksApi } from '../services/api/blocksApi';
import { suggestionsApi, AISuggestion as ApiSuggestion } from '../services/api/suggestionsApi';
import { commentsApi, Comment as ApiComment } from '../services/api/commentsApi';
import analyticsApi, { TokenUsage } from '../services/api/analyticsApi';

// Use API document interface and extend with local state
interface EditorDocument extends Omit<ApiDocument, 'created_at' | 'updated_at'> {
  content: string; // Local content state
  lastSaved: Date | null;
  collaborators: DocumentCollaboration[];
}

// Use API suggestion interface with local extensions
interface EditorSuggestion extends ApiSuggestion {
  title?: string; // For backwards compatibility with UI
  description?: string; // For backwards compatibility with UI  
  line?: number; // For backwards compatibility with UI
}

// Use API comment interface with local extensions
interface EditorComment {
  id: string;
  text: string; // Map from API content field
  author: {
    name: string;
    avatar?: string;
    email: string;
  };
  createdAt: Date; // Convert from API created_at string
  line?: number; // Map from API position.line
  resolved: boolean; // Map from API status === 'resolved'
  replies: EditorComment[]; // Transform nested replies
}

// Use API health interface with local extensions for UI compatibility
interface EditorHealthScore {
  overall: number;
  readingLevel: number;
  wordiness: number;
  passiveVoice: number;
  languageClarity: number;
  brandVoice: number;
}

interface EditorHealthIssue {
  id: string;
  type: 'readingLevel' | 'wordiness' | 'passiveVoice' | 'languageClarity' | 'brandVoice';
  severity: 'low' | 'medium' | 'high';
  sentence: string; // Map from API location.text
  suggestion: string; // Map from API suggested_fix or description
  line: number; // Estimated line number
  startOffset: number; // Map from API location.start
  endOffset: number; // Map from API location.end
}

interface Citation {
  id: string;
  title: string;
  authors: string[];
  year: number;
  source: string;
  type: 'journal' | 'book' | 'website' | 'report' | 'article';
  doi?: string;
  url?: string;
  abstract?: string;
  tags?: string[];
  style: 'apa' | 'mla' | 'chicago';
  usageCount: number;
  dateAdded: Date;
}

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  color: string;
  isOnline: boolean;
  lastSeen?: Date;
}

interface UserPresence extends User {
  status: 'active' | 'idle' | 'away' | 'offline';
  cursor?: {
    x: number;
    y: number;
    selection?: { start: number; end: number };
  };
  currentAction?: 'typing' | 'selecting' | 'editing' | 'viewing';
  location?: { line: number; character: number };
}

interface Checkpoint {
  id: string;
  timestamp: Date;
  message?: string;
  wordCount: number;
  author: string;
  isAutoSave: boolean;
  changes?: { added: number; removed: number };
}

interface DraftVersion {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  lastModified: Date;
  wordCount: number;
  author: string;
  isActive: boolean;
  checkpoints: Checkpoint[];
  parentVersionId?: string;
}

const Editor: React.FC = () => {
  const { documentId } = useParams<{ documentId?: string }>();
  const navigate = useNavigate();
  const editorRef = useRef<HTMLDivElement>(null);
  
  // Mobile detection
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const shouldUseMobile = shouldUseMobileEditor();
    console.log('Device detection:', {
      userAgent: navigator.userAgent,
      viewportWidth: window.innerWidth,
      shouldUseMobile
    });
    setIsMobile(shouldUseMobile);
    
    // Listen for window resize to detect orientation changes
    const handleResize = () => {
      const shouldUseMobileResized = shouldUseMobileEditor();
      console.log('Resize detection:', {
        viewportWidth: window.innerWidth,
        shouldUseMobile: shouldUseMobileResized
      });
      setIsMobile(shouldUseMobileResized);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Document state
  const [document, setDocument] = useState<EditorDocument | null>(null);
  const [isDocumentLoading, setIsDocumentLoading] = useState(true);
  const [documentError, setDocumentError] = useState<string | null>(null);

  // Editor state
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [activeFormats, setActiveFormats] = useState<Set<string>>(new Set());
  const [isGenerating, setIsGenerating] = useState(false);
  const [showAIPanel, setShowAIPanel] = useState(true);
  const [activeAITab, setActiveAITab] = useState<'ai' | 'outline' | 'comments'>('ai');
  // Removed cursorPosition state to fix jumping cursor issue
  const [showCommandPalette, setShowCommandPalette] = useState(false);
  const [focusMode, setFocusMode] = useState(false);
  const [leftSidebarCollapsed, setLeftSidebarCollapsed] = useState(false);
  const [rightSidebarCollapsed, setRightSidebarCollapsed] = useState(false);
  const [editorLines, setEditorLines] = useState<HTMLElement[]>([]);
  const [selectedText, setSelectedText] = useState<string>('');

  // Enterprise Features State
  const [healthScore, setHealthScore] = useState<EditorHealthScore>({
    overall: 78,
    readingLevel: 82,
    wordiness: 75,
    passiveVoice: 88,
    languageClarity: 95,
    brandVoice: 70,
  });
  const [healthIssues, setHealthIssues] = useState<EditorHealthIssue[]>([]);
  const [isHealthAnalyzing, setIsHealthAnalyzing] = useState(false);
  const [healthError, setHealthError] = useState<string | null>(null);
  const [isHealthPanelOpen, setIsHealthPanelOpen] = useState(false);

  // Citation State
  const [isCitationSearchOpen, setIsCitationSearchOpen] = useState(false);
  const [isCitationManagerOpen, setIsCitationManagerOpen] = useState(false);
  const [citations, setCitations] = useState<Citation[]>([]);

  // Version Control State
  const [isVersionControlOpen, setIsVersionControlOpen] = useState(false);
  const [currentVersion, setCurrentVersion] = useState<DraftVersion>({
    id: 'main',
    name: 'Main Draft',
    description: 'Primary working version',
    createdAt: new Date(Date.now() - 86400000),
    lastModified: new Date(),
    wordCount: 0,
    author: 'Current User',
    isActive: true,
    checkpoints: [],
  });
  const [versions, setVersions] = useState<DraftVersion[]>([]);

  // Import State
  const [isImportWizardOpen, setIsImportWizardOpen] = useState(false);

  // Analytics State
  const [isDocumentAnalyticsOpen, setIsDocumentAnalyticsOpen] = useState(false);
  const analyticsService = useAnalyticsService();
  const analyticsStore = useAnalyticsStore();

  // Persona State
  const [isPersonaFeedbackOpen, setIsPersonaFeedbackOpen] = useState(false);
  const personaStore = usePersonaStore();

  // Collaboration State
  const [currentUser] = useState<User>({
    id: 'current-user',
    name: 'You',
    email: '<EMAIL>',
    color: '#3b82f6',
    isOnline: true,
  });
  const [collaborators, setCollaborators] = useState<UserPresence[]>([]);
  const [connectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connected');

  // Calculate reading time
  const readingTime = Math.ceil((document?.content?.split(' ').filter(Boolean).length || 0) / 200); // 200 words per minute

  // Token usage state
  const [tokenUsage, setTokenUsage] = useState<{ used: number; limit: number }>({ used: 0, limit: 25000 });
  const [isTokenUsageLoading, setIsTokenUsageLoading] = useState(false);

  // Transform DocumentCollaboration to simple collaborator format for EditorHeader
  const transformCollaborators = (apiCollaborators: DocumentCollaboration[]) => {
    return apiCollaborators.map(collab => ({
      id: collab.user.id,
      name: collab.user.display_name || collab.user.email,
      avatar: collab.user.avatar_url,
      isOnline: collab.status === 'active'
    }));
  };

  // Load token usage data
  const loadTokenUsage = async () => {
    try {
      setIsTokenUsageLoading(true);
      const usage = await analyticsApi.getTokenUsage(30); // Last 30 days
      setTokenUsage({
        used: usage.total_tokens_used,
        limit: usage.tokens_limit
      });
    } catch (error) {
      console.error('Failed to load token usage:', error);
      // Keep default values on error
    } finally {
      setIsTokenUsageLoading(false);
    }
  };

  // Health analysis transformation functions
  const transformHealthMetrics = (metrics: HealthMetrics): EditorHealthScore => ({
    overall: metrics.overall_score,
    readingLevel: metrics.readability_score,
    wordiness: 100 - Math.round(metrics.analysis_details.readability.avg_sentence_length / 20 * 100),
    passiveVoice: 100 - metrics.analysis_details.clarity.passive_voice_percentage,
    languageClarity: metrics.clarity_score,
    brandVoice: metrics.brand_alignment_score || 85
  });

  const transformHealthIssues = (issues: ApiHealthIssue[]): EditorHealthIssue[] => {
    return issues.map(issue => ({
      id: issue.id,
      type: issue.issue_type === 'readability' ? 'readingLevel' :
            issue.issue_type === 'clarity' ? 'languageClarity' :
            issue.issue_type === 'voice' ? 'brandVoice' :
            issue.issue_type === 'style' ? 'wordiness' : 'passiveVoice',
      severity: issue.severity,
      sentence: issue.location.text,
      suggestion: issue.suggested_fix || issue.description,
      line: Math.floor(issue.location.start / 50) + 1, // Estimate line number
      startOffset: issue.location.start,
      endOffset: issue.location.end
    }));
  };

  // Suggestions state
  const [suggestions, setSuggestions] = useState<EditorSuggestion[]>([]);
  const [isSuggestionsLoading, setIsSuggestionsLoading] = useState(false);
  const [suggestionsError, setSuggestionsError] = useState<string | null>(null);

  // Comments state
  const [comments, setComments] = useState<EditorComment[]>([]);
  const [isCommentsLoading, setIsCommentsLoading] = useState(false);
  const [commentsError, setCommentsError] = useState<string | null>(null);

  // Document structure state
  const [documentStructure, setDocumentStructure] = useState<any[]>([]);
  const [isStructureLoading, setIsStructureLoading] = useState(false);
  const [structureError, setStructureError] = useState<string | null>(null);

  // Auto-save function definition
  const handleAutoSave = useCallback(async () => {
    if (!document?.content?.trim()) return;
    
    setIsAutoSaving(true);
    try {
      // Get document blocks to update with new content
      const blocks = await blocksApi.getDocumentBlocks(document.id);
      
      if (blocks.length === 0) {
        // Create initial block if document has no blocks
        await blocksApi.createBlock({
          document_id: document.id,
          type: 'paragraph',
          position: 0,
          content: document.content
        });
      } else {
        // Update first block with document content (simplified approach)
        // In a full implementation, we'd need to diff and sync multiple blocks
        const firstBlock = blocks[0];
        if (firstBlock.content !== document.content) {
          await blocksApi.updateBlock(firstBlock.id, {
            content: document.content
          });
        }
      }
      
      setDocument(prev => prev ? { ...prev, lastSaved: new Date() } : null);
    } catch (error) {
      console.error('Auto-save failed:', error);
      // Don't show error to user for auto-save failures
    } finally {
      setIsAutoSaving(false);
    }
  }, [document]);

  // Auto-save functionality
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      if (document?.content?.trim()) {
        handleAutoSave();
      }
    }, 30000); // Auto-save every 30 seconds

    return () => clearInterval(autoSaveInterval);
  }, [document?.content, handleAutoSave]);

  // Writing Health Analysis using API
  useEffect(() => {
    const analyzeContent = async () => {
      if (!document?.content?.trim()) return;
      
      try {
        setIsHealthAnalyzing(true);
        setHealthError(null);

        // Analyze text using health API
        const healthMetrics = await healthApi.analyzeText({
          text: document.content,
          options: {
            include_suggestions: true,
            analysis_depth: 'detailed',
            focus_areas: ['readability', 'clarity', 'voice', 'brand']
          }
        });

        // Get health issues for the document
        const healthIssues = await healthApi.getDocumentHealthIssues(document.id, {
          status: 'open'
        });

        // Transform API data to UI format
        const editorScore = transformHealthMetrics(healthMetrics);
        const editorIssues = transformHealthIssues(healthIssues);

        setHealthScore(editorScore);
        setHealthIssues(editorIssues);
        
        // Update current version word count (estimate from content)
        const wordCount = document.content.trim().split(/\s+/).filter(Boolean).length;
        setCurrentVersion(prev => ({ ...prev, wordCount, lastModified: new Date() }));
      } catch (error) {
        console.error('Failed to analyze content health:', error);
        setHealthError(error instanceof Error ? error.message : 'Health analysis failed');
      } finally {
        setIsHealthAnalyzing(false);
      }
    };

    const timeoutId = setTimeout(analyzeContent, 1000); // Debounce API calls
    return () => clearTimeout(timeoutId);
  }, [document?.content, document?.id]);

  // Initialize versions
  useEffect(() => {
    setVersions([currentVersion]);
  }, []);

  // Load document data from API
  useEffect(() => {
    const loadDocument = async () => {
      try {
        setIsDocumentLoading(true);
        setDocumentError(null);

        if (documentId && documentId !== 'new') {
          // Load existing document
          const [docData, collaborators] = await Promise.all([
            documentsApi.getDocument(documentId),
            documentsApi.getCollaborators(documentId)
          ]);

          // Load document content from blocks
          const blocks = await blocksApi.getDocumentBlocks(documentId);
          const content = blocks
            .sort((a, b) => a.position - b.position)
            .map(block => block.content)
            .join('\n');

          setDocument({
            ...docData,
            content,
            lastSaved: new Date(docData.updated_at),
            collaborators
          });

          analyticsService.trackDocumentOpened(documentId);
        } else {
          // Create new document
          const newDoc = await documentsApi.createDocument({
            title: 'Untitled Document',
            type: 'general'
          });

          // Create initial block for new document
          await blocksApi.createBlock({
            document_id: newDoc.id,
            type: 'paragraph',
            position: 0,
            content: ''
          });

          setDocument({
            ...newDoc,
            content: '',
            lastSaved: new Date(),
            collaborators: []
          });

          analyticsService.trackDocumentCreated(newDoc.id, newDoc.title);
          
          // Update URL to reflect the new document ID
          navigate(`/app/editor/${newDoc.id}`, { replace: true });
        }
      } catch (error) {
        console.error('Failed to load document:', error);
        setDocumentError(error instanceof Error ? error.message : 'Failed to load document');
      } finally {
        setIsDocumentLoading(false);
      }
    };

    loadDocument();
    loadTokenUsage(); // Load token usage on document load

    // Cleanup on unmount
    return () => {
      analyticsService.cleanup();
    };
  }, [documentId, navigate]);

  // Load suggestions for the current document
  const loadSuggestions = async () => {
    if (!document) return;
    
    try {
      setIsSuggestionsLoading(true);
      setSuggestionsError(null);
      
      // Get suggestions for all blocks in the document
      const documentSuggestions = await suggestionsApi.getDocumentSuggestions(document.id, {
        status: 'pending'
      });
      
      // Transform API suggestions to editor format
      const editorSuggestions: EditorSuggestion[] = documentSuggestions.map(suggestion => ({
        ...suggestion,
        title: `${suggestion.agent_type} - ${suggestion.category}`,
        description: suggestion.explanation,
        line: suggestion.position.line || 1
      }));
      
      setSuggestions(editorSuggestions);
    } catch (error) {
      console.error('Failed to load suggestions:', error);
      setSuggestionsError(error instanceof Error ? error.message : 'Failed to load suggestions');
    } finally {
      setIsSuggestionsLoading(false);
    }
  };

  // Suggestions are now loaded on-demand when needed

  // Transform API comment to editor comment format
  const transformComment = (apiComment: ApiComment): EditorComment => ({
    id: apiComment.id,
    text: apiComment.content,
    author: apiComment.author || {
      name: 'Unknown User',
      email: '<EMAIL>'
    },
    createdAt: new Date(apiComment.created_at),
    line: apiComment.position?.line,
    resolved: apiComment.status === 'resolved',
    replies: apiComment.replies?.map(transformComment) || []
  });

  // Load comments for the current document
  const loadComments = async () => {
    if (!document) return;
    
    try {
      setIsCommentsLoading(true);
      setCommentsError(null);
      
      // Get comments for the document
      const documentComments = await commentsApi.getDocumentComments(document.id, {
        include_resolved: true,
        include_deleted: false
      });
      
      // Transform API comments to editor format
      const editorComments = documentComments.map(transformComment);
      setComments(editorComments);
    } catch (error) {
      console.error('Failed to load comments:', error);
      setCommentsError(error instanceof Error ? error.message : 'Failed to load comments');
    } finally {
      setIsCommentsLoading(false);
    }
  };

  // Comments are now loaded on-demand when needed

  // Load document structure for outline
  const loadDocumentStructure = async () => {
    if (!document) return;
    
    try {
      setIsStructureLoading(true);
      setStructureError(null);
      
      // Get hierarchical structure from the API
      const structure = await blocksApi.getDocumentStructure(document.id);
      setDocumentStructure(structure);
    } catch (error) {
      console.error('Failed to load document structure:', error);
      setStructureError(error instanceof Error ? error.message : 'Failed to load document structure');
    } finally {
      setIsStructureLoading(false);
    }
  };

  // Document structure is now loaded on-demand when needed

  // Track content changes for analytics
  useEffect(() => {
    if (document?.content && document?.id) {
      const wordCount = document.content.trim().split(/\s+/).filter(Boolean).length;
      analyticsService.updateWritingProgress(document.id, wordCount, document.content);
    }
  }, [document?.content, document?.id]);


  const handleTitleChange = useCallback(async (title: string) => {
    if (!document) return;
    
    setDocument(prev => prev ? { ...prev, title } : null);
    
    try {
      await documentsApi.updateDocument(document.id, { title });
    } catch (error) {
      console.error('Failed to update title:', error);
      // Revert title change on error
      setDocument(prev => prev ? { ...prev, title: document.title } : null);
    }
  }, [document]);

  const handleContentChange = useCallback((e: React.FormEvent<HTMLDivElement>) => {
    const content = e.currentTarget.textContent || '';
    setDocument(prev => prev ? { ...prev, content } : null);
  }, []);

  const handleFormatClick = useCallback((format: string) => {
    // Toggle format
    const newFormats = new Set(activeFormats);
    if (newFormats.has(format)) {
      newFormats.delete(format);
    } else {
      newFormats.add(format);
    }
    setActiveFormats(newFormats);
    
    // Apply formatting to selection
    (document as any).execCommand(format, false);
  }, [activeFormats]);

  const handleSuggestionAccept = useCallback(async (suggestionId: string) => {
    console.log('Accepting suggestion:', suggestionId);
    const suggestion = suggestions.find(s => s.id === suggestionId);
    if (!suggestion) return;
    
    try {
      await suggestionsApi.acceptSuggestion(suggestionId);
      
      // Remove accepted suggestion from local state
      setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
      
      // Track analytics
      analyticsService.trackAISuggestionAccepted(suggestion.agent_type);
      
      // Apply the suggestion to the document content
      if (document) {
        const newContent = document.content.replace(
          suggestion.original_text, 
          suggestion.suggested_text
        );
        setDocument(prev => prev ? { ...prev, content: newContent } : null);
      }
    } catch (error) {
      console.error('Failed to accept suggestion:', error);
    }
  }, [suggestions, document]);

  const handleSuggestionDismiss = useCallback(async (suggestionId: string) => {
    console.log('Dismissing suggestion:', suggestionId);
    const suggestion = suggestions.find(s => s.id === suggestionId);
    if (!suggestion) return;
    
    try {
      await suggestionsApi.rejectSuggestion(suggestionId, {
        reason: 'not_helpful'
      });
      
      // Remove dismissed suggestion from local state
      setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
      
      // Track analytics
      analyticsService.trackAISuggestionDismissed(suggestion.agent_type);
    } catch (error) {
      console.error('Failed to dismiss suggestion:', error);
    }
  }, [suggestions]);

  const handleSuggestionRerun = useCallback(async (suggestionId: string) => {
    console.log('Re-running suggestion:', suggestionId);
    const suggestion = suggestions.find(s => s.id === suggestionId);
    if (!suggestion || !document) return;
    
    try {
      // Request new suggestions for the same block
      const response = await suggestionsApi.requestSuggestions(suggestion.block_id, {
        agents: [suggestion.agent_type],
        intent: 'improve',
        context_level: 'sentence'
      });
      
      // If suggestions are returned immediately, update state
      if (response.suggestions) {
        const editorSuggestions: EditorSuggestion[] = response.suggestions.map(newSuggestion => ({
          ...newSuggestion,
          title: `${newSuggestion.agent_type} - ${newSuggestion.category}`,
          description: newSuggestion.explanation,
          line: newSuggestion.position.line || 1
        }));
        
        // Replace the old suggestion with new ones
        setSuggestions(prev => [
          ...prev.filter(s => s.id !== suggestionId),
          ...editorSuggestions
        ]);
      }
      
      // Track analytics
      analyticsService.trackAISuggestionRerun(suggestion.agent_type);
    } catch (error) {
      console.error('Failed to re-run suggestion:', error);
    }
  }, [suggestions, document]);

  const handleGenerateContent = useCallback(async (mode?: 'fix-its' | 'rephrase' | 'build-from' | 'analyze', options?: any) => {
    if (!document) return;
    
    setIsGenerating(true);
    console.log('Generating content:', mode, options);
    
    // Track AI generation
    if (mode) {
      analyticsService.trackAIGeneration(mode, true);
    }
    
    // Ensure we're on the AI tab and show the panel
    if (activeAITab !== 'ai') {
      setActiveAITab('ai');
    }
    if (!showAIPanel) {
      setShowAIPanel(true);
    }
    
    try {
      if (mode === 'fix-its') {
        // Request suggestions from the API
        const response = await suggestionsApi.getDocumentSuggestions(document.id, {
          status: 'pending'
        });
        
        // If no suggestions exist, request new ones
        if (response.length === 0) {
          // Get the document blocks to request suggestions for each
          const blocks = await blocksApi.getDocumentBlocks(document.id);
          
          // Request suggestions for each block
          const suggestionPromises = blocks.map(block => 
            suggestionsApi.requestSuggestions(block.id, {
              agents: ['grammar', 'style', 'content'],
              intent: 'improve',
              context_level: 'paragraph'
            })
          );
          
          await Promise.all(suggestionPromises);
          
          // Reload suggestions after requesting
          await loadSuggestions();
        } else {
          // Transform existing suggestions to editor format
          const editorSuggestions: EditorSuggestion[] = response.map(suggestion => ({
            ...suggestion,
            title: `${suggestion.agent_type} - ${suggestion.category}`,
            description: suggestion.explanation,
            line: suggestion.position.line || 1
          }));
          
          setSuggestions(editorSuggestions);
        }
      } else if (mode === 'rephrase' || mode === 'build-from' || mode === 'analyze') {
        // For other modes, request content-specific suggestions
        const blocks = await blocksApi.getDocumentBlocks(document.id);
        const intent = mode === 'rephrase' ? 'enhance' : mode === 'build-from' ? 'improve' : 'analyze';
        
        const suggestionPromises = blocks.map(block => 
          suggestionsApi.requestSuggestions(block.id, {
            agents: mode === 'rephrase' ? ['style'] : mode === 'build-from' ? ['content'] : ['grammar', 'style'],
            intent: intent as any,
            context_level: 'paragraph'
          })
        );
        
        await Promise.all(suggestionPromises);
        await loadSuggestions();
      }
    } catch (error) {
      console.error('Failed to generate content:', error);
      setSuggestionsError(error instanceof Error ? error.message : 'Failed to generate suggestions');
    } finally {
      setIsGenerating(false);
    }
  }, [document, loadSuggestions]);

  // Comment handlers
  const handleAddComment = useCallback(async (text: string, line?: number) => {
    if (!document) return;
    
    // Ensure we're on the comments tab and show the panel
    if (activeAITab !== 'comments') {
      setActiveAITab('comments');
    }
    if (!showAIPanel) {
      setShowAIPanel(true);
    }
    
    // Load existing comments if not already loaded
    if (comments.length === 0 && !isCommentsLoading) {
      await loadComments();
    }
    
    try {
      // For now, we'll create comments at the document level
      // In a full implementation, we'd need to determine which block the comment applies to
      const blocks = await blocksApi.getDocumentBlocks(document.id);
      const firstBlock = blocks[0]; // Simplified - use first block
      
      if (!firstBlock) return;
      
      const newComment = await commentsApi.createComment({
        block_id: firstBlock.id,
        content: text,
        type: 'general',
        position: line ? { start: 0, end: 0, line } : undefined
      });
      
      // Transform and add the new comment to local state
      const editorComment = transformComment(newComment);
      setComments(prev => [editorComment, ...prev]);
    } catch (error) {
      console.error('Failed to add comment:', error);
      setCommentsError(error instanceof Error ? error.message : 'Failed to add comment');
    }
  }, [document]);

  const handleResolveComment = useCallback(async (commentId: string) => {
    try {
      const comment = comments.find(c => c.id === commentId);
      if (!comment) return;
      
      if (comment.status === 'resolved') {
        // Unresolve the comment
        await commentsApi.updateComment(commentId, { status: 'open' });
      } else {
        // Resolve the comment
        await commentsApi.resolveComment(commentId);
      }
      
      // Reload comments to get updated status
      await loadComments();
    } catch (error) {
      console.error('Failed to resolve comment:', error);
      setCommentsError(error instanceof Error ? error.message : 'Failed to resolve comment');
    }
  }, [comments, loadComments]);

  const handleReplyToComment = useCallback(async (commentId: string, text: string) => {
    try {
      await commentsApi.replyToComment(commentId, text);
      
      // Reload comments to get the new reply
      await loadComments();
    } catch (error) {
      console.error('Failed to reply to comment:', error);
      setCommentsError(error instanceof Error ? error.message : 'Failed to reply to comment');
    }
  }, [loadComments]);

  const handleShare = useCallback(() => {
    console.log('Sharing document');
    // Implementation for sharing
  }, []);

  const handleExport = useCallback(() => {
    console.log('Exporting document');
    // Implementation for exporting
  }, []);

  const handleSave = useCallback(async () => {
    if (!document?.content?.trim()) return;
    
    try {
      setIsAutoSaving(true);
      
      // Force save all content to blocks
      const blocks = await blocksApi.getDocumentBlocks(document.id);
      
      if (blocks.length === 0) {
        // Create initial block if document has no blocks
        await blocksApi.createBlock({
          document_id: document.id,
          type: 'paragraph',
          position: 0,
          content: document.content
        });
      } else {
        // Update first block with document content
        const firstBlock = blocks[0];
        await blocksApi.updateBlock(firstBlock.id, {
          content: document.content
        });
      }
      
      // Update document metadata if needed
      await documentsApi.updateDocument(document.id, {
        updated_at: new Date().toISOString()
      });
      
      setDocument(prev => prev ? { ...prev, lastSaved: new Date() } : null);
    } catch (error) {
      console.error('Save failed:', error);
      // Show error to user for manual save failures
      alert('Failed to save document. Please try again.');
    } finally {
      setIsAutoSaving(false);
    }
  }, [document]);

  // Enterprise Feature Handlers
  const handleFixHealthIssue = useCallback(async (issueId: string) => {
    console.log('Fixing health issue:', issueId);
    
    try {
      // Try to auto-fix the issue if possible
      const autoFixResult = await healthApi.autoFixIssues([issueId]);
      
      if (autoFixResult.fixed_issues.includes(issueId)) {
        // Issue was auto-fixed - update document content if needed
        const updatedContent = autoFixResult.updated_content;
        if (document && Object.keys(updatedContent).length > 0) {
          // In a full implementation, we'd update the specific blocks
          // For now, we'll just remove the issue from the list
          setHealthIssues(prev => prev.filter(issue => issue.id !== issueId));
        }
      } else {
        // Auto-fix failed, manually resolve the issue
        await healthApi.resolveHealthIssue(issueId, {
          action: 'ignored',
          comment: 'Manually resolved by user'
        });
        
        // Remove the issue from the list
        setHealthIssues(prev => prev.filter(issue => issue.id !== issueId));
      }
    } catch (error) {
      console.error('Failed to fix health issue:', error);
      setHealthError(error instanceof Error ? error.message : 'Failed to fix health issue');
    }
  }, [document]);

  const handleInsertCitation = useCallback((citation: Citation) => {
    const citationText = ` [${citation.authors[0].split(',')[0]} et al., ${citation.year}]`;
    setDocument(prev => ({ ...prev, content: prev.content + citationText }));
    setCitations(prev => {
      const updated = prev.map(c => 
        c.id === citation.id ? { ...c, usageCount: c.usageCount + 1 } : c
      );
      if (!updated.find(c => c.id === citation.id)) {
        updated.push({ ...citation, usageCount: 1, dateAdded: new Date() });
      }
      return updated;
    });
    setIsCitationSearchOpen(false);
  }, []);

  const handleCreateVersion = useCallback((name: string, description?: string) => {
    if (!document) return;
    
    const newVersion: DraftVersion = {
      id: `version-${Date.now()}`,
      name,
      description,
      createdAt: new Date(),
      lastModified: new Date(),
      wordCount: document.content.split(/\s+/).filter(Boolean).length,
      author: currentUser.name,
      isActive: false,
      checkpoints: [],
    };
    setVersions(prev => [...prev, newVersion]);
  }, [document, currentUser.name]);

  const handleSwitchVersion = useCallback((versionId: string) => {
    setVersions(prev => prev.map(v => ({ ...v, isActive: v.id === versionId })));
    const newActiveVersion = versions.find(v => v.id === versionId);
    if (newActiveVersion) {
      setCurrentVersion(newActiveVersion);
    }
  }, [versions]);

  const handleImportComplete = useCallback((files: any[], options: any) => {
    console.log('Import completed:', files, options);
    // Simulate adding imported content
    if (files.length > 0 && document) {
      const importedText = files.map(f => f.extractedText).join('\n\n');
      if (options.mergeIntoDocument) {
        setDocument(prev => prev ? { 
          ...prev, 
          content: prev.content + '\n\n' + importedText 
        } : null);
      }
    }
    setIsImportWizardOpen(false);
  }, [document]);

  // Removed cursor tracking to fix jumping cursor issue

  // Focus on editor when component mounts
  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.focus();
    }
  }, []);

  // Global keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Command palette: Cmd/Ctrl + K
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setShowCommandPalette(true);
      }
      
      // AI Generate: Cmd/Ctrl + Shift + A
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'A') {
        e.preventDefault();
        handleGenerateContent();
      }
      
      // Save: Cmd/Ctrl + S
      if ((e.metaKey || e.ctrlKey) && e.key === 's') {
        e.preventDefault();
        handleSave();
      }
      
      // Focus Mode: Cmd/Ctrl + Shift + F
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'F') {
        e.preventDefault();
        const newFocusMode = !focusMode;
        setFocusMode(newFocusMode);
        analyticsService.trackFocusModeUsage(newFocusMode);
      }
      
      // Toggle AI Panel: Cmd/Ctrl + J
      if ((e.metaKey || e.ctrlKey) && e.key === 'j') {
        e.preventDefault();
        setShowAIPanel(!showAIPanel);
      }
      
      // Version History: Cmd/Ctrl + Shift + H
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'H') {
        e.preventDefault();
        setIsVersionControlOpen(true);
      }
      
      // Citation Search: Cmd/Ctrl + Shift + C
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'C') {
        e.preventDefault();
        setIsCitationSearchOpen(true);
      }
      
      // Import: Cmd/Ctrl + Shift + I
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'I') {
        e.preventDefault();
        setIsImportWizardOpen(true);
      }
      
      // Persona Feedback: Cmd/Ctrl + Shift + P
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        setIsPersonaFeedbackOpen(true);
      }
    };

    if (typeof window !== 'undefined' && window.document) {
      window.document.addEventListener('keydown', handleKeyDown);
      return () => window.document.removeEventListener('keydown', handleKeyDown);
    }
  }, [focusMode, showAIPanel, handleGenerateContent, handleSave]);

  const handleNavigateToBlock = useCallback((blockId: string) => {
    // In a full implementation, we would scroll to the specific block
    // For now, we'll just focus the editor
    if (editorRef.current) {
      editorRef.current.focus();
      console.log('Navigating to block:', blockId);
    }
  }, []);

  const handleNavigateToLine = useCallback((lineNumber: number) => {
    if (editorRef.current) {
      const lines = editorRef.current.textContent?.split('\n') || [];
      if (lineNumber <= lines.length) {
        // Scroll to approximate line position
        const lineHeight = 24; // Approximate line height
        const targetY = (lineNumber - 1) * lineHeight;
        editorRef.current.scrollIntoView({ behavior: 'smooth' });
        
        // Focus the editor
        editorRef.current.focus();
        
        // Set cursor to approximate position
        const range = (document as any).createRange();
        const selection = window.getSelection();
        
        if (selection) {
          const textContent = editorRef.current.textContent || '';
          const lineStart = textContent.split('\n').slice(0, lineNumber - 1).join('\n').length + (lineNumber > 1 ? 1 : 0);
          
          try {
            const walker = (document as any).createTreeWalker(
              editorRef.current,
              NodeFilter.SHOW_TEXT,
              null
            );
            
            let charCount = 0;
            let targetNode = editorRef.current;
            let targetOffset = 0;
            
            while (walker.nextNode()) {
              const node = walker.currentNode;
              const nodeLength = node.textContent?.length || 0;
              
              if (charCount + nodeLength >= lineStart) {
                targetNode = node;
                targetOffset = lineStart - charCount;
                break;
              }
              charCount += nodeLength;
            }
            
            range.setStart(targetNode, Math.max(0, targetOffset));
            range.collapse(true);
            selection.removeAllRanges();
            selection.addRange(range);
          } catch (error) {
            console.warn('Could not set cursor position:', error);
          }
        }
      }
    }
  }, []);

  const handleCommand = useCallback((commandId: string) => {
    switch (commandId) {
      case 'ai-generate':
        handleGenerateContent();
        break;
      case 'ai-improve':
        // Implementation for improving writing
        console.log('Improving writing...');
        break;
      case 'ai-grammar':
        // Implementation for grammar check
        console.log('Checking grammar...');
        break;
      case 'save':
        handleSave();
        break;
      case 'share':
        handleShare();
        break;
      case 'export':
        handleExport();
        break;
      case 'new-document':
        // Implementation for new document
        window.location.href = '/app/editor';
        break;
      case 'bookmark':
        // Implementation for bookmarking
        console.log('Bookmarking document...');
        break;
      case 'focus-mode':
        setFocusMode(!focusMode);
        break;
      case 'citations':
        setIsCitationManagerOpen(true);
        break;
      case 'import':
        setIsImportWizardOpen(true);
        break;
      case 'versions':
        setIsVersionControlOpen(true);
        break;
      case 'writing-health':
        setIsHealthPanelOpen(true);
        break;
      case 'document-analytics':
        setIsDocumentAnalyticsOpen(true);
        break;
      case 'persona-feedback':
        setIsPersonaFeedbackOpen(true);
        break;
      default:
        console.log('Unknown command:', commandId);
    }
  }, [handleGenerateContent, handleSave, handleShare, handleExport, focusMode]);

  const handleAITabChange = useCallback(async (tab: 'ai' | 'outline' | 'comments') => {
    setActiveAITab(tab);
    if (!showAIPanel) {
      setShowAIPanel(true);
    }
    
    // Load data on-demand based on active tab
    if (document) {
      if (tab === 'ai' && suggestions.length === 0 && !isSuggestionsLoading) {
        await loadSuggestions();
      } else if (tab === 'comments' && comments.length === 0 && !isCommentsLoading) {
        await loadComments();
      } else if (tab === 'outline' && documentStructure.length === 0 && !isStructureLoading) {
        await loadDocumentStructure();
      }
    }
  }, [showAIPanel, document, suggestions.length, isSuggestionsLoading, comments.length, isCommentsLoading, documentStructure.length, isStructureLoading, loadSuggestions, loadComments, loadDocumentStructure]);

  // Show loading state
  if (isDocumentLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
          <p className="mt-4 text-slate-600">Loading document...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (documentError) {
    return (
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-white">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-500 text-xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold text-slate-900 mb-2">Failed to load document</h3>
          <p className="text-slate-600 mb-4">{documentError}</p>
          <button
            onClick={() => navigate('/app/dashboard')}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  // Document not loaded
  if (!document) {
    return (
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-white">
        <div className="text-center">
          <p className="text-slate-600">Document not found</p>
        </div>
      </div>
    );
  }

  // Render mobile editor for mobile devices
  if (isMobile) {
    return (
      <MobileEditor
        documentId={document.id}
        initialContent={document.content}
        onSave={(content) => {
          setDocument(prev => prev ? { ...prev, content } : null);
          handleSave();
        }}
        onBack={() => navigate('/app/dashboard')}
      />
    );
  }

  return (
    <div className="h-screen flex bg-gradient-to-br from-slate-50 to-white">
      {/* Left Toolbar */}
      {!focusMode && (
        <div className="flex-shrink-0 z-40">
          <LeftToolbar
            activeTab={activeAITab}
            onTabChange={handleAITabChange}
            onGenerateContent={handleGenerateContent}
            onToggleFocusMode={() => setFocusMode(!focusMode)}
            onOpenCommandPalette={() => setShowCommandPalette(true)}
            isGenerating={isGenerating}
            isCollapsed={leftSidebarCollapsed}
            onToggleCollapse={() => setLeftSidebarCollapsed(!leftSidebarCollapsed)}
          />
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Editor Header */}
        {!focusMode && (
          <div className="border-b border-slate-200 bg-white relative overflow-visible z-[100]">
            <div className="flex items-center justify-between px-6 py-3 relative overflow-visible">
              <EditorHeader
                title={document.title}
                onTitleChange={handleTitleChange}
                lastSaved={document.lastSaved}
                isAutoSaving={isAutoSaving}
                collaborators={transformCollaborators(document.collaborators)}
                onShare={handleShare}
                onExport={handleExport}
                onSave={handleSave}
                onOpenCommandPalette={() => setShowCommandPalette(true)}
                tokenUsage={tokenUsage}
              />
              
              {/* Quick Actions */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setIsPersonaFeedbackOpen(true)}
                  className="flex items-center px-3 py-1.5 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <UserGroupIcon className="w-4 h-4 mr-1" />
                  Personas
                </button>
                <button
                  onClick={() => setIsImportWizardOpen(true)}
                  className="flex items-center px-3 py-1.5 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <CloudArrowUpIcon className="w-4 h-4 mr-1" />
                  Import
                </button>
                <button
                  onClick={() => setIsCitationManagerOpen(true)}
                  className="flex items-center px-3 py-1.5 text-sm bg-slate-200 text-slate-700 rounded-lg hover:bg-slate-300 transition-colors"
                >
                  <BookOpenIcon className="w-4 h-4 mr-1" />
                  Citations
                </button>
                <button
                  onClick={() => setIsVersionControlOpen(true)}
                  className="flex items-center px-3 py-1.5 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <ClockIcon className="w-4 h-4 mr-1" />
                  Versions
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Editor Toolbar */}
        {!focusMode && (
          <EditorToolbar
            onFormatClick={handleFormatClick}
            activeFormats={activeFormats}
          />
        )}

      {/* Main Editor Layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Document Editor */}
        <div className="flex-1 flex flex-col relative">
          {/* Writing Area */}
          <div className="flex-1 overflow-y-auto">
            <div className="max-w-4xl mx-auto px-8 py-12">
              {/* Content Editor */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="min-h-screen"
              >
                <div
                  ref={editorRef}
                  contentEditable
                  suppressContentEditableWarning
                  onInput={handleContentChange}
                  onSelect={() => {
                    const selection = window.getSelection();
                    if (selection && selection.toString().trim()) {
                      setSelectedText(selection.toString());
                    }
                  }}
                  onKeyDown={(e) => {
                    // Citation trigger: @@ 
                    if (e.key === '@' && e.target instanceof HTMLElement) {
                      const text = e.target.textContent || '';
                      if (text.endsWith('@')) {
                        setTimeout(() => setIsCitationSearchOpen(true), 100);
                      }
                    }
                  }}
                  className="min-h-96 outline-none text-slate-900 leading-relaxed text-lg focus:ring-0 selection:bg-purple-100 selection:text-purple-900"
                  style={{
                    lineHeight: '1.75',
                    fontFamily: 'Inter, system-ui, sans-serif',
                  }}
                  data-placeholder="Start writing your document here..."
                >
                  {!document?.content && (
                    <div className="text-slate-400 pointer-events-none select-none">
                      <p className="text-2xl font-light mb-4">✨ Start writing something amazing...</p>
                      <p className="text-base opacity-75">
                        Press <kbd className="px-2 py-1 bg-slate-200 rounded text-sm font-mono">Cmd+K</kbd> for commands, or just start typing.
                      </p>
                    </div>
                  )}
                </div>
              </motion.div>

              {/* Focus Mode Indicator */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="fixed bottom-8 left-8 lg:left-72 bg-white/90 backdrop-blur-sm rounded-2xl px-4 py-2 shadow-lg border border-slate-200/50"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-slate-700">Focus Mode</span>
                  <div className="text-xs text-slate-500">
                    {document?.content?.split(' ').filter(word => word.length > 0).length || 0} words
                  </div>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Expand button when right sidebar is collapsed - SAME position */}
          <AnimatePresence>
            {(rightSidebarCollapsed || !showAIPanel) && !focusMode && (
              <motion.button
                onClick={async () => {
                  if (rightSidebarCollapsed) {
                    setRightSidebarCollapsed(false);
                  }
                  setShowAIPanel(true);
                  
                  // Load data for the active tab when panel is opened
                  if (document) {
                    if (activeAITab === 'ai' && suggestions.length === 0 && !isSuggestionsLoading) {
                      await loadSuggestions();
                    } else if (activeAITab === 'comments' && comments.length === 0 && !isCommentsLoading) {
                      await loadComments();
                    } else if (activeAITab === 'outline' && documentStructure.length === 0 && !isStructureLoading) {
                      await loadDocumentStructure();
                    }
                  }
                }}
                className="fixed bottom-4 right-4 w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg shadow-xl flex items-center justify-center z-50 group"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ChevronLeftIcon className="w-5 h-5 text-white" />
                <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg" />
              </motion.button>
            )}
          </AnimatePresence>
        </div>

        {/* AI Panel */}
        <AnimatePresence>
          {showAIPanel && !focusMode && !rightSidebarCollapsed && (
            <motion.div
              initial={{ x: '100%', opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: '100%', opacity: 0 }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="z-50"
            >
              <EnhancedAIPanel
                suggestions={suggestions}
                onAcceptSuggestion={handleSuggestionAccept}
                onDismissSuggestion={handleSuggestionDismiss}
                onRerunSuggestion={handleSuggestionRerun}
                onGenerateContent={handleGenerateContent}
                isGenerating={isGenerating}
                documentContent={document.content}
                selectedText={selectedText}
                onNavigateToLine={handleNavigateToLine}
                activeTab={activeAITab}
                onTabChange={handleAITabChange}
                isCollapsed={rightSidebarCollapsed}
                onToggleCollapse={() => setRightSidebarCollapsed(!rightSidebarCollapsed)}
                comments={comments}
                onAddComment={handleAddComment}
                onResolveComment={handleResolveComment}
                onReplyToComment={handleReplyToComment}
                documentStructure={documentStructure}
                onNavigateToBlock={handleNavigateToBlock}
                isStructureLoading={isStructureLoading}
                structureError={structureError}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Status Bar */}
      {!focusMode && (
        <StatusBar
          content={document.content}
          isAutoSaving={isAutoSaving}
          lastSaved={document.lastSaved}
          readingTime={readingTime}
          focusMode={focusMode}
          onToggleFocusMode={() => setFocusMode(!focusMode)}
          writingHealthScore={healthScore.overall}
          documentId={document.id}
        />
      )}

      {/* Command Palette */}
      <CommandPalette
        isOpen={showCommandPalette}
        onClose={() => setShowCommandPalette(false)}
        onCommand={handleCommand}
      />

      {/* Focus Mode Toggle */}
      {focusMode && (
        <motion.button
          onClick={() => setFocusMode(false)}
          className="fixed top-4 right-4 z-50 p-3 bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200/50 text-slate-600 hover:text-slate-900 transition-colors"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </motion.button>
        )}

      {/* Enterprise Features */}
      
      {/* Writing Health Badge */}
      <WritingHealthBadge
        score={healthScore}
        onOpenPanel={() => setIsHealthPanelOpen(true)}
        isVisible={!focusMode}
      />

      {/* Writing Health Panel */}
      <WritingHealthPanel
        isOpen={isHealthPanelOpen}
        onClose={() => setIsHealthPanelOpen(false)}
        score={healthScore}
        issues={healthIssues}
        onFixIssue={handleFixHealthIssue}
        onNavigateToLine={handleNavigateToLine}
      />

      {/* Smart Citation Search */}
      <SmartCitationSearch
        isOpen={isCitationSearchOpen}
        onClose={() => setIsCitationSearchOpen(false)}
        onInsertCitation={handleInsertCitation}
      />

      {/* Citation Manager */}
      <CitationManager
        isOpen={isCitationManagerOpen}
        onClose={() => setIsCitationManagerOpen(false)}
        citations={citations}
        onAddCitation={() => setIsCitationSearchOpen(true)}
        onEditCitation={(citation) => console.log('Edit citation:', citation)}
        onDeleteCitation={(id) => setCitations(prev => prev.filter(c => c.id !== id))}
        onInsertCitation={handleInsertCitation}
        onExportBibliography={(style, citations) => console.log('Export:', style, citations)}
      />

      {/* Version Control */}
      <VersionControl
        isOpen={isVersionControlOpen}
        onClose={() => setIsVersionControlOpen(false)}
        currentVersion={currentVersion}
        versions={versions}
        onCreateVersion={handleCreateVersion}
        onSwitchVersion={handleSwitchVersion}
        onMergeVersions={(sourceId, targetId) => console.log('Merge:', sourceId, targetId)}
        onRestoreCheckpoint={(checkpointId) => console.log('Restore:', checkpointId)}
        onCreateCheckpoint={(message) => console.log('Create checkpoint:', message)}
        onPreviewCheckpoint={(checkpointId) => console.log('Preview:', checkpointId)}
      />

      {/* Import Wizard */}
      <ImportWizard
        isOpen={isImportWizardOpen}
        onClose={() => setIsImportWizardOpen(false)}
        onImportComplete={handleImportComplete}
      />

        {/* Document Analytics Panel */}
        <DocumentAnalyticsPanel
          documentId={document.id}
          documentContent={document.content}
          isOpen={isDocumentAnalyticsOpen}
          onClose={() => setIsDocumentAnalyticsOpen(false)}
          onRefresh={() => {
            // Trigger analytics refresh
            analyticsStore.calculateInsights();
            analyticsStore.updateStreak();
            analyticsStore.checkAchievements();
          }}
        />

        {/* Persona Feedback Panel */}
        <PersonaFeedbackPanel
          documentId={document.id}
          documentContent={document.content}
          isOpen={isPersonaFeedbackOpen}
          onClose={() => setIsPersonaFeedbackOpen(false)}
          onGenerateFeedback={() => {
            // Optional callback after feedback generation
            console.log('Persona feedback generated');
          }}
        />

        {/* Collaborative cursors removed to fix cursor jumping issue */}
      </div>
    </div>
  );
};

export default Editor;