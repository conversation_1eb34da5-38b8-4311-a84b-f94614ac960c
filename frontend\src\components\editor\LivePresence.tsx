import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  UserIcon,
  CursorArrowRaysIcon,
  EyeIcon,
  PencilIcon,
  ChatBubbleLeftIcon,
  SignalIcon,
  WifiIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  color: string;
  isOnline: boolean;
  lastSeen?: Date;
}

interface UserPresence extends User {
  status: 'active' | 'idle' | 'away' | 'offline';
  cursor?: {
    x: number;
    y: number;
    selection?: {
      start: number;
      end: number;
    };
  };
  currentAction?: 'typing' | 'selecting' | 'editing' | 'viewing';
  location?: {
    line: number;
    character: number;
  };
}

interface LivePresenceProps {
  currentUser: User;
  users: UserPresence[];
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
  onUserClick?: (user: UserPresence) => void;
  showCursors?: boolean;
  showAvatars?: boolean;
  maxAvatars?: number;
}

const LivePresence: React.FC<LivePresenceProps> = ({
  currentUser,
  users,
  connectionStatus,
  onUserClick,
  showCursors = true,
  showAvatars = true,
  maxAvatars = 5,
}) => {
  const [showAllUsers, setShowAllUsers] = useState(false);
  const [hoveredUser, setHoveredUser] = useState<string | null>(null);

  const activeUsers = users.filter(user => user.status !== 'offline');
  const visibleUsers = showAllUsers ? activeUsers : activeUsers.slice(0, maxAvatars);
  const hiddenCount = Math.max(0, activeUsers.length - maxAvatars);

  const getStatusColor = (status: UserPresence['status']) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'idle': return 'bg-yellow-500';
      case 'away': return 'bg-orange-500';
      case 'offline': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const getActionIcon = (action: UserPresence['currentAction']) => {
    switch (action) {
      case 'typing': return PencilIcon;
      case 'selecting': return CursorArrowRaysIcon;
      case 'editing': return PencilIcon;
      case 'viewing': return EyeIcon;
      default: return UserIcon;
    }
  };

  const getConnectionIcon = () => {
    switch (connectionStatus) {
      case 'connected': return WifiIcon;
      case 'connecting': return SignalIcon;
      case 'disconnected': return ExclamationTriangleIcon;
      default: return WifiIcon;
    }
  };

  const getConnectionColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-600';
      case 'connecting': return 'text-yellow-600';
      case 'disconnected': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const formatLastSeen = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="relative">
      {/* User Cursors */}
      {showCursors && (
        <div className="fixed inset-0 pointer-events-none z-30">
          <AnimatePresence>
            {users
              .filter(user => user.cursor && user.status === 'active')
              .map(user => (
                <motion.div
                  key={`cursor-${user.id}`}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="absolute pointer-events-none"
                  style={{
                    left: user.cursor!.x,
                    top: user.cursor!.y,
                    transform: 'translate(-2px, -2px)',
                  }}
                >
                  {/* Cursor */}
                  <div className="relative">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      className="drop-shadow-lg"
                    >
                      <path
                        d="M2 2L18 8L8 10L6 18L2 2Z"
                        fill={user.color}
                        stroke="white"
                        strokeWidth="1"
                      />
                    </svg>
                    
                    {/* User label */}
                    <div
                      className="absolute top-4 left-2 px-2 py-1 rounded-md text-xs font-medium text-white shadow-lg whitespace-nowrap"
                      style={{ backgroundColor: user.color }}
                    >
                      {user.name}
                    </div>
                  </div>

                  {/* Selection highlight */}
                  {user.cursor?.selection && (
                    <div
                      className="absolute rounded opacity-30"
                      style={{
                        backgroundColor: user.color,
                        width: '200px', // This would be calculated based on actual selection
                        height: '20px',
                        top: '20px',
                        left: '0px',
                      }}
                    />
                  )}
                </motion.div>
              ))}
          </AnimatePresence>
        </div>
      )}

      {/* Avatar Bar */}
      {showAvatars && (
        <div className="flex items-center space-x-2">

          {/* User Avatars */}
          <div className="flex items-center -space-x-2">
            <AnimatePresence>
              {visibleUsers.map((user, index) => {
                const ActionIcon = getActionIcon(user.currentAction);
                
                return (
                  <motion.div
                    key={user.id}
                    initial={{ opacity: 0, scale: 0.8, x: 20 }}
                    animate={{ opacity: 1, scale: 1, x: 0 }}
                    exit={{ opacity: 0, scale: 0.8, x: -20 }}
                    transition={{ delay: index * 0.1 }}
                    className="relative group"
                    onMouseEnter={() => setHoveredUser(user.id)}
                    onMouseLeave={() => setHoveredUser(null)}
                  >
                    <button
                      onClick={() => onUserClick?.(user)}
                      className="relative w-10 h-10 rounded-full border-2 border-white shadow-lg hover:scale-110 transition-transform duration-200"
                      style={{ zIndex: visibleUsers.length - index }}
                    >
                      {user.avatar ? (
                        <img
                          src={user.avatar}
                          alt={user.name}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <div
                          className="w-full h-full rounded-full flex items-center justify-center text-white font-semibold text-sm"
                          style={{ backgroundColor: user.color }}
                        >
                          {user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </div>
                      )}

                      {/* Status indicator */}
                      <div
                        className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(user.status)}`}
                      />

                      {/* Action indicator */}
                      {user.currentAction && user.status === 'active' && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="absolute -top-1 -left-1 w-5 h-5 bg-white rounded-full border border-slate-200 flex items-center justify-center shadow-md"
                        >
                          <ActionIcon className="w-3 h-3 text-slate-600" />
                        </motion.div>
                      )}
                    </button>

                    {/* Tooltip */}
                    <AnimatePresence>
                      {hoveredUser === user.id && (
                        <motion.div
                          initial={{ opacity: 0, y: 10, scale: 0.8 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: 10, scale: 0.8 }}
                          className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-slate-900 text-white px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap shadow-xl z-50"
                        >
                          <div className="space-y-1">
                            <div className="font-semibold">{user.name}</div>
                            <div className="text-xs opacity-75">
                              {user.status === 'active' && user.currentAction && (
                                <span className="capitalize">{user.currentAction}</span>
                              )}
                              {user.status === 'active' && user.location && (
                                <span> • Line {user.location.line}</span>
                              )}
                              {user.status !== 'active' && user.lastSeen && (
                                <span>Last seen {formatLastSeen(user.lastSeen)}</span>
                              )}
                            </div>
                          </div>
                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-slate-900" />
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                );
              })}
            </AnimatePresence>

            {/* More users indicator */}
            {hiddenCount > 0 && !showAllUsers && (
              <motion.button
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                onClick={() => setShowAllUsers(true)}
                className="w-10 h-10 rounded-full bg-slate-200 border-2 border-white shadow-lg flex items-center justify-center text-slate-700 font-semibold text-sm hover:bg-slate-300 transition-colors"
              >
                +{hiddenCount}
              </motion.button>
            )}
          </div>

          {/* Collapse button */}
          {showAllUsers && hiddenCount > 0 && (
            <button
              onClick={() => setShowAllUsers(false)}
              className="px-2 py-1 text-xs text-slate-500 hover:text-slate-700 transition-colors"
            >
              Show less
            </button>
          )}
        </div>
      )}

      {/* Activity Feed (Optional) */}
      {hoveredUser && (
        <AnimatePresence>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="absolute top-full mt-2 right-0 w-64 bg-white/95 backdrop-blur-xl rounded-xl border border-slate-200/50 shadow-xl p-4 z-40"
          >
            <h4 className="font-semibold text-slate-900 mb-3">Recent Activity</h4>
            <div className="space-y-2 text-sm">
              {users
                .filter(user => user.status === 'active')
                .slice(0, 3)
                .map(user => (
                  <div key={user.id} className="flex items-center space-x-3">
                    <div
                      className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium"
                      style={{ backgroundColor: user.color }}
                    >
                      {user.name[0]}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-slate-900 truncate">{user.name}</p>
                      <p className="text-slate-500 text-xs">
                        {user.currentAction && (
                          <span className="capitalize">{user.currentAction}</span>
                        )}
                        {user.location && (
                          <span> at line {user.location.line}</span>
                        )}
                      </p>
                    </div>
                  </div>
                ))}
            </div>
          </motion.div>
        </AnimatePresence>
      )}
    </div>
  );
};

export default LivePresence;