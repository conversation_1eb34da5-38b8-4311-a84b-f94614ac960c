import { baseApi } from './baseApi';

export interface DocumentationFile {
  filename: string;
  title: string;
  description: string;
  size: number;
  last_modified: number;
  category: string;
}

export interface DocumentationContent {
  filename: string;
  content: string;
  size: number;
  last_modified: number;
}

export interface ApiStats {
  total_endpoints: number;
  stable_endpoints: number;
  beta_endpoints: number;
  deprecated_endpoints: number;
  categories: Record<string, number>;
  latest_version: string;
  uptime: string;
  avg_response_time: string;
}

export interface CodeExample {
  language: string;
  endpoint?: string;
  code?: string;
  examples?: Record<string, string>;
}

class DeveloperApi {
  /**
   * Get list of available documentation files
   */
  async getDocumentationList(): Promise<DocumentationFile[]> {
    const response = await baseApi.get('/developer/docs');
    // Handle API response structure with success/data wrapper
    return response.data?.data || response.data || [];
  }

  /**
   * Get content of a specific documentation file
   */
  async getDocumentation(filename: string): Promise<DocumentationContent> {
    const response = await baseApi.get(`/developer/docs/${filename}`);
    return response.data?.data || response.data;
  }

  /**
   * Get OpenAPI specification
   */
  async getOpenApiSpec(): Promise<object> {
    const response = await baseApi.get('/developer/openapi-spec');
    return response.data;
  }

  /**
   * Get API statistics and metadata
   */
  async getApiStats(): Promise<ApiStats> {
    const response = await baseApi.get('/developer/api-stats');
    return response.data?.data || response.data;
  }

  /**
   * Get code examples for a specific language
   */
  async getCodeExamples(language: string, endpoint?: string): Promise<CodeExample> {
    const url = endpoint 
      ? `/developer/code-examples/${language}?endpoint=${endpoint}`
      : `/developer/code-examples/${language}`;
    
    const response = await baseApi.get(url);
    return response.data?.data || response.data;
  }

  /**
   * Get supported programming languages for code examples
   */
  getSupportedLanguages(): string[] {
    return ['javascript', 'python', 'curl', 'php', 'go'];
  }
}

export const developerApi = new DeveloperApi();