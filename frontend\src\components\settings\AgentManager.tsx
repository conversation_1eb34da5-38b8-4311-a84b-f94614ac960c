import React, { useState } from 'react';
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon, 
  DocumentDuplicateIcon,
  EyeIcon,
  EyeSlashIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon,
  Cog6ToothIcon,
  RocketLaunchIcon,
  BookmarkIcon
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';
import { useAgentStore } from '../../stores/agentStore';
import { CustomAgent } from '../../types/agent';
import AgentEditor from './AgentEditor';

const AgentManager: React.FC = () => {
  const {
    agents,
    deleteAgent,
    duplicateAgent,
    toggleAgentActive,
    bulkDeleteAgents,
    exportAgents,
    importAgents,
    resetToDefaults,
  } = useAgentStore();

  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingAgent, setEditingAgent] = useState<CustomAgent | null>(null);
  const [viewFilter, setViewFilter] = useState<'all' | 'active' | 'built-in' | 'custom'>('all');
  const [searchQuery, setSearchQuery] = useState('');

  const filteredAgents = agents.filter(agent => {
    // Search filter
    const matchesSearch = searchQuery === '' || 
      agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      agent.specialty.toLowerCase().includes(searchQuery.toLowerCase()) ||
      agent.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    // View filter
    const matchesFilter = viewFilter === 'all' ||
      (viewFilter === 'active' && agent.isActive) ||
      (viewFilter === 'built-in' && agent.isBuiltIn) ||
      (viewFilter === 'custom' && !agent.isBuiltIn);

    return matchesSearch && matchesFilter;
  });

  const handleSelectAgent = (agentId: string) => {
    setSelectedAgents(prev => 
      prev.includes(agentId) 
        ? prev.filter(id => id !== agentId)
        : [...prev, agentId]
    );
  };

  const handleSelectAll = () => {
    const allIds = filteredAgents.map(agent => agent.id);
    setSelectedAgents(selectedAgents.length === allIds.length ? [] : allIds);
  };

  const handleBulkDelete = () => {
    if (confirm(`Are you sure you want to delete ${selectedAgents.length} agent(s)?`)) {
      bulkDeleteAgents(selectedAgents);
      setSelectedAgents([]);
    }
  };

  const handleExport = () => {
    const data = exportAgents(selectedAgents.length > 0 ? selectedAgents : undefined);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `agents-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = e.target?.result as string;
          importAgents(data);
        } catch (error) {
          alert('Failed to import agents. Please check the file format.');
        }
      };
      reader.readAsText(file);
    }
  };

  const getCapabilityColor = (action: string) => {
    const colors = {
      edit: 'bg-blue-100 text-blue-700',
      delete: 'bg-red-100 text-red-700',
      add: 'bg-green-100 text-green-700',
      restructure: 'bg-purple-100 text-purple-700',
      'find-duplicates': 'bg-orange-100 text-orange-700',
      'fact-check': 'bg-indigo-100 text-indigo-700',
      'tone-adjust': 'bg-pink-100 text-pink-700',
    };
    return colors[action as keyof typeof colors] || 'bg-gray-100 text-gray-700';
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Agent Management</h2>
            <p className="text-sm text-gray-500">Manage your AI writing agents and their capabilities</p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowCreateModal(true)}
              className="btn-primary flex items-center text-sm"
            >
              <PlusIcon className="w-4 h-4 mr-1" />
              Create Agent
            </button>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search agents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="input-field"
            />
          </div>
          <div className="flex items-center space-x-2">
            <select
              value={viewFilter}
              onChange={(e) => setViewFilter(e.target.value as any)}
              className="input-field text-sm"
            >
              <option value="all">All Agents</option>
              <option value="active">Active Only</option>
              <option value="built-in">Built-in</option>
              <option value="custom">Custom</option>
            </select>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedAgents.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-blue-900">
                {selectedAgents.length} agent(s) selected
              </span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleExport}
                  className="text-sm text-blue-600 hover:text-blue-700 flex items-center"
                >
                  <ArrowDownTrayIcon className="w-4 h-4 mr-1" />
                  Export
                </button>
                <button
                  onClick={handleBulkDelete}
                  className="text-sm text-red-600 hover:text-red-700 flex items-center"
                >
                  <TrashIcon className="w-4 h-4 mr-1" />
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Action Bar */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <button
              onClick={handleSelectAll}
              className="text-sm text-gray-600 hover:text-gray-700"
            >
              {selectedAgents.length === filteredAgents.length ? 'Deselect All' : 'Select All'}
            </button>
          </div>
          <div className="flex items-center space-x-2">
            <label className="flex items-center text-sm text-gray-600 cursor-pointer">
              <ArrowUpTrayIcon className="w-4 h-4 mr-1" />
              Import
              <input
                type="file"
                accept=".json"
                onChange={handleImport}
                className="hidden"
              />
            </label>
            <button
              onClick={handleExport}
              className="text-sm text-gray-600 hover:text-gray-700 flex items-center"
            >
              <ArrowDownTrayIcon className="w-4 h-4 mr-1" />
              Export All
            </button>
            <button
              onClick={() => {
                if (confirm('Reset to default agents? This will remove all custom agents.')) {
                  resetToDefaults();
                }
              }}
              className="text-sm text-orange-600 hover:text-orange-700 flex items-center"
            >
              <Cog6ToothIcon className="w-4 h-4 mr-1" />
              Reset
            </button>
          </div>
        </div>
      </div>

      {/* Agents Grid */}
      <div className="p-6">
        <div className="grid gap-4">
          <AnimatePresence>
            {filteredAgents.map((agent) => (
              <motion.div
                key={agent.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`border rounded-lg p-4 transition-all duration-200 ${
                  selectedAgents.includes(agent.id)
                    ? 'border-blue-300 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                } ${!agent.isActive ? 'opacity-60' : ''}`}
              >
                <div className="flex items-start space-x-4">
                  {/* Selection Checkbox */}
                  <input
                    type="checkbox"
                    checked={selectedAgents.includes(agent.id)}
                    onChange={() => handleSelectAgent(agent.id)}
                    className="mt-1 rounded"
                  />

                  {/* Agent Color Indicator */}
                  <div className={`w-3 h-3 rounded-full bg-${agent.color}-500 mt-1.5 flex-shrink-0`} />

                  {/* Agent Details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium text-gray-900">{agent.name}</h3>
                        {agent.isBuiltIn && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                            <BookmarkIcon className="w-3 h-3 mr-1" />
                            Built-in
                          </span>
                        )}
                        {!agent.isActive && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-600">
                            Inactive
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => toggleAgentActive(agent.id)}
                          className={`p-1 rounded ${
                            agent.isActive 
                              ? 'text-green-600 hover:text-green-700' 
                              : 'text-gray-400 hover:text-gray-500'
                          }`}
                          title={agent.isActive ? 'Deactivate' : 'Activate'}
                        >
                          {agent.isActive ? (
                            <EyeIcon className="w-4 h-4" />
                          ) : (
                            <EyeSlashIcon className="w-4 h-4" />
                          )}
                        </button>
                        <button
                          onClick={() => setEditingAgent(agent)}
                          className="p-1 text-gray-400 hover:text-gray-500 rounded"
                          title="Edit"
                        >
                          <PencilIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => duplicateAgent(agent.id)}
                          className="p-1 text-gray-400 hover:text-gray-500 rounded"
                          title="Duplicate"
                        >
                          <DocumentDuplicateIcon className="w-4 h-4" />
                        </button>
                        {!agent.isBuiltIn && (
                          <button
                            onClick={() => {
                              if (confirm(`Delete "${agent.name}"?`)) {
                                deleteAgent(agent.id);
                              }
                            }}
                            className="p-1 text-red-400 hover:text-red-500 rounded"
                            title="Delete"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    </div>

                    <p className="text-sm text-gray-600 mb-3">{agent.description}</p>
                    
                    <div className="text-xs text-gray-500 mb-3">
                      <span className="font-medium">Specialty:</span> {agent.specialty}
                    </div>

                    {/* Capabilities */}
                    <div className="mb-3">
                      <div className="flex flex-wrap gap-1">
                        {agent.capabilities.filter(cap => cap.enabled).map((capability) => (
                          <span
                            key={capability.id}
                            className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCapabilityColor(capability.action)}`}
                          >
                            {capability.name}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Tags */}
                    {agent.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {agent.tags.map((tag) => (
                          <span
                            key={tag}
                            className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-600"
                          >
                            #{tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {filteredAgents.length === 0 && (
          <div className="text-center py-12">
            <RocketLaunchIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No agents found</h3>
            <p className="text-gray-500 mb-4">
              {searchQuery 
                ? 'Try adjusting your search terms or filters.'
                : 'Create your first custom agent to get started.'}
            </p>
            {!searchQuery && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="btn-primary"
              >
                Create Your First Agent
              </button>
            )}
          </div>
        )}
      </div>

      {/* Agent Editor Modal */}
      {(showCreateModal || editingAgent) && (
        <AgentEditor
          agent={editingAgent}
          onClose={() => {
            setShowCreateModal(false);
            setEditingAgent(null);
          }}
          onSave={() => {
            setShowCreateModal(false);
            setEditingAgent(null);
          }}
        />
      )}
    </div>
  );
};

export default AgentManager;