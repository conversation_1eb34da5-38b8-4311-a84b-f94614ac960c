import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuthStore } from '@/stores/authStore';
import Layout from '@/components/layout/Layout';
import LoadingScreen from '@/components/common/LoadingScreen';

// Lazy load pages for better performance
const Home = React.lazy(() => import('@/pages/Home'));
const Editor = React.lazy(() => import('@/pages/Editor'));
const Dashboard = React.lazy(() => import('@/pages/Dashboard'));
const Analytics = React.lazy(() => import('@/pages/Analytics'));
const Login = React.lazy(() => import('@/pages/Login'));
const Signup = React.lazy(() => import('@/pages/Signup'));
const Settings = React.lazy(() => import('@/pages/Settings'));
const DeveloperPortal = React.lazy(() => import('@/pages/DeveloperPortal'));

// Protected route wrapper
interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthStore();

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

// Public route wrapper (redirect if already authenticated)
const PublicRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthStore();

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

const App: React.FC = () => {
  const initializeAuth = useAuthStore((state) => state.initialize);

  useEffect(() => {
    // Initialize authentication when app starts
    initializeAuth();
  }, [initializeAuth]);

  return (
    <div className="App">
      <React.Suspense fallback={<LoadingScreen />}>
        <Routes>
          {/* Public routes */}
          <Route path="/" element={<Home />} />
          <Route
            path="/login"
            element={
              <PublicRoute>
                <Login />
              </PublicRoute>
            }
          />
          <Route
            path="/signup"
            element={
              <PublicRoute>
                <Signup />
              </PublicRoute>
            }
          />

          {/* Protected routes */}
          <Route
            path="/app"
            element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }
          >
            {/* Dashboard */}
            <Route index element={<Navigate to="/app/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />

            {/* Analytics */}
            <Route path="analytics" element={<Analytics />} />

            {/* Editor routes */}
            <Route path="editor/:documentId?" element={<Editor />} />

            {/* Settings */}
            <Route path="settings" element={<Settings />} />

            {/* Developer Portal */}
            <Route path="developers" element={<DeveloperPortal />} />
          </Route>

          {/* Catch all - redirect to home */}
          <Route
            path="*"
            element={<Navigate to="/" replace />}
          />
        </Routes>
      </React.Suspense>
    </div>
  );
};

export default App;