"""
Text Block Operations API Endpoints

Purpose: Provides REST API endpoints for text block operations including:
- Creating, reading, updating, and deleting text blocks within documents
- Block-level AI analysis and suggestion management
- Real-time collaborative editing support
- Block version history and change tracking

Responsibilities:
- Text block CRUD operations via HTTP endpoints
- Block-level content management and versioning
- AI suggestion integration for individual blocks
- Collaborative editing conflict resolution
- Block metadata and analytics tracking

Used by: Frontend editor components, real-time collaboration features
Dependencies: core.database, core.auth, core.multi_agent_engine
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime
import structlog

from api.auth import get_current_user
from core.auth import UserClaims
from core.multi_agent_engine import MultiAgentEngine
from core.database import get_database, DatabaseService

# Configure logging
logger = structlog.get_logger(__name__)

# Create router
router = APIRouter()

# Request/Response Models
class BlockCreate(BaseModel):
    """Request model for creating a text block."""
    document_id: str
    content: str = Field(..., description="Block text content")
    type: str = Field(default="paragraph", description="Block type: paragraph, heading, list, etc.")
    position: int = Field(..., ge=0, description="Block position in document")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)

class BlockUpdate(BaseModel):
    """Request model for updating a text block."""
    content: Optional[str] = None
    type: Optional[str] = None
    position: Optional[int] = Field(None, ge=0)
    metadata: Optional[Dict[str, Any]] = None

class BlockAnalysisRequest(BaseModel):
    """Request model for block AI analysis."""
    agent_types: List[str] = Field(default=["grammar", "style"], description="Agent types to run")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict)
    options: Optional[Dict[str, Any]] = Field(default_factory=dict)

class BlockResponse(BaseModel):
    """Response model for block data."""
    success: bool
    data: Dict[str, Any]

class BlockListResponse(BaseModel):
    """Response model for block list."""
    success: bool
    data: List[Dict[str, Any]]
    meta: Dict[str, Any]

class DocumentStructure(BaseModel):
    """Response model for document structure."""
    id: str
    type: str
    content: str
    position: int
    depth: int
    parent_id: Optional[str] = None
    children: List[Dict[str, Any]] = []
    metadata: Dict[str, Any] = {}

class DocumentStructureResponse(BaseModel):
    """Response model for document structure."""
    success: bool
    data: List[DocumentStructure]

@router.get("/document/{document_id}", response_model=BlockListResponse)
async def get_document_blocks(
    document_id: str,
    current_user: UserClaims = Depends(get_current_user),
    include_content: bool = Query(default=True),
    block_type: Optional[str] = Query(default=None)
):
    """
    Get all blocks for a specific document.
    
    Args:
        document_id: Document ID to get blocks for
        current_user: Current authenticated user
        include_content: Whether to include block content
        block_type: Filter by block type
        
    Returns:
        BlockListResponse: List of document blocks
    """
    try:
        logger.info(
            "Getting document blocks",
            document_id=document_id,
            user_id=current_user.user_id,
            include_content=include_content,
            block_type=block_type
        )
        
        # Get blocks from database
        db = await get_database()
        
        query = """
            SELECT 
                id, document_id, content, type, position, word_count,
                content_length, metadata, created_at, updated_at
            FROM blocks 
            WHERE document_id = $1
        """
        params = [document_id]
        
        # Apply filters
        if block_type:
            query += " AND type = $2"
            params.append(block_type)
        
        # Order by position
        query += " ORDER BY position ASC"
        
        blocks_data = await db.execute_query(query, params, fetch="all")
        
        # Format response data
        filtered_blocks = []
        for block in blocks_data:
            block_dict = dict(block)
            if not include_content:
                block_dict['content'] = None
            filtered_blocks.append(block_dict)
        
        return BlockListResponse(
            success=True,
            data=filtered_blocks,
            meta={
                "total": len(filtered_blocks),
                "document_id": document_id
            }
        )
        
    except Exception as e:
        logger.error("Failed to get document blocks", error=str(e), document_id=document_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve document blocks"
        )

@router.get("/document/{document_id}/structure", response_model=DocumentStructureResponse)
async def get_document_structure(
    document_id: str,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Get the hierarchical structure of a document with all blocks organized by type and level.
    
    Args:
        document_id: UUID of the document
        current_user: Current authenticated user
        
    Returns:
        DocumentStructureResponse: Hierarchical structure of document blocks
    """
    try:
        logger.info(
            "Getting document structure",
            document_id=document_id,
            user_id=current_user.user_id
        )
        
        db = await get_database()
        
        # First verify the user has access to this document
        document_access_query = """
            SELECT id FROM documents 
            WHERE id = $1 AND owner_id = $2 AND deleted_at IS NULL
        """
        
        document_exists = await db.execute_query(
            document_access_query, 
            [document_id, current_user.user_id], 
            fetch="one"
        )
        
        if not document_exists:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Get all blocks for the document with structure information
        structure_query = """
            SELECT 
                id, type, content, position, depth, parent_id, metadata,
                created_at, updated_at
            FROM blocks 
            WHERE document_id = $1 AND deleted_at IS NULL
            ORDER BY position ASC, depth ASC
        """
        
        blocks = await db.execute_query(structure_query, [document_id], fetch="all")
        
        # Build hierarchical structure
        structure_data = []
        block_map = {}
        
        # First pass: create all block objects
        for block in blocks:
            block_obj = DocumentStructure(
                id=str(block["id"]),
                type=block["type"],
                content=block["content"],
                position=block["position"],
                depth=block["depth"] if block["depth"] is not None else 0,
                parent_id=str(block["parent_id"]) if block["parent_id"] else None,
                children=[],
                metadata=block["metadata"] if block["metadata"] else {}
            )
            block_map[str(block["id"])] = block_obj
            
            # Add to root level if no parent
            if not block["parent_id"]:
                structure_data.append(block_obj)
        
        # Second pass: build parent-child relationships
        for block in blocks:
            if block["parent_id"]:
                parent_id = str(block["parent_id"])
                block_id = str(block["id"])
                if parent_id in block_map and block_id in block_map:
                    # Convert child to dict for JSON serialization
                    child_dict = block_map[block_id].dict()
                    block_map[parent_id].children.append(child_dict)
        
        logger.info(
            "Document structure retrieved successfully", 
            document_id=document_id,
            block_count=len(blocks),
            root_blocks=len(structure_data)
        )
        
        return DocumentStructureResponse(
            success=True,
            data=structure_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get document structure", error=str(e), document_id=document_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to get document structure"
        )

@router.post("/", response_model=BlockResponse)
async def create_block(
    block_data: BlockCreate,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Create a new text block.
    
    Args:
        block_data: Block creation data
        current_user: Current authenticated user
        
    Returns:
        BlockResponse: Created block data
    """
    try:
        logger.info(
            "Creating block",
            document_id=block_data.document_id,
            user_id=current_user.user_id,
            block_type=block_data.type,
            position=block_data.position
        )
        
        # Create block in database
        db = await get_database()
        now = datetime.utcnow()
        
        insert_query = """
            INSERT INTO blocks (
                document_id, content, type, position, word_count,
                metadata, created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $7
            ) RETURNING *
        """
        
        word_count = len(block_data.content.split()) if block_data.content else 0
        
        params = [
            block_data.document_id, block_data.content, block_data.type,
            block_data.position, word_count, block_data.metadata or {}, now
        ]
        
        block = await db.execute_query(insert_query, params, fetch="one")
        
        logger.info("Block created successfully", block_id=block["id"])
        
        return BlockResponse(
            success=True,
            data=block
        )
        
    except Exception as e:
        logger.error("Failed to create block", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to create block"
        )

@router.get("/{block_id}", response_model=BlockResponse)
async def get_block(
    block_id: str,
    current_user: UserClaims = Depends(get_current_user),
    include_suggestions: bool = Query(default=False)
):
    """
    Get a specific text block by ID.
    
    Args:
        block_id: Block ID to retrieve
        current_user: Current authenticated user
        include_suggestions: Whether to include AI suggestions
        
    Returns:
        BlockResponse: Block data
    """
    try:
        logger.info("Getting block", block_id=block_id, user_id=current_user.user_id)
        
        # Get block from database
        db = await get_database()
        
        query = """
            SELECT 
                id, document_id, content, type, position, word_count,
                metadata, created_at, updated_at
            FROM blocks 
            WHERE id = $1
        """
        
        block = await db.execute_query(query, [block_id], fetch="one")
        
        if not block:
            raise HTTPException(status_code=404, detail="Block not found")
        
        block_dict = dict(block)
        
        if include_suggestions:
            # Get suggestions from database
            suggestions_query = """
                SELECT 
                    id, block_id, agent_type, type, severity, status, position,
                    original_text, suggested_text, explanation, confidence, created_at
                FROM suggestions 
                WHERE block_id = $1 AND status = 'pending'
                ORDER BY created_at DESC
            """
            suggestions = await db.execute_query(suggestions_query, [block_id], fetch="all")
            block_dict["suggestions"] = [dict(s) for s in suggestions]
        
        return BlockResponse(
            success=True,
            data=block_dict
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get block", error=str(e), block_id=block_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve block"
        )

@router.put("/{block_id}", response_model=BlockResponse)
async def update_block(
    block_id: str,
    block_updates: BlockUpdate,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Update a text block.
    
    Args:
        block_id: Block ID to update
        block_updates: Block update data
        current_user: Current authenticated user
        
    Returns:
        BlockResponse: Updated block data
    """
    try:
        logger.info("Updating block", block_id=block_id, user_id=current_user.user_id)
        
        # Update block in database
        db = await get_database()
        
        # Build dynamic update query
        update_fields = []
        params = []
        param_index = 1
        
        update_data = block_updates.dict(exclude_unset=True)
        for field, value in update_data.items():
            if value is not None:
                update_fields.append(f"{field} = ${param_index}")
                params.append(value)
                param_index += 1
                
                # Update word count if content changed
                if field == "content":
                    update_fields.append(f"word_count = ${param_index}")
                    params.append(len(value.split()))
                    param_index += 1
        
        if not update_fields:
            raise HTTPException(
                status_code=400,
                detail="No valid fields to update"
            )
        
        # Update timestamp
        update_fields.append(f"updated_at = ${param_index}")
        params.append(datetime.utcnow())
        param_index += 1
        
        # Add WHERE condition
        params.append(block_id)
        
        query = f"""
            UPDATE blocks 
            SET {', '.join(update_fields)}
            WHERE id = ${param_index}
            RETURNING *
        """
        
        block = await db.execute_query(query, params, fetch="one")
        
        if not block:
            raise HTTPException(status_code=404, detail="Block not found")
        
        logger.info("Block updated successfully", block_id=block_id)
        
        return BlockResponse(
            success=True,
            data=dict(block)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update block", error=str(e), block_id=block_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to update block"
        )

@router.delete("/{block_id}")
async def delete_block(
    block_id: str,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Delete a text block.
    
    Args:
        block_id: Block ID to delete
        current_user: Current authenticated user
        
    Returns:
        dict: Success response
    """
    try:
        logger.info("Deleting block", block_id=block_id, user_id=current_user.user_id)
        
        # Delete block from database
        db = await get_database()
        
        query = "DELETE FROM blocks WHERE id = $1 RETURNING id"
        result = await db.execute_query(query, [block_id], fetch="one")
        
        if not result:
            raise HTTPException(status_code=404, detail="Block not found")
        
        logger.info("Block deleted successfully", block_id=block_id)
        
        return {"success": True, "message": "Block deleted successfully"}
        
    except Exception as e:
        logger.error("Failed to delete block", error=str(e), block_id=block_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to delete block"
        )

@router.post("/{block_id}/analyze", response_model=BlockResponse)
async def analyze_block(
    block_id: str,
    analysis_request: BlockAnalysisRequest,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Run AI analysis on a specific block.
    
    Args:
        block_id: Block ID to analyze
        analysis_request: Analysis configuration
        current_user: Current authenticated user
        
    Returns:
        BlockResponse: Analysis results and suggestions
    """
    try:
        logger.info(
            "Analyzing block",
            block_id=block_id,
            user_id=current_user.user_id,
            agent_types=analysis_request.agent_types
        )
        
        # Run AI analysis on block
        result = await MultiAgentEngine.analyze_block(
            block_id=block_id,
            agent_types=analysis_request.agent_types,
            context=analysis_request.context,
            options=analysis_request.options
        )
        
        logger.info("Block analysis completed", block_id=block_id)
        
        return BlockResponse(
            success=True,
            data=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to analyze block", error=str(e), block_id=block_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to analyze block"
        )

@router.get("/{block_id}/suggestions", response_model=BlockResponse)
async def get_block_suggestions(
    block_id: str,
    current_user: UserClaims = Depends(get_current_user),
    agent_type: Optional[str] = Query(default=None),
    status: Optional[str] = Query(default=None)
):
    """
    Get AI suggestions for a specific block.
    
    Args:
        block_id: Block ID to get suggestions for
        current_user: Current authenticated user
        agent_type: Filter by agent type
        status: Filter by suggestion status
        
    Returns:
        BlockResponse: Block suggestions
    """
    try:
        logger.info(
            "Getting block suggestions",
            block_id=block_id,
            user_id=current_user.user_id,
            agent_type=agent_type,
            status=status
        )
        
        # Get suggestions from database
        db = await get_database()
        
        query = """
            SELECT 
                id, block_id, agent_type, type, severity, status, position,
                original_text, suggested_text, explanation, confidence, created_at
            FROM suggestions 
            WHERE block_id = $1
        """
        params = [block_id]
        param_index = 2
        
        # Apply filters
        if agent_type:
            query += f" AND agent_type = ${param_index}"
            params.append(agent_type)
            param_index += 1
            
        if status:
            query += f" AND status = ${param_index}"
            params.append(status)
            param_index += 1
        
        query += " ORDER BY created_at DESC"
        
        suggestions_data = await db.execute_query(query, params, fetch="all")
        suggestions = [dict(s) for s in suggestions_data]
        
        return BlockResponse(
            success=True,
            data={
                "block_id": block_id,
                "suggestions": suggestions,
                "total": len(suggestions)
            }
        )
        
    except Exception as e:
        logger.error("Failed to get block suggestions", error=str(e), block_id=block_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve block suggestions"
        )