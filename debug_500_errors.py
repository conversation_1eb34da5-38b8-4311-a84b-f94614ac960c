#!/usr/bin/env python3
"""
Enhanced Debug Script for 500 Errors in Revisionary API

This script makes detailed requests to the failing endpoints and captures
the exact error messages to help diagnose the issues. Now includes database
schema validation to identify API-schema misalignments.
"""

import requests
import json
import traceback
import asyncio
import asyncpg
import os
from typing import Dict, Any, List
from urllib.parse import urlparse

class DetailedAPITester:
    def __init__(self, base_url: str = "http://localhost:8000/api/v1", auth_token: str = "dev-token"):
        self.base_url = base_url.rstrip('/')
        self.auth_token = auth_token
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        })
        
        # Database connection for schema validation
        self.db_params = self._get_db_connection_params()

    def _get_db_connection_params(self) -> Dict[str, Any]:
        """Get database connection parameters from environment variables."""
        # Try to load from .env file first - check both root and backend directories
        project_root = os.path.dirname(os.path.abspath(__file__))
        env_paths = [
            os.path.join(project_root, '.env'),
            os.path.join(project_root, 'backend', '.env'),
            os.path.join(project_root, 'backend/.env')
        ]
        
        env_loaded = False
        for env_file in env_paths:
            if os.path.exists(env_file):
                try:
                    from dotenv import load_dotenv
                    load_dotenv(env_file)
                    print(f"📁 Loaded environment from {env_file}")
                    env_loaded = True
                    break
                except ImportError:
                    print(f"⚠️  python-dotenv not installed, reading from system environment only")
                    break
        
        if not env_loaded:
            print(f"⚠️  No .env file found in {env_paths}")
            print(f"💡 Create .env from .env.example: cp backend/.env.example backend/.env")
        
        # Get DATABASE_URL from environment
        database_url = os.getenv('DATABASE_URL')
        
        if database_url:
            # Parse the DATABASE_URL (Supabase format)
            parsed = urlparse(database_url)
            params = {
                'host': parsed.hostname,
                'port': parsed.port or 5432,
                'database': parsed.path.lstrip('/'),
                'user': parsed.username,
                'password': parsed.password,
            }
            print(f"🔗 Using DATABASE_URL: {parsed.hostname}:{parsed.port or 5432}")
            return params
        else:
            # Fallback to individual environment variables or defaults
            params = {
                'host': os.getenv('DB_HOST', 'localhost'),
                'port': int(os.getenv('DB_PORT', 5432)),
                'database': os.getenv('DB_NAME', 'revisionary'),
                'user': os.getenv('DB_USER', 'postgres'),
                'password': os.getenv('DB_PASSWORD', 'password'),
            }
            print(f"🔗 Using default/env vars: {params['host']}:{params['port']}")
            print(f"⚠️  DATABASE_URL not found in environment")
            return params

    def debug_endpoint(self, method: str, path: str, data: Dict = None, params: Dict = None):
        """Debug a specific endpoint with detailed error reporting."""
        
        url = f"{self.base_url}{path}"
        print(f"\n🔍 DEBUGGING: {method.upper()} {path}")
        print(f"📍 Full URL: {url}")
        print(f"🔑 Auth Token: {self.auth_token[:20]}...")
        
        if data:
            print(f"📦 Request Data: {json.dumps(data, indent=2)}")
        if params:
            print(f"🔗 Query Params: {params}")
        
        try:
            kwargs = {
                'timeout': 30,
                'params': params or {}
            }
            
            if data and method.upper() in ['POST', 'PUT', 'PATCH']:
                kwargs['json'] = data
            
            response = self.session.request(method.upper(), url, **kwargs)
            
            print(f"📊 Status Code: {response.status_code}")
            print(f"⏱️  Response Time: {response.elapsed.total_seconds():.3f}s")
            
            # Try to parse JSON response
            try:
                response_data = response.json()
                print(f"📋 Response Headers: {dict(response.headers)}")
                print(f"📄 Response Body:")
                print(json.dumps(response_data, indent=2))
                
                # Look for error details
                if 'detail' in response_data:
                    print(f"🚨 Error Detail: {response_data['detail']}")
                if 'error' in response_data:
                    print(f"🚨 Error: {response_data['error']}")
                if 'message' in response_data:
                    print(f"🚨 Message: {response_data['message']}")
                    
            except json.JSONDecodeError:
                print(f"📄 Raw Response (first 500 chars):")
                print(response.text[:500])
                
        except Exception as e:
            print(f"💥 Request Failed: {str(e)}")
            print(f"🔍 Exception Type: {type(e).__name__}")
            traceback.print_exc()
        
        print("─" * 80)

    async def validate_table_schema(self, table_name: str, expected_columns: List[str]) -> Dict[str, Any]:
        """Validate that expected columns exist in the database table."""
        try:
            # Add statement_cache_size=0 for Supabase pgbouncer compatibility
            conn = await asyncpg.connect(**self.db_params, statement_cache_size=0)
            
            # Get actual columns from database
            query = """
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = $1 AND table_schema = 'public'
                ORDER BY ordinal_position;
            """
            
            rows = await conn.fetch(query, table_name)
            await conn.close()
            
            actual_columns = [row['column_name'] for row in rows]
            missing_columns = [col for col in expected_columns if col not in actual_columns]
            extra_columns = [col for col in actual_columns if col not in expected_columns]
            
            result = {
                'table_name': table_name,
                'expected_columns': expected_columns,
                'actual_columns': actual_columns,
                'missing_columns': missing_columns,
                'extra_columns': extra_columns,
                'is_valid': len(missing_columns) == 0,
                'column_details': [dict(row) for row in rows]
            }
            
            print(f"\n🔍 SCHEMA VALIDATION: {table_name}")
            print(f"   Expected columns: {len(expected_columns)}")
            print(f"   Actual columns:   {len(actual_columns)}")
            if missing_columns:
                print(f"   ❌ Missing: {', '.join(missing_columns)}")
            if extra_columns:
                print(f"   ➕ Extra: {', '.join(extra_columns[:5])}{'...' if len(extra_columns) > 5 else ''}")
            if result['is_valid']:
                print(f"   ✅ Schema OK")
            
            return result
            
        except Exception as e:
            print(f"❌ Schema validation failed for {table_name}: {e}")
            return {
                'table_name': table_name,
                'error': str(e),
                'is_valid': False
            }

    async def debug_with_schema_validation(self):
        """Debug failing endpoints with schema validation context."""
        
        print("🚨 ENHANCED DEBUGGING WITH SCHEMA VALIDATION")
        print("=" * 80)
        
        # Define the problematic endpoints and their expected schemas
        endpoints_to_validate = [
            {
                'method': 'POST',
                'path': '/documents',
                'data': {"title": "Debug Test", "type": "blog", "content": "Test"},
                'table': 'documents',
                'expected_columns': ['id', 'owner_id', 'title', 'type', 'status', 'description',
                                   'word_count', 'character_count', 'version', 'tags', 'is_public',
                                   'language', 'created_at', 'updated_at', 'published_at']
            },
            {
                'method': 'POST',
                'path': '/blocks',
                'data': {"document_id": "00000000-0000-0000-0000-000000000000", 
                        "content": "Test", "type": "paragraph", "position": 0},
                'table': 'blocks',
                'expected_columns': ['id', 'document_id', 'parent_id', 'type', 'position', 'depth',
                                   'content', 'content_hash', 'content_length', 'word_count', 
                                   'metadata', 'created_at', 'updated_at', 'deleted_at']
            },
            {
                'method': 'GET',
                'path': '/personas',
                'table': 'personas',
                'expected_columns': ['id', 'feedback_style', 'is_active', 'is_template', 
                                   'usage_count', 'effectiveness_score', 'created_at', 'updated_at']
            },
            {
                'method': 'GET',
                'path': '/agents',
                'table': 'custom_agents',
                'expected_columns': ['id', 'user_id', 'name', 'description', 'type', 'capabilities',
                                   'priority', 'is_active', 'is_template', 'model_preference',
                                   'max_context_tokens', 'temperature', 'execution_timeout',
                                   'created_at', 'updated_at', 'last_used_at']
            }
        ]
        
        # Validate schemas first
        schema_results = {}
        for endpoint_info in endpoints_to_validate:
            table_name = endpoint_info['table']
            expected_cols = endpoint_info['expected_columns']
            
            print(f"\n📊 Validating {table_name} table schema...")
            schema_result = await self.validate_table_schema(table_name, expected_cols)
            schema_results[table_name] = schema_result
        
        print(f"\n🔍 DEBUGGING API ENDPOINTS")
        print("─" * 50)
        
        # Now debug the endpoints with schema context
        for endpoint_info in endpoints_to_validate:
            method = endpoint_info['method']
            path = endpoint_info['path']
            data = endpoint_info.get('data')
            table = endpoint_info['table']
            
            print(f"\n🎯 Testing {method} {path} (→ {table} table)")
            
            # Check schema first
            schema_result = schema_results.get(table, {})
            if not schema_result.get('is_valid', False):
                print(f"   ⚠️  WARNING: Schema issues detected for {table} table")
                if 'missing_columns' in schema_result:
                    print(f"   Missing: {', '.join(schema_result['missing_columns'])}")
            
            # Debug the endpoint
            self.debug_endpoint(method, path, data)
        
        # Summary of schema issues
        print(f"\n📋 SCHEMA VALIDATION SUMMARY")
        print("─" * 50)
        
        for table_name, result in schema_results.items():
            if result.get('is_valid', False):
                print(f"   ✅ {table_name:<15} - Schema OK")
            else:
                missing = result.get('missing_columns', [])
                if missing:
                    print(f"   ❌ {table_name:<15} - Missing: {', '.join(missing)}")
                elif 'error' in result:
                    print(f"   ❌ {table_name:<15} - Error: {result['error']}")
        
        return schema_results

    def debug_all_failing_endpoints(self):
        """Debug all the endpoints that are returning 500 errors."""
        
        print("🚨 DEBUGGING 500 ERRORS - DETAILED ANALYSIS")
        print("=" * 80)
        
        # 1. POST /documents (500 error)
        self.debug_endpoint(
            "POST", 
            "/documents", 
            data={
                "title": "Debug Test Document",
                "type": "blog", 
                "content": "This is a test document for debugging."
            }
        )
        
        # 2. POST /health/analyze (500 error)
        self.debug_endpoint(
            "POST",
            "/health/analyze",
            data={
                "text": "This is test content for health analysis.",
                "document_type": "blog"
            }
        )
        
        # 3. GET /personas (500 error)
        self.debug_endpoint("GET", "/personas")
        
        # 4. GET /agents (500 error)  
        self.debug_endpoint("GET", "/agents")
        
        print("\n🎯 SUMMARY:")
        print("Look for common patterns in the errors above:")
        print("- Database connection issues")
        print("- Missing tables or schema problems") 
        print("- Authentication/authorization failures")
        print("- Missing environment variables")
        print("- Import or dependency issues")

async def main():
    """Main function to debug the failing endpoints with schema validation."""
    
    tester = DetailedAPITester()
    
    try:
        # Run enhanced debugging with schema validation
        schema_results = await tester.debug_with_schema_validation()
        
        print(f"\n💡 NEXT STEPS:")
        print("─" * 50)
        
        # Analyze results and provide recommendations
        has_schema_issues = any(not result.get('is_valid', False) for result in schema_results.values())
        
        if has_schema_issues:
            print("   1. 🔧 Fix database schema misalignments first")
            print("   2. 📝 Update API code to match actual database schema")
            print("   3. 🔄 Run migration scripts if needed")
            print("   4. ✅ Re-test endpoints after schema fixes")
        else:
            print("   1. ✅ Database schemas look correct")
            print("   2. 🔍 Check for other issues (imports, dependencies, etc.)")
            print("   3. 📊 Review server logs for detailed error messages")
        
        print(f"\n📄 Consider running the enhanced schema validator for more details:")
        print(f"   python enhanced_schema_validator.py")
        
    except Exception as e:
        print(f"❌ Enhanced debugging failed: {e}")
        traceback.print_exc()
        
        # Fallback to basic debugging
        print(f"\n🔄 Falling back to basic endpoint debugging...")
        tester.debug_all_failing_endpoints()

if __name__ == "__main__":
    asyncio.run(main())