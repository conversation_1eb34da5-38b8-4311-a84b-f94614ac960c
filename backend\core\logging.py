"""
Production Logging and Monitoring System

Purpose: Provides enterprise-grade structured logging and basic metrics collection
- Correlation ID tracking for request tracing
- Performance metrics collection
- Error monitoring and alerting
- Production-ready log formatting

Features:
- Request/response logging with timing
- Error tracking with stack traces
- Performance metrics aggregation
- Health monitoring endpoints
- Structured log output for monitoring tools
"""

import time
import uuid
import json
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timed<PERSON><PERSON>
from contextvars import ContextVar
from dataclasses import dataclass, asdict
import structlog
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from core.settings import settings

# Context variable for request correlation ID
request_id_var: ContextVar[str] = ContextVar("request_id", default="")

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


@dataclass
class RequestMetrics:
    """Metrics collected for each request."""
    method: str
    path: str
    status_code: int
    duration_ms: float
    user_id: Optional[str]
    request_id: str
    timestamp: datetime
    user_agent: Optional[str]
    ip_address: Optional[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging."""
        return asdict(self)


@dataclass
class ErrorMetrics:
    """Metrics collected for errors."""
    error_type: str
    error_message: str
    status_code: int
    request_id: str
    user_id: Optional[str]
    endpoint: str
    timestamp: datetime
    stack_trace: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging."""
        return asdict(self)


@dataclass
class PerformanceMetrics:
    """Performance metrics for monitoring."""
    endpoint: str
    avg_response_time: float
    p95_response_time: float
    request_count: int
    error_count: int
    error_rate: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging."""
        return asdict(self)


class MetricsCollector:
    """Collects and aggregates metrics for monitoring."""
    
    def __init__(self):
        self.request_metrics: List[RequestMetrics] = []
        self.error_metrics: List[ErrorMetrics] = []
        self.performance_cache: Dict[str, List[float]] = {}
        self.last_aggregation = datetime.now()
        
    def add_request_metric(self, metric: RequestMetrics):
        """Add a request metric."""
        self.request_metrics.append(metric)
        
        # Keep only last hour of metrics
        cutoff = datetime.now() - timedelta(hours=1)
        self.request_metrics = [m for m in self.request_metrics if m.timestamp > cutoff]
        
        # Update performance cache
        endpoint_key = f"{metric.method} {metric.path}"
        if endpoint_key not in self.performance_cache:
            self.performance_cache[endpoint_key] = []
        self.performance_cache[endpoint_key].append(metric.duration_ms)
        
        # Keep only last 100 requests per endpoint
        if len(self.performance_cache[endpoint_key]) > 100:
            self.performance_cache[endpoint_key] = self.performance_cache[endpoint_key][-100:]
    
    def add_error_metric(self, metric: ErrorMetrics):
        """Add an error metric."""
        self.error_metrics.append(metric)
        
        # Keep only last hour of errors
        cutoff = datetime.now() - timedelta(hours=1)
        self.error_metrics = [m for m in self.error_metrics if m.timestamp > cutoff]
    
    def get_performance_metrics(self, minutes: int = 15) -> List[PerformanceMetrics]:
        """Get aggregated performance metrics for the last N minutes."""
        cutoff = datetime.now() - timedelta(minutes=minutes)
        recent_requests = [m for m in self.request_metrics if m.timestamp > cutoff]
        
        # Group by endpoint
        endpoint_groups = {}
        for metric in recent_requests:
            endpoint = f"{metric.method} {metric.path}"
            if endpoint not in endpoint_groups:
                endpoint_groups[endpoint] = []
            endpoint_groups[endpoint].append(metric)
        
        # Calculate performance metrics
        performance_metrics = []
        for endpoint, metrics in endpoint_groups.items():
            if not metrics:
                continue
                
            durations = [m.duration_ms for m in metrics]
            errors = [m for m in metrics if m.status_code >= 400]
            
            # Calculate percentiles
            durations.sort()
            p95_index = int(len(durations) * 0.95)
            p95_response_time = durations[p95_index] if durations else 0.0
            
            performance_metrics.append(PerformanceMetrics(
                endpoint=endpoint,
                avg_response_time=sum(durations) / len(durations) if durations else 0.0,
                p95_response_time=p95_response_time,
                request_count=len(metrics),
                error_count=len(errors),
                error_rate=len(errors) / len(metrics) if metrics else 0.0,
                timestamp=datetime.now()
            ))
        
        return performance_metrics
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get overall system health status."""
        recent_metrics = self.get_performance_metrics(minutes=5)
        
        # Calculate overall health
        total_requests = sum(m.request_count for m in recent_metrics)
        total_errors = sum(m.error_count for m in recent_metrics)
        overall_error_rate = total_errors / total_requests if total_requests > 0 else 0.0
        
        # Calculate average response time
        if recent_metrics:
            avg_response_time = sum(m.avg_response_time for m in recent_metrics) / len(recent_metrics)
        else:
            avg_response_time = 0.0
        
        # Determine health status
        if overall_error_rate > 0.1:  # >10% error rate
            health_status = "unhealthy"
        elif overall_error_rate > 0.05 or avg_response_time > 2000:  # >5% error rate or >2s response
            health_status = "degraded"
        else:
            health_status = "healthy"
        
        return {
            "status": health_status,
            "metrics": {
                "requests_per_minute": total_requests,
                "error_rate": overall_error_rate,
                "avg_response_time_ms": avg_response_time,
                "total_errors": total_errors
            },
            "timestamp": datetime.now().isoformat()
        }


# Global metrics collector
metrics_collector = MetricsCollector()


class StructuredLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for structured request/response logging."""
    
    async def dispatch(self, request: Request, call_next):
        # Generate correlation ID
        correlation_id = str(uuid.uuid4())
        request_id_var.set(correlation_id)
        
        # Extract request information
        start_time = time.time()
        user_id = getattr(request.state, 'user_id', None) if hasattr(request, 'state') else None
        user_agent = request.headers.get('user-agent')
        ip_address = request.client.host if request.client else None
        
        # Log request start
        logger.info(
            "Request started",
            method=request.method,
            path=request.url.path,
            query_params=str(request.query_params),
            correlation_id=correlation_id,
            user_id=user_id,
            user_agent=user_agent,
            ip_address=ip_address
        )
        
        # Process request
        try:
            response = await call_next(request)
            duration_ms = (time.time() - start_time) * 1000
            
            # Log successful request
            logger.info(
                "Request completed",
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                duration_ms=round(duration_ms, 2),
                correlation_id=correlation_id,
                user_id=user_id
            )
            
            # Collect metrics
            metrics_collector.add_request_metric(RequestMetrics(
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                duration_ms=duration_ms,
                user_id=user_id,
                request_id=correlation_id,
                timestamp=datetime.now(),
                user_agent=user_agent,
                ip_address=ip_address
            ))
            
            # Add correlation ID to response headers
            response.headers["X-Correlation-ID"] = correlation_id
            
            return response
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            
            # Log error
            logger.error(
                "Request failed",
                method=request.method,
                path=request.url.path,
                duration_ms=round(duration_ms, 2),
                correlation_id=correlation_id,
                user_id=user_id,
                error=str(e),
                exc_info=True
            )
            
            # Collect error metrics
            metrics_collector.add_error_metric(ErrorMetrics(
                error_type=type(e).__name__,
                error_message=str(e),
                status_code=500,
                request_id=correlation_id,
                user_id=user_id,
                endpoint=f"{request.method} {request.url.path}",
                timestamp=datetime.now(),
                stack_trace=str(e)
            ))
            
            raise


def get_correlation_id() -> str:
    """Get the current request correlation ID."""
    return request_id_var.get("")


def log_business_event(event_type: str, details: Dict[str, Any], user_id: Optional[str] = None):
    """Log a business event with structured data."""
    logger.info(
        "Business event",
        event_type=event_type,
        details=details,
        user_id=user_id,
        correlation_id=get_correlation_id()
    )


def log_llm_usage(
    model: str,
    prompt_tokens: int,
    completion_tokens: int,
    cost: float,
    user_id: Optional[str] = None,
    agent_type: Optional[str] = None
):
    """Log LLM usage for cost tracking."""
    logger.info(
        "LLM usage",
        model=model,
        prompt_tokens=prompt_tokens,
        completion_tokens=completion_tokens,
        total_tokens=prompt_tokens + completion_tokens,
        cost_usd=cost,
        user_id=user_id,
        agent_type=agent_type,
        correlation_id=get_correlation_id()
    )


def log_citation_processing(
    operation: str,
    citation_count: int,
    style: str,
    user_id: Optional[str] = None,
    processing_time_ms: Optional[float] = None
):
    """Log citation processing operations."""
    logger.info(
        "Citation processing",
        operation=operation,
        citation_count=citation_count,
        style=style,
        processing_time_ms=processing_time_ms,
        user_id=user_id,
        correlation_id=get_correlation_id()
    )


async def log_health_analysis(
    blocks_analyzed: int,
    total_score: float,
    user_id: str,
    processing_time_ms: float
):
    """Log health analysis operations."""
    logger.info(
        "Health analysis completed",
        blocks_analyzed=blocks_analyzed,
        total_score=total_score,
        processing_time_ms=processing_time_ms,
        user_id=user_id,
        correlation_id=get_correlation_id()
    )


# Monitoring endpoints helper functions
def get_system_metrics() -> Dict[str, Any]:
    """Get comprehensive system metrics."""
    performance_metrics = metrics_collector.get_performance_metrics()
    health_status = metrics_collector.get_health_status()
    
    return {
        "health": health_status,
        "performance": [m.to_dict() for m in performance_metrics],
        "environment": settings.env,
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }


def get_error_summary() -> Dict[str, Any]:
    """Get error summary for the last hour."""
    recent_errors = [
        error for error in metrics_collector.error_metrics
        if error.timestamp > datetime.now() - timedelta(hours=1)
    ]
    
    # Group errors by type
    error_groups = {}
    for error in recent_errors:
        error_type = error.error_type
        if error_type not in error_groups:
            error_groups[error_type] = []
        error_groups[error_type].append(error)
    
    # Calculate error statistics
    error_stats = {}
    for error_type, errors in error_groups.items():
        error_stats[error_type] = {
            "count": len(errors),
            "endpoints": list(set(error.endpoint for error in errors)),
            "latest_occurrence": max(error.timestamp for error in errors).isoformat()
        }
    
    return {
        "total_errors": len(recent_errors),
        "error_types": error_stats,
        "timestamp": datetime.now().isoformat()
    }