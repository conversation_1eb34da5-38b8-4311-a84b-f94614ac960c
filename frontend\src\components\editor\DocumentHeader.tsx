import React, { useState, useEffect } from 'react';
import {
  CloudArrowUpIcon,
  ShareIcon,
  DocumentArrowDownIcon,
  EllipsisHorizontalIcon,
  ClockIcon,
  UserGroupIcon,
  BookmarkIcon,
  TagIcon,
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

interface DocumentHeaderProps {
  title: string;
  onTitleChange: (title: string) => void;
  lastSaved: Date | null;
  isAutoSaving: boolean;
  collaborators: Array<{ id: string; name: string; avatar?: string; isOnline: boolean }>;
  onShare: () => void;
  onExport: () => void;
  onSave: () => void;
}

const DocumentHeader: React.FC<DocumentHeaderProps> = ({
  title,
  onTitleChange,
  lastSaved,
  isAutoSaving,
  collaborators,
  onShare,
  onExport,
  onSave,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [localTitle, setLocalTitle] = useState(title);
  const [showMenu, setShowMenu] = useState(false);

  useEffect(() => {
    setLocalTitle(title);
  }, [title]);

  const handleTitleSubmit = () => {
    setIsEditing(false);
    onTitleChange(localTitle);
  };

  const getTimeAgo = (date: Date | null) => {
    if (!date) return 'Never saved';
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="sticky top-16 z-40 bg-white/95 backdrop-blur-xl border-b border-slate-200/50 shadow-sm">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Left section - Title and info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-4">
              {/* Document title */}
              <div className="flex-1 min-w-0">
                {isEditing ? (
                  <input
                    type="text"
                    value={localTitle}
                    onChange={(e) => setLocalTitle(e.target.value)}
                    onBlur={handleTitleSubmit}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') handleTitleSubmit();
                      if (e.key === 'Escape') {
                        setLocalTitle(title);
                        setIsEditing(false);
                      }
                    }}
                    className="text-xl font-bold text-slate-900 bg-transparent border-0 outline-none focus:ring-2 focus:ring-purple-500/20 rounded-lg px-2 py-1 w-full"
                    autoFocus
                  />
                ) : (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="text-xl font-bold text-slate-900 hover:text-purple-700 transition-colors text-left truncate max-w-md"
                  >
                    {title || 'Untitled Document'}
                  </button>
                )}
              </div>

              {/* Save status */}
              <div className="flex items-center space-x-2 text-sm">
                {isAutoSaving ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="flex items-center text-purple-600"
                  >
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="w-4 h-4 mr-2"
                    >
                      <CloudArrowUpIcon className="w-4 h-4" />
                    </motion.div>
                    Saving...
                  </motion.div>
                ) : (
                  <div className="flex items-center text-slate-500">
                    <ClockIcon className="w-4 h-4 mr-2" />
                    {getTimeAgo(lastSaved)}
                  </div>
                )}
              </div>
            </div>

            {/* Document metadata */}
            <div className="flex items-center space-x-4 mt-2">
              <div className="flex items-center space-x-2">
                <TagIcon className="w-4 h-4 text-slate-400" />
                <span className="text-sm text-slate-500">Draft</span>
              </div>
              <div className="flex items-center space-x-2">
                <BookmarkIcon className="w-4 h-4 text-slate-400" />
                <span className="text-sm text-slate-500">Research Paper</span>
              </div>
            </div>
          </div>

          {/* Center section - Collaborators */}
          {collaborators.length > 0 && (
            <div className="flex items-center space-x-2 mx-6">
              <UserGroupIcon className="w-4 h-4 text-slate-400" />
              <div className="flex -space-x-2">
                {collaborators.slice(0, 3).map((collaborator) => (
                  <motion.div
                    key={collaborator.id}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="relative"
                  >
                    {collaborator.avatar ? (
                      <img
                        src={collaborator.avatar}
                        alt={collaborator.name}
                        className="w-8 h-8 rounded-full border-2 border-white shadow-sm"
                      />
                    ) : (
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full border-2 border-white shadow-sm flex items-center justify-center">
                        <span className="text-xs font-bold text-white">
                          {collaborator.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                    {collaborator.isOnline && (
                      <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
                    )}
                  </motion.div>
                ))}
                {collaborators.length > 3 && (
                  <div className="w-8 h-8 bg-slate-200 rounded-full border-2 border-white shadow-sm flex items-center justify-center">
                    <span className="text-xs font-bold text-slate-600">
                      +{collaborators.length - 3}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Right section - Actions */}
          <div className="flex items-center space-x-3">
            <motion.button
              onClick={onShare}
              className="flex items-center px-4 py-2 text-sm font-semibold text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-xl transition-all duration-200"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ShareIcon className="w-4 h-4 mr-2" />
              Share
            </motion.button>

            <motion.button
              onClick={onExport}
              className="flex items-center px-4 py-2 text-sm font-semibold text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-xl transition-all duration-200"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <DocumentArrowDownIcon className="w-4 h-4 mr-2" />
              Export
            </motion.button>

            <motion.button
              onClick={onSave}
              className="flex items-center px-6 py-2 text-sm font-semibold text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:shadow-lg hover:shadow-purple-500/25 rounded-xl transition-all duration-200"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <CloudArrowUpIcon className="w-4 h-4 mr-2" />
              Save
            </motion.button>

            {/* More menu */}
            <div className="relative">
              <motion.button
                onClick={() => setShowMenu(!showMenu)}
                className="p-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-xl transition-all duration-200"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <EllipsisHorizontalIcon className="w-5 h-5" />
              </motion.button>

              <AnimatePresence>
                {showMenu && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95, y: -10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.95, y: -10 }}
                    className="absolute top-12 right-0 bg-white rounded-2xl shadow-2xl border border-slate-200/50 py-2 min-w-48 z-50"
                  >
                    <button className="w-full flex items-center px-4 py-3 text-sm text-slate-700 hover:bg-slate-50 transition-colors">
                      <TagIcon className="w-4 h-4 mr-3" />
                      Add Tags
                    </button>
                    <button className="w-full flex items-center px-4 py-3 text-sm text-slate-700 hover:bg-slate-50 transition-colors">
                      <BookmarkIcon className="w-4 h-4 mr-3" />
                      Change Template
                    </button>
                    <div className="border-t border-slate-200 my-2"></div>
                    <button className="w-full flex items-center px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors">
                      Delete Document
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentHeader;