import React, { useState } from 'react';
import {
  CloudArrowUpIcon,
  ShareIcon,
  DocumentArrowDownIcon,
  EllipsisHorizontalIcon,
  ClockIcon,
  UserGroupIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon,
  CommandLineIcon,
  CreditCardIcon,
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

interface EditorHeaderProps {
  title: string;
  onTitleChange: (title: string) => void;
  lastSaved: Date | null;
  isAutoSaving: boolean;
  collaborators: Array<{ id: string; name: string; avatar?: string; isOnline: boolean }>;
  onShare: () => void;
  onExport: () => void;
  onSave: () => void;
  onOpenCommandPalette: () => void;
  tokenUsage?: { used: number; limit: number };
}

const EditorHeader: React.FC<EditorHeaderProps> = ({
  title,
  onTitleChange,
  lastSaved,
  isAutoSaving,
  collaborators,
  onShare,
  onExport,
  onSave,
  onOpenCommandPalette,
  tokenUsage,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [localTitle, setLocalTitle] = useState(title);
  const [showMenu, setShowMenu] = useState(false);

  React.useEffect(() => {
    setLocalTitle(title);
  }, [title]);

  const handleTitleSubmit = () => {
    setIsEditing(false);
    onTitleChange(localTitle);
  };

  const getTimeAgo = (date: Date | null) => {
    if (!date) return 'Never saved';
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  const getTokenUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-500 bg-red-50';
    if (percentage >= 75) return 'text-yellow-500 bg-yellow-50';
    return 'text-green-500 bg-green-50';
  };

  const tokenPercentage = tokenUsage ? (tokenUsage.used / tokenUsage.limit) * 100 : 0;

  return (
    <header className="flex items-center justify-between h-14 px-6 bg-white/95 backdrop-blur-xl border-b border-slate-200/50 relative">
      {/* Left section - Document title and status */}
      <div className="flex items-center space-x-4 flex-1 min-w-0">
        {/* Document title */}
        <div className="flex items-center space-x-2 min-w-0">
          {isEditing ? (
            <input
              type="text"
              value={localTitle}
              onChange={(e) => setLocalTitle(e.target.value)}
              onBlur={handleTitleSubmit}
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleTitleSubmit();
                if (e.key === 'Escape') {
                  setLocalTitle(title);
                  setIsEditing(false);
                }
              }}
              className="text-lg font-semibold text-slate-900 bg-transparent border-0 outline-none focus:ring-2 focus:ring-purple-500/20 rounded-lg px-2 py-1 min-w-0"
              autoFocus
            />
          ) : (
            <button
              onClick={() => setIsEditing(true)}
              className="text-lg font-semibold text-slate-900 hover:text-purple-700 transition-colors text-left truncate"
            >
              {title || 'Untitled Document'}
            </button>
          )}

          {/* Save status */}
          <div className="flex items-center text-sm">
            {isAutoSaving ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="flex items-center text-purple-600"
              >
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-4 h-4 mr-1"
                >
                  <CloudArrowUpIcon className="w-4 h-4" />
                </motion.div>
                <span className="text-xs">Saving...</span>
              </motion.div>
            ) : (
              <div className="flex items-center text-slate-500">
                <ClockIcon className="w-4 h-4 mr-1" />
                <span className="text-xs">{getTimeAgo(lastSaved)}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Center section - Search */}
      <div className="flex-1 max-w-md mx-8">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-4 w-4 text-slate-400" />
          </div>
          <input
            type="text"
            placeholder="Search in document..."
            onClick={onOpenCommandPalette}
            className="block w-full pl-9 pr-12 py-2 bg-slate-50/50 border border-slate-200/50 rounded-xl text-sm placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500/30 transition-all duration-200 cursor-pointer"
            readOnly
          />
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <div className="flex items-center space-x-1 bg-slate-200/50 px-1.5 py-0.5 rounded text-xs">
              <CommandLineIcon className="h-3 w-3 text-slate-400" />
              <span className="text-slate-400 font-medium">⌘K</span>
            </div>
          </div>
        </div>
      </div>

      {/* Right section */}
      <div className="flex items-center space-x-3">
        {/* Token Usage - Now visible directly */}
        {tokenUsage && (
          <div className="flex items-center space-x-2 px-3 py-1.5 bg-slate-50 rounded-lg border border-slate-200">
            <CreditCardIcon className="w-4 h-4 text-slate-500" />
            <div className="text-xs">
              <span className="text-slate-600">Tokens: </span>
              <span className={`font-medium ${tokenPercentage >= 90 ? 'text-red-600' : tokenPercentage >= 75 ? 'text-yellow-600' : 'text-green-600'}`}>
                {tokenUsage.used.toLocaleString()} / {tokenUsage.limit.toLocaleString()}
              </span>
            </div>
            <div className="w-16 bg-slate-200 rounded-full h-1.5">
              <div 
                className={`h-1.5 rounded-full transition-all duration-300 ${tokenPercentage >= 90 ? 'bg-red-500' : tokenPercentage >= 75 ? 'bg-yellow-500' : 'bg-green-500'}`}
                style={{ width: `${Math.min(tokenPercentage, 100)}%` }}
              />
            </div>
          </div>
        )}

        {/* Actions menu */}
        <div className="relative z-[200]">
          <motion.button
            onClick={() => setShowMenu(!showMenu)}
            className="p-2 rounded-lg text-slate-600 hover:text-slate-900 hover:bg-slate-100 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <EllipsisHorizontalIcon className="w-5 h-5" />
          </motion.button>

          <AnimatePresence>
            {showMenu && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -10 }}
                className="absolute right-0 top-full mt-2 w-48 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-[9999]"
              >
                <button
                  onClick={() => { onShare(); setShowMenu(false); }}
                  className="w-full px-4 py-2 text-left text-sm text-slate-700 hover:bg-slate-50 flex items-center"
                >
                  <ShareIcon className="w-4 h-4 mr-3" />
                  Share
                </button>
                <button
                  onClick={() => { onExport(); setShowMenu(false); }}
                  className="w-full px-4 py-2 text-left text-sm text-slate-700 hover:bg-slate-50 flex items-center"
                >
                  <DocumentArrowDownIcon className="w-4 h-4 mr-3" />
                  Export
                </button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Save button */}
        <motion.button
          onClick={onSave}
          className="flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:shadow-lg hover:shadow-purple-500/25 rounded-lg transition-all duration-200"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <CloudArrowUpIcon className="w-4 h-4 mr-2" />
          Save
        </motion.button>
      </div>
    </header>
  );
};

export default EditorHeader;