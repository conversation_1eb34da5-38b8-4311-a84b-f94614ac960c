import React, { useState } from 'react';
import {
  ChatBubbleLeftIcon,
  PlusIcon,
  EllipsisVerticalIcon,
  UserCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

interface Comment {
  id: string;
  text: string;
  author: {
    name: string;
    avatar?: string;
    email: string;
  };
  createdAt: Date;
  line?: number;
  resolved: boolean;
  replies: Comment[];
}

interface CommentsPanelProps {
  comments: Comment[];
  onAddComment: (text: string, line?: number) => void;
  onResolveComment: (commentId: string) => void;
  onReplyToComment: (commentId: string, text: string) => void;
}

const CommentsPanel: React.FC<CommentsPanelProps> = ({
  comments,
  onAddComment,
  onResolveComment,
  onReplyToComment,
}) => {
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');
  const [filter, setFilter] = useState<'all' | 'unresolved' | 'resolved'>('all');

  const filteredComments = comments.filter(comment => {
    if (filter === 'unresolved') return !comment.resolved;
    if (filter === 'resolved') return comment.resolved;
    return true;
  });

  const handleSubmitComment = () => {
    if (newComment.trim()) {
      onAddComment(newComment.trim());
      setNewComment('');
    }
  };

  const handleSubmitReply = (commentId: string) => {
    if (replyText.trim()) {
      onReplyToComment(commentId, replyText.trim());
      setReplyText('');
      setReplyingTo(null);
    }
  };

  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <ChatBubbleLeftIcon className="w-5 h-5 text-purple-600" />
          <h3 className="text-sm font-semibold text-slate-900">Comments</h3>
          <span className="text-xs text-slate-500 bg-slate-100 px-2 py-0.5 rounded-full">
            {comments.length}
          </span>
        </div>
        
        <div className="flex items-center space-x-1 bg-slate-100 rounded-lg p-1">
          {['all', 'unresolved', 'resolved'].map((filterType) => (
            <button
              key={filterType}
              onClick={() => setFilter(filterType as any)}
              className={`px-2 py-1 text-xs font-medium rounded transition-colors ${
                filter === filterType
                  ? 'bg-white text-purple-700 shadow-sm'
                  : 'text-slate-600 hover:text-slate-900'
              }`}
            >
              {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Add new comment */}
      <div className="bg-slate-50 rounded-lg p-3">
        <textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Add a comment..."
          className="w-full bg-white border border-slate-200 rounded-lg p-3 text-sm resize-none focus:outline-none focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500/30"
          rows={3}
        />
        <div className="flex justify-end mt-2">
          <motion.button
            onClick={handleSubmitComment}
            disabled={!newComment.trim()}
            className="px-4 py-2 bg-purple-600 text-white text-sm font-semibold rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <PlusIcon className="w-4 h-4 mr-1 inline" />
            Add Comment
          </motion.button>
        </div>
      </div>

      {/* Comments list */}
      <div className="space-y-3">
        {filteredComments.length === 0 ? (
          <div className="text-center py-8">
            <ChatBubbleLeftIcon className="w-12 h-12 text-slate-300 mx-auto mb-3" />
            <p className="text-sm text-slate-500 mb-2">
              {filter === 'all' ? 'No comments yet' : `No ${filter} comments`}
            </p>
            <p className="text-xs text-slate-400">
              {filter === 'all' 
                ? 'Select text in the editor to add your first comment.'
                : 'Switch to "All" to see other comments.'
              }
            </p>
          </div>
        ) : (
          filteredComments.map((comment, index) => (
            <motion.div
              key={comment.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`border rounded-lg overflow-hidden ${
                comment.resolved 
                  ? 'border-green-200 bg-green-50/50' 
                  : 'border-slate-200 bg-white'
              }`}
            >
              <div className="p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    {comment.author.avatar ? (
                      <img
                        src={comment.author.avatar}
                        alt={comment.author.name}
                        className="w-8 h-8 rounded-full"
                      />
                    ) : (
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
                        <span className="text-xs font-bold text-white">
                          {comment.author.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                    <div>
                      <p className="text-sm font-semibold text-slate-900">
                        {comment.author.name}
                      </p>
                      <div className="flex items-center space-x-2 text-xs text-slate-500">
                        <ClockIcon className="w-3 h-3" />
                        <span>{getTimeAgo(comment.createdAt)}</span>
                        {comment.line && (
                          <>
                            <span>•</span>
                            <span>Line {comment.line}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {comment.resolved && (
                      <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">
                        Resolved
                      </span>
                    )}
                    <button className="p-1 text-slate-400 hover:text-slate-600 transition-colors">
                      <EllipsisVerticalIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <p className="text-sm text-slate-700 mb-3 leading-relaxed">
                  {comment.text}
                </p>

                {/* Replies */}
                {comment.replies.length > 0 && (
                  <div className="ml-6 border-l-2 border-slate-200 pl-4 space-y-3">
                    {comment.replies.map((reply) => (
                      <div key={reply.id} className="flex items-start space-x-3">
                        {reply.author.avatar ? (
                          <img
                            src={reply.author.avatar}
                            alt={reply.author.name}
                            className="w-6 h-6 rounded-full"
                          />
                        ) : (
                          <div className="w-6 h-6 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full flex items-center justify-center">
                            <span className="text-xs font-bold text-white">
                              {reply.author.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        )}
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="text-sm font-semibold text-slate-900">
                              {reply.author.name}
                            </span>
                            <span className="text-xs text-slate-500">
                              {getTimeAgo(reply.createdAt)}
                            </span>
                          </div>
                          <p className="text-sm text-slate-700">{reply.text}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Reply form */}
                {replyingTo === comment.id && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="mt-3 ml-6 border-l-2 border-purple-200 pl-4"
                  >
                    <textarea
                      value={replyText}
                      onChange={(e) => setReplyText(e.target.value)}
                      placeholder="Write a reply..."
                      className="w-full bg-white border border-slate-200 rounded-lg p-2 text-sm resize-none focus:outline-none focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500/30"
                      rows={2}
                      autoFocus
                    />
                    <div className="flex justify-end space-x-2 mt-2">
                      <button
                        onClick={() => setReplyingTo(null)}
                        className="px-3 py-1 text-sm text-slate-600 hover:text-slate-900 transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={() => handleSubmitReply(comment.id)}
                        disabled={!replyText.trim()}
                        className="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700 disabled:opacity-50 transition-colors"
                      >
                        Reply
                      </button>
                    </div>
                  </motion.div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between mt-3 pt-3 border-t border-slate-200">
                  <button
                    onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                    className="text-sm text-purple-600 hover:text-purple-700 font-medium transition-colors"
                  >
                    Reply
                  </button>
                  
                  <button
                    onClick={() => onResolveComment(comment.id)}
                    className={`text-sm font-medium transition-colors ${
                      comment.resolved
                        ? 'text-green-600 hover:text-green-700'
                        : 'text-slate-600 hover:text-slate-900'
                    }`}
                  >
                    {comment.resolved ? 'Unresolve' : 'Resolve'}
                  </button>
                </div>
              </div>
            </motion.div>
          ))
        )}
      </div>
    </div>
  );
};

export default CommentsPanel;