/**
 * Application Configuration
 * 
 * Centralized configuration management for the frontend application
 */

interface AppConfig {
  // API Configuration
  api: {
    baseUrl: string;
    wsUrl: string;
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
  };
  
  // Firebase Configuration
  firebase: {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
    measurementId?: string;
  };
  
  // Development Settings
  dev: {
    enabled: boolean;
    userEmail: string;
    userName: string;
  };
  
  // Feature Flags
  features: {
    websocket: boolean;
    personas: boolean;
    agents: boolean;
    healthAnalysis: boolean;
    citations: boolean;
    analytics: boolean;
  };
  
  // Application Settings
  app: {
    name: string;
    version: string;
    environment: 'development' | 'staging' | 'production';
  };
  
  // Cache Settings
  cache: {
    duration: number;
    healthCacheDuration: number;
  };
  
  // WebSocket Settings
  websocket: {
    reconnectAttempts: number;
    reconnectDelay: number;
  };
  
  // Rate Limiting
  rateLimit: {
    api: number;
    websocket: number;
  };
  
  // Monitoring
  monitoring: {
    sentryDsn?: string;
    gaId?: string;
  };
}

// Helper function to get boolean env variables
const getBoolean = (value: string | undefined, defaultValue: boolean): boolean => {
  if (value === undefined) return defaultValue;
  return value.toLowerCase() === 'true';
};

// Helper function to get number env variables
const getNumber = (value: string | undefined, defaultValue: number): number => {
  if (value === undefined) return defaultValue;
  const num = parseInt(value, 10);
  return isNaN(num) ? defaultValue : num;
};

// Create configuration object
export const config: AppConfig = {
  api: {
    baseUrl: import.meta.env.VITE_API_URL || 'http://localhost:8000',
    wsUrl: import.meta.env.VITE_WS_URL || 'ws://localhost:8000',
    timeout: getNumber(import.meta.env.VITE_API_TIMEOUT, 30000),
    retryAttempts: 3,
    retryDelay: 1000,
  },
  
  firebase: {
    apiKey: import.meta.env.VITE_FIREBASE_API_KEY || '',
    authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || '',
    projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || '',
    storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || '',
    messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || '',
    appId: import.meta.env.VITE_FIREBASE_APP_ID || '',
    measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID,
  },
  
  dev: {
    enabled: getBoolean(import.meta.env.VITE_DEV_MODE, false),
    userEmail: import.meta.env.VITE_DEV_USER_EMAIL || '<EMAIL>',
    userName: import.meta.env.VITE_DEV_USER_NAME || 'Developer User',
  },
  
  features: {
    websocket: getBoolean(import.meta.env.VITE_ENABLE_WEBSOCKET, true),
    personas: getBoolean(import.meta.env.VITE_ENABLE_PERSONAS, true),
    agents: getBoolean(import.meta.env.VITE_ENABLE_AGENTS, true),
    healthAnalysis: getBoolean(import.meta.env.VITE_ENABLE_HEALTH_ANALYSIS, true),
    citations: getBoolean(import.meta.env.VITE_ENABLE_CITATIONS, true),
    analytics: getBoolean(import.meta.env.VITE_ENABLE_ANALYTICS, true),
  },
  
  app: {
    name: import.meta.env.VITE_APP_NAME || 'Revisionary',
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    environment: (import.meta.env.VITE_APP_ENVIRONMENT as any) || 'development',
  },
  
  cache: {
    duration: getNumber(import.meta.env.VITE_CACHE_DURATION, 300000),
    healthCacheDuration: getNumber(import.meta.env.VITE_HEALTH_CACHE_DURATION, 300000),
  },
  
  websocket: {
    reconnectAttempts: getNumber(import.meta.env.VITE_WS_RECONNECT_ATTEMPTS, 5),
    reconnectDelay: getNumber(import.meta.env.VITE_WS_RECONNECT_DELAY, 1000),
  },
  
  rateLimit: {
    api: getNumber(import.meta.env.VITE_API_RATE_LIMIT, 100),
    websocket: getNumber(import.meta.env.VITE_WS_RATE_LIMIT, 50),
  },
  
  monitoring: {
    sentryDsn: import.meta.env.VITE_SENTRY_DSN,
    gaId: import.meta.env.VITE_GA_MEASUREMENT_ID,
  },
};

// Environment checks
export const isDevelopment = config.app.environment === 'development';
export const isProduction = config.app.environment === 'production';
export const isStaging = config.app.environment === 'staging';

// Feature flag helpers
export const isFeatureEnabled = (feature: keyof typeof config.features): boolean => {
  return config.features[feature];
};

// Validate configuration
export const validateConfig = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Check required Firebase config
  if (!config.firebase.apiKey) {
    errors.push('Firebase API key is required');
  }
  if (!config.firebase.authDomain) {
    errors.push('Firebase auth domain is required');
  }
  if (!config.firebase.projectId) {
    errors.push('Firebase project ID is required');
  }
  
  // Check API configuration
  if (!config.api.baseUrl) {
    errors.push('API base URL is required');
  }
  
  // Validate WebSocket URL
  if (config.features.websocket && !config.api.wsUrl) {
    errors.push('WebSocket URL is required when WebSocket feature is enabled');
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
};

// Log configuration in development
if (isDevelopment) {
  console.log('App Configuration:', {
    environment: config.app.environment,
    apiUrl: config.api.baseUrl,
    wsUrl: config.api.wsUrl,
    features: config.features,
    devMode: config.dev.enabled,
  });
  
  const validation = validateConfig();
  if (!validation.valid) {
    console.warn('Configuration validation errors:', validation.errors);
  }
}

export default config;