{"timestamp": "20250701_225103", "base_url": "http://localhost:8000/api/v1", "summary": {"total_tests": 14, "successful": 12, "failed": 2, "success_rate": 85.71428571428571, "avg_response_time": 0.27983944756644114}, "results": [{"endpoint": "/auth/me", "method": "GET", "status_code": 200, "success": true, "response_time": 2.0454583168029785, "error_message": null, "response_data": {"success": true, "data": {"id": "14285842-26d6-4a48-8b7c-1ac76fa5c488", "email": "<EMAIL>", "display_name": "<PERSON><PERSON> User", "avatar_url": null, "subscription_tier": "professional", "subscription_status": "active", "created_at": "2025-07-02T02:51:00.035875", "last_active_at": "2025-07-02T02:51:00.035875", "preferences": {"theme": "dark", "notifications": true, "language": "en"}}}}, {"endpoint": "/documents", "method": "GET", "status_code": 200, "success": true, "response_time": 0.24754810333251953, "error_message": null, "response_data": {"success": true, "data": [{"id": "dc5540d9-f7dd-4a2a-ac35-72b60901ddd0", "owner_id": "14285842-26d6-4a48-8b7c-1ac76fa5c488", "title": "Untitled Document", "type": "general", "status": "draft", "description": null, "word_count": 0, "character_count": 0, "version": 1, "tags": [], "is_public": false, "language": "en", "created_at": "2025-07-02T02:02:33.958887Z", "updated_at": "2025-07-02T02:02:33.958887Z", "published_at": null}, {"id": "0fb84a8d-ac7a-4886-bae3-aa24264a313e", "owner_id": "14285842-26d6-4a48-8b7c-1ac76fa5c488", "title": "Untitled Document", "type": "general", "status": "draft", "description": null, "word_count": 0, "character_count": 0, "version": 1, "tags": [], "is_public": false, "language": "en", "created_at": "2025-07-02T02:02:33.955887Z", "updated_at": "2025-07-02T02:02:33.955887Z", "published_at": null}, {"id": "7ece6d2e-379b-42d7-9bf5-5ffd44fce5ff", "owner_id": "14285842-26d6-4a48-8b7c-1ac76fa5c488", "title": "Untitled Document", "type": "general", "status": "draft", "description": null, "word_count": 0, "character_count": 0, "version": 1, "tags": [], "is_public": false, "language": "en", "created_at": "2025-07-02T01:55:56.326463Z", "updated_at": "2025-07-02T01:55:56.326463Z", "published_at": null}, {"id": "3728ef54-3e71-4412-91f2-f352e10d1c47", "owner_id": "14285842-26d6-4a48-8b7c-1ac76fa5c488", "title": "Untitled Document", "type": "general", "status": "draft", "description": null, "word_count": 0, "character_count": 0, "version": 1, "tags": [], "is_public": false, "language": "en", "created_at": "2025-07-02T01:55:56.325465Z", "updated_at": "2025-07-02T01:55:56.325465Z", "published_at": null}, {"id": "0ff0ee3a-f33d-4407-8462-c236a7ff6802", "owner_id": "14285842-26d6-4a48-8b7c-1ac76fa5c488", "title": "Untitled Document", "type": "general", "status": "draft", "description": null, "word_count": 0, "character_count": 0, "version": 1, "tags": [], "is_public": false, "language": "en", "created_at": "2025-07-02T01:55:25.552133Z", "updated_at": "2025-07-02T01:55:25.552133Z", "published_at": null}], "meta": {"total": 38, "limit": 5, "offset": 0, "has_more": true}}}, {"endpoint": "/documents", "method": "POST", "status_code": 500, "success": false, "response_time": 0.2682802677154541, "error_message": "Expected 200, got 500", "response_data": {"success": false, "error": {"code": 500, "message": "Failed to create document: CheckViolationError: new row for relation \"blocks\" violates check constraint \"blocks_root_no_parent\"\nDETAIL:  Failing row contains (1a382721-09cc-480f-9f71-c7e48c4072f4, 35e638ee-3798-4fc0-a307-cb8f816eb3ce, null, paragraph, 0, 0, 1a382721_09cc_480f_9f71_c7e48c4072f4, Test content, 9d9595c5d94fb65b824f56e9999527dba9542481580d69feb89056aabaa0aa87, 12, 0, {}, 2025-07-02 06:51:00.50322+00, 2025-07-02 06:51:00.50322+00, null)."}, "meta": {"path": "/api/v1/documents/", "method": "POST", "timestamp": 1751424660.7647755}}}, {"endpoint": "/analytics/token-usage", "method": "GET", "status_code": 200, "success": true, "response_time": 0.48093438148498535, "error_message": null, "response_data": {"success": true, "data": {"user_id": "14285842-26d6-4a48-8b7c-1ac76fa5c488", "total_tokens_used": 47070, "tokens_limit": 25000, "tokens_remaining": 0, "usage_percentage": 100.0, "current_period_start": "2025-06-03T00:00:00", "current_period_end": "2025-07-02T23:59:59.999999", "daily_breakdown": [{"date": "2025-06-19", "tokens_used": 15420, "operations": 45, "documents_edited": 3}, {"date": "2025-06-20", "tokens_used": 18760, "operations": 52, "documents_edited": 4}, {"date": "2025-06-21", "tokens_used": 12890, "operations": 38, "documents_edited": 2}], "tokens_by_model": {"gpt-4": 38140, "gpt-3.5-turbo": 8930}, "tokens_by_agent": {"research": 11150, "citations": 6850, "summaries": 10680, "suggestions": 12750, "health_metrics": 5640}}}}, {"endpoint": "/health/analyze", "method": "POST", "status_code": 500, "success": false, "response_time": 0.0, "error_message": "Expected 200, got 500", "response_data": {"success": false, "error": {"code": 500, "message": "Failed to analyze text health: UnboundLocalError: cannot access local variable 'job_id' where it is not associated with a value"}, "meta": {"path": "/api/v1/health/analyze", "method": "POST", "timestamp": **********.451434}}}, {"endpoint": "/developer/docs", "method": "GET", "status_code": 200, "success": true, "response_time": 0.0, "error_message": null, "response_data": {"success": true, "data": [{"filename": "api-documentation.md", "title": "Revisionary - API Documentation", "description": "The Revisionary API provides programmatic access to all writing assistance features. This RESTful AP...", "size": 43120, "last_modified": **********.3583887, "category": "API"}]}}, {"endpoint": "/developer/docs/api-documentation.md", "method": "GET", "status_code": 200, "success": true, "response_time": 0.0025177001953125, "error_message": null, "response_data": {"success": true, "data": {"filename": "api-documentation.md", "content": "# Revisionary - API Documentation\n\n## 1. Overview\n\nThe Revisionary API provides programmatic access to all writing assistance features. This RESTful API uses JSON for request and response bodies, with additional WebSocket endpoints for real-time features.\n\n### Base URL\n```\nProduction: https://api.revisionary.app/v1\nStaging: https://api-staging.revisionary.app/v1\n```\n\n### Authentication\nAll API requests require authentication via access tokens obtained through Firebase ID token exchange:\n\n1. **First, exchange your Firebase ID token for an access token:**\n```\nPOST /auth/token\nContent-Type: application/json\n{\n  \"firebase_token\": \"<firebase-id-token>\"\n}\n```\n\n2. **Then use the access token for all subsequent requests:**\n```\nAuthorization: Bearer <access-token>\n```\n\nAccess tokens expire after 1 hour and can be refreshed using the refresh token.\n\n### Rate Limiting\nRate limits are applied per user based on subscription tier:\n- **Free**: 100 requests/hour\n- **Professional**: 1,000 requests/hour\n- **Studio**: 10,000 requests/hour\n- **Enterprise**: Unlimited\n\n### Response Format\n```json\n{\n  \"success\": true,\n  \"data\": { ... },\n  \"meta\": {\n    \"request_id\": \"req_123abc\",\n    \"timestamp\": \"2024-01-15T10:30:00Z\"\n  }\n}\n```\n\n### Error Format\n```json\n{\n  \"success\": false,\n  \"error\": {\n    \"code\": \"INVALID_REQUEST\",\n    \"message\": \"The request body is invalid\",\n    \"details\": { ... }\n  },\n  \"meta\": {\n    \"request_id\": \"req_123abc\",\n    \"timestamp\": \"2024-01-15T10:30:00Z\"\n  }\n}\n```\n\n## 2. Authentication Endpoints\n\n### POST /auth/token\nExchange Firebase ID token for API access token.\n\n**Request:**\n```json\n{\n  \"firebase_token\": \"eyJhbGciOiJIUzI1NiIs...\"\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"access_token\": \"at_abc123...\",\n    \"refresh_token\": \"rt_def456...\",\n    \"expires_in\": 3600,\n    \"user\": {\n      \"id\": \"user_123\",\n      \"email\": \"<EMAIL>\",\n      \"subscription_tier\": \"professional\"\n    }\n  }\n}\n```\n\n### POST /auth/refresh\nRefresh an expired access token.\n\n**Request:**\n```json\n{\n  \"refresh_token\": \"rt_def456...\"\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"access_token\": \"at_ghi789...\",\n    \"expires_in\": 3600\n  }\n}\n```\n\n## 3. Document Management\n\n### GET /documents\nList all documents for the authenticated user.\n\n**Query Parameters:**\n- `page` (integer): Page number (default: 1)\n- `limit` (integer): Items per page (default: 20, max: 100)\n- `sort` (string): Sort field (created_at, updated_at, title)\n- `order` (string): Sort order (asc, desc)\n- `search` (string): Search in title and content\n- `type` (string): Filter by document type (creative, academic, professional, general)\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"documents\": [\n      {\n        \"id\": \"doc_123\",\n        \"title\": \"My Novel\",\n        \"type\": \"creative\",\n        \"word_count\": 45230,\n        \"created_at\": \"2024-01-10T08:00:00Z\",\n        \"updated_at\": \"2024-01-15T10:30:00Z\",\n        \"metadata\": {\n          \"genre\": \"fantasy\",\n          \"status\": \"draft\"\n        }\n      }\n    ],\n    \"pagination\": {\n      \"page\": 1,\n      \"limit\": 20,\n      \"total\": 145,\n      \"pages\": 8\n    }\n  }\n}\n```\n\n### POST /documents\nCreate a new document.\n\n**Request:**\n```json\n{\n  \"title\": \"Research Paper\",\n  \"type\": \"academic\",\n  \"content\": \"# Introduction\\n\\nThis paper explores...\",\n  \"metadata\": {\n    \"subject\": \"Computer Science\",\n    \"deadline\": \"2024-02-01\"\n  }\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"doc_456\",\n    \"title\": \"Research Paper\",\n    \"type\": \"academic\",\n    \"word_count\": 150,\n    \"created_at\": \"2024-01-15T10:35:00Z\",\n    \"updated_at\": \"2024-01-15T10:35:00Z\",\n    \"blocks\": [\n      {\n        \"id\": \"block_789\",\n        \"type\": \"section\",\n        \"content\": \"# Introduction\\n\\nThis paper explores...\",\n        \"position\": 0\n      }\n    ]\n  }\n}\n```\n\n### GET /documents/{document_id}\nGet a specific document with all blocks.\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"doc_123\",\n    \"title\": \"My Novel\",\n    \"type\": \"creative\",\n    \"word_count\": 45230,\n    \"created_at\": \"2024-01-10T08:00:00Z\",\n    \"updated_at\": \"2024-01-15T10:30:00Z\",\n    \"metadata\": { ... },\n    \"blocks\": [\n      {\n        \"id\": \"block_001\",\n        \"type\": \"chapter\",\n        \"content\": \"# Chapter 1: The Beginning\",\n        \"position\": 0,\n        \"children\": [\"block_002\", \"block_003\"]\n      },\n      {\n        \"id\": \"block_002\",\n        \"type\": \"paragraph\",\n        \"parent_id\": \"block_001\",\n        \"content\": \"It was a dark and stormy night...\",\n        \"position\": 0\n      }\n    ],\n    \"outline\": [\n      {\n        \"id\": \"block_001\",\n        \"title\": \"Chapter 1: The Beginning\",\n        \"type\": \"chapter\",\n        \"children\": [...]\n      }\n    ]\n  }\n}\n```\n\n### PATCH /documents/{document_id}\nUpdate document metadata.\n\n**Request:**\n```json\n{\n  \"title\": \"My Novel - Revised\",\n  \"metadata\": {\n    \"status\": \"editing\"\n  }\n}\n```\n\n### DELETE /documents/{document_id}\nDelete a document.\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"message\": \"Document deleted successfully\"\n  }\n}\n```\n\n## 4. Block Operations\n\n### GET /blocks/{block_id}\nGet a specific block with suggestions.\n\n**Query Parameters:**\n- `include_suggestions` (boolean): Include AI suggestions (default: true)\n- `suggestion_status` (string): Filter suggestions by status (pending, accepted, rejected)\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"block_123\",\n    \"document_id\": \"doc_123\",\n    \"type\": \"paragraph\",\n    \"content\": \"The protagonist walked slowly through the forest.\",\n    \"position\": 5,\n    \"hash\": \"a1b2c3d4...\",\n    \"suggestions\": [\n      {\n        \"id\": \"sug_001\",\n        \"agent\": \"style\",\n        \"severity\": \"medium\",\n        \"original_text\": \"walked slowly\",\n        \"suggested_text\": \"ambled\",\n        \"explanation\": \"More concise and evocative verb choice\",\n        \"confidence\": 0.85,\n        \"position\": {\n          \"start\": 17,\n          \"end\": 29\n        },\n        \"status\": \"pending\"\n      }\n    ]\n  }\n}\n```\n\n### PATCH /blocks/{block_id}\nUpdate block content.\n\n**Request:**\n```json\n{\n  \"content\": \"The protagonist ambled through the misty forest.\",\n  \"cursor_position\": 48,\n  \"client_hash\": \"a1b2c3d4...\"\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"block_123\",\n    \"content\": \"The protagonist ambled through the misty forest.\",\n    \"hash\": \"e5f6g7h8...\",\n    \"version\": 2,\n    \"suggestions_invalidated\": [\"sug_001\"]\n  }\n}\n```\n\n### POST /blocks/{block_id}/suggestions\nRequest AI suggestions for a block.\n\n**Request:**\n```json\n{\n  \"agents\": [\"grammar\", \"style\", \"structure\"],\n  \"intent\": \"improve\",\n  \"context_level\": \"paragraph\",\n  \"options\": {\n    \"style_preferences\": [\"concise\", \"vivid\"],\n    \"formality\": \"casual\"\n  }\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"job_id\": \"job_789\",\n    \"status\": \"processing\",\n    \"estimated_time\": 2.5,\n    \"stream_url\": \"/v1/ai/stream/job_789\"\n  }\n}\n```\n\n## 5. Agent Management\n\nNote: The AI operations are now handled through the agent system. See Agent Management endpoints below for current AI functionality.\n\n## 6. Persona System Endpoints\n\n### GET /personas\nList all personas for the authenticated user.\n\n**Query Parameters:**\n- `type` (string): Filter by persona type (academic, professional, creative, etc.)\n- `active` (boolean): Filter by active status (default: true)\n- `template` (boolean): Include template personas\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"personas\": [\n      {\n        \"id\": \"persona_123\",\n        \"name\": \"Academic Reviewer\",\n        \"type\": \"academic\",\n        \"description\": \"Graduate-level peer reviewer focused on methodology\",\n        \"demographics\": {\n          \"age_range\": \"25-35\",\n          \"education\": \"PhD\",\n          \"expertise\": \"Research methodology\"\n        },\n        \"is_active\": true,\n        \"usage_count\": 15,\n        \"effectiveness_score\": 0.87,\n        \"created_at\": \"2024-01-10T08:00:00Z\"\n      }\n    ]\n  }\n}\n```\n\n### POST /personas\nCreate a new persona.\n\n**Request:**\n```json\n{\n  \"name\": \"Young Adult Reader\",\n  \"type\": \"age_specific\",\n  \"description\": \"Target audience for YA fiction\",\n  \"demographics\": {\n    \"age_range\": \"16-24\",\n    \"reading_level\": \"high_school\",\n    \"interests\": [\"fantasy\", \"romance\", \"adventure\"]\n  },\n  \"reading_preferences\": {\n    \"pace\": \"fast\",\n    \"complexity\": \"medium\",\n    \"emotional_engagement\": \"high\"\n  },\n  \"personality_traits\": {\n    \"openness\": 0.8,\n    \"analytical_thinking\": 0.6,\n    \"emotional_sensitivity\": 0.9\n  },\n  \"feedback_style\": {\n    \"tone\": \"encouraging\",\n    \"detail_level\": \"medium\",\n    \"focus_areas\": [\"engagement\", \"relatability\"]\n  }\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"persona_456\",\n    \"name\": \"Young Adult Reader\",\n    \"type\": \"age_specific\",\n    \"is_active\": true,\n    \"created_at\": \"2024-01-15T10:30:00Z\"\n  }\n}\n```\n\n### GET /personas/{persona_id}\nGet detailed persona information.\n\n### PUT /personas/{persona_id}\nUpdate persona configuration.\n\n### DELETE /personas/{persona_id}\nDeactivate a persona.\n\n### POST /personas/{persona_id}/feedback\nRequest feedback from a specific persona.\n\n**Request:**\n```json\n{\n  \"block_id\": \"block_789\",\n  \"text\": \"The protagonist's journey through the enchanted forest...\",\n  \"context\": {\n    \"genre\": \"fantasy\",\n    \"target_audience\": \"young_adult\",\n    \"chapter\": 3\n  }\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"feedback_id\": \"feedback_123\",\n    \"persona_id\": \"persona_456\",\n    \"session_id\": \"session_789\",\n    \"comprehension_score\": 0.85,\n    \"engagement_score\": 0.92,\n    \"emotional_score\": 0.88,\n    \"style_score\": 0.79,\n    \"overall_score\": 0.86,\n    \"feedback_text\": \"This passage effectively captures the wonder and mystery that young adult readers crave...\",\n    \"specific_issues\": [\n      {\n        \"type\": \"pacing\",\n        \"description\": \"The description could be more concise to maintain engagement\",\n        \"position\": {\"start\": 45, \"end\": 128}\n      }\n    ],\n    \"suggestions\": [\n      {\n        \"type\": \"engagement\",\n        \"suggestion\": \"Add more sensory details to help readers visualize the scene\",\n        \"priority\": \"medium\"\n      }\n    ],\n    \"confidence\": 0.91\n  }\n}\n```\n\n### POST /personas/multi-feedback\nRequest feedback from multiple personas simultaneously.\n\n**Request:**\n```json\n{\n  \"block_id\": \"block_789\",\n  \"persona_ids\": [\"persona_123\", \"persona_456\", \"persona_789\"],\n  \"text\": \"Content to analyze...\",\n  \"options\": {\n    \"include_cross_analysis\": true,\n    \"conflict_detection\": true\n  }\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"session_id\": \"session_abc\",\n    \"feedback\": [\n      {\n        \"persona_id\": \"persona_123\",\n        \"feedback\": { /* Individual feedback object */ }\n      }\n    ],\n    \"cross_audience_analysis\": {\n      \"overall_appeal_score\": 0.82,\n      \"conflicts\": [\n        {\n          \"type\": \"complexity_mismatch\",\n          \"description\": \"Content may be too complex for younger personas\",\n          \"affected_personas\": [\"persona_456\"]\n        }\n      ],\n      \"optimization_suggestions\": [\n        {\n          \"suggestion\": \"Simplify technical language in paragraph 2\",\n          \"impact\": \"Improves accessibility for younger readers\"\n        }\n      ]\n    }\n  }\n}\n```\n\n## 7. Custom Agent System Endpoints\n\n### GET /agents\nList all custom agents for the authenticated user.\n\n**Query Parameters:**\n- `type` (string): Filter by agent type\n- `active` (boolean): Filter by active status\n- `template` (boolean): Include template agents\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"agents\": [\n      {\n        \"id\": \"agent_123\",\n        \"name\": \"Academic Style Enforcer\",\n        \"type\": \"style\",\n        \"description\": \"Ensures academic writing standards\",\n        \"capabilities\": [\"edit\", \"tone_adjustment\", \"consistency_check\"],\n        \"priority\": 75,\n        \"is_active\": true,\n        \"model_preference\": \"gpt-4.1-mini\",\n        \"usage_stats\": {\n          \"total_invocations\": 156,\n          \"acceptance_rate\": 0.78,\n          \"avg_processing_time\": 1200\n        },\n        \"created_at\": \"2024-01-05T14:20:00Z\",\n        \"last_used_at\": \"2024-01-15T09:15:00Z\"\n      }\n    ]\n  }\n}\n```\n\n### POST /agents\nCreate a new custom agent.\n\n**Request:**\n```json\n{\n  \"name\": \"Business Report Optimizer\",\n  \"type\": \"professional\",\n  \"description\": \"Optimizes content for executive consumption\",\n  \"capabilities\": [\"edit\", \"condense\", \"tone_adjustment\"],\n  \"priority\": 60,\n  \"model_preference\": \"gpt-4.1-full\",\n  \"max_context_tokens\": 8000,\n  \"temperature\": 0.2,\n  \"style_rules\": [\n    {\n      \"category\": \"tone\",\n      \"rule_name\": \"executive_formality\",\n      \"rule_value\": {\n        \"formality_level\": \"high\",\n        \"avoid_jargon\": true,\n        \"prefer_active_voice\": true\n      }\n    }\n  ]\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"agent_456\",\n    \"name\": \"Business Report Optimizer\",\n    \"type\": \"professional\",\n    \"is_active\": true,\n    \"created_at\": \"2024-01-15T11:00:00Z\"\n  }\n}\n```\n\n### PUT /agents/{agent_id}\nUpdate agent configuration.\n\n### DELETE /agents/{agent_id}\nDeactivate a custom agent.\n\n### POST /agents/{agent_id}/execute\nExecute a custom agent on specific content.\n\n**Request:**\n```json\n{\n  \"block_id\": \"block_789\",\n  \"text\": \"The quarterly results demonstrate significant growth...\",\n  \"context\": {\n    \"document_type\": \"business_report\",\n    \"audience\": \"executives\"\n  },\n  \"options\": {\n    \"max_suggestions\": 10,\n    \"min_confidence\": 0.7\n  }\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"execution_id\": \"exec_123\",\n    \"suggestions\": [\n      {\n        \"id\": \"sug_001\",\n        \"type\": \"edit\",\n        \"original_text\": \"demonstrate significant growth\",\n        \"suggested_text\": \"show 23% year-over-year growth\",\n        \"explanation\": \"More specific and quantifiable for executive audience\",\n        \"confidence\": 0.87,\n        \"position\": {\"start\": 25, \"end\": 52}\n      }\n    ],\n    \"processing_time_ms\": 1150,\n    \"tokens_used\": 245\n  }\n}\n```\n\n### GET /agents/{agent_id}/analytics\nGet performance analytics for a specific agent.\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"usage_stats\": {\n      \"total_invocations\": 156,\n      \"avg_processing_time_ms\": 1200,\n      \"acceptance_rate\": 0.78,\n      \"avg_confidence\": 0.84,\n      \"tokens_used\": 12450,\n      \"error_rate\": 0.02\n    },\n    \"performance_trends\": [\n      {\n        \"date\": \"2024-01-15\",\n        \"invocations\": 12,\n        \"acceptance_rate\": 0.83\n      }\n    ],\n    \"most_common_suggestions\": [\n      {\n        \"type\": \"tone_adjustment\",\n        \"count\": 45,\n        \"avg_confidence\": 0.89\n      }\n    ]\n  }\n}\n```\n\n## 8. Writing Health Analysis Endpoints\n\n### POST /health/analyze\nAnalyze writing health for a text block.\n\n**Request:**\n```json\n{\n  \"block_id\": \"block_789\",\n  \"text\": \"The comprehensive analysis of the aforementioned data demonstrates...\",\n  \"options\": {\n    \"include_suggestions\": true,\n    \"target_audience\": \"general\",\n    \"brand_voice\": \"professional\"\n  }\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"health_id\": \"health_123\",\n    \"overall_score\": 72.5,\n    \"metrics\": {\n      \"readability\": {\n        \"score\": 65.2,\n        \"flesch_kincaid_grade\": 12.3,\n        \"flesch_reading_ease\": 52.1,\n        \"target_grade\": \"college\"\n      },\n      \"clarity\": {\n        \"score\": 68.8,\n        \"avg_sentence_length\": 22.5,\n        \"complex_words_percentage\": 18.2,\n        \"passive_voice_percentage\": 25.0\n      },\n      \"voice_consistency\": {\n        \"score\": 78.3,\n        \"tone_variation\": 0.12,\n        \"formality_consistency\": 0.85\n      },\n      \"brand_alignment\": {\n        \"score\": 71.2,\n        \"voice_match\": 0.73,\n        \"terminology_consistency\": 0.69\n      }\n    },\n    \"issues\": [\n      {\n        \"id\": \"issue_001\",\n        \"type\": \"clarity\",\n        \"severity\": \"medium\",\n        \"category\": \"wordiness\",\n        \"description\": \"Sentence contains unnecessary filler words\",\n        \"position\": {\"start\": 0, \"end\": 65},\n        \"suggested_fix\": \"Remove 'aforementioned' and 'comprehensive'\",\n        \"confidence\": 0.89\n      }\n    ],\n    \"suggestions\": [\n      {\n        \"type\": \"readability\",\n        \"suggestion\": \"Break long sentences into shorter, clearer statements\",\n        \"impact\": \"Improves comprehension for broader audience\"\n      }\n    ],\n    \"processing_time_ms\": 340\n  }\n}\n```\n\n### GET /health/trends\nGet writing health trends for a user.\n\n**Query Parameters:**\n- `period` (string): day, week, month (default: month)\n- `document_id` (string): Filter by specific document\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"period\": \"month\",\n    \"trends\": [\n      {\n        \"date\": \"2024-01-15\",\n        \"avg_score\": 74.2,\n        \"analyses_count\": 23,\n        \"top_issues\": [\"readability\", \"clarity\"]\n      }\n    ],\n    \"improvement_rate\": 8.5,\n    \"focus_areas\": [\n      {\n        \"metric\": \"readability\",\n        \"current_score\": 72.1,\n        \"trend\": \"improving\",\n        \"change_percentage\": 12.3\n      }\n    ]\n  }\n}\n```\n\n### POST /health/batch-analyze\nAnalyze multiple blocks simultaneously.\n\n**Request:**\n```json\n{\n  \"blocks\": [\n    {\n      \"block_id\": \"block_123\",\n      \"text\": \"First paragraph content...\"\n    },\n    {\n      \"block_id\": \"block_456\",\n      \"text\": \"Second paragraph content...\"\n    }\n  ],\n  \"options\": {\n    \"priority\": \"normal\",\n    \"include_cross_block_analysis\": true\n  }\n}\n```\n\n## 9. Analytics & Achievement Endpoints\n\n### GET /analytics/sessions\nGet writing session analytics.\n\n**Query Parameters:**\n- `start_date` (string): ISO 8601 date\n- `end_date` (string): ISO 8601 date\n- `document_id` (string): Filter by document\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"sessions\": [\n      {\n        \"id\": \"session_123\",\n        \"start_time\": \"2024-01-15T09:00:00Z\",\n        \"end_time\": \"2024-01-15T11:30:00Z\",\n        \"words_written\": 847,\n        \"time_writing_ms\": 6420000,\n        \"time_thinking_ms\": 2580000,\n        \"ai_interactions\": 8,\n        \"revisions_count\": 23,\n        \"productivity_score\": 0.78,\n        \"focus_score\": 0.71\n      }\n    ],\n    \"summary\": {\n      \"total_sessions\": 45,\n      \"total_words\": 23456,\n      \"avg_session_length_ms\": 5400000,\n      \"avg_productivity_score\": 0.73\n    }\n  }\n}\n```\n\n### GET /analytics/goals\nGet user's writing goals and progress.\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"goals\": [\n      {\n        \"id\": \"goal_123\",\n        \"type\": \"daily_words\",\n        \"target\": 500,\n        \"current\": 347,\n        \"progress_percentage\": 69.4,\n        \"timeframe\": \"daily\",\n        \"status\": \"in_progress\",\n        \"streak_days\": 5,\n        \"created_at\": \"2024-01-10T00:00:00Z\"\n      }\n    ],\n    \"streaks\": {\n      \"current_writing_streak\": 5,\n      \"longest_writing_streak\": 23,\n      \"current_goal_streak\": 3\n    }\n  }\n}\n```\n\n### POST /analytics/goals\nCreate a new writing goal.\n\n**Request:**\n```json\n{\n  \"type\": \"weekly_words\",\n  \"target\": 2500,\n  \"timeframe\": \"weekly\",\n  \"start_date\": \"2024-01-15\"\n}\n```\n\n### GET /analytics/achievements\nGet user achievements.\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"achievements\": [\n      {\n        \"id\": \"achievement_123\",\n        \"name\": \"First Thousand\",\n        \"description\": \"Write your first 1,000 words\",\n        \"category\": \"milestone\",\n        \"rarity\": \"common\",\n        \"progress\": 100,\n        \"unlocked_at\": \"2024-01-12T15:30:00Z\",\n        \"points\": 100\n      }\n    ],\n    \"stats\": {\n      \"total_achievements\": 15,\n      \"total_points\": 2340,\n      \"common_achievements\": 8,\n      \"rare_achievements\": 5,\n      \"epic_achievements\": 2,\n      \"legendary_achievements\": 0\n    }\n  }\n}\n```\n\n### POST /analytics/events\nTrack a custom analytics event.\n\n**Request:**\n```json\n{\n  \"event_type\": \"feature_used\",\n  \"event_data\": {\n    \"feature\": \"persona_feedback\",\n    \"persona_count\": 3,\n    \"session_id\": \"session_123\"\n  },\n  \"document_id\": \"doc_456\"\n}\n```\n\n## 10. Cross-Reference Intelligence Endpoints\n\n### GET /entities\nList entities in a document.\n\n**Query Parameters:**\n- `document_id` (string): Required document ID\n- `type` (string): Filter by entity type\n- `search` (string): Search entity names\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"entities\": [\n      {\n        \"id\": \"entity_123\",\n        \"name\": \"Professor Smith\",\n        \"type\": \"character\",\n        \"aliases\": [\"Prof. Smith\", \"Dr. Smith\"],\n        \"description\": \"Physics professor and mentor figure\",\n        \"attributes\": {\n          \"age\": \"mid-50s\",\n          \"occupation\": \"University Professor\",\n          \"expertise\": \"Quantum Physics\"\n        },\n        \"mention_count\": 23,\n        \"importance_score\": 0.85,\n        \"first_mentioned_block_id\": \"block_012\",\n        \"relationships\": [\n          {\n            \"target_entity_id\": \"entity_456\",\n            \"relationship_type\": \"professional\",\n            \"relationship_name\": \"mentor\"\n          }\n        ]\n      }\n    ]\n  }\n}\n```\n\n### POST /entities\nCreate a new entity.\n\n**Request:**\n```json\n{\n  \"document_id\": \"doc_123\",\n  \"name\": \"Central University\",\n  \"type\": \"location\",\n  \"description\": \"Prestigious research university\",\n  \"attributes\": {\n    \"type\": \"educational_institution\",\n    \"size\": \"large\",\n    \"location\": \"urban\"\n  },\n  \"first_mentioned_block_id\": \"block_045\"\n}\n```\n\n### GET /entities/{entity_id}/consistency\nCheck consistency for a specific entity.\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"entity_id\": \"entity_123\",\n    \"consistency_score\": 0.82,\n    \"violations\": [\n      {\n        \"id\": \"violation_001\",\n        \"type\": \"attribute_conflict\",\n        \"severity\": \"moderate\",\n        \"description\": \"Character age inconsistency between chapters 3 and 7\",\n        \"conflicting_blocks\": [\"block_123\", \"block_456\"],\n        \"suggested_resolution\": \"Standardize age reference to 'mid-50s'\"\n      }\n    ],\n    \"timeline_issues\": [],\n    \"relationship_conflicts\": []\n  }\n}\n```\n\n### POST /documents/{document_id}/consistency-check\nRun full consistency analysis on a document.\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"job_id\": \"consistency_job_123\",\n    \"status\": \"processing\",\n    \"estimated_time_ms\": 15000,\n    \"stream_url\": \"/v1/consistency/stream/consistency_job_123\"\n  }\n}\n```\n\n## 11. Citation Management Endpoints\n\n### GET /citations\nList citations in a document.\n\n**Query Parameters:**\n- `document_id` (string): Required document ID\n- `type` (string): Filter by citation type\n- `style` (string): Filter by citation style\n- `verified` (boolean): Filter by verification status\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"citations\": [\n      {\n        \"id\": \"citation_123\",\n        \"citation_key\": \"smith2023quantum\",\n        \"type\": \"journal_article\",\n        \"title\": \"Advances in Quantum Computing Applications\",\n        \"authors\": [\"Dr. Jane Smith\", \"Prof. John Doe\"],\n        \"publication_year\": 2023,\n        \"journal_name\": \"Journal of Quantum Research\",\n        \"volume\": \"15\",\n        \"issue\": \"3\",\n        \"page_numbers\": \"245-267\",\n        \"doi\": \"10.1000/xyz123\",\n        \"formatted_citation\": \"Smith, J., & Doe, J. (2023). Advances in Quantum Computing Applications. Journal of Quantum Research, 15(3), 245-267.\",\n        \"citation_style\": \"apa\",\n        \"is_verified\": true,\n        \"position\": {\"start\": 156, \"end\": 178}\n      }\n    ]\n  }\n}\n```\n\n### POST /citations\nAdd a new citation.\n\n**Request:**\n```json\n{\n  \"document_id\": \"doc_123\",\n  \"block_id\": \"block_456\",\n  \"citation_key\": \"doe2024research\",\n  \"type\": \"book\",\n  \"title\": \"Modern Research Methods\",\n  \"authors\": [\"Dr. John Doe\"],\n  \"publication_year\": 2024,\n  \"publisher\": \"Academic Press\",\n  \"isbn\": \"978-0-123456-78-9\",\n  \"citation_style\": \"apa\",\n  \"position\": {\"start\": 245, \"end\": 267}\n}\n```\n\n### POST /citations/search\nSearch for citations in external databases.\n\n**Request:**\n```json\n{\n  \"query\": \"quantum computing applications\",\n  \"filters\": {\n    \"year_min\": 2020,\n    \"year_max\": 2024,\n    \"type\": \"journal_article\"\n  },\n  \"sources\": [\"crossref\", \"pubmed\", \"arxiv\"]\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"results\": [\n      {\n        \"title\": \"Quantum Computing in Artificial Intelligence\",\n        \"authors\": [\"Dr. Alice Johnson\", \"Prof. Bob Wilson\"],\n        \"publication_year\": 2023,\n        \"journal\": \"AI Research Quarterly\",\n        \"doi\": \"10.1000/ai2023.456\",\n        \"abstract\": \"This paper explores the intersection of quantum computing...\",\n        \"relevance_score\": 0.92,\n        \"source\": \"crossref\"\n      }\n    ],\n    \"total_results\": 156,\n    \"search_time_ms\": 234\n  }\n}\n```\n\n### GET /reference-library\nGet user's personal reference library.\n\n### POST /reference-library\nAdd a reference to personal library.\n\n### POST /citations/{citation_id}/verify\nVerify citation accuracy.\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"is_valid\": true,\n    \"verification_details\": {\n      \"doi_valid\": true,\n      \"publication_exists\": true,\n      \"metadata_accurate\": true,\n      \"accessibility\": \"open_access\"\n    },\n    \"suggestions\": [],\n    \"verified_at\": \"2024-01-15T12:00:00Z\"\n  }\n}\n```\n\n## 12. Real-time Endpoints\n\n### SSE /ai/stream/{job_id}\nStream AI processing results via Server-Sent Events.\n\n**Event Types:**\n```\nevent: progress\ndata: {\"progress\": 0.45, \"stage\": \"analyzing_context\"}\n\nevent: suggestion\ndata: {\"agent\": \"grammar\", \"suggestion\": {...}}\n\nevent: complete\ndata: {\"total_suggestions\": 12, \"processing_time\": 1.8}\n\nevent: error\ndata: {\"error\": \"Processing failed\", \"code\": \"AI_ERROR\"}\n```\n\n### WebSocket Connection\nReal-time document collaboration via Socket.io.\n\n**Connection:**\n```javascript\nimport { io } from 'socket.io-client';\n\nconst socket = io('wss://api.revisionary.app', {\n  auth: {\n    token: 'your-access-token'\n  }\n});\n\n// Join a document room\nsocket.emit('join_document', {\n  document_id: 'doc_123',\n  user_id: 'user_456'\n});\n```\n\n**Message Types:**\n\n**Cursor Update:**\n```json\n{\n  \"type\": \"cursor\",\n  \"user_id\": \"user_456\",\n  \"position\": {\n    \"block_id\": \"block_123\",\n    \"offset\": 45\n  }\n}\n```\n\n**Content Update:**\n```json\n{\n  \"type\": \"update\",\n  \"block_id\": \"block_123\",\n  \"operation\": {\n    \"type\": \"insert\",\n    \"position\": 45,\n    \"text\": \"new text\"\n  },\n  \"version\": 15\n}\n```\n\n**Presence:**\n```json\n{\n  \"type\": \"presence\",\n  \"users\": [\n    {\n      \"id\": \"user_456\",\n      \"name\": \"John Doe\",\n      \"color\": \"#FF5733\",\n      \"cursor\": {...}\n    }\n  ]\n}\n```\n\n## 7. Export Endpoints\n\n### POST /export\nExport document in various formats.\n\n**Request:**\n```json\n{\n  \"document_id\": \"doc_123\",\n  \"format\": \"docx\",\n  \"options\": {\n    \"include_comments\": true,\n    \"include_suggestions\": false,\n    \"template\": \"manuscript\",\n    \"styling\": {\n      \"font\": \"Times New Roman\",\n      \"size\": 12,\n      \"line_spacing\": 2.0\n    }\n  }\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"export_id\": \"exp_789\",\n    \"status\": \"processing\",\n    \"estimated_time\": 10,\n    \"webhook_url\": \"https://your-domain.com/webhook/exp_789\"\n  }\n}\n```\n\n### GET /export/{export_id}\nCheck export status or download.\n\n**Response (Processing):**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"status\": \"processing\",\n    \"progress\": 0.75\n  }\n}\n```\n\n**Response (Complete):**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"status\": \"complete\",\n    \"download_url\": \"https://storage.revisionary.app/exports/exp_789.docx\",\n    \"expires_at\": \"2024-01-15T11:35:00Z\",\n    \"file_size\": 145230,\n    \"page_count\": 89\n  }\n}\n```\n\n## 8. User & Subscription\n\n### GET /user/profile\nGet current user profile.\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"user_123\",\n    \"email\": \"<EMAIL>\",\n    \"name\": \"Jane Doe\",\n    \"subscription\": {\n      \"tier\": \"professional\",\n      \"status\": \"active\",\n      \"renews_at\": \"2024-02-15T00:00:00Z\",\n      \"limits\": {\n        \"monthly_tokens\": 225000,\n        \"tokens_used\": 45230,\n        \"documents\": \"unlimited\",\n        \"collaborators\": 10\n      }\n    },\n    \"preferences\": {\n      \"default_document_type\": \"creative\",\n      \"ui_theme\": \"dark\",\n      \"editor_font\": \"monospace\"\n    }\n  }\n}\n```\n\n### GET /user/usage\nGet usage statistics.\n\n**Query Parameters:**\n- `period` (string): day, week, month, year\n- `start_date` (string): ISO 8601 date\n- `end_date` (string): ISO 8601 date\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"period\": \"month\",\n    \"usage\": {\n      \"tokens\": {\n        \"total\": 45230,\n        \"by_model\": {\n          \"gpt-4.1-nano\": 12500,\n          \"gpt-4.1-mini\": 18230,\n          \"gpt-4.1\": 10500,\n          \"gemini-2.5-flash\": 4000\n        },\n        \"by_agent\": {\n          \"grammar\": 8500,\n          \"style\": 15230,\n          \"structure\": 12000,\n          \"content\": 9500\n        }\n      },\n      \"documents\": {\n        \"created\": 15,\n        \"edited\": 87,\n        \"words_written\": 125000\n      },\n      \"ai_operations\": {\n        \"suggestions\": 1250,\n        \"generations\": 89,\n        \"rewrites\": 156\n      }\n    }\n  }\n}\n```\n\n## 13. Enhanced Search Endpoints\n\n### POST /search\nSearch across documents.\n\n**Request:**\n```json\n{\n  \"query\": \"character development\",\n  \"scope\": \"all_documents\",\n  \"filters\": {\n    \"document_types\": [\"creative\"],\n    \"date_range\": {\n      \"start\": \"2024-01-01\",\n      \"end\": \"2024-01-31\"\n    }\n  },\n  \"options\": {\n    \"include_context\": true,\n    \"highlight\": true,\n    \"limit\": 20\n  }\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"results\": [\n      {\n        \"document_id\": \"doc_123\",\n        \"document_title\": \"My Novel\",\n        \"block_id\": \"block_456\",\n        \"score\": 0.95,\n        \"highlight\": \"...the <mark>character development</mark> in this chapter...\",\n        \"context\": \"Previous paragraph... [match] ...Next paragraph\"\n      }\n    ],\n    \"total_results\": 45,\n    \"facets\": {\n      \"document_types\": {\n        \"creative\": 38,\n        \"academic\": 7\n      }\n    }\n  }\n}\n```\n\n## 14. Mobile Sync Endpoints\n\n### POST /mobile/sync\nSynchronize mobile changes with server.\n\n**Request:**\n```json\n{\n  \"device_id\": \"device_abc123\",\n  \"last_sync_timestamp\": \"2024-01-15T10:00:00Z\",\n  \"changes\": [\n    {\n      \"type\": \"block_update\",\n      \"block_id\": \"block_123\",\n      \"content\": \"Updated content from mobile...\",\n      \"timestamp\": \"2024-01-15T10:30:00Z\",\n      \"client_id\": \"client_456\"\n    }\n  ],\n  \"conflicts_resolution\": \"server_wins\"\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"sync_id\": \"sync_789\",\n    \"conflicts\": [\n      {\n        \"block_id\": \"block_123\",\n        \"conflict_type\": \"concurrent_edit\",\n        \"server_version\": \"Server content...\",\n        \"client_version\": \"Mobile content...\",\n        \"resolution\": \"merged\",\n        \"merged_content\": \"Final merged content...\"\n      }\n    ],\n    \"applied_changes\": 15,\n    \"rejected_changes\": 1,\n    \"next_sync_timestamp\": \"2024-01-15T11:00:00Z\"\n  }\n}\n```\n\n### GET /mobile/delta\nGet changes since last sync.\n\n**Query Parameters:**\n- `since_timestamp` (string): ISO 8601 timestamp\n- `document_ids` (array): Filter by specific documents\n- `include_metadata` (boolean): Include document metadata\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"changes\": [\n      {\n        \"type\": \"document_created\",\n        \"document_id\": \"doc_new\",\n        \"timestamp\": \"2024-01-15T10:45:00Z\",\n        \"data\": { /* Document object */ }\n      },\n      {\n        \"type\": \"block_updated\",\n        \"block_id\": \"block_456\",\n        \"document_id\": \"doc_123\",\n        \"timestamp\": \"2024-01-15T10:50:00Z\",\n        \"data\": { /* Updated block */ }\n      }\n    ],\n    \"has_more\": false,\n    \"sync_timestamp\": \"2024-01-15T11:00:00Z\"\n  }\n}\n```\n\n### POST /mobile/offline-queue\nSubmit offline actions queue.\n\n**Request:**\n```json\n{\n  \"device_id\": \"device_abc123\",\n  \"queue\": [\n    {\n      \"action\": \"create_document\",\n      \"temp_id\": \"temp_doc_1\",\n      \"data\": {\n        \"title\": \"Offline Document\",\n        \"type\": \"creative\"\n      },\n      \"timestamp\": \"2024-01-15T08:30:00Z\"\n    },\n    {\n      \"action\": \"update_block\",\n      \"temp_id\": \"temp_block_1\",\n      \"parent_temp_id\": \"temp_doc_1\",\n      \"data\": {\n        \"content\": \"Content created offline...\"\n      },\n      \"timestamp\": \"2024-01-15T08:32:00Z\"\n    }\n  ]\n}\n```\n\n## 15. Webhook Events\n\nConfigure webhooks to receive real-time notifications.\n\n### Event Types\n\n**document.created**\n```json\n{\n  \"event\": \"document.created\",\n  \"timestamp\": \"2024-01-15T10:40:00Z\",\n  \"data\": {\n    \"document_id\": \"doc_789\",\n    \"user_id\": \"user_123\",\n    \"title\": \"New Document\"\n  }\n}\n```\n\n**ai.suggestion.complete**\n```json\n{\n  \"event\": \"ai.suggestion.complete\",\n  \"timestamp\": \"2024-01-15T10:41:00Z\",\n  \"data\": {\n    \"job_id\": \"job_456\",\n    \"document_id\": \"doc_123\",\n    \"block_id\": \"block_789\",\n    \"suggestions_count\": 5,\n    \"processing_time\": 1.8\n  }\n}\n```\n\n**export.complete**\n```json\n{\n  \"event\": \"export.complete\",\n  \"timestamp\": \"2024-01-15T10:42:00Z\",\n  \"data\": {\n    \"export_id\": \"exp_123\",\n    \"document_id\": \"doc_456\",\n    \"format\": \"pdf\",\n    \"download_url\": \"https://storage.revisionary.app/exports/exp_123.pdf\"\n  }\n}\n```\n\n## 16. Advanced Error Codes\n\n### New Error Codes for Advanced Features\n\n| Code | HTTP Status | Description |\n|------|-------------|-------------|\n| PERSONA_LIMIT_EXCEEDED | 429 | Maximum number of personas reached for subscription tier |\n| AGENT_EXECUTION_TIMEOUT | 408 | Custom agent execution timed out |\n| HEALTH_ANALYSIS_FAILED | 500 | Writing health analysis service error |\n| CONSISTENCY_CHECK_IN_PROGRESS | 409 | Consistency check already running for this document |\n| CITATION_VERIFICATION_FAILED | 422 | Unable to verify citation against external sources |\n| CROSS_AUDIENCE_CONFLICT | 422 | Unresolvable conflicts between persona feedback |\n| ENTITY_RELATIONSHIP_INVALID | 400 | Invalid entity relationship configuration |\n| MOBILE_SYNC_CONFLICT | 409 | Unresolvable sync conflict between mobile and server |\n| ACHIEVEMENT_ALREADY_UNLOCKED | 409 | Attempt to unlock already achieved achievement |\n| AGENT_TEMPLATE_INVALID | 400 | Custom agent template contains invalid configuration |\n| PERSONA_FEEDBACK_LIMIT | 429 | Daily persona feedback limit exceeded |\n| HEALTH_WORKER_UNAVAILABLE | 503 | Writing health analysis workers at capacity |\n\n## 17. Error Codes (Core)\n\n| Code | HTTP Status | Description |\n|------|-------------|-------------|\n| UNAUTHORIZED | 401 | Invalid or expired authentication token |\n| FORBIDDEN | 403 | Insufficient permissions for this operation |\n| NOT_FOUND | 404 | Requested resource not found |\n| RATE_LIMITED | 429 | Rate limit exceeded |\n| INVALID_REQUEST | 400 | Request body or parameters invalid |\n| CONFLICT | 409 | Resource conflict (e.g., stale content hash) |\n| AI_ERROR | 500 | AI service error |\n| SERVICE_UNAVAILABLE | 503 | Service temporarily unavailable |\n\n## 18. SDK Examples (Enhanced)\n\n### JavaScript/TypeScript (Enhanced)\n```typescript\nimport { RevisionaryClient } from '@revisionary/sdk';\n\nconst client = new RevisionaryClient({\n  apiKey: 'your-api-key',\n  environment: 'production'\n});\n\n// Create a document\nconst doc = await client.documents.create({\n  title: 'My Story',\n  type: 'creative'\n});\n\n// Create personas for feedback\nconst academiPersona = await client.personas.create({\n  name: 'Academic Reviewer',\n  type: 'academic',\n  demographics: { education: 'PhD', expertise: 'Literature' }\n});\n\nconst youngAdultPersona = await client.personas.create({\n  name: 'YA Reader',\n  type: 'age_specific',\n  demographics: { age_range: '16-24' }\n});\n\n// Create custom agent\nconst styleAgent = await client.agents.create({\n  name: 'Creative Style Enhancer',\n  type: 'style',\n  capabilities: ['edit', 'tone_adjustment'],\n  styleRules: {\n    tone: { creativity: 'high', formality: 'low' }\n  }\n});\n\n// Get multi-persona feedback\nconst feedback = await client.personas.getMultiFeedback({\n  blockId: 'block_123',\n  personaIds: [academiPersona.id, youngAdultPersona.id],\n  text: 'Once upon a time...'\n});\n\n// Execute custom agent\nconst suggestions = await client.agents.execute(styleAgent.id, {\n  blockId: 'block_123',\n  text: 'The story begins...'\n});\n\n// Analyze writing health\nconst health = await client.health.analyze({\n  blockId: 'block_123',\n  text: 'Content to analyze...'\n});\n\n// Track analytics event\nawait client.analytics.trackEvent({\n  eventType: 'feature_used',\n  eventData: { feature: 'persona_feedback' }\n});\n\n// Check entity consistency\nconst consistency = await client.crossReference.checkConsistency('doc_123');\n\n// Listen for real-time updates\nconst unsubscribe = client.documents.subscribe(doc.id, (event) => {\n  console.log('Document updated:', event);\n});\n```\n\n### Python (Enhanced)\n```python\nfrom revisionary import RevisionaryClient\nfrom datetime import datetime, timedelta\n\nclient = RevisionaryClient(api_key=\"your-api-key\")\n\n# Create document\ndoc = client.documents.create(\n    title=\"Research Paper\",\n    type=\"academic\"\n)\n\n# Create academic personas\nacademic_persona = client.personas.create(\n    name=\"Peer Reviewer\",\n    type=\"academic\",\n    demographics={\"education\": \"PhD\", \"field\": \"Computer Science\"}\n)\n\n# Create custom agents\ngrammar_agent = client.agents.create(\n    name=\"Academic Grammar Enforcer\",\n    type=\"grammar\",\n    capabilities=[\"edit\", \"consistency_check\"],\n    style_rules={\n        \"formality\": {\"level\": \"high\", \"avoid_contractions\": True}\n    }\n)\n\n# Get persona feedback\nfeedback = client.personas.get_feedback(\n    persona_id=academic_persona.id,\n    block_id=\"block_123\",\n    text=\"The methodology employed in this study...\"\n)\n\nprint(f\"Academic feedback score: {feedback.overall_score}\")\nprint(f\"Suggestions: {feedback.suggestions}\")\n\n# Execute custom agent\nagent_result = client.agents.execute(\n    agent_id=grammar_agent.id,\n    block_id=\"block_123\",\n    text=\"The data demonstrates significant results.\"\n)\n\n# Analyze writing health\nhealth_analysis = client.health.analyze(\n    block_id=\"block_123\",\n    text=\"Content to analyze...\",\n    options={\"target_audience\": \"academic\"}\n)\n\nprint(f\"Health score: {health_analysis.overall_score}\")\nprint(f\"Issues: {len(health_analysis.issues)}\")\n\n# Track writing session\nsession = client.analytics.start_session()\nclient.analytics.track_words_written(session.id, 250)\nclient.analytics.end_session(session.id)\n\n# Check achievements\nachievements = client.analytics.get_achievements()\nprint(f\"Unlocked achievements: {len(achievements)}\")\n\n# Manage entities\ncharacter = client.entities.create(\n    document_id=doc.id,\n    name=\"Dr. Smith\",\n    type=\"character\",\n    attributes={\"role\": \"research_supervisor\", \"age\": \"50s\"}\n)\n\n# Check consistency\nconsistency_report = client.cross_reference.check_consistency(doc.id)\nprint(f\"Consistency violations: {len(consistency_report.violations)}\")\n\n# Add citations\ncitation = client.citations.create(\n    document_id=doc.id,\n    citation_key=\"smith2023\",\n    title=\"Advanced Research Methods\",\n    authors=[\"Dr. J. Smith\"],\n    type=\"journal_article\",\n    publication_year=2023\n)\n\n# Verify citation\nverification = client.citations.verify(citation.id)\nprint(f\"Citation valid: {verification.is_valid}\")\n\n# Stream results for complex operations\nfor event in client.ai.stream(job.id):\n    print(f\"Suggestion from {event.agent}: {event.suggestion}\")\n```\n\n### cURL\n```bash\n# Create document\ncurl -X POST https://api.revisionary.app/v1/documents \\\n  -H \"Authorization: Bearer your-token\" \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"title\": \"My Document\",\n    \"type\": \"general\"\n  }'\n\n# Get suggestions\ncurl -X POST https://api.revisionary.app/v1/blocks/block_123/suggestions \\\n  -H \"Authorization: Bearer your-token\" \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"agents\": [\"grammar\", \"style\"],\n    \"intent\": \"improve\"\n  }'\n```\n\n## 19. Best Practices (Enhanced)\n\n### Advanced Feature Best Practices\n\n1. **Persona System**:\n   - Limit active personas to 3-5 for optimal performance\n   - Cache persona feedback for similar content\n   - Use batch feedback requests for multiple blocks\n   - Handle cross-audience conflicts gracefully\n\n2. **Custom Agents**:\n   - Set appropriate priorities to avoid conflicts\n   - Monitor agent performance and adjust configurations\n   - Use templates for common agent patterns\n   - Implement circuit breakers for failing agents\n\n3. **Writing Health**:\n   - Debounce health analysis requests (500ms minimum)\n   - Process health analysis in background workers\n   - Cache health results for unchanged content\n   - Provide progressive feedback during analysis\n\n4. **Cross-Reference Intelligence**:\n   - Batch entity updates for better performance\n   - Run consistency checks during idle periods\n   - Cache entity relationships for faster lookups\n   - Use incremental consistency checking\n\n5. **Mobile Synchronization**:\n   - Implement exponential backoff for sync retries\n   - Use delta synchronization for large documents\n   - Handle offline scenarios gracefully\n   - Prioritize user-initiated changes in conflicts\n\n6. **Citation Management**:\n   - Verify citations asynchronously\n   - Cache external citation lookups\n   - Provide fallback formatting for failed verifications\n   - Batch citation verification requests\n\n## 20. Best Practices (Core)\n\n1. **Caching**: Cache document structure and suggestions client-side\n2. **Pagination**: Always paginate list endpoints\n3. **Retries**: Implement exponential backoff for failed requests\n4. **Webhooks**: Use webhooks for async operations instead of polling\n5. **Batch Operations**: Group multiple operations when possible\n6. **Error Handling**: Always handle rate limit and service errors gracefully\n\n## 21. API Versioning\n\nThe API uses URL versioning (e.g., `/v1/`, `/v2/`). Breaking changes will result in a new API version. Deprecated endpoints will be supported for at least 6 months with advance notice.\n\n### Deprecation Headers\n```\nX-API-Deprecation-Date: 2024-12-31\nX-API-Deprecation-Info: https://docs.revisionary.app/deprecations/endpoint-name\nX-Feature-Support: personas=v1.2,agents=v1.1,health=v1.0\n```\n\n### Feature Flags\n```\nX-Enable-Personas: true\nX-Enable-Custom-Agents: true\nX-Enable-Health-Analysis: true\nX-Enable-Cross-Reference: true\nX-Enable-Mobile-Sync: true\n```\n\n## 22. Rate Limiting for Advanced Features\n\n### Feature-Specific Limits\n\n| Feature | Free Tier | Professional | Studio | Enterprise |\n|---------|-----------|--------------|--------|-----------|\n| Persona Feedback | 10/day | 100/day | 500/day | Unlimited |\n| Custom Agent Executions | 50/day | 500/day | 2000/day | Unlimited |\n| Health Analyses | 100/day | 1000/day | 5000/day | Unlimited |\n| Consistency Checks | 5/day | 25/day | 100/day | Unlimited |\n| Citation Verifications | 25/day | 200/day | 1000/day | Unlimited |\n| Cross-Reference Operations | 100/day | 1000/day | 5000/day | Unlimited |\n\n### Rate Limit Headers\n```\nX-RateLimit-Personas-Remaining: 85\nX-RateLimit-Personas-Reset: **********\nX-RateLimit-Agents-Remaining: 450\nX-RateLimit-Health-Remaining: 850\n```\n\nThis comprehensive API documentation now covers all the advanced features discovered in the frontend implementation, providing developers with complete information needed to integrate with the Revisionary backend services.", "size": 43120, "last_modified": **********.3583887}}}, {"endpoint": "/developer/openapi-spec", "method": "GET", "status_code": 200, "success": true, "response_time": 0.05287480354309082, "error_message": null, "response_data": {"openapi": "3.1.0", "info": {"title": "Revisionary API", "description": "\n            AI-powered writing assistance API providing comprehensive tools for document creation,\n            editing, and enhancement with advanced analytics and persona-driven writing assistance.\n            \n            ## Authentication\n            All endpoints require authentication via Bearer tokens obtained through Firebase ID token exchange.\n            \n            ## Rate Limiting\n            - Free: 100 requests/hour\n            - Professional: 1,000 requests/hour  \n            - Studio: 10,000 requests/hour\n            - Enterprise: Unlimited\n            \n            ## Base URL\n            - Production: `https://api.revisionary.app/v1`\n            - Development: `http://localhost:8000/api/v1`\n            ", "contact": {"name": "Revisionary API Support", "url": "https://revisionary.app/support", "email": "<EMAIL>"}, "license": {"name": "Proprietary", "url": "https://revisionary.app/terms"}, "version": "1.0.0"}, "paths": {"/api/v1/auth/token": {"post": {"tags": ["authentication"], "summary": "Exchange Firebase Token", "description": "Exchange Firebase ID token for API access token.\n\nThis endpoint validates the Firebase ID token and returns\nan API access token that can be used for authenticated requests.\n\nArgs:\n    request: Token exchange request with Firebase ID token\n    \nReturns:\n    TokenExchangeResponse: API access token and user information\n    \nRaises:\n    HTTPException: If Firebase token is invalid", "operationId": "exchange_firebase_token_api_v1_auth_token_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenExchangeRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenExchangeResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/refresh": {"post": {"tags": ["authentication"], "summary": "Refresh Access Token", "description": "Refresh an expired access token using refresh token.\n\nArgs:\n    request: Refresh token request\n    \nReturns:\n    TokenExchangeResponse: New access token\n    \nRaises:\n    HTTPException: If refresh token is invalid", "operationId": "refresh_access_token_api_v1_auth_refresh_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenExchangeResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/me": {"get": {"tags": ["authentication"], "summary": "Get Current User Profile", "description": "Get current authenticated user's profile information.\n\nArgs:\n    current_user: Current authenticated user from token\n    \nReturns:\n    UserProfileResponse: User profile information", "operationId": "get_current_user_profile_api_v1_auth_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/logout": {"post": {"tags": ["authentication"], "summary": "Logout User", "description": "Logout current user and invalidate tokens.\n\nArgs:\n    current_user: Current authenticated user from token\n    \nReturns:\n    dict: Success response", "operationId": "logout_user_api_v1_auth_logout_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/auth/validate": {"get": {"tags": ["authentication"], "summary": "Validate Token", "description": "Validate current access token.\n\nArgs:\n    current_user: Current authenticated user from token\n    \nReturns:\n    dict: Token validation response", "operationId": "validate_token_api_v1_auth_validate_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/documents/": {"get": {"tags": ["documents"], "summary": "List User Documents", "description": "List all documents for the authenticated user.\n\nArgs:\n    current_user: Current authenticated user\n    limit: Maximum number of documents to return\n    offset: Number of documents to skip for pagination\n    type: Filter by document type\n    status: Filter by document status\n    search: Search query for document titles/content\n    \nReturns:\n    DocumentListResponse: List of user's documents", "operationId": "list_user_documents_api_v1_documents__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "default": 20, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["documents"], "summary": "Create Document", "description": "Create a new document for the authenticated user.\n\nArgs:\n    document_data: Document creation data\n    current_user: Current authenticated user\n    \nReturns:\n    DocumentResponse: Created document data", "operationId": "create_document_api_v1_documents__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/documents/{document_id}": {"get": {"tags": ["documents"], "summary": "Get Document", "description": "Get a specific document by ID.\n\nArgs:\n    document_id: Document ID to retrieve\n    current_user: Current authenticated user\n    include_content: Whether to include document content\n    \nReturns:\n    DocumentResponse: Document data", "operationId": "get_document_api_v1_documents__document_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}, {"name": "include_content", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Include Content"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["documents"], "summary": "Update Document", "description": "Update a document.\n\nArgs:\n    document_id: Document ID to update\n    document_updates: Document update data\n    current_user: Current authenticated user\n    \nReturns:\n    DocumentResponse: Updated document data", "operationId": "update_document_api_v1_documents__document_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["documents"], "summary": "Delete Document", "description": "Delete a document.\n\nArgs:\n    document_id: Document ID to delete\n    current_user: Current authenticated user\n    \nReturns:\n    dict: Success response", "operationId": "delete_document_api_v1_documents__document_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/documents/{document_id}/duplicate": {"post": {"tags": ["documents"], "summary": "Duplicate Document", "description": "Create a duplicate of an existing document.\n\nArgs:\n    document_id: Document ID to duplicate\n    current_user: Current authenticated user\n    new_title: Optional new title for the duplicate\n    \nReturns:\n    DocumentResponse: Duplicated document data", "operationId": "duplicate_document_api_v1_documents__document_id__duplicate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}, {"name": "new_title", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "New Title"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/documents/{document_id}/collaborators": {"get": {"tags": ["documents"], "summary": "Get Document Collaborators", "description": "Get all collaborators for a document.\n\nArgs:\n    document_id: Document ID to get collaborators for\n    current_user: Current authenticated user\n    \nReturns:\n    CollaboratorResponse: List of collaborators with their details", "operationId": "get_document_collaborators_api_v1_documents__document_id__collaborators_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollaboratorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/documents/{document_id}/suggestions": {"get": {"tags": ["documents"], "summary": "Get Document Suggestions", "description": "Get all suggestions for a specific document.\n\nArgs:\n    document_id: UUID of the document\n    status: Filter suggestions by status (pending, accepted, rejected)\n    current_user: Current authenticated user\n    \nReturns:\n    SuggestionsResponse: List of suggestions for the document", "operationId": "get_document_suggestions_api_v1_documents__document_id__suggestions_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Document Id"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by suggestion status", "default": "pending", "title": "Status"}, "description": "Filter by suggestion status"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuggestionsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/documents/{document_id}/comments": {"get": {"tags": ["documents"], "summary": "Get Document Comments", "description": "Get all comments for a specific document.\n\nArgs:\n    document_id: UUID of the document\n    include_resolved: Whether to include resolved comments\n    include_deleted: Whether to include deleted comments\n    current_user: Current authenticated user\n    \nReturns:\n    CommentsResponse: List of comments for the document", "operationId": "get_document_comments_api_v1_documents__document_id__comments_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Document Id"}}, {"name": "include_resolved", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Include resolved comments", "default": true, "title": "Include Resolved"}, "description": "Include resolved comments"}, {"name": "include_deleted", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Include deleted comments", "default": false, "title": "Include Deleted"}, "description": "Include deleted comments"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommentsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/blocks/document/{document_id}": {"get": {"tags": ["blocks"], "summary": "Get Document Blocks", "description": "Get all blocks for a specific document.\n\nArgs:\n    document_id: Document ID to get blocks for\n    current_user: Current authenticated user\n    include_content: Whether to include block content\n    block_type: Filter by block type\n    \nReturns:\n    BlockListResponse: List of document blocks", "operationId": "get_document_blocks_api_v1_blocks_document__document_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}, {"name": "include_content", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Include Content"}}, {"name": "block_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Block Type"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/blocks/document/{document_id}/structure": {"get": {"tags": ["blocks"], "summary": "Get Document Structure", "description": "Get the hierarchical structure of a document with all blocks organized by type and level.\n\nArgs:\n    document_id: UUID of the document\n    current_user: Current authenticated user\n    \nReturns:\n    DocumentStructureResponse: Hierarchical structure of document blocks", "operationId": "get_document_structure_api_v1_blocks_document__document_id__structure_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentStructureResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/blocks/": {"post": {"tags": ["blocks"], "summary": "Create Block", "description": "Create a new text block.\n\nArgs:\n    block_data: Block creation data\n    current_user: Current authenticated user\n    \nReturns:\n    BlockResponse: Created block data", "operationId": "create_block_api_v1_blocks__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/blocks/{block_id}": {"get": {"tags": ["blocks"], "summary": "Get Block", "description": "Get a specific text block by ID.\n\nArgs:\n    block_id: Block ID to retrieve\n    current_user: Current authenticated user\n    include_suggestions: Whether to include AI suggestions\n    \nReturns:\n    BlockResponse: Block data", "operationId": "get_block_api_v1_blocks__block_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "block_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Block Id"}}, {"name": "include_suggestions", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Include Suggestions"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["blocks"], "summary": "Update Block", "description": "Update a text block.\n\nArgs:\n    block_id: Block ID to update\n    block_updates: Block update data\n    current_user: Current authenticated user\n    \nReturns:\n    BlockResponse: Updated block data", "operationId": "update_block_api_v1_blocks__block_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "block_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Block Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["blocks"], "summary": "Delete Block", "description": "Delete a text block.\n\nArgs:\n    block_id: Block ID to delete\n    current_user: Current authenticated user\n    \nReturns:\n    dict: Success response", "operationId": "delete_block_api_v1_blocks__block_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "block_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Block Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/blocks/{block_id}/analyze": {"post": {"tags": ["blocks"], "summary": "Analyze Block", "description": "Run AI analysis on a specific block.\n\nArgs:\n    block_id: Block ID to analyze\n    analysis_request: Analysis configuration\n    current_user: Current authenticated user\n    \nReturns:\n    BlockResponse: Analysis results and suggestions", "operationId": "analyze_block_api_v1_blocks__block_id__analyze_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "block_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Block Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockAnalysisRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/blocks/{block_id}/suggestions": {"get": {"tags": ["blocks"], "summary": "Get Block Suggestions", "description": "Get AI suggestions for a specific block.\n\nArgs:\n    block_id: Block ID to get suggestions for\n    current_user: Current authenticated user\n    agent_type: Filter by agent type\n    status: Filter by suggestion status\n    \nReturns:\n    BlockResponse: Block suggestions", "operationId": "get_block_suggestions_api_v1_blocks__block_id__suggestions_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "block_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Block Id"}}, {"name": "agent_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Agent Type"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlockResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/agents/": {"get": {"tags": ["agents"], "summary": "List User Agents", "description": "List all custom agents for the authenticated user.\n\nArgs:\n    current_user: Current authenticated user\n    limit: Maximum number of agents to return\n    offset: Number of agents to skip for pagination\n    agent_type: Filter by agent type\n    is_active: Filter by active status\n    \nReturns:\n    AgentListResponse: List of user's custom agents", "operationId": "list_user_agents_api_v1_agents__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "default": 50, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "agent_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Agent Type"}}, {"name": "is_active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["agents"], "summary": "Create Custom Agent", "description": "Create a new custom agent for the authenticated user.\n\nArgs:\n    agent_data: Agent creation data\n    current_user: Current authenticated user\n    \nReturns:\n    AgentResponse: Created agent data", "operationId": "create_custom_agent_api_v1_agents__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomAgentCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/agents/{agent_id}": {"get": {"tags": ["agents"], "summary": "Get Agent Details", "description": "Get details for a specific custom agent.\n\nArgs:\n    agent_id: Agent ID to retrieve\n    current_user: Current authenticated user\n    \nReturns:\n    AgentResponse: Agent details", "operationId": "get_agent_details_api_v1_agents__agent_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Agent Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["agents"], "summary": "Update Custom Agent", "description": "Update a custom agent.\n\nArgs:\n    agent_id: Agent ID to update\n    agent_updates: Agent update data\n    current_user: Current authenticated user\n    \nReturns:\n    AgentResponse: Updated agent data", "operationId": "update_custom_agent_api_v1_agents__agent_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Agent Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomAgentUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["agents"], "summary": "Delete Custom Agent", "description": "Delete (deactivate) a custom agent.\n\nArgs:\n    agent_id: Agent ID to delete\n    current_user: Current authenticated user\n    \nReturns:\n    dict: Success response", "operationId": "delete_custom_agent_api_v1_agents__agent_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Agent Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/agents/{agent_id}/execute": {"post": {"tags": ["agents"], "summary": "Execute Agent", "description": "Execute a specific agent on provided text.\n\nArgs:\n    agent_id: Agent ID to execute\n    execution_request: Execution request data\n    current_user: Current authenticated user\n    \nReturns:\n    AgentResponse: Agent execution results", "operationId": "execute_agent_api_v1_agents__agent_id__execute_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Agent Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentExecutionRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/agents/execute-batch": {"post": {"tags": ["agents"], "summary": "Execute Multiple Agents", "description": "Execute multiple agents on the same text.\n\nArgs:\n    execution_request: Bulk execution request data\n    current_user: Current authenticated user\n    \nReturns:\n    AgentResponse: Combined execution results", "operationId": "execute_multiple_agents_api_v1_agents_execute_batch_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkAgentExecutionRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/agents/{agent_id}/analytics": {"get": {"tags": ["agents"], "summary": "Get Agent Analytics", "description": "Get performance analytics for a specific agent.\n\nArgs:\n    agent_id: Agent ID to get analytics for\n    current_user: Current authenticated user\n    days: Number of days to include in analytics\n    \nReturns:\n    AgentResponse: Agent performance analytics", "operationId": "get_agent_analytics_api_v1_agents__agent_id__analytics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "agent_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Agent Id"}}, {"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "default": 30, "title": "Days"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/agents/templates": {"get": {"tags": ["agents"], "summary": "Get Agent Templates", "description": "Get available agent templates.\n\nArgs:\n    current_user: Current authenticated user\n    category: Filter by template category\n    \nReturns:\n    AgentListResponse: Available agent templates", "operationId": "get_agent_templates_api_v1_agents_templates_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/personas/": {"get": {"tags": ["personas"], "summary": "List Personas", "description": "List all personas for the authenticated user.\n\nArgs:\n    current_user: Authenticated user from JWT token\n    limit: Maximum number of personas to return\n    offset: Number of personas to skip for pagination\n    persona_type: Filter by persona type (academic, professional, creative, personal)\n    is_active: Filter by active status\n    include_templates: Include persona templates in results\n\nReturns:\n    PersonaListResponse: List of personas with metadata", "operationId": "list_personas_api_v1_personas__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "default": 50, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "persona_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Persona Type"}}, {"name": "is_active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Is Active"}}, {"name": "include_templates", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Include Templates"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonaListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["personas"], "summary": "Create Persona", "description": "Create a new persona for the authenticated user.\n\nArgs:\n    persona: Persona creation data\n    current_user: Authenticated user from JWT token\n\nReturns:\n    PersonaResponse: Created persona data", "operationId": "create_persona_api_v1_personas__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonaCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonaResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/personas/{persona_id}": {"get": {"tags": ["personas"], "summary": "Get Persona", "description": "Get a specific persona by ID.\n\nArgs:\n    persona_id: UUID of the persona to retrieve\n    current_user: Authenticated user from JWT token\n\nReturns:\n    PersonaResponse: Persona data", "operationId": "get_persona_api_v1_personas__persona_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "persona_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Persona Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonaResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["personas"], "summary": "Update Persona", "description": "Update a persona.\n\nArgs:\n    persona_id: UUID of the persona to update\n    updates: Persona update data\n    current_user: Authenticated user from JWT token\n\nReturns:\n    PersonaResponse: Updated persona data", "operationId": "update_persona_api_v1_personas__persona_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "persona_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Persona Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonaUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonaResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["personas"], "summary": "Delete Persona", "description": "Delete (soft delete) a persona.\n\nArgs:\n    persona_id: UUID of the persona to delete\n    current_user: Authenticated user from JWT token\n\nReturns:\n    PersonaResponse: Confirmation of deletion", "operationId": "delete_persona_api_v1_personas__persona_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "persona_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Persona Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonaResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/personas/{persona_id}/feedback": {"post": {"tags": ["personas"], "summary": "Request <PERSON><PERSON>", "description": "Request feedback from a specific persona.\n\nArgs:\n    persona_id: UUID of the persona to get feedback from\n    request: Feedback request data\n    background_tasks: Background task handler\n    current_user: Authenticated user from JWT token\n\nReturns:\n    FeedbackResponse: Persona feedback data", "operationId": "request_persona_feedback_api_v1_personas__persona_id__feedback_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "persona_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Persona Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonaFeedbackRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeedbackResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/personas/multi-feedback": {"post": {"tags": ["personas"], "summary": "Request Multi Persona Feedback", "description": "Request feedback from multiple personas simultaneously.\n\nArgs:\n    request: Multi-feedback request data\n    background_tasks: Background task handler\n    current_user: Authenticated user from JWT token\n\nReturns:\n    MultiFeedbackResponse: Aggregated multi-persona feedback", "operationId": "request_multi_persona_feedback_api_v1_personas_multi_feedback_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MultiFeedbackRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MultiFeedbackResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/personas/templates": {"get": {"tags": ["personas"], "summary": "<PERSON> <PERSON> Templates", "description": "Get available persona templates.\n\nArgs:\n    current_user: Authenticated user from JWT token\n    persona_type: Filter by persona type\n\nReturns:\n    PersonaListResponse: List of persona templates", "operationId": "get_persona_templates_api_v1_personas_templates_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "persona_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Persona Type"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonaListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/personas/templates/{template_id}/instantiate": {"post": {"tags": ["personas"], "summary": "Create <PERSON>a From Template", "description": "Create a new persona from a template.\n\nArgs:\n    template_id: UUID of the template to use\n    customization: Template customization data\n    current_user: Authenticated user from JWT token\n\nReturns:\n    PersonaResponse: Created persona data", "operationId": "create_persona_from_template_api_v1_personas_templates__template_id__instantiate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Template Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonaTemplateCustomization"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonaResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/health/analyze": {"post": {"tags": ["health"], "summary": "Analyze Text Health", "description": "Analyze text health metrics and provide improvement suggestions.\n\nArgs:\n    analysis_request: Text analysis request data\n    current_user: Current authenticated user\n    \nReturns:\n    HealthResponse: Health analysis results", "operationId": "analyze_text_health_api_v1_health_analyze_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthAnalysisRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/health/batch-analyze": {"post": {"tags": ["health"], "summary": "Batch Analyze Health", "description": "Perform batch health analysis on multiple texts.\n\nArgs:\n    batch_request: Batch analysis request data\n    current_user: Current authenticated user\n    \nReturns:\n    HealthResponse: Batch analysis results", "operationId": "batch_analyze_health_api_v1_health_batch_analyze_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchHealthAnalysisRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/health/trends": {"get": {"tags": ["health"], "summary": "Get Health Trends", "description": "Get health trends and analytics for the user.\n\nArgs:\n    current_user: Current authenticated user\n    days: Number of days to include in trends\n    document_id: Filter by specific document\n    metric_type: Filter by specific metric type\n    \nReturns:\n    HealthResponse: Health trends data", "operationId": "get_health_trends_api_v1_health_trends_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "default": 30, "title": "Days"}}, {"name": "document_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Document Id"}}, {"name": "metric_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Metric Type"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/health/issues/{block_id}": {"get": {"tags": ["health"], "summary": "Get Block Health Issues", "description": "Get health issues for a specific block.\n\nArgs:\n    block_id: Block ID to get issues for\n    current_user: Current authenticated user\n    severity: Filter by issue severity\n    status: Filter by issue status\n    \nReturns:\n    HealthListResponse: List of health issues", "operationId": "get_block_health_issues_api_v1_health_issues__block_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "block_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Block Id"}}, {"name": "severity", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Severity"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/health/resolve-issue": {"post": {"tags": ["health"], "summary": "Resolve Health Issue", "description": "Mark a health issue as resolved.\n\nArgs:\n    resolution: Issue resolution data\n    current_user: Current authenticated user\n    \nReturns:\n    HealthResponse: Resolution confirmation", "operationId": "resolve_health_issue_api_v1_health_resolve_issue_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthIssueResolution"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/health/preferences": {"get": {"tags": ["health"], "summary": "Get Health Preferences", "description": "Get user's health analysis preferences.\n\nArgs:\n    current_user: Current authenticated user\n    \nReturns:\n    HealthResponse: User health preferences", "operationId": "get_health_preferences_api_v1_health_preferences_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}}, "security": [{"HTTPBearer": []}]}, "put": {"tags": ["health"], "summary": "Update Health Preferences", "description": "Update user's health analysis preferences.\n\nArgs:\n    preferences: Updated preference data\n    current_user: Current authenticated user\n    \nReturns:\n    HealthResponse: Updated preferences", "operationId": "update_health_preferences_api_v1_health_preferences_put", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthPreferencesUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/health/debounced-stats": {"get": {"tags": ["health"], "summary": "Get Debounced Health Stats", "description": "Get statistics for the debounced health analysis service.\n\nArgs:\n    current_user: Current authenticated user\n    \nReturns:\n    HealthResponse: Debounced service statistics", "operationId": "get_debounced_health_stats_api_v1_health_debounced_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/citations/": {"post": {"tags": ["citations"], "summary": "Create Citation", "description": "Create a new citation with validation and metadata enrichment.\n\nArgs:\n    citation: Citation data to create\n    background_tasks: Background tasks for async operations\n    current_user: Current authenticated user\n    db: Database connection\n    \nReturns:\n    CitationResponse: Created citation data", "operationId": "create_citation_api_v1_citations__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CitationCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CitationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["citations"], "summary": "List Citations", "description": "List citations with filtering and pagination.\n\nArgs:\n    document_id: Filter by document ID\n    citation_type: Filter by citation type\n    verified_only: Only return verified citations\n    limit: Maximum results to return\n    offset: Results to skip for pagination\n    current_user: Current authenticated user\n    db: Database connection\n    \nReturns:\n    CitationListResponse: List of citations", "operationId": "list_citations_api_v1_citations__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by document ID", "title": "Document Id"}, "description": "Filter by document ID"}, {"name": "citation_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/CitationType"}, {"type": "null"}], "description": "Filter by citation type", "title": "Citation Type"}, "description": "Filter by citation type"}, {"name": "verified_only", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Only return verified citations", "default": false, "title": "Verified Only"}, "description": "Only return verified citations"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Maximum number of citations to return", "default": 50, "title": "Limit"}, "description": "Maximum number of citations to return"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of citations to skip", "default": 0, "title": "Offset"}, "description": "Number of citations to skip"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CitationListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/citations/{citation_id}": {"get": {"tags": ["citations"], "summary": "Get Citation", "description": "Get a specific citation by ID.\n\nArgs:\n    citation_id: Citation ID to retrieve\n    current_user: Current authenticated user\n    db: Database connection\n    \nReturns:\n    CitationResponse: Citation data", "operationId": "get_citation_api_v1_citations__citation_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "citation_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Citation Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CitationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["citations"], "summary": "Update Citation", "description": "Update an existing citation.\n\nArgs:\n    citation_id: Citation ID to update\n    updates: Citation updates to apply\n    background_tasks: Background tasks for async operations\n    current_user: Current authenticated user\n    db: Database connection\n    \nReturns:\n    CitationResponse: Updated citation data", "operationId": "update_citation_api_v1_citations__citation_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "citation_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Citation Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CitationUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CitationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["citations"], "summary": "Delete Citation", "description": "Delete a citation.\n\nArgs:\n    citation_id: Citation ID to delete\n    current_user: Current authenticated user\n    db: Database connection\n    \nReturns:\n    Dict: Deletion confirmation", "operationId": "delete_citation_api_v1_citations__citation_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "citation_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Citation Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Delete Citation Api V1 Citations  Citation Id  Delete"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/citations/export/bibliography": {"post": {"tags": ["citations"], "summary": "Export Bibliography", "description": "Export a formatted bibliography for a document.\n\nArgs:\n    export_request: Bibliography export parameters\n    background_tasks: Background tasks for async operations\n    current_user: Current authenticated user\n    db: Database connection\n    \nReturns:\n    BibliographyExportResponse: Generated bibliography", "operationId": "export_bibliography_api_v1_citations_export_bibliography_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BibliographyExportRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BibliographyExportResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/citations/verify": {"post": {"tags": ["citations"], "summary": "Verify Citations", "description": "Verify citations against external databases.\n\nArgs:\n    verification_request: Citations to verify\n    background_tasks: Background tasks for async operations\n    current_user: Current authenticated user\n    db: Database connection\n    \nReturns:\n    Dict: Verification results", "operationId": "verify_citations_api_v1_citations_verify_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CitationVerificationRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Verify Citations Api V1 Citations Verify Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/citations/styles": {"get": {"tags": ["citations"], "summary": "Get Citation <PERSON>", "description": "Get list of supported citation styles.\n\nReturns:\n    Dict: Supported citation styles with descriptions", "operationId": "get_citation_styles_api_v1_citations_styles_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Get Citation Styles Api V1 Citations Styles Get"}}}}}}}, "/api/v1/citations/validate": {"post": {"tags": ["citations"], "summary": "Validate Citation", "description": "Validate citation data for CSL processing.\n\nArgs:\n    citation: Citation data to validate\n    current_user: Current authenticated user\n    \nReturns:\n    Dict: Validation results", "operationId": "validate_citation_api_v1_citations_validate_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CitationCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Validate Citation Api V1 Citations Validate Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/analytics/sessions": {"get": {"tags": ["analytics", "analytics"], "summary": "Get Writing Sessions", "description": "Get user's writing sessions with optional filters.", "operationId": "get_writing_sessions_api_v1_analytics_sessions_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "document_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Document Id"}}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Date"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalyticsListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["analytics", "analytics"], "summary": "Start Writing Session", "description": "Start a new writing session.", "operationId": "start_writing_session_api_v1_analytics_sessions_post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WritingSessionCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalyticsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/sessions/{session_id}": {"put": {"tags": ["analytics", "analytics"], "summary": "Update Writing Session", "description": "Update a writing session.", "operationId": "update_writing_session_api_v1_analytics_sessions__session_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Session Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WritingSessionUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalyticsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/goals": {"get": {"tags": ["analytics", "analytics"], "summary": "Get Writing Goals", "description": "Get user's writing goals.", "operationId": "get_writing_goals_api_v1_analytics_goals_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "pattern": "^(active|completed|paused)$"}, {"type": "null"}], "title": "Status"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalyticsListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["analytics", "analytics"], "summary": "Create Writing Goal", "description": "Create a new writing goal.", "operationId": "create_writing_goal_api_v1_analytics_goals_post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WritingGoalCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalyticsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/goals/{goal_id}": {"put": {"tags": ["analytics", "analytics"], "summary": "Update Writing Goal", "description": "Update a writing goal.", "operationId": "update_writing_goal_api_v1_analytics_goals__goal_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "goal_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Goal Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WritingGoalUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalyticsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/achievements": {"get": {"tags": ["analytics", "analytics"], "summary": "Get User Achievements", "description": "Get user's unlocked achievements.", "operationId": "get_user_achievements_api_v1_analytics_achievements_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalyticsListResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/analytics/events": {"post": {"tags": ["analytics", "analytics"], "summary": "Track Usage Event", "description": "Track a usage event.", "operationId": "track_usage_event_api_v1_analytics_events_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "event_type", "in": "query", "required": true, "schema": {"type": "string", "title": "Event Type"}}, {"name": "document_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Document Id"}}, {"name": "tokens_used", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tokens Used"}}, {"name": "model_used", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model Used"}}, {"name": "processing_time_ms", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Processing Time Ms"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Event Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/analytics/dashboard": {"get": {"tags": ["analytics", "analytics"], "summary": "Get Dashboard Summary", "description": "Get analytics dashboard summary.", "operationId": "get_dashboard_summary_api_v1_analytics_dashboard_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalyticsResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/analytics/token-usage": {"get": {"tags": ["analytics", "analytics"], "summary": "Get User Token Usage", "description": "Get current user's token usage statistics.", "operationId": "get_user_token_usage_api_v1_analytics_token_usage_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "default": 30, "title": "Days"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalyticsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/monitoring/health": {"get": {"tags": ["monitoring"], "summary": "Health Check", "description": "Comprehensive health check endpoint for load balancers and monitoring.\n\nReturns:\n    Dict: System health status with service checks", "operationId": "health_check_api_v1_monitoring_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/monitoring/health/ready": {"get": {"tags": ["monitoring"], "summary": "Readiness Check", "description": "Readiness check for Kubernetes and container orchestration.\n\nReturns:\n    Dict: Service readiness status", "operationId": "readiness_check_api_v1_monitoring_health_ready_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/monitoring/health/live": {"get": {"tags": ["monitoring"], "summary": "Liveness Check", "description": "Liveness check for Kubernetes and container orchestration.\n\nReturns:\n    Dict: Basic application liveness status", "operationId": "liveness_check_api_v1_monitoring_health_live_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/monitoring/metrics": {"get": {"tags": ["monitoring"], "summary": "Get Metrics", "description": "Get system performance metrics for monitoring tools.\n\nArgs:\n    minutes: Time window for metrics aggregation\n    \nReturns:\n    Dict: Comprehensive system metrics", "operationId": "get_metrics_api_v1_monitoring_metrics_get", "parameters": [{"name": "minutes", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 60, "minimum": 1, "description": "Time window in minutes", "default": 15, "title": "Minutes"}, "description": "Time window in minutes"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/monitoring/metrics/errors": {"get": {"tags": ["monitoring"], "summary": "Get Error Metrics", "description": "Get error metrics and summaries for monitoring.\n\nReturns:\n    Dict: Error statistics and summaries", "operationId": "get_error_metrics_api_v1_monitoring_metrics_errors_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/monitoring/metrics/performance": {"get": {"tags": ["monitoring"], "summary": "Get Performance Metrics", "description": "Get detailed performance metrics.\n\nArgs:\n    minutes: Time window for metrics\n    endpoint: Optional endpoint filter\n    \nReturns:\n    Dict: Detailed performance metrics", "operationId": "get_performance_metrics_api_v1_monitoring_metrics_performance_get", "parameters": [{"name": "minutes", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 60, "minimum": 1, "default": 15, "title": "Minutes"}}, {"name": "endpoint", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by specific endpoint", "title": "Endpoint"}, "description": "Filter by specific endpoint"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/monitoring/status": {"get": {"tags": ["monitoring"], "summary": "Get System Status", "description": "Get comprehensive system status for dashboard.\n\nReturns:\n    Dict: Complete system status information", "operationId": "get_system_status_api_v1_monitoring_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/monitoring/events": {"post": {"tags": ["monitoring"], "summary": "Log Custom Event", "description": "Log a custom business event for monitoring.\n\nArgs:\n    event_data: Event data to log\n    current_user: Current authenticated user\n    \nReturns:\n    Dict: Event logging confirmation", "operationId": "log_custom_event_api_v1_monitoring_events_post", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Event Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/monitoring/metrics/prometheus": {"get": {"tags": ["monitoring"], "summary": "Get Prometheus Metrics", "description": "Get metrics in Prometheus format for monitoring integration.\n\nReturns:\n    str: Metrics in Prometheus format", "operationId": "get_prometheus_metrics_api_v1_monitoring_metrics_prometheus_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/developer/docs/{filename}": {"get": {"tags": ["developer", "Developer Portal"], "summary": "Get Documentation", "description": "Serve documentation files from the docs directory.\n\nSecurity: Only serves predefined documentation files to prevent path traversal.", "operationId": "get_documentation_api_v1_developer_docs__filename__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "filename", "in": "path", "required": true, "schema": {"type": "string", "title": "Filename"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/developer/docs": {"get": {"tags": ["developer", "Developer Portal"], "summary": "List Documentation", "description": "List available documentation files with metadata.", "operationId": "list_documentation_api_v1_developer_docs_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/developer/openapi-spec": {"get": {"tags": ["developer", "Developer Portal"], "summary": "Get Openapi Spec", "description": "Generate and return the OpenAPI specification for the API.", "operationId": "get_openapi_spec_api_v1_developer_openapi_spec_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/developer/api-stats": {"get": {"tags": ["developer", "Developer Portal"], "summary": "Get Api Stats", "description": "Get API statistics and metadata for the developer portal.", "operationId": "get_api_stats_api_v1_developer_api_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/developer/code-examples/{language}": {"get": {"tags": ["developer", "Developer Portal"], "summary": "Get Code Examples", "description": "Generate code examples for API endpoints in different languages.", "operationId": "get_code_examples_api_v1_developer_code_examples__language__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "language", "in": "path", "required": true, "schema": {"type": "string", "title": "Language"}}, {"name": "endpoint", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Endpoint"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/health": {"get": {"summary": "Health Check", "description": "Comprehensive health check endpoint.", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/test": {"get": {"summary": "Simple Test", "description": "Simple test endpoint without complex dependencies.", "operationId": "simple_test_test_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"AgentExecutionRequest": {"properties": {"text": {"type": "string", "minLength": 1, "title": "Text"}, "context": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Context"}, "options": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Options"}}, "type": "object", "required": ["text"], "title": "AgentExecutionRequest", "description": "Request model for executing an agent."}, "AgentListResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["success", "data", "meta"], "title": "AgentListResponse", "description": "Response model for agent list."}, "AgentResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"additionalProperties": true, "type": "object", "title": "Data"}}, "type": "object", "required": ["success", "data"], "title": "AgentResponse", "description": "Response model for agent data."}, "AnalyticsListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "data": {"items": {}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "AnalyticsListResponse", "description": "Standard response wrapper for lists of analytics items."}, "AnalyticsResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "data": {"title": "Data"}}, "type": "object", "required": ["data"], "title": "AnalyticsResponse", "description": "Standard response wrapper for single analytics items."}, "BatchHealthAnalysisRequest": {"properties": {"texts": {"items": {"type": "string"}, "type": "array", "maxItems": 50, "minItems": 1, "title": "Texts"}, "block_ids": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Block Ids"}, "document_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Document Id"}, "analysis_type": {"type": "string", "title": "Analysis Type", "default": "comprehensive"}}, "type": "object", "required": ["texts"], "title": "BatchHealthAnalysisRequest", "description": "Request model for batch health analysis."}, "BibliographyExportRequest": {"properties": {"document_id": {"type": "string", "title": "Document Id", "description": "Document ID to export bibliography for"}, "style": {"$ref": "#/components/schemas/CitationStyle", "description": "Citation style to use", "default": "apa"}, "format": {"type": "string", "enum": ["text", "html", "rtf", "json"], "title": "Format", "description": "Output format", "default": "text"}, "include_urls": {"type": "boolean", "title": "Include Urls", "description": "Include URLs in citations", "default": true}, "include_doi": {"type": "boolean", "title": "Include Doi", "description": "Include DOI in citations", "default": true}, "sort_by": {"type": "string", "enum": ["author", "year", "title", "citation_key"], "title": "Sort By", "description": "Sort order", "default": "author"}}, "type": "object", "required": ["document_id"], "title": "BibliographyExportRequest", "description": "Request model for bibliography export."}, "BibliographyExportResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"additionalProperties": true, "type": "object", "title": "Data"}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}}, "type": "object", "required": ["success", "data"], "title": "BibliographyExportResponse", "description": "Response model for bibliography export."}, "BlockAnalysisRequest": {"properties": {"agent_types": {"items": {"type": "string"}, "type": "array", "title": "Agent Types", "description": "Agent types to run", "default": ["grammar", "style"]}, "context": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Context"}, "options": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Options"}}, "type": "object", "title": "BlockAnalysisRequest", "description": "Request model for block AI analysis."}, "BlockCreate": {"properties": {"document_id": {"type": "string", "title": "Document Id"}, "content": {"type": "string", "title": "Content", "description": "Block text content"}, "type": {"type": "string", "title": "Type", "description": "Block type: paragraph, heading, list, etc.", "default": "paragraph"}, "position": {"type": "integer", "minimum": 0.0, "title": "Position", "description": "Block position in document"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["document_id", "content", "position"], "title": "BlockCreate", "description": "Request model for creating a text block."}, "BlockListResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["success", "data", "meta"], "title": "BlockListResponse", "description": "Response model for block list."}, "BlockResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"additionalProperties": true, "type": "object", "title": "Data"}}, "type": "object", "required": ["success", "data"], "title": "BlockResponse", "description": "Response model for block data."}, "BlockUpdate": {"properties": {"content": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content"}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}, "position": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Position"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "title": "BlockUpdate", "description": "Request model for updating a text block."}, "BulkAgentExecutionRequest": {"properties": {"text": {"type": "string", "minLength": 1, "title": "Text"}, "agent_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "minItems": 1, "title": "Agent Ids"}, "context": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Context"}, "options": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Options"}}, "type": "object", "required": ["text", "agent_ids"], "title": "BulkAgentExecutionRequest", "description": "Request model for executing multiple agents."}, "Citation": {"properties": {"id": {"type": "string", "title": "Id"}, "document_id": {"type": "string", "title": "Document Id"}, "block_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Block Id"}, "citation_key": {"type": "string", "title": "Citation Key"}, "citation_type": {"$ref": "#/components/schemas/CitationType"}, "title": {"type": "string", "title": "Title"}, "authors": {"items": {"type": "string"}, "type": "array", "title": "Authors"}, "publication_year": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Publication Year"}, "publisher": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Publisher"}, "doi": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Url"}, "page_numbers": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Page Numbers"}, "volume": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Volume"}, "issue": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Issue"}, "journal_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Journal Name"}, "isbn": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Isbn"}, "raw_citation": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Raw Citation"}, "formatted_citation": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Formatted Citation"}, "citation_style": {"$ref": "#/components/schemas/CitationStyle"}, "position": {"anyOf": [{"additionalProperties": {"type": "integer"}, "type": "object"}, {"type": "null"}], "title": "Position"}, "is_verified": {"type": "boolean", "title": "Is Verified"}, "verification_status": {"$ref": "#/components/schemas/VerificationStatus"}, "verification_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Verification Date"}, "access_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Access Date"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "document_id", "block_id", "citation_key", "citation_type", "title", "authors", "publication_year", "publisher", "doi", "url", "page_numbers", "volume", "issue", "journal_name", "isbn", "raw_citation", "formatted_citation", "citation_style", "position", "is_verified", "verification_status", "verification_date", "access_date", "created_at", "updated_at"], "title": "Citation", "description": "Response model for citation data."}, "CitationCreate": {"properties": {"document_id": {"type": "string", "title": "Document Id", "description": "Document ID where citation is used"}, "block_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Block Id", "description": "Specific block ID if applicable"}, "citation_key": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Citation Key", "description": "Unique citation key"}, "citation_type": {"$ref": "#/components/schemas/CitationType", "description": "Type of citation"}, "title": {"type": "string", "minLength": 1, "title": "Title", "description": "Title of the cited work"}, "authors": {"items": {"type": "string"}, "type": "array", "minItems": 1, "title": "Authors", "description": "List of authors"}, "publication_year": {"anyOf": [{"type": "integer", "maximum": 2030.0, "minimum": 1000.0}, {"type": "null"}], "title": "Publication Year", "description": "Publication year"}, "publisher": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Publisher", "description": "Publisher name"}, "doi": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "DOI identifier"}, "url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Url", "description": "Web URL"}, "page_numbers": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Page Numbers", "description": "Page numbers"}, "volume": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Volume", "description": "Volume number"}, "issue": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Issue", "description": "Issue number"}, "journal_name": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Journal Name", "description": "Journal name"}, "isbn": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Isbn", "description": "ISBN"}, "raw_citation": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Raw Citation", "description": "Raw citation text"}, "position": {"anyOf": [{"additionalProperties": {"type": "integer"}, "type": "object"}, {"type": "null"}], "title": "Position", "description": "Position in document"}, "access_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Access Date", "description": "Date of access for web sources"}}, "type": "object", "required": ["document_id", "citation_key", "citation_type", "title", "authors"], "title": "CitationCreate", "description": "Request model for creating a new citation."}, "CitationListResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"items": {"$ref": "#/components/schemas/Citation"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["success", "data", "meta"], "title": "CitationListResponse", "description": "Response model for citation lists."}, "CitationResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"$ref": "#/components/schemas/Citation"}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}}, "type": "object", "required": ["success", "data"], "title": "CitationResponse", "description": "Standard response model for citation operations."}, "CitationStyle": {"type": "string", "enum": ["apa", "mla", "chicago-author-date", "chicago-fullnote-bibliography", "harvard-cite-them-right", "vancouver", "ieee", "nature", "science", "cell", "american-medical-association", "turabian-fullnote-bibliography", "oscola", "the-bluebook", "american-sociological-association"], "title": "CitationStyle", "description": "Citation styles supported (starting with 15 most common)."}, "CitationType": {"type": "string", "enum": ["book", "journal_article", "conference_paper", "thesis", "website", "report", "patent", "software", "dataset", "preprint"], "title": "CitationType", "description": "Types of citations supported."}, "CitationUpdate": {"properties": {"citation_key": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Citation Key"}, "citation_type": {"anyOf": [{"$ref": "#/components/schemas/CitationType"}, {"type": "null"}]}, "title": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Title"}, "authors": {"anyOf": [{"items": {"type": "string"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Authors"}, "publication_year": {"anyOf": [{"type": "integer", "maximum": 2030.0, "minimum": 1000.0}, {"type": "null"}], "title": "Publication Year"}, "publisher": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Publisher"}, "doi": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Url"}, "page_numbers": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Page Numbers"}, "volume": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Volume"}, "issue": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Issue"}, "journal_name": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Journal Name"}, "isbn": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Isbn"}, "raw_citation": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Raw Citation"}, "position": {"anyOf": [{"additionalProperties": {"type": "integer"}, "type": "object"}, {"type": "null"}], "title": "Position"}, "access_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Access Date"}}, "type": "object", "title": "CitationUpdate", "description": "Request model for updating an existing citation."}, "CitationVerificationRequest": {"properties": {"citation_ids": {"items": {"type": "string"}, "type": "array", "maxItems": 50, "minItems": 1, "title": "Citation Ids", "description": "Citation IDs to verify"}, "verify_doi": {"type": "boolean", "title": "<PERSON><PERSON><PERSON>", "description": "Verify DOI resolution", "default": true}, "verify_metadata": {"type": "boolean", "title": "<PERSON><PERSON><PERSON>", "description": "Verify and enrich metadata", "default": true}, "check_duplicates": {"type": "boolean", "title": "Check Duplicates", "description": "Check for duplicate citations", "default": true}}, "type": "object", "required": ["citation_ids"], "title": "CitationVerificationRequest", "description": "Request model for citation verification."}, "CollaboratorResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Data"}}, "type": "object", "required": ["success", "data"], "title": "CollaboratorResponse", "description": "Response model for document collaborators."}, "Comment": {"properties": {"id": {"type": "string", "title": "Id"}, "block_id": {"type": "string", "title": "Block Id"}, "author_id": {"type": "string", "title": "Author Id"}, "author_name": {"type": "string", "title": "Author Name"}, "author_email": {"type": "string", "title": "Author <PERSON><PERSON>"}, "parent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Id"}, "content": {"type": "string", "title": "Content"}, "position": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Position"}, "type": {"type": "string", "title": "Type", "default": "general"}, "status": {"type": "string", "title": "Status", "default": "open"}, "resolved_by": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Resolved By"}, "resolved_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Resolved At"}, "created_at": {"type": "string", "title": "Created At"}, "updated_at": {"type": "string", "title": "Updated At"}, "deleted_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deleted At"}}, "type": "object", "required": ["id", "block_id", "author_id", "author_name", "author_email", "content", "created_at", "updated_at"], "title": "Comment", "description": "Response model for a single comment."}, "CommentsResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"items": {"$ref": "#/components/schemas/Comment"}, "type": "array", "title": "Data"}}, "type": "object", "required": ["success", "data"], "title": "CommentsResponse", "description": "Response model for document comments."}, "CustomAgentCreate": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "type": {"type": "string", "title": "Type", "description": "Agent type: grammar, style, content, etc."}, "capabilities": {"items": {"type": "string"}, "type": "array", "title": "Capabilities"}, "style_rules": {"additionalProperties": true, "type": "object", "title": "Style Rules"}, "priority": {"type": "integer", "maximum": 100.0, "minimum": 0.0, "title": "Priority", "default": 50}, "llm_config": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON>fig"}}, "type": "object", "required": ["name", "type"], "title": "CustomAgentCreate", "description": "Request model for creating a custom agent."}, "CustomAgentUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "capabilities": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Capabilities"}, "style_rules": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Style Rules"}, "priority": {"anyOf": [{"type": "integer", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Priority"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}, "llm_config": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON>fig"}}, "type": "object", "title": "CustomAgentUpdate", "description": "Request model for updating a custom agent."}, "DocumentCreate": {"properties": {"title": {"type": "string", "maxLength": 500, "minLength": 1, "title": "Title"}, "type": {"type": "string", "title": "Type", "description": "Document type: creative, academic, professional, etc."}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content", "description": "Initial document content", "default": ""}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags"}, "is_public": {"type": "boolean", "title": "Is Public", "default": false}, "language": {"type": "string", "title": "Language", "default": "en"}}, "type": "object", "required": ["title", "type"], "title": "DocumentCreate", "description": "Request model for creating a document."}, "DocumentListResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["success", "data", "meta"], "title": "DocumentListResponse", "description": "Response model for document list."}, "DocumentResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"additionalProperties": true, "type": "object", "title": "Data"}}, "type": "object", "required": ["success", "data"], "title": "DocumentResponse", "description": "Response model for document data."}, "DocumentStructure": {"properties": {"id": {"type": "string", "title": "Id"}, "type": {"type": "string", "title": "Type"}, "content": {"type": "string", "title": "Content"}, "position": {"type": "integer", "title": "Position"}, "depth": {"type": "integer", "title": "De<PERSON><PERSON>"}, "parent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Id"}, "children": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Children", "default": []}, "metadata": {"additionalProperties": true, "type": "object", "title": "<PERSON><PERSON><PERSON>", "default": {}}}, "type": "object", "required": ["id", "type", "content", "position", "depth"], "title": "DocumentStructure", "description": "Response model for document structure."}, "DocumentStructureResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"items": {"$ref": "#/components/schemas/DocumentStructure"}, "type": "array", "title": "Data"}}, "type": "object", "required": ["success", "data"], "title": "DocumentStructureResponse", "description": "Response model for document structure."}, "DocumentUpdate": {"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 500, "minLength": 1}, {"type": "null"}], "title": "Title"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags"}, "is_public": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Public"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}}, "type": "object", "title": "DocumentUpdate", "description": "Request model for updating a document."}, "FeedbackResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"additionalProperties": true, "type": "object", "title": "Data"}}, "type": "object", "required": ["success", "data"], "title": "FeedbackResponse", "description": "Response model for persona feedback."}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HealthAnalysisRequest": {"properties": {"text": {"type": "string", "minLength": 1, "title": "Text", "description": "Text to analyze"}, "block_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Block Id", "description": "Associated block ID"}, "document_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Document Id", "description": "Associated document ID"}, "analysis_type": {"type": "string", "title": "Analysis Type", "description": "Type of analysis to perform", "default": "comprehensive"}, "preferences": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Preferences"}}, "type": "object", "required": ["text"], "title": "HealthAnalysisRequest", "description": "Request model for text health analysis."}, "HealthIssueResolution": {"properties": {"issue_id": {"type": "string", "title": "Issue Id"}, "resolution_type": {"type": "string", "title": "Resolution Type", "description": "How the issue was resolved"}, "user_feedback": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "User <PERSON>"}}, "type": "object", "required": ["issue_id", "resolution_type"], "title": "HealthIssueResolution", "description": "Request model for resolving a health issue."}, "HealthListResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["success", "data", "meta"], "title": "HealthListResponse", "description": "Response model for health data lists."}, "HealthPreferencesUpdate": {"properties": {"focus_areas": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Focus Areas"}, "scoring_weights": {"anyOf": [{"additionalProperties": {"type": "number"}, "type": "object"}, {"type": "null"}], "title": "Scoring Weights"}, "notification_threshold": {"anyOf": [{"type": "integer", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Notification Threshold"}, "auto_analysis": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Auto Analysis"}}, "type": "object", "title": "HealthPreferencesUpdate", "description": "Request model for updating health preferences."}, "HealthResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"additionalProperties": true, "type": "object", "title": "Data"}}, "type": "object", "required": ["success", "data"], "title": "HealthResponse", "description": "Response model for health analysis."}, "MultiFeedbackRequest": {"properties": {"block_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Block Id"}, "persona_ids": {"items": {"type": "string"}, "type": "array", "maxItems": 10, "minItems": 1, "title": "Persona Ids"}, "text": {"type": "string", "minLength": 1, "title": "Text"}, "context": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Context"}, "options": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Options"}, "include_cross_analysis": {"type": "boolean", "title": "Include Cross Analysis", "default": true}}, "type": "object", "required": ["persona_ids", "text"], "title": "MultiFeedbackRequest", "description": "Request model for multi-persona feedback."}, "MultiFeedbackResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"additionalProperties": true, "type": "object", "title": "Data"}}, "type": "object", "required": ["success", "data"], "title": "MultiFeedbackResponse", "description": "Response model for multi-persona feedback."}, "PersonaCreate": {"properties": {"feedback_style": {"additionalProperties": true, "type": "object", "title": "Feedback Style"}, "is_template": {"type": "boolean", "title": "Is Template", "default": false}}, "type": "object", "title": "PersonaCreate", "description": "Request model for creating a persona."}, "PersonaFeedbackRequest": {"properties": {"block_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Block Id"}, "text": {"type": "string", "minLength": 1, "title": "Text"}, "context": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Context"}, "analysis_type": {"type": "string", "title": "Analysis Type", "description": "standard, quick, comprehensive", "default": "standard"}}, "type": "object", "required": ["text"], "title": "PersonaFeedbackRequest", "description": "Request model for single persona feedback."}, "PersonaListResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["success", "data", "meta"], "title": "PersonaListResponse", "description": "Response model for persona list."}, "PersonaResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"additionalProperties": true, "type": "object", "title": "Data"}}, "type": "object", "required": ["success", "data"], "title": "PersonaResponse", "description": "Response model for persona data."}, "PersonaTemplateCustomization": {"properties": {"customizations": {"additionalProperties": true, "type": "object", "title": "Customizations"}}, "type": "object", "title": "PersonaTemplateCustomization", "description": "Request model for customizing persona templates."}, "PersonaUpdate": {"properties": {"feedback_style": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Feedback Style"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}, "type": "object", "title": "PersonaUpdate", "description": "Request model for updating a persona."}, "RefreshTokenRequest": {"properties": {"refresh_token": {"type": "string", "title": "Refresh <PERSON>"}}, "type": "object", "required": ["refresh_token"], "title": "RefreshTokenRequest", "description": "Request model for token refresh."}, "Suggestion": {"properties": {"id": {"type": "string", "title": "Id"}, "block_id": {"type": "string", "title": "Block Id"}, "agent_type": {"type": "string", "title": "Agent Type"}, "severity": {"type": "string", "title": "Severity"}, "category": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category"}, "original_text": {"type": "string", "title": "Original Text"}, "suggested_text": {"type": "string", "title": "Suggested Text"}, "explanation": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Explanation"}, "confidence": {"type": "number", "title": "Confidence"}, "position": {"additionalProperties": true, "type": "object", "title": "Position"}, "original_hash": {"type": "string", "title": "Original Hash"}, "status": {"type": "string", "title": "Status", "default": "pending"}, "user_feedback": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User <PERSON>"}, "feedback_note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Feedback Note"}, "tokens_used": {"type": "integer", "title": "Tokens Used", "default": 0}, "model_used": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model Used"}, "processing_time_ms": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Processing Time Ms"}, "created_at": {"type": "string", "title": "Created At"}, "resolved_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Resolved At"}, "resolved_by": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Resolved By"}, "expires_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Expires At"}}, "type": "object", "required": ["id", "block_id", "agent_type", "severity", "original_text", "suggested_text", "confidence", "position", "original_hash", "created_at"], "title": "Suggestion", "description": "Response model for a single suggestion."}, "SuggestionsResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"items": {"$ref": "#/components/schemas/Suggestion"}, "type": "array", "title": "Data"}}, "type": "object", "required": ["success", "data"], "title": "SuggestionsResponse", "description": "Response model for document suggestions."}, "TokenExchangeRequest": {"properties": {"firebase_token": {"type": "string", "title": "Firebase Token"}}, "type": "object", "required": ["firebase_token"], "title": "TokenExchangeRequest", "description": "Request model for Firebase token exchange."}, "TokenExchangeResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"additionalProperties": true, "type": "object", "title": "Data"}}, "type": "object", "required": ["success", "data"], "title": "TokenExchangeResponse", "description": "Response model for token exchange."}, "UserProfileResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"additionalProperties": true, "type": "object", "title": "Data"}}, "type": "object", "required": ["success", "data"], "title": "UserProfileResponse", "description": "Response model for user profile."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "VerificationStatus": {"type": "string", "enum": ["unverified", "verified", "failed", "pending"], "title": "VerificationStatus", "description": "Citation verification status."}, "WritingGoalCreate": {"properties": {"type": {"type": "string", "title": "Type", "description": "daily_words, weekly_words, daily_time, weekly_time"}, "target": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Target"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}}, "type": "object", "required": ["type", "target"], "title": "WritingGoalCreate"}, "WritingGoalUpdate": {"properties": {"target": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Target"}, "current": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Current"}, "status": {"anyOf": [{"type": "string", "pattern": "^(active|completed|paused)$"}, {"type": "null"}], "title": "Status"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}}, "type": "object", "title": "WritingGoalUpdate"}, "WritingSessionCreate": {"properties": {"document_id": {"type": "string", "format": "uuid", "title": "Document Id"}, "target_word_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Target Word Count"}, "session_goal": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Goal"}, "focus_mode": {"type": "boolean", "title": "Focus Mode", "default": false}}, "type": "object", "required": ["document_id"], "title": "WritingSessionCreate"}, "WritingSessionUpdate": {"properties": {"end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "words_written": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Words Written"}, "words_at_start": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Words At Start"}, "ai_suggestions_accepted": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Ai Suggestions Accepted"}, "ai_suggestions_dismissed": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Ai Suggestions Dismissed"}, "ai_generations_used": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Ai Generations Used"}, "focus_mode_used": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Focus Mode Used"}, "features_used": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Features Used"}}, "type": "object", "title": "WritingSessionUpdate"}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Enter your JWT token obtained from the auth endpoint"}}}, "security": [{"bearerAuth": []}], "servers": [{"url": "https://api.revisionary.app/v1", "description": "Production server"}, {"url": "http://localhost:8000/api/v1", "description": "Development server"}]}}, {"endpoint": "/developer/api-stats", "method": "GET", "status_code": 200, "success": true, "response_time": 0.0, "error_message": null, "response_data": {"success": true, "data": {"total_endpoints": 45, "stable_endpoints": 38, "beta_endpoints": 6, "deprecated_endpoints": 1, "categories": {"Authentication": 4, "Documents": 12, "Analytics": 8, "AI Generation": 7, "Personas": 5, "Agents": 4, "Health": 3, "Admin": 2}, "latest_version": "1.0.0", "uptime": "99.9%", "avg_response_time": "125ms"}}}, {"endpoint": "/developer/code-examples/python", "method": "GET", "status_code": 200, "success": true, "response_time": 0.002013683319091797, "error_message": null, "response_data": {"success": true, "data": {"language": "python", "examples": {"get_documents": "\nimport requests\n\n# Get user documents\nresponse = requests.get(\n    'http://localhost:8000/api/v1/documents',\n    headers={\n        'Authorization': f'Bearer {token}',\n        'Content-Type': 'application/json'\n    }\n)\n\ndata = response.json()\nprint(data)\n", "create_document": "\nimport requests\n\n# Create a new document\nresponse = requests.post(\n    'http://localhost:8000/api/v1/documents',\n    headers={\n        'Authorization': f'Bearer {token}',\n        'Content-Type': 'application/json'\n    },\n    json={\n        'title': 'My New Document',\n        'type': 'blog', \n        'content': 'Document content here...'\n    }\n)\n\ndata = response.json()\nprint(data)\n", "token_usage": "\nimport requests\n\n# Get token usage information\nresponse = requests.get(\n    'http://localhost:8000/api/v1/analytics/token-usage',\n    headers={\n        'Authorization': f'Bearer {token}',\n        'Content-Type': 'application/json'\n    }\n)\n\nusage = response.json()\nprint(f\"Used: {usage['data']['total_tokens_used']}/{usage['data']['tokens_limit']}\")\n"}}}}, {"endpoint": "/personas", "method": "GET", "status_code": 200, "success": true, "response_time": 0.2056882381439209, "error_message": null, "response_data": {"success": true, "data": [{"id": "ecdc923e-c3f1-4faf-a747-5760516121fd", "feedback_style": "{\"tone\": \"supportive and clear\", \"focus\": \"team effectiveness and clarity\", \"style\": \"practical guidance\", \"collaboration\": \"emphasized\"}", "is_active": true, "is_template": false, "usage_count": 19, "effectiveness_score": "0.91", "created_at": "2025-06-26T02:04:51.910287+00:00Z", "updated_at": "2025-06-26T02:04:51.910287+00:00Z"}, {"id": "0d954ea4-f420-4ba9-980d-75356109f28d", "feedback_style": "{\"tone\": \"constructive\", \"focus\": \"accuracy and methodology\", \"style\": \"detailed explanations\", \"criticism_approach\": \"evidence-based\"}", "is_active": true, "is_template": false, "usage_count": 15, "effectiveness_score": "0.94", "created_at": "2025-06-26T02:04:51.910287+00:00Z", "updated_at": "2025-06-26T02:04:51.910287+00:00Z"}, {"id": "0cb69d01-e09a-47a3-b4b7-33c0ed21e8a8", "feedback_style": "{\"tone\": \"encouraging and supportive\", \"focus\": \"practical skills and techniques\", \"style\": \"step-by-step guidance\", \"patience\": \"high\"}", "is_active": true, "is_template": false, "usage_count": 12, "effectiveness_score": "0.89", "created_at": "2025-06-26T02:04:51.910287+00:00Z", "updated_at": "2025-06-26T02:04:51.910287+00:00Z"}, {"id": "0a51c941-d0bb-43b4-a121-9d96229c1678", "feedback_style": "{\"tone\": \"encouraging\", \"focus\": \"clarity and impact\", \"style\": \"practical suggestions\", \"engagement\": \"interactive\"}", "is_active": true, "is_template": false, "usage_count": 8, "effectiveness_score": "0.87", "created_at": "2025-06-26T02:04:51.910287+00:00Z", "updated_at": "2025-06-26T02:04:51.910287+00:00Z"}], "meta": {"total": 8, "limit": 50, "offset": 0, "has_more": false}}}, {"endpoint": "/blocks/generate", "method": "POST", "status_code": 405, "success": true, "response_time": 0.0, "error_message": null, "response_data": {"detail": "Method Not Allowed"}}, {"endpoint": "/agents", "method": "GET", "status_code": 200, "success": true, "response_time": 0.2358558177947998, "error_message": null, "response_data": {"success": true, "data": [{"id": "c283a772-f87c-4af6-a001-b86c02651a64", "user_id": "14285842-26d6-4a48-8b7c-1ac76fa5c488", "name": "Medical Terminology Validator", "description": "Specialized agent for medical terminology accuracy and consistency", "type": "technical", "capabilities": ["edit", "add", "consistency_check", "fact_check"], "priority": 70, "is_active": true, "created_at": "2025-06-26T02:04:51.910287Z", "updated_at": "2025-06-26T02:04:51.910287Z"}, {"id": "0cac9a04-d7ab-4e4a-8926-610974ec1541", "user_id": "14285842-26d6-4a48-8b7c-1ac76fa5c488", "name": "Research Structure Optimizer", "description": "Agent focused on improving academic paper structure and logical flow", "type": "structure", "capabilities": ["restructure", "add", "consistency_check", "readability_optimization"], "priority": 60, "is_active": true, "created_at": "2025-06-26T02:04:51.910287Z", "updated_at": "2025-06-26T02:04:51.910287Z"}], "meta": {"total": 2, "limit": 50, "offset": 0, "has_more": false}}}, {"endpoint": "/monitoring/status", "method": "GET", "status_code": 200, "success": true, "response_time": 0.37658095359802246, "error_message": null, "response_data": {"status": "unhealthy", "health": {"status": "unhealthy", "timestamp": "2025-07-01T22:51:02.881730", "version": "1.0.0", "environment": "development", "services": {"database": {"supabase": true, "postgresql": true, "details": {"supabase": "Connected", "postgresql": "Connected"}}, "redis": {"status": "mocked"}, "application": {"status": "unhealthy", "metrics": {"requests_per_minute": 35, "error_rate": 0.14285714285714285, "avg_response_time_ms": 111.5278336736891, "total_errors": 5}, "timestamp": "2025-07-01T22:51:03.258311"}}, "success": false}, "summary": {"requests_last_5_minutes": 35, "avg_response_time_ms": 111.53, "total_errors_last_hour": 0, "error_rate": 0.0}, "services": {"database": {"supabase": true, "postgresql": true, "details": {"supabase": "Connected", "postgresql": "Connected"}}, "redis": {"status": "mocked"}, "application": {"status": "unhealthy", "metrics": {"requests_per_minute": 35, "error_rate": 0.14285714285714285, "avg_response_time_ms": 111.5278336736891, "total_errors": 5}, "timestamp": "2025-07-01T22:51:03.258311"}}, "environment": "development", "timestamp": "2025-07-01T22:51:03.258311"}}]}