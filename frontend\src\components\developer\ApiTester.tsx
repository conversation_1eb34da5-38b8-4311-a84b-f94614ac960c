import React, { useState } from 'react';
import { 
  PlayIcon, 
  ClipboardDocumentIcon,
  CheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { baseApi } from '@/services/api/baseApi';

interface ApiTesterProps {
  method: string;
  path: string;
  onClose: () => void;
}

const ApiTester: React.FC<ApiTesterProps> = ({ method, path, onClose }) => {
  // Set default request body based on endpoint
  const getDefaultRequestBody = () => {
    if (path.includes('/documents') && ['POST', 'PUT'].includes(method)) {
      return JSON.stringify({
        title: "Test Document",
        type: "blog",
        content: "This is a test document created from the API explorer."
      }, null, 2);
    }
    if (path.includes('/blocks/generate') && method === 'POST') {
      return JSON.stringify({
        prompt: "Write a paragraph about artificial intelligence",
        type: "paragraph"
      }, null, 2);
    }
    return '{}';
  };

  const [requestBody, setRequestBody] = useState(getDefaultRequestBody());
  const [queryParams, setQueryParams] = useState('');
  const [response, setResponse] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  const executeRequest = async () => {
    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      let url = path;
      if (queryParams.trim()) {
        const params = new URLSearchParams();
        queryParams.split('&').forEach(param => {
          const [key, value] = param.split('=');
          if (key && value) {
            params.append(key.trim(), value.trim());
          }
        });
        url += `?${params.toString()}`;
      }

      let requestData = undefined;
      if (['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) && requestBody.trim()) {
        try {
          requestData = JSON.parse(requestBody);
        } catch (e) {
          throw new Error('Invalid JSON in request body');
        }
      }

      // Remove /api/v1 prefix as baseApi already has it
      const apiUrl = url.replace('/api/v1', '');
      
      let result;
      switch (method.toUpperCase()) {
        case 'GET':
          result = await baseApi.get(apiUrl);
          break;
        case 'POST':
          result = await baseApi.post(apiUrl, requestData);
          break;
        case 'PUT':
          result = await baseApi.put(apiUrl, requestData);
          break;
        case 'PATCH':
          result = await baseApi.patch(apiUrl, requestData);
          break;
        case 'DELETE':
          result = await baseApi.delete(apiUrl);
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${method}`);
      }
      setResponse({
        status: result.status,
        data: result.data,
        headers: result.headers
      });
    } catch (err: any) {
      console.error('API Test Error:', err);
      const errorMessage = err.response?.data?.detail || 
                         err.response?.data?.message || 
                         err.message || 
                         'Request failed';
      setError(errorMessage);
      
      if (err.response) {
        setResponse({
          status: err.response.status,
          data: err.response.data,
          headers: err.response.headers
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const generateCurlCommand = () => {
    let curl = `curl -X ${method.toUpperCase()} "${window.location.origin}${path}"`;
    
    if (queryParams.trim()) {
      const params = new URLSearchParams();
      queryParams.split('&').forEach(param => {
        const [key, value] = param.split('=');
        if (key && value) {
          params.append(key.trim(), value.trim());
        }
      });
      curl = `curl -X ${method.toUpperCase()} "${window.location.origin}${path}?${params.toString()}"`;
    }
    
    curl += ' -H "Authorization: Bearer $TOKEN"';
    curl += ' -H "Content-Type: application/json"';
    
    if (['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) && requestBody.trim()) {
      curl += ` -d '${requestBody}'`;
    }
    
    return curl;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold">API Tester</h2>
              <div className="flex items-center space-x-3 mt-2">
                <span className={`px-2 py-1 rounded text-xs font-mono font-semibold ${
                  method === 'GET' ? 'bg-blue-100 text-blue-800' :
                  method === 'POST' ? 'bg-green-100 text-green-800' :
                  method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                } bg-white`}>
                  {method}
                </span>
                <code className="text-white text-sm font-mono">{path}</code>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 text-2xl font-bold"
            >
              ×
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6 h-[calc(90vh-120px)] overflow-auto">
          {/* Request Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-slate-900">Request</h3>
            
            {/* Query Parameters - Only show for GET requests */}
            {method.toUpperCase() === 'GET' && (
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Query Parameters (key=value&key2=value2)
                </label>
                <input
                  type="text"
                  value={queryParams}
                  onChange={(e) => setQueryParams(e.target.value)}
                  placeholder="limit=10&offset=0"
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
            )}

            {/* Request Body */}
            {['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) && (
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Request Body (JSON)
                </label>
                <textarea
                  value={requestBody}
                  onChange={(e) => setRequestBody(e.target.value)}
                  placeholder='{"title": "Test Document", "type": "blog"}'
                  className="w-full h-32 px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent font-mono text-sm"
                />
              </div>
            )}

            {/* Execute Button */}
            <button
              onClick={executeRequest}
              disabled={loading}
              className="w-full flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              ) : (
                <>
                  <PlayIcon className="w-5 h-5 mr-2" />
                  Execute Request
                </>
              )}
            </button>

            {/* cURL Command */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium text-slate-700">cURL Command</label>
                <button
                  onClick={() => copyToClipboard(generateCurlCommand())}
                  className="flex items-center text-sm text-purple-600 hover:text-purple-700"
                >
                  {copied ? (
                    <CheckIcon className="w-4 h-4 mr-1" />
                  ) : (
                    <ClipboardDocumentIcon className="w-4 h-4 mr-1" />
                  )}
                  {copied ? 'Copied!' : 'Copy'}
                </button>
              </div>
              <pre className="text-xs bg-slate-100 p-3 rounded-lg overflow-x-auto">
                {generateCurlCommand()}
              </pre>
            </div>
          </div>

          {/* Response */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-slate-900">Response</h3>
            
            {error && (
              <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
                <ExclamationTriangleIcon className="w-5 h-5 text-red-500 mr-2" />
                <span className="text-red-700 text-sm">{error}</span>
              </div>
            )}

            {response && (
              <div className="space-y-3">
                {/* Status */}
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-slate-700">Status:</span>
                  <span className={`px-2 py-1 rounded text-sm font-mono ${
                    response.status >= 200 && response.status < 300 
                      ? 'bg-green-100 text-green-800'
                      : response.status >= 400
                      ? 'bg-red-100 text-red-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {response.status}
                  </span>
                </div>

                {/* Response Body */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="text-sm font-medium text-slate-700">Response Body</label>
                    <button
                      onClick={() => copyToClipboard(JSON.stringify(response.data, null, 2))}
                      className="flex items-center text-sm text-purple-600 hover:text-purple-700"
                    >
                      <ClipboardDocumentIcon className="w-4 h-4 mr-1" />
                      Copy
                    </button>
                  </div>
                  <pre className="bg-slate-100 p-3 rounded-lg overflow-auto text-xs font-mono max-h-64">
                    {JSON.stringify(response.data, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {!response && !error && !loading && (
              <div className="text-center py-8 text-slate-500">
                <PlayIcon className="w-12 h-12 mx-auto mb-4 text-slate-300" />
                <p>Click "Execute Request" to test this endpoint</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiTester;