#!/usr/bin/env python3
"""
LLM Service Integration Test
Tests the integration between LLM Service and all worker types.
"""

import sys
import os
import ast
import json
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

def test_llm_service_structure():
    """Test LLM service structure and capabilities."""
    print("Testing LLM service structure...")
    
    try:
        llm_service_file = backend_path / "core" / "llm_service.py"
        
        if not llm_service_file.exists():
            print("❌ llm_service.py not found")
            return False
        
        with open(llm_service_file, 'r') as f:
            content = f.read()
        
        # Check for core LLM service patterns
        required_patterns = [
            'class LLMService',
            'get_completion',
            'agent_type',
            'context',
            'model_configs'
        ]
        
        for pattern in required_patterns:
            if pattern not in content:
                print(f"❌ Missing LLM service pattern: {pattern}")
                return False
        
        print("✓ LLM service structure found")
        
        # Check for model support patterns
        model_patterns = [
            'gpt',
            'gemini',
            'claude'
        ]
        
        found_models = 0
        for pattern in model_patterns:
            if pattern.lower() in content.lower():
                found_models += 1
        
        if found_models == 0:
            print("❌ No AI model providers found")
            return False
        
        print(f"✓ Found {found_models} model provider references")
        return True
        
    except Exception as e:
        print(f"❌ Error testing LLM service: {e}")
        return False

def test_worker_llm_integration():
    """Test that all workers properly integrate with LLM service."""
    print("Testing worker LLM service integration...")
    
    try:
        workers_dir = backend_path / "workers"
        
        if not workers_dir.exists():
            print("❌ workers directory not found")
            return False
        
        # Find all worker files
        worker_files = list(workers_dir.glob("*_worker.py"))
        
        if not worker_files:
            print("❌ No worker files found")
            return False
        
        integrated_workers = 0
        
        for worker_file in worker_files:
            worker_name = worker_file.stem
            
            with open(worker_file, 'r') as f:
                content = f.read()
            
            # Check for LLM service integration patterns
            integration_patterns = [
                'LLMService',
                'llm_service',
                'get_completion',
                'model'
            ]
            
            worker_integrated = False
            for pattern in integration_patterns:
                if pattern in content:
                    worker_integrated = True
                    break
            
            if worker_integrated:
                integrated_workers += 1
                print(f"✓ {worker_name} integrates with LLM service")
            else:
                print(f"⚠️  {worker_name} may not integrate with LLM service")
        
        if integrated_workers >= 2:  # At least health and persona workers should integrate
            print(f"✓ {integrated_workers} workers integrate with LLM service")
            return True
        else:
            print(f"❌ Only {integrated_workers} workers integrate with LLM service")
            return False
        
    except Exception as e:
        print(f"❌ Error testing worker integration: {e}")
        return False

def test_model_configuration():
    """Test model configuration and preferences."""
    print("Testing model configuration...")
    
    try:
        # Check models.yaml configuration
        models_file = backend_path.parent / "infra" / "models.yaml"
        
        if not models_file.exists():
            print("❌ infra/models.yaml not found")
            return False
        
        with open(models_file, 'r') as f:
            content = f.read()
        
        # Check for model configurations
        model_config_patterns = [
            'gpt',
            'gemini', 
            'agent_routing',
            'use_cases',
            'primary'
        ]
        
        for pattern in model_config_patterns:
            if pattern.lower() not in content.lower():
                print(f"❌ Missing model config pattern: {pattern}")
                return False
        
        print("✓ Model configuration file found and structured")
        
        # Check settings integration
        settings_file = backend_path / "core" / "settings.py"
        
        if settings_file.exists():
            with open(settings_file, 'r') as f:
                settings_content = f.read()
            
            if 'llm' in settings_content.lower() or 'model' in settings_content.lower():
                print("✓ Settings has LLM configuration")
            else:
                print("⚠️  Settings may be missing LLM configuration")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing model configuration: {e}")
        return False

def test_agent_specific_prompts():
    """Test that different worker types have agent-specific prompts."""
    print("Testing agent-specific prompt handling...")
    
    try:
        # Check specific workers for prompt customization
        workers_to_check = [
            ('health_worker.py', ['health', 'readability', 'clarity']),
            ('persona_worker.py', ['persona', 'feedback', 'cross_audience']),
            ('base_worker.py', ['prompt', 'context', 'agent_type'])
        ]
        
        for worker_file, expected_patterns in workers_to_check:
            file_path = backend_path / "workers" / worker_file
            
            if not file_path.exists():
                print(f"❌ {worker_file} not found")
                return False
            
            with open(file_path, 'r') as f:
                content = f.read()
            
            found_patterns = 0
            for pattern in expected_patterns:
                if pattern in content.lower():
                    found_patterns += 1
            
            if found_patterns >= len(expected_patterns) // 2:  # At least half the patterns
                print(f"✓ {worker_file} has agent-specific patterns")
            else:
                print(f"⚠️  {worker_file} may be missing agent-specific patterns")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing prompt handling: {e}")
        return False

def test_llm_error_handling():
    """Test error handling in LLM service integration."""
    print("Testing LLM error handling...")
    
    try:
        # Check LLM service for error handling
        llm_service_file = backend_path / "core" / "llm_service.py"
        
        with open(llm_service_file, 'r') as f:
            content = f.read()
        
        error_handling_patterns = [
            'try:',
            'except',
            'raise',
            'error',
            'timeout',
            'retry'
        ]
        
        found_error_handling = 0
        for pattern in error_handling_patterns:
            if pattern.lower() in content.lower():
                found_error_handling += 1
        
        if found_error_handling >= 4:  # Should have multiple error handling patterns
            print("✓ LLM service has comprehensive error handling")
        else:
            print(f"⚠️  LLM service has limited error handling ({found_error_handling} patterns)")
        
        # Check workers for error handling
        workers_with_error_handling = 0
        workers_dir = backend_path / "workers"
        
        for worker_file in workers_dir.glob("*_worker.py"):
            with open(worker_file, 'r') as f:
                worker_content = f.read()
            
            if 'try:' in worker_content and 'except' in worker_content:
                workers_with_error_handling += 1
        
        if workers_with_error_handling >= 2:
            print(f"✓ {workers_with_error_handling} workers have error handling")
            return True
        else:
            print(f"❌ Only {workers_with_error_handling} workers have error handling")
            return False
        
    except Exception as e:
        print(f"❌ Error testing error handling: {e}")
        return False

def test_performance_considerations():
    """Test performance optimizations in LLM integration."""
    print("Testing LLM performance considerations...")
    
    try:
        # Check for performance patterns
        llm_service_file = backend_path / "core" / "llm_service.py"
        
        with open(llm_service_file, 'r') as f:
            content = f.read()
        
        performance_patterns = [
            'cache',
            'timeout',
            'max_tokens',
            'temperature',
            'async',
            'await'
        ]
        
        found_performance = 0
        for pattern in performance_patterns:
            if pattern.lower() in content.lower():
                found_performance += 1
        
        if found_performance >= 4:
            print("✓ LLM service has performance optimizations")
        else:
            print(f"⚠️  LLM service has limited performance optimizations ({found_performance} patterns)")
        
        # Check workers for async patterns
        workers_with_async = 0
        workers_dir = backend_path / "workers"
        
        for worker_file in workers_dir.glob("*_worker.py"):
            with open(worker_file, 'r') as f:
                worker_content = f.read()
            
            if 'async def' in worker_content and 'await' in worker_content:
                workers_with_async += 1
        
        if workers_with_async >= 2:
            print(f"✓ {workers_with_async} workers use async patterns")
            return True
        else:
            print(f"❌ Only {workers_with_async} workers use async patterns")
            return False
        
    except Exception as e:
        print(f"❌ Error testing performance: {e}")
        return False

def run_llm_integration_tests():
    """Run all LLM service integration tests."""
    print("=" * 70)
    print("LLM SERVICE INTEGRATION TESTS")
    print("=" * 70)
    
    tests = [
        test_llm_service_structure,
        test_worker_llm_integration,
        test_model_configuration,
        test_agent_specific_prompts,
        test_llm_error_handling,
        test_performance_considerations
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n{test.__name__.replace('_', ' ').title()}:")
        try:
            if test():
                passed += 1
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test error: {e}")
    
    print("\n" + "=" * 70)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed >= total * 0.8:  # 80% pass rate acceptable for LLM integration
        print("🎉 LLM SERVICE INTEGRATION TESTS PASSED!")
        print("✅ LLM service is properly integrated with workers")
        return True
    else:
        print("❌ LLM integration tests failed")
        return False

if __name__ == "__main__":
    success = run_llm_integration_tests()
    sys.exit(0 if success else 1)