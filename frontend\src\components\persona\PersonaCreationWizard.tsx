import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  UserGroupIcon,
  AcademicCapIcon,
  BriefcaseIcon,
  PencilIcon,
  UserIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CheckIcon,
  SparklesIcon,
  InformationCircleIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';
import { usePersonaStore } from '../../stores/personaStore';
import { PersonaCategory, EducationLevel, ReaderPersona } from '../../types/persona';

interface PersonaCreationWizardProps {
  isOpen: boolean;
  onClose: () => void;
  onPersonaCreated?: (persona: ReaderPersona) => void;
}

interface PersonaFormData {
  name: string;
  description: string;
  category: PersonaCategory;
  demographics: {
    age?: number;
    education?: EducationLevel;
    profession?: string;
    culturalBackground?: string;
    location?: string;
  };
  readingPreferences: {
    genres?: string[];
    readingSpeed?: 'slow' | 'average' | 'fast';
    attentionSpan?: 'short' | 'medium' | 'long';
    preferredLength?: 'brief' | 'moderate' | 'detailed';
    complexity?: 'simple' | 'moderate' | 'complex';
  };
  personality: {
    traits?: string[];
    emotionalSensitivity?: 'low' | 'medium' | 'high';
    criticalThinking?: 'low' | 'medium' | 'high';
  };
  context: {
    relationship?: 'stranger' | 'acquaintance' | 'colleague' | 'friend' | 'family';
    purpose?: 'entertainment' | 'education' | 'work' | 'evaluation' | 'personal';
    expertise?: 'novice' | 'intermediate' | 'expert' | 'specialist';
    timeConstraints?: 'none' | 'limited' | 'urgent';
  };
  feedbackStyle: {
    tone?: 'harsh' | 'constructive' | 'encouraging' | 'gentle';
    focus?: 'content' | 'style' | 'structure' | 'emotion' | 'technical';
    detail?: 'high-level' | 'specific' | 'line-by-line';
    priorities?: string[];
  };
  color: string;
}

const PersonaCreationWizard: React.FC<PersonaCreationWizardProps> = ({
  isOpen,
  onClose,
  onPersonaCreated,
}) => {
  const { createPersona } = usePersonaStore();
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<PersonaFormData>({
    name: '',
    description: '',
    category: 'custom',
    demographics: {},
    readingPreferences: {},
    personality: {},
    context: {},
    feedbackStyle: {},
    color: 'blue',
  });

  const steps = [
    { id: 'basic', title: 'Basic Info', description: 'Name and category' },
    { id: 'demographics', title: 'Demographics', description: 'Age, education, background' },
    { id: 'reading', title: 'Reading Habits', description: 'Preferences and style' },
    { id: 'personality', title: 'Personality', description: 'Traits and thinking style' },
    { id: 'context', title: 'Context', description: 'Relationship and purpose' },
    { id: 'feedback', title: 'Feedback Style', description: 'How they give feedback' },
  ];

  const categoryOptions = [
    { value: 'academic', label: 'Academic', icon: AcademicCapIcon, color: 'blue' },
    { value: 'professional', label: 'Professional', icon: BriefcaseIcon, color: 'purple' },
    { value: 'creative', label: 'Creative', icon: PencilIcon, color: 'pink' },
    { value: 'personal', label: 'Personal', icon: UserIcon, color: 'green' },
    { value: 'custom', label: 'Custom', icon: UserGroupIcon, color: 'gray' },
  ];

  const colorOptions = [
    'blue', 'purple', 'pink', 'green', 'yellow', 'orange', 'red', 'indigo', 'gray'
  ];

  const updateFormData = (section: keyof PersonaFormData, data: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: typeof data === 'object' && section !== 'name' && section !== 'description' && section !== 'category' && section !== 'color'
        ? { ...prev[section], ...data }
        : data
    }));
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    const newPersona = {
      name: formData.name,
      description: formData.description,
      category: formData.category,
      demographics: formData.demographics,
      readingPreferences: formData.readingPreferences,
      personality: formData.personality,
      context: formData.context,
      feedbackStyle: formData.feedbackStyle,
      isActive: true,
      tags: [formData.category, 'custom'],
      color: formData.color,
    };

    const createdPersona = createPersona(newPersona);
    onPersonaCreated?.(createdPersona as ReaderPersona);
    onClose();
    
    // Reset form
    setFormData({
      name: '',
      description: '',
      category: 'custom',
      demographics: {},
      readingPreferences: {},
      personality: {},
      context: {},
      feedbackStyle: {},
      color: 'blue',
    });
    setCurrentStep(0);
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 0: return formData.name.trim() && formData.description.trim();
      case 1: return true; // Demographics are optional
      case 2: return true; // Reading preferences are optional
      case 3: return true; // Personality is optional
      case 4: return true; // Context is optional
      case 5: return true; // Feedback style is optional
      default: return false;
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black/50 z-[9999] flex items-center justify-center p-4">
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="p-6 border-b border-slate-200 bg-gradient-to-r from-purple-50 to-pink-50">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-slate-900 flex items-center">
                  <SparklesIcon className="w-6 h-6 mr-2 text-purple-600" />
                  Create Custom Persona
                </h2>
                <p className="text-slate-600 mt-1">
                  Define a unique reader persona for targeted feedback
                </p>
              </div>
              <button
                onClick={onClose}
                className="p-2 text-slate-600 hover:text-slate-900 transition-colors"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>

            {/* Progress Bar */}
            <div className="mt-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-slate-700">
                  Step {currentStep + 1} of {steps.length}: {steps[currentStep].title}
                </span>
                <span className="text-sm text-slate-500">
                  {Math.round(((currentStep + 1) / steps.length) * 100)}% complete
                </span>
              </div>
              <div className="w-full bg-slate-200 rounded-full h-2">
                <div
                  className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
                />
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
            <AnimatePresence mode="wait">
              {currentStep === 0 && (
                <BasicInfoStep
                  formData={formData}
                  updateFormData={updateFormData}
                  categoryOptions={categoryOptions}
                  colorOptions={colorOptions}
                />
              )}
              {currentStep === 1 && (
                <DemographicsStep
                  formData={formData}
                  updateFormData={updateFormData}
                />
              )}
              {currentStep === 2 && (
                <ReadingPreferencesStep
                  formData={formData}
                  updateFormData={updateFormData}
                />
              )}
              {currentStep === 3 && (
                <PersonalityStep
                  formData={formData}
                  updateFormData={updateFormData}
                />
              )}
              {currentStep === 4 && (
                <ContextStep
                  formData={formData}
                  updateFormData={updateFormData}
                />
              )}
              {currentStep === 5 && (
                <FeedbackStyleStep
                  formData={formData}
                  updateFormData={updateFormData}
                />
              )}
            </AnimatePresence>
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-slate-200 bg-slate-50">
            <div className="flex items-center justify-between">
              <button
                onClick={handlePrevious}
                disabled={currentStep === 0}
                className="flex items-center px-4 py-2 text-slate-600 hover:text-slate-900 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <ChevronLeftIcon className="w-4 h-4 mr-1" />
                Previous
              </button>

              <div className="flex space-x-3">
                {currentStep === steps.length - 1 ? (
                  <button
                    onClick={handleSubmit}
                    disabled={!isStepValid()}
                    className="flex items-center px-6 py-2 bg-purple-600 text-white rounded-xl hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <CheckIcon className="w-4 h-4 mr-2" />
                    Create Persona
                  </button>
                ) : (
                  <button
                    onClick={handleNext}
                    disabled={!isStepValid()}
                    className="flex items-center px-6 py-2 bg-purple-600 text-white rounded-xl hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Next
                    <ChevronRightIcon className="w-4 h-4 ml-1" />
                  </button>
                )}
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

// Step Components
const BasicInfoStep: React.FC<{
  formData: PersonaFormData;
  updateFormData: (section: keyof PersonaFormData, data: any) => void;
  categoryOptions: any[];
  colorOptions: string[];
}> = ({ formData, updateFormData, categoryOptions, colorOptions }) => (
  <motion.div
    initial={{ opacity: 0, x: 20 }}
    animate={{ opacity: 1, x: 0 }}
    exit={{ opacity: 0, x: -20 }}
    className="space-y-6"
  >
    <div>
      <label className="block text-sm font-medium text-slate-700 mb-2">
        Persona Name *
      </label>
      <input
        type="text"
        value={formData.name}
        onChange={(e) => updateFormData('name', e.target.value)}
        placeholder="e.g., Graduate Student, Busy Parent, Book Club Member"
        className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
      />
    </div>

    <div>
      <label className="block text-sm font-medium text-slate-700 mb-2">
        Description *
      </label>
      <textarea
        value={formData.description}
        onChange={(e) => updateFormData('description', e.target.value)}
        placeholder="Brief description of this persona's background and perspective"
        rows={3}
        className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
      />
    </div>

    <div>
      <label className="block text-sm font-medium text-slate-700 mb-3">
        Category
      </label>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        {categoryOptions.map((option) => {
          const Icon = option.icon;
          return (
            <button
              key={option.value}
              onClick={() => updateFormData('category', option.value)}
              className={`p-4 rounded-xl border-2 transition-all ${
                formData.category === option.value
                  ? `border-${option.color}-500 bg-${option.color}-50`
                  : 'border-slate-200 hover:border-slate-300'
              }`}
            >
              <Icon className={`w-6 h-6 mx-auto mb-2 ${
                formData.category === option.value
                  ? `text-${option.color}-600`
                  : 'text-slate-500'
              }`} />
              <div className="text-sm font-medium text-slate-900">
                {option.label}
              </div>
            </button>
          );
        })}
      </div>
    </div>

    <div>
      <label className="block text-sm font-medium text-slate-700 mb-3">
        Color Theme
      </label>
      <div className="flex flex-wrap gap-3">
        {colorOptions.map((color) => (
          <button
            key={color}
            onClick={() => updateFormData('color', color)}
            className={`w-8 h-8 rounded-full bg-${color}-500 border-2 transition-all ${
              formData.color === color
                ? 'border-slate-700 scale-110'
                : 'border-slate-300 hover:scale-105'
            }`}
          />
        ))}
      </div>
    </div>
  </motion.div>
);

const DemographicsStep: React.FC<{
  formData: PersonaFormData;
  updateFormData: (section: keyof PersonaFormData, data: any) => void;
}> = ({ formData, updateFormData }) => (
  <motion.div
    initial={{ opacity: 0, x: 20 }}
    animate={{ opacity: 1, x: 0 }}
    exit={{ opacity: 0, x: -20 }}
    className="space-y-6"
  >
    <div>
      <h3 className="text-lg font-semibold text-slate-900 mb-2">Demographics</h3>
      <p className="text-slate-600">Optional demographic information to better understand your persona.</p>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <label className="block text-sm font-medium text-slate-700 mb-2">
          Age
        </label>
        <input
          type="number"
          value={formData.demographics.age || ''}
          onChange={(e) => updateFormData('demographics', { age: parseInt(e.target.value) || undefined })}
          placeholder="25"
          min="1"
          max="120"
          className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-slate-700 mb-2">
          Education Level
        </label>
        <select
          value={formData.demographics.education || ''}
          onChange={(e) => updateFormData('demographics', { education: e.target.value || undefined })}
          className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        >
          <option value="">Select education level</option>
          <option value="elementary">Elementary</option>
          <option value="high-school">High School</option>
          <option value="some-college">Some College</option>
          <option value="bachelors">Bachelor's Degree</option>
          <option value="masters">Master's Degree</option>
          <option value="doctorate">Doctorate</option>
          <option value="professional">Professional Degree</option>
        </select>
      </div>
    </div>

    <div>
      <label className="block text-sm font-medium text-slate-700 mb-2">
        Profession
      </label>
      <input
        type="text"
        value={formData.demographics.profession || ''}
        onChange={(e) => updateFormData('demographics', { profession: e.target.value || undefined })}
        placeholder="e.g., Teacher, Engineer, Writer, Student"
        className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
      />
    </div>

    <div>
      <label className="block text-sm font-medium text-slate-700 mb-2">
        Cultural Background
      </label>
      <input
        type="text"
        value={formData.demographics.culturalBackground || ''}
        onChange={(e) => updateFormData('demographics', { culturalBackground: e.target.value || undefined })}
        placeholder="e.g., American, International, Multicultural"
        className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
      />
    </div>

    <div>
      <label className="block text-sm font-medium text-slate-700 mb-2">
        Location
      </label>
      <input
        type="text"
        value={formData.demographics.location || ''}
        onChange={(e) => updateFormData('demographics', { location: e.target.value || undefined })}
        placeholder="e.g., Urban, Suburban, Rural, International"
        className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
      />
    </div>
  </motion.div>
);

const ReadingPreferencesStep: React.FC<{
  formData: PersonaFormData;
  updateFormData: (section: keyof PersonaFormData, data: any) => void;
}> = ({ formData, updateFormData }) => {
  const [genreInput, setGenreInput] = useState('');

  const addGenre = () => {
    if (genreInput.trim()) {
      const currentGenres = formData.readingPreferences.genres || [];
      updateFormData('readingPreferences', {
        ...formData.readingPreferences,
        genres: [...currentGenres, genreInput.trim()]
      });
      setGenreInput('');
    }
  };

  const removeGenre = (index: number) => {
    const currentGenres = formData.readingPreferences.genres || [];
    updateFormData('readingPreferences', {
      ...formData.readingPreferences,
      genres: currentGenres.filter((_, i) => i !== index)
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-6"
    >
      <div>
        <h3 className="text-lg font-semibold text-slate-900 mb-2">Reading Preferences</h3>
        <p className="text-slate-600">How does this persona typically read and consume content?</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-slate-700 mb-2">
          Preferred Genres
        </label>
        <div className="flex gap-2 mb-3">
          <input
            type="text"
            value={genreInput}
            onChange={(e) => setGenreInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && addGenre()}
            placeholder="Enter a genre and press Enter"
            className="flex-1 px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          />
          <button
            onClick={addGenre}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
          >
            Add
          </button>
        </div>
        <div className="flex flex-wrap gap-2">
          {(formData.readingPreferences.genres || []).map((genre, index) => (
            <span
              key={index}
              className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm flex items-center"
            >
              {genre}
              <button
                onClick={() => removeGenre(index)}
                className="ml-2 text-purple-600 hover:text-purple-800"
              >
                ×
              </button>
            </span>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">
            Reading Speed
          </label>
          <select
            value={formData.readingPreferences.readingSpeed || ''}
            onChange={(e) => updateFormData('readingPreferences', { ...formData.readingPreferences, readingSpeed: e.target.value || undefined })}
            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="">Select reading speed</option>
            <option value="slow">Slow - Takes time to process</option>
            <option value="average">Average - Normal pace</option>
            <option value="fast">Fast - Quick reader</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">
            Attention Span
          </label>
          <select
            value={formData.readingPreferences.attentionSpan || ''}
            onChange={(e) => updateFormData('readingPreferences', { ...formData.readingPreferences, attentionSpan: e.target.value || undefined })}
            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="">Select attention span</option>
            <option value="short">Short - Prefers brief content</option>
            <option value="medium">Medium - Moderate attention</option>
            <option value="long">Long - Can focus for extended periods</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">
            Preferred Length
          </label>
          <select
            value={formData.readingPreferences.preferredLength || ''}
            onChange={(e) => updateFormData('readingPreferences', { ...formData.readingPreferences, preferredLength: e.target.value || undefined })}
            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="">Select preferred length</option>
            <option value="brief">Brief - Short and concise</option>
            <option value="moderate">Moderate - Balanced length</option>
            <option value="detailed">Detailed - Comprehensive content</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">
            Complexity Preference
          </label>
          <select
            value={formData.readingPreferences.complexity || ''}
            onChange={(e) => updateFormData('readingPreferences', { ...formData.readingPreferences, complexity: e.target.value || undefined })}
            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="">Select complexity level</option>
            <option value="simple">Simple - Easy to understand</option>
            <option value="moderate">Moderate - Balanced complexity</option>
            <option value="complex">Complex - Advanced concepts</option>
          </select>
        </div>
      </div>
    </motion.div>
  );
};

const PersonalityStep: React.FC<{
  formData: PersonaFormData;
  updateFormData: (section: keyof PersonaFormData, data: any) => void;
}> = ({ formData, updateFormData }) => {
  const [traitInput, setTraitInput] = useState('');

  const addTrait = () => {
    if (traitInput.trim()) {
      const currentTraits = formData.personality.traits || [];
      updateFormData('personality', {
        ...formData.personality,
        traits: [...currentTraits, traitInput.trim()]
      });
      setTraitInput('');
    }
  };

  const removeTrait = (index: number) => {
    const currentTraits = formData.personality.traits || [];
    updateFormData('personality', {
      ...formData.personality,
      traits: currentTraits.filter((_, i) => i !== index)
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-6"
    >
      <div>
        <h3 className="text-lg font-semibold text-slate-900 mb-2">Personality & Psychology</h3>
        <p className="text-slate-600">What personality traits and thinking patterns define this persona?</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-slate-700 mb-2">
          Personality Traits
        </label>
        <div className="flex gap-2 mb-3">
          <input
            type="text"
            value={traitInput}
            onChange={(e) => setTraitInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && addTrait()}
            placeholder="e.g., analytical, creative, patient"
            className="flex-1 px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          />
          <button
            onClick={addTrait}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
          >
            Add
          </button>
        </div>
        <div className="flex flex-wrap gap-2">
          {(formData.personality.traits || []).map((trait, index) => (
            <span
              key={index}
              className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm flex items-center"
            >
              {trait}
              <button
                onClick={() => removeTrait(index)}
                className="ml-2 text-blue-600 hover:text-blue-800"
              >
                ×
              </button>
            </span>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">
            Emotional Sensitivity
          </label>
          <select
            value={formData.personality.emotionalSensitivity || ''}
            onChange={(e) => updateFormData('personality', { ...formData.personality, emotionalSensitivity: e.target.value || undefined })}
            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="">Select sensitivity level</option>
            <option value="low">Low - Logical, less emotional</option>
            <option value="medium">Medium - Balanced emotional response</option>
            <option value="high">High - Very emotionally responsive</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">
            Critical Thinking
          </label>
          <select
            value={formData.personality.criticalThinking || ''}
            onChange={(e) => updateFormData('personality', { ...formData.personality, criticalThinking: e.target.value || undefined })}
            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="">Select thinking style</option>
            <option value="low">Low - Accepts information readily</option>
            <option value="medium">Medium - Some questioning</option>
            <option value="high">High - Highly analytical and questioning</option>
          </select>
        </div>
      </div>
    </motion.div>
  );
};

const ContextStep: React.FC<{
  formData: PersonaFormData;
  updateFormData: (section: keyof PersonaFormData, data: any) => void;
}> = ({ formData, updateFormData }) => (
  <motion.div
    initial={{ opacity: 0, x: 20 }}
    animate={{ opacity: 1, x: 0 }}
    exit={{ opacity: 0, x: -20 }}
    className="space-y-6"
  >
    <div>
      <h3 className="text-lg font-semibold text-slate-900 mb-2">Context & Purpose</h3>
      <p className="text-slate-600">What's the relationship and purpose when this persona reads your content?</p>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <label className="block text-sm font-medium text-slate-700 mb-2">
          Relationship to Writer
        </label>
        <select
          value={formData.context.relationship || ''}
          onChange={(e) => updateFormData('context', { ...formData.context, relationship: e.target.value || undefined })}
          className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        >
          <option value="">Select relationship</option>
          <option value="stranger">Stranger - No prior connection</option>
          <option value="acquaintance">Acquaintance - Limited familiarity</option>
          <option value="colleague">Colleague - Professional relationship</option>
          <option value="friend">Friend - Personal relationship</option>
          <option value="family">Family - Close personal bond</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-slate-700 mb-2">
          Reading Purpose
        </label>
        <select
          value={formData.context.purpose || ''}
          onChange={(e) => updateFormData('context', { ...formData.context, purpose: e.target.value || undefined })}
          className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        >
          <option value="">Select purpose</option>
          <option value="entertainment">Entertainment - For enjoyment</option>
          <option value="education">Education - To learn something</option>
          <option value="work">Work - Professional necessity</option>
          <option value="evaluation">Evaluation - To assess quality</option>
          <option value="personal">Personal - Personal interest</option>
        </select>
      </div>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <label className="block text-sm font-medium text-slate-700 mb-2">
          Subject Expertise
        </label>
        <select
          value={formData.context.expertise || ''}
          onChange={(e) => updateFormData('context', { ...formData.context, expertise: e.target.value || undefined })}
          className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        >
          <option value="">Select expertise level</option>
          <option value="novice">Novice - Little knowledge</option>
          <option value="intermediate">Intermediate - Some knowledge</option>
          <option value="expert">Expert - Deep knowledge</option>
          <option value="specialist">Specialist - Domain expert</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-slate-700 mb-2">
          Time Constraints
        </label>
        <select
          value={formData.context.timeConstraints || ''}
          onChange={(e) => updateFormData('context', { ...formData.context, timeConstraints: e.target.value || undefined })}
          className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        >
          <option value="">Select time constraints</option>
          <option value="none">None - Plenty of time</option>
          <option value="limited">Limited - Some time pressure</option>
          <option value="urgent">Urgent - Very time-constrained</option>
        </select>
      </div>
    </div>
  </motion.div>
);

const FeedbackStyleStep: React.FC<{
  formData: PersonaFormData;
  updateFormData: (section: keyof PersonaFormData, data: any) => void;
}> = ({ formData, updateFormData }) => {
  const [priorityInput, setPriorityInput] = useState('');

  const addPriority = () => {
    if (priorityInput.trim()) {
      const currentPriorities = formData.feedbackStyle.priorities || [];
      updateFormData('feedbackStyle', {
        ...formData.feedbackStyle,
        priorities: [...currentPriorities, priorityInput.trim()]
      });
      setPriorityInput('');
    }
  };

  const removePriority = (index: number) => {
    const currentPriorities = formData.feedbackStyle.priorities || [];
    updateFormData('feedbackStyle', {
      ...formData.feedbackStyle,
      priorities: currentPriorities.filter((_, i) => i !== index)
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-6"
    >
      <div>
        <h3 className="text-lg font-semibold text-slate-900 mb-2">Feedback Style</h3>
        <p className="text-slate-600">How does this persona typically give feedback and what do they focus on?</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">
            Feedback Tone
          </label>
          <select
            value={formData.feedbackStyle.tone || ''}
            onChange={(e) => updateFormData('feedbackStyle', { ...formData.feedbackStyle, tone: e.target.value || undefined })}
            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="">Select tone</option>
            <option value="harsh">Harsh - Direct and critical</option>
            <option value="constructive">Constructive - Balanced feedback</option>
            <option value="encouraging">Encouraging - Supportive approach</option>
            <option value="gentle">Gentle - Kind and careful</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">
            Feedback Focus
          </label>
          <select
            value={formData.feedbackStyle.focus || ''}
            onChange={(e) => updateFormData('feedbackStyle', { ...formData.feedbackStyle, focus: e.target.value || undefined })}
            className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="">Select focus area</option>
            <option value="content">Content - Ideas and information</option>
            <option value="style">Style - Writing style and voice</option>
            <option value="structure">Structure - Organization and flow</option>
            <option value="emotion">Emotion - Emotional impact</option>
            <option value="technical">Technical - Grammar and mechanics</option>
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-slate-700 mb-2">
          Detail Level
        </label>
        <select
          value={formData.feedbackStyle.detail || ''}
          onChange={(e) => updateFormData('feedbackStyle', { ...formData.feedbackStyle, detail: e.target.value || undefined })}
          className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        >
          <option value="">Select detail level</option>
          <option value="high-level">High-level - General overview</option>
          <option value="specific">Specific - Targeted feedback</option>
          <option value="line-by-line">Line-by-line - Detailed analysis</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-slate-700 mb-2">
          Feedback Priorities
        </label>
        <div className="flex gap-2 mb-3">
          <input
            type="text"
            value={priorityInput}
            onChange={(e) => setPriorityInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && addPriority()}
            placeholder="e.g., clarity, engagement, accuracy"
            className="flex-1 px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          />
          <button
            onClick={addPriority}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
          >
            Add
          </button>
        </div>
        <div className="flex flex-wrap gap-2">
          {(formData.feedbackStyle.priorities || []).map((priority, index) => (
            <span
              key={index}
              className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm flex items-center"
            >
              {priority}
              <button
                onClick={() => removePriority(index)}
                className="ml-2 text-green-600 hover:text-green-800"
              >
                ×
              </button>
            </span>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default PersonaCreationWizard;