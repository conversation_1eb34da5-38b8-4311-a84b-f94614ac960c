/**
 * Analytics API Service
 * 
 * Handles all analytics-related API calls including:
 * - Token usage tracking and statistics
 * - Writing sessions and goals
 * - User achievements and progress
 * - Dashboard analytics data
 */

import { baseApi, ApiResponse } from './baseApi';

export interface TokenUsage {
  user_id: string;
  total_tokens_used: number;
  tokens_limit: number;
  tokens_remaining: number;
  usage_percentage: number;
  current_period_start: string;
  current_period_end: string;
  daily_breakdown: Array<{
    date: string;
    tokens_used: number;
    operations: number;
    documents_edited: number;
  }>;
  tokens_by_model: Record<string, number>;
  tokens_by_agent: Record<string, number>;
}

export interface WritingSession {
  id: string;
  user_id: string;
  document_id: string;
  start_time: string;
  end_time?: string;
  words_written: number;
  words_at_start: number;
  ai_suggestions_accepted: number;
  ai_suggestions_dismissed: number;
  ai_generations_used: number;
  focus_mode_used: boolean;
  session_goal?: string;
  target_word_count?: number;
  created_at: string;
  updated_at: string;
}

export interface WritingGoal {
  id: string;
  user_id: string;
  type: 'daily_words' | 'weekly_words' | 'monthly_words' | 'daily_time' | 'weekly_time' | 'project_deadline';
  target_value: number;
  current_progress: number;
  unit: 'words' | 'minutes' | 'sessions';
  deadline?: string;
  status: 'active' | 'completed' | 'paused';
  title: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface UserAchievement {
  id: string;
  user_id: string;
  achievement_id: string;
  unlocked_at: string;
  progress_data?: Record<string, any>;
}

export interface DashboardSummary {
  total_sessions: number;
  total_words_written: number;
  total_time_spent_minutes: number;
  active_goals: number;
  completed_goals: number;
  unlocked_achievements: number;
  current_streak: number;
  today_words: number;
  today_time: number;
  week_words: number;
  week_time: number;
  ai_acceptance_rate: number;
  quality_score_average: number;
}

class AnalyticsApiService {
  /**
   * Get user's token usage statistics
   */
  async getTokenUsage(days: number = 30): Promise<TokenUsage> {
    const response = await baseApi.get<TokenUsage>(`/analytics/token-usage?days=${days}`);
    return response.data;
  }

  /**
   * Get writing sessions
   */
  async getWritingSessions(params?: {
    limit?: number;
    offset?: number;
    document_id?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<WritingSession[]> {
    const queryParams = new URLSearchParams();
    if (params?.limit) queryParams.set('limit', params.limit.toString());
    if (params?.offset) queryParams.set('offset', params.offset.toString());
    if (params?.document_id) queryParams.set('document_id', params.document_id);
    if (params?.start_date) queryParams.set('start_date', params.start_date);
    if (params?.end_date) queryParams.set('end_date', params.end_date);

    const response = await baseApi.get<WritingSession[]>(`/analytics/sessions?${queryParams}`);
    return response.data;
  }

  /**
   * Start a writing session
   */
  async startWritingSession(sessionData: {
    document_id: string;
    target_word_count?: number;
    session_goal?: string;
    focus_mode?: boolean;
  }): Promise<WritingSession> {
    const response = await baseApi.post<WritingSession>('/analytics/sessions', sessionData);
    return response.data;
  }

  /**
   * Update a writing session
   */
  async updateWritingSession(sessionId: string, updates: {
    end_time?: string;
    words_written?: number;
    words_at_start?: number;
    ai_suggestions_accepted?: number;
    ai_suggestions_dismissed?: number;
    ai_generations_used?: number;
    focus_mode_used?: boolean;
  }): Promise<WritingSession> {
    const response = await baseApi.put<WritingSession>(`/analytics/sessions/${sessionId}`, updates);
    return response.data;
  }

  /**
   * Get writing goals
   */
  async getWritingGoals(status?: 'active' | 'completed' | 'paused'): Promise<WritingGoal[]> {
    const params = status ? `?status=${status}` : '';
    const response = await baseApi.get<WritingGoal[]>(`/analytics/goals${params}`);
    return response.data;
  }

  /**
   * Create a writing goal
   */
  async createWritingGoal(goalData: {
    type: WritingGoal['type'];
    target_value: number;
    unit: WritingGoal['unit'];
    deadline?: string;
    title: string;
    description?: string;
  }): Promise<WritingGoal> {
    const response = await baseApi.post<WritingGoal>('/analytics/goals', goalData);
    return response.data;
  }

  /**
   * Update a writing goal
   */
  async updateWritingGoal(goalId: string, updates: {
    target_value?: number;
    deadline?: string;
    status?: WritingGoal['status'];
    title?: string;
    description?: string;
  }): Promise<WritingGoal> {
    const response = await baseApi.put<WritingGoal>(`/analytics/goals/${goalId}`, updates);
    return response.data;
  }

  /**
   * Get user achievements
   */
  async getUserAchievements(): Promise<UserAchievement[]> {
    const response = await baseApi.get<UserAchievement[]>('/analytics/achievements');
    return response.data;
  }

  /**
   * Track a usage event
   */
  async trackUsageEvent(eventData: {
    event_type: string;
    event_data: Record<string, any>;
    document_id?: string;
    tokens_used?: number;
    model_used?: string;
    processing_time_ms?: number;
  }): Promise<void> {
    await baseApi.post('/analytics/events', eventData);
  }

  /**
   * Get dashboard summary
   */
  async getDashboardSummary(): Promise<DashboardSummary> {
    const response = await baseApi.get<DashboardSummary>('/analytics/dashboard');
    return response.data;
  }
}

const analyticsApi = new AnalyticsApiService();
export default analyticsApi;