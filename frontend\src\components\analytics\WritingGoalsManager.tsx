import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  ClockIcon,
  TrophyIcon,
  CalendarDaysIcon,
  DocumentTextIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';
import { WritingGoal, GoalType, GoalPeriod } from '../../types/analytics';
import { useAnalyticsStore, useAnalyticsSelectors } from '../../stores/analyticsStore';

interface WritingGoalsManagerProps {
  className?: string;
}

const WritingGoalsManager: React.FC<WritingGoalsManagerProps> = ({ className = '' }) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState<WritingGoal | null>(null);
  
  const analyticsStore = useAnalyticsStore();
  const analyticsSelectors = useAnalyticsSelectors();
  
  const activeGoals = analyticsSelectors.getActiveGoals();
  const completedGoals = analyticsSelectors.getCompletedGoals();

  // Form state
  const [formData, setFormData] = useState({
    type: 'daily_words' as GoalType,
    target: 500,
    period: 'daily' as GoalPeriod,
    deadline: '',
  });

  const goalTypeConfig = {
    daily_words: {
      icon: DocumentTextIcon,
      label: 'Daily Words',
      description: 'Write a certain number of words each day',
      unit: 'words',
      defaultTarget: 500,
    },
    weekly_words: {
      icon: DocumentTextIcon,
      label: 'Weekly Words',
      description: 'Write a certain number of words each week',
      unit: 'words',
      defaultTarget: 3500,
    },
    daily_time: {
      icon: ClockIcon,
      label: 'Daily Writing Time',
      description: 'Spend time writing each day',
      unit: 'minutes',
      defaultTarget: 30,
    },
    weekly_time: {
      icon: ClockIcon,
      label: 'Weekly Writing Time',
      description: 'Spend time writing each week',
      unit: 'minutes',
      defaultTarget: 210,
    },
    writing_streak: {
      icon: TrophyIcon,
      label: 'Writing Streak',
      description: 'Maintain a consecutive writing streak',
      unit: 'days',
      defaultTarget: 7,
    },
    documents_completed: {
      icon: CheckCircleIcon,
      label: 'Complete Documents',
      description: 'Finish a certain number of documents',
      unit: 'documents',
      defaultTarget: 1,
    },
  };

  const handleCreateGoal = () => {
    const newGoal: Omit<WritingGoal, 'id' | 'createdAt'> = {
      type: formData.type,
      target: formData.target,
      current: 0,
      period: formData.period,
      status: 'active',
      deadline: formData.deadline ? new Date(formData.deadline) : undefined,
    };

    analyticsStore.addGoal(newGoal);
    setShowCreateForm(false);
    setFormData({
      type: 'daily_words',
      target: 500,
      period: 'daily',
      deadline: '',
    });
  };

  const handleEditGoal = (goal: WritingGoal) => {
    setEditingGoal(goal);
    setFormData({
      type: goal.type,
      target: goal.target,
      period: goal.period,
      deadline: goal.deadline ? goal.deadline.toISOString().split('T')[0] : '',
    });
    setShowCreateForm(true);
  };

  const handleUpdateGoal = () => {
    if (!editingGoal) return;

    const updatedGoal: WritingGoal = {
      ...editingGoal,
      type: formData.type,
      target: formData.target,
      period: formData.period,
      deadline: formData.deadline ? new Date(formData.deadline) : undefined,
    };

    analyticsStore.updateGoal(updatedGoal);
    setEditingGoal(null);
    setShowCreateForm(false);
    setFormData({
      type: 'daily_words',
      target: 500,
      period: 'daily',
      deadline: '',
    });
  };

  const handleDeleteGoal = (goalId: string) => {
    analyticsStore.removeGoal(goalId);
  };

  const getProgressPercentage = (goal: WritingGoal): number => {
    return Math.min((goal.current / goal.target) * 100, 100);
  };

  const formatTarget = (goal: WritingGoal): string => {
    const config = goalTypeConfig[goal.type];
    return `${goal.target.toLocaleString()} ${config.unit}`;
  };

  const formatCurrent = (goal: WritingGoal): string => {
    const config = goalTypeConfig[goal.type];
    return `${goal.current.toLocaleString()} ${config.unit}`;
  };

  const renderGoalCard = (goal: WritingGoal, isCompleted = false) => {
    const config = goalTypeConfig[goal.type];
    const IconComponent = config.icon;
    const progress = getProgressPercentage(goal);

    return (
      <motion.div
        key={goal.id}
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className={`bg-white border rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow ${
          isCompleted ? 'border-green-200 bg-green-50/50' : 'border-slate-200'
        }`}
      >
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`p-3 rounded-xl ${
              isCompleted ? 'bg-green-100' : 'bg-purple-100'
            }`}>
              <IconComponent className={`w-6 h-6 ${
                isCompleted ? 'text-green-600' : 'text-purple-600'
              }`} />
            </div>
            <div>
              <h3 className="font-semibold text-slate-900">{config.label}</h3>
              <p className="text-sm text-slate-600 capitalize">{goal.period}</p>
            </div>
          </div>
          
          {!isCompleted && (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleEditGoal(goal)}
                className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
              >
                <PencilIcon className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleDeleteGoal(goal.id)}
                className="p-2 text-slate-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              >
                <TrashIcon className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>

        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-slate-600">Progress</span>
            <span className="text-sm font-medium text-slate-900">
              {formatCurrent(goal)} / {formatTarget(goal)}
            </span>
          </div>
          <div className="w-full bg-slate-200 rounded-full h-3 overflow-hidden">
            <motion.div
              className={`h-full rounded-full ${
                isCompleted ? 'bg-green-500' : 'bg-purple-500'
              }`}
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
          <div className="flex items-center justify-between mt-2">
            <span className="text-xs text-slate-500">
              {progress.toFixed(1)}% complete
            </span>
            {goal.deadline && (
              <span className="text-xs text-slate-500 flex items-center">
                <CalendarDaysIcon className="w-3 h-3 mr-1" />
                Due {goal.deadline.toLocaleDateString()}
              </span>
            )}
          </div>
        </div>

        {isCompleted && goal.completedAt && (
          <div className="text-sm text-green-600 flex items-center">
            <CheckCircleIcon className="w-4 h-4 mr-1" />
            Completed on {goal.completedAt.toLocaleDateString()}
          </div>
        )}
      </motion.div>
    );
  };

  return (
    <div className={`bg-white rounded-3xl border border-slate-200 p-8 ${className}`}>
      <div className="flex items-center justify-between mb-8">
        <div>
          <h2 className="text-2xl font-bold text-slate-900 flex items-center">
            <TrophyIcon className="w-7 h-7 mr-3 text-purple-600" />
            Writing Goals
          </h2>
          <p className="text-slate-600 mt-1">Set and track your writing objectives</p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="inline-flex items-center px-4 py-2 bg-purple-600 text-white font-semibold rounded-xl hover:bg-purple-700 transition-colors"
        >
          <PlusIcon className="w-5 h-5 mr-2" />
          New Goal
        </button>
      </div>

      {/* Create/Edit Goal Form */}
      <AnimatePresence>
        {showCreateForm && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-8 overflow-hidden"
          >
            <div className="bg-slate-50 rounded-2xl p-6 border border-slate-200">
              <h3 className="text-lg font-semibold text-slate-900 mb-4">
                {editingGoal ? 'Edit Goal' : 'Create New Goal'}
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Goal Type
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => {
                      const type = e.target.value as GoalType;
                      setFormData({
                        ...formData,
                        type,
                        target: goalTypeConfig[type].defaultTarget,
                      });
                    }}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    {Object.entries(goalTypeConfig).map(([key, config]) => (
                      <option key={key} value={key}>
                        {config.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Target ({goalTypeConfig[formData.type].unit})
                  </label>
                  <input
                    type="number"
                    value={formData.target}
                    onChange={(e) => setFormData({ ...formData, target: parseInt(e.target.value) || 0 })}
                    min="1"
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Period
                  </label>
                  <select
                    value={formData.period}
                    onChange={(e) => setFormData({ ...formData, period: e.target.value as GoalPeriod })}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                    <option value="custom">Custom</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Deadline (Optional)
                  </label>
                  <input
                    type="date"
                    value={formData.deadline}
                    onChange={(e) => setFormData({ ...formData, deadline: e.target.value })}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              <div className="mt-4 p-4 bg-purple-50 rounded-lg">
                <p className="text-sm text-purple-700">
                  <SparklesIcon className="w-4 h-4 inline mr-1" />
                  {goalTypeConfig[formData.type].description}
                </p>
              </div>
              
              <div className="flex items-center justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowCreateForm(false);
                    setEditingGoal(null);
                  }}
                  className="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={editingGoal ? handleUpdateGoal : handleCreateGoal}
                  className="px-6 py-2 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors"
                >
                  {editingGoal ? 'Update Goal' : 'Create Goal'}
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Active Goals */}
      {activeGoals.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-slate-900 mb-4">Active Goals</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {activeGoals.map(goal => renderGoalCard(goal))}
          </div>
        </div>
      )}

      {/* Completed Goals */}
      {completedGoals.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-slate-900 mb-4">Recently Completed</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {completedGoals.slice(0, 4).map(goal => renderGoalCard(goal, true))}
          </div>
        </div>
      )}

      {/* Empty state */}
      {activeGoals.length === 0 && completedGoals.length === 0 && !showCreateForm && (
        <div className="text-center py-12">
          <TrophyIcon className="w-16 h-16 mx-auto text-slate-300 mb-4" />
          <h3 className="text-lg font-semibold text-slate-900 mb-2">No Goals Yet</h3>
          <p className="text-slate-600 mb-6">
            Set your first writing goal to start tracking your progress
          </p>
          <button
            onClick={() => setShowCreateForm(true)}
            className="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-semibold rounded-xl hover:bg-purple-700 transition-colors"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            Create Your First Goal
          </button>
        </div>
      )}
    </div>
  );
};

export default WritingGoalsManager;