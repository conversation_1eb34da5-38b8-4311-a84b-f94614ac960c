import React, { useState, useEffect } from 'react';
import Swagger<PERSON> from 'swagger-ui-react';
import 'swagger-ui-react/swagger-ui.css';
import { 
  PlayIcon, 
  CommandLineIcon, 
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  KeyIcon,
  CubeIcon
} from '@heroicons/react/24/outline';
import { useAuthStore } from '@/stores/authStore';
import ApiTester from './ApiTester';

interface ApiEndpoint {
  method: string;
  path: string;
  description: string;
  status: 'stable' | 'beta' | 'deprecated';
  tags: string[];
  requiresAuth: boolean;
}

const mockApiEndpoints: ApiEndpoint[] = [
  {
    method: 'GET',
    path: '/api/v1/analytics/token-usage',
    description: 'Get current token usage and quota information',
    status: 'stable',
    tags: ['Analytics'],
    requiresAuth: true
  },
  {
    method: 'GET',
    path: '/api/v1/documents',
    description: 'List user documents with pagination and filtering',
    status: 'stable',
    tags: ['Documents'],
    requiresAuth: true
  },
  {
    method: 'POST',
    path: '/api/v1/documents',
    description: 'Create a new document',
    status: 'stable',
    tags: ['Documents'],
    requiresAuth: true
  },
  {
    method: 'GET',
    path: '/api/v1/documents/{document_id}',
    description: 'Get a specific document by ID',
    status: 'stable',
    tags: ['Documents'],
    requiresAuth: true
  },
  {
    method: 'PUT',
    path: '/api/v1/documents/{document_id}',
    description: 'Update document content and metadata',
    status: 'stable',
    tags: ['Documents'],
    requiresAuth: true
  },
  {
    method: 'DELETE',
    path: '/api/v1/documents/{document_id}',
    description: 'Delete a document',
    status: 'stable',
    tags: ['Documents'],
    requiresAuth: true
  },
  {
    method: 'GET',
    path: '/api/v1/auth/profile',
    description: 'Get current user profile information',
    status: 'stable',
    tags: ['Authentication'],
    requiresAuth: true
  },
  {
    method: 'POST',
    path: '/api/v1/blocks/generate',
    description: 'Generate AI content blocks',
    status: 'beta',
    tags: ['AI Generation'],
    requiresAuth: true
  },
  {
    method: 'GET',
    path: '/api/v1/personas',
    description: 'List available writing personas',
    status: 'stable',
    tags: ['Personas'],
    requiresAuth: true
  },
  {
    method: 'POST',
    path: '/api/v1/agents/create',
    description: 'Create a new AI agent',
    status: 'beta',
    tags: ['Agents'],
    requiresAuth: true
  }
];

const ApiExplorer: React.FC = () => {
  const [activeView, setActiveView] = useState<'endpoints' | 'swagger'>('endpoints');
  const [selectedTag, setSelectedTag] = useState<string>('All');
  const [swaggerSpec, setSwaggerSpec] = useState<object | null>(null);
  const [loading, setLoading] = useState(false);
  const [testingEndpoint, setTestingEndpoint] = useState<ApiEndpoint | null>(null);
  const { user } = useAuthStore();

  const allTags = ['All', ...Array.from(new Set(mockApiEndpoints.flatMap(endpoint => endpoint.tags)))];

  const filteredEndpoints = selectedTag === 'All' 
    ? mockApiEndpoints 
    : mockApiEndpoints.filter(endpoint => endpoint.tags.includes(selectedTag));

  const loadSwaggerSpec = async () => {
    setLoading(true);
    try {
      // Import the developer API and fetch real OpenAPI spec
      const { developerApi } = await import('@/services/api/developerApi');
      const spec = await developerApi.getOpenApiSpec();
      setSwaggerSpec(spec);
    } catch (error) {
      console.error('Failed to load OpenAPI spec:', error);
      
      // Fallback to mock OpenAPI spec
      const mockSpec = {
        openapi: "3.0.0",
        info: {
          title: "Revisionary API",
          version: "1.0.0",
          description: "AI-powered writing assistance API",
          contact: {
            name: "API Support",
            email: "<EMAIL>"
          }
        },
        servers: [
          {
            url: "https://api.revisionary.app/v1",
            description: "Production server"
          },
          {
            url: "http://localhost:8000/api/v1",
            description: "Development server"
          }
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: "http",
              scheme: "bearer",
              bearerFormat: "JWT"
            }
          }
        },
        security: [
          {
            bearerAuth: []
          }
        ],
        paths: {
          "/analytics/token-usage": {
            get: {
              tags: ["Analytics"],
              summary: "Get token usage",
              description: "Get current token usage and quota information for the authenticated user",
              responses: {
                "200": {
                  description: "Successful response",
                  content: {
                    "application/json": {
                      schema: {
                        type: "object",
                        properties: {
                          success: { type: "boolean" },
                          data: {
                            type: "object",
                            properties: {
                              user_id: { type: "string" },
                              total_tokens_used: { type: "integer" },
                              tokens_limit: { type: "integer" },
                              tokens_remaining: { type: "integer" },
                              usage_percentage: { type: "number" }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          "/documents": {
            get: {
              tags: ["Documents"],
              summary: "List documents",
              description: "Get a paginated list of user documents",
              parameters: [
                {
                  name: "limit",
                  in: "query",
                  schema: { type: "integer", default: 10 }
                },
                {
                  name: "offset",
                  in: "query", 
                  schema: { type: "integer", default: 0 }
                }
              ],
              responses: {
                "200": {
                  description: "List of documents",
                  content: {
                    "application/json": {
                      schema: {
                        type: "array",
                        items: {
                          type: "object",
                          properties: {
                            id: { type: "string" },
                            title: { type: "string" },
                            content: { type: "string" },
                            created_at: { type: "string", format: "date-time" },
                            updated_at: { type: "string", format: "date-time" }
                          }
                        }
                      }
                    }
                  }
                }
              }
            },
            post: {
              tags: ["Documents"],
              summary: "Create document",
              description: "Create a new document",
              requestBody: {
                required: true,
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      required: ["title"],
                      properties: {
                        title: { type: "string" },
                        content: { type: "string", default: "" },
                        type: { type: "string", enum: ["blog", "academic", "business", "creative"] }
                      }
                    }
                  }
                }
              },
              responses: {
                "201": {
                  description: "Document created successfully"
                }
              }
            }
          }
        }
      };
      
      setSwaggerSpec(mockSpec);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeView === 'swagger' && !swaggerSpec) {
      loadSwaggerSpec();
    }
  }, [activeView, swaggerSpec]);

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'POST': return 'bg-green-100 text-green-800 border-green-200';
      case 'PUT': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'DELETE': return 'bg-red-100 text-red-800 border-red-200';
      case 'PATCH': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'stable': return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
      case 'beta': return <ExclamationTriangleIcon className="w-4 h-4 text-yellow-500" />;
      case 'deprecated': return <ClockIcon className="w-4 h-4 text-red-500" />;
      default: return <CheckCircleIcon className="w-4 h-4 text-gray-500" />;
    }
  };

  const renderEndpointsList = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">API Endpoints</h2>
          <p className="text-slate-600 mt-1">Explore available API endpoints and their specifications</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <KeyIcon className="w-5 h-5 text-green-500" />
            <span className="text-sm text-slate-600">
              Authenticated as: {user?.displayName || 'Developer'}
            </span>
          </div>
        </div>
      </div>

      {/* Tags Filter */}
      <div className="flex flex-wrap gap-2">
        {allTags.map((tag) => (
          <button
            key={tag}
            onClick={() => setSelectedTag(tag)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedTag === tag
                ? 'bg-purple-600 text-white shadow-lg'
                : 'bg-white text-slate-600 border border-slate-200 hover:border-slate-300'
            }`}
          >
            {tag}
          </button>
        ))}
      </div>

      {/* Endpoints */}
      <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-slate-100">
        {filteredEndpoints.map((endpoint, index) => (
          <div
            key={`${endpoint.method}-${endpoint.path}`}
            className="bg-white border border-slate-200 rounded-xl p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-4">
                <span className={`px-3 py-1 rounded-lg text-sm font-mono font-semibold border ${getMethodColor(endpoint.method)}`}>
                  {endpoint.method}
                </span>
                <code className="text-lg font-mono text-slate-700 font-medium">
                  {endpoint.path}
                </code>
              </div>
              <div className="flex items-center space-x-3">
                {getStatusIcon(endpoint.status)}
                <span className="text-sm text-slate-500 capitalize font-medium">{endpoint.status}</span>
                {endpoint.requiresAuth && (
                  <KeyIcon className="w-4 h-4 text-amber-500" title="Requires authentication" />
                )}
              </div>
            </div>
            
            <p className="text-slate-600 mb-4">{endpoint.description}</p>
            
            <div className="flex items-center justify-between">
              <div className="flex flex-wrap gap-2">
                {endpoint.tags.map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 bg-slate-100 text-slate-600 text-xs font-medium rounded-md"
                  >
                    {tag}
                  </span>
                ))}
              </div>
              <button 
                onClick={() => setTestingEndpoint(endpoint)}
                className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium"
              >
                <PlayIcon className="w-4 h-4 mr-2" />
                Try it out
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderSwaggerUI = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-slate-900 mb-2">Interactive API Explorer</h2>
        <p className="text-slate-600">Test API endpoints directly with Swagger UI</p>
      </div>

      {loading ? (
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-4 text-slate-600">Loading API specification...</p>
          </div>
        </div>
      ) : swaggerSpec ? (
        <div className="bg-white rounded-xl border border-slate-200 overflow-hidden max-h-[80vh] overflow-y-auto scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-slate-100">
          <SwaggerUI
            spec={swaggerSpec}
            docExpansion="list"
            defaultModelsExpandDepth={2}
            defaultModelExpandDepth={2}
            tryItOutEnabled={true}
            requestInterceptor={(request) => {
              // Add authentication header if user is logged in
              if (user) {
                request.headers['Authorization'] = `Bearer ${user.accessToken || 'dev-token'}`;
              }
              return request;
            }}
            onComplete={(system) => {
              // Custom styling can be applied here
              console.log('Swagger UI loaded', system);
            }}
          />
        </div>
      ) : (
        <div className="text-center py-12">
          <CubeIcon className="w-12 h-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-600 mb-4">Failed to load API specification</p>
          <button
            onClick={loadSwaggerSpec}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Retry
          </button>
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-8">
      {/* View Toggle */}
      <div className="flex items-center space-x-1 bg-slate-100 p-1 rounded-lg w-fit">
        <button
          onClick={() => setActiveView('endpoints')}
          className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-all ${
            activeView === 'endpoints'
              ? 'bg-white text-purple-600 shadow-sm'
              : 'text-slate-600 hover:text-slate-900'
          }`}
        >
          <DocumentTextIcon className="w-4 h-4 mr-2" />
          Endpoints
        </button>
        <button
          onClick={() => setActiveView('swagger')}
          className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-all ${
            activeView === 'swagger'
              ? 'bg-white text-purple-600 shadow-sm'
              : 'text-slate-600 hover:text-slate-900'
          }`}
        >
          <CommandLineIcon className="w-4 h-4 mr-2" />
          Interactive
        </button>
      </div>

      {/* Content */}
      {activeView === 'endpoints' ? renderEndpointsList() : renderSwaggerUI()}

      {/* API Tester Modal */}
      {testingEndpoint && (
        <ApiTester
          method={testingEndpoint.method}
          path={testingEndpoint.path}
          onClose={() => setTestingEndpoint(null)}
        />
      )}
    </div>
  );
};

export default ApiExplorer;