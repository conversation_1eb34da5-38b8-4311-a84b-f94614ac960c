import React from 'react';
import { motion } from 'framer-motion';
import { ArrowTrendingUpIcon, ArrowTrendingDownIcon } from '@heroicons/react/24/outline';

interface DataPoint {
  date: string;
  value: number;
  label?: string;
}

interface ProgressChartProps {
  data: DataPoint[];
  title: string;
  color?: 'purple' | 'blue' | 'green' | 'orange' | 'red';
  type?: 'line' | 'bar';
  showTrend?: boolean;
  unit?: string;
  className?: string;
}

const ProgressChart: React.FC<ProgressChartProps> = ({
  data,
  title,
  color = 'purple',
  type = 'line',
  showTrend = true,
  unit = '',
  className = ''
}) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-2xl border border-slate-200 p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-slate-900 mb-4">{title}</h3>
        <div className="flex items-center justify-center h-32 text-slate-500">
          No data available
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const range = maxValue - minValue || 1;

  // Calculate trend
  const firstValue = data[0]?.value || 0;
  const lastValue = data[data.length - 1]?.value || 0;
  const trendPercentage = firstValue > 0 ? ((lastValue - firstValue) / firstValue) * 100 : 0;
  const isPositiveTrend = trendPercentage >= 0;

  const colorClasses = {
    purple: {
      primary: 'stroke-purple-500',
      secondary: 'fill-purple-100',
      bar: 'bg-purple-500',
      trend: isPositiveTrend ? 'text-green-600' : 'text-red-600',
    },
    blue: {
      primary: 'stroke-blue-500',
      secondary: 'fill-blue-100',
      bar: 'bg-blue-500',
      trend: isPositiveTrend ? 'text-green-600' : 'text-red-600',
    },
    green: {
      primary: 'stroke-green-500',
      secondary: 'fill-green-100',
      bar: 'bg-green-500',
      trend: isPositiveTrend ? 'text-green-600' : 'text-red-600',
    },
    orange: {
      primary: 'stroke-orange-500',
      secondary: 'fill-orange-100',
      bar: 'bg-orange-500',
      trend: isPositiveTrend ? 'text-green-600' : 'text-red-600',
    },
    red: {
      primary: 'stroke-red-500',
      secondary: 'fill-red-100',
      bar: 'bg-red-500',
      trend: isPositiveTrend ? 'text-green-600' : 'text-red-600',
    },
  };

  const colors = colorClasses[color];

  const renderLineChart = () => {
    const width = 400;
    const height = 120;
    const padding = 20;

    const xStep = (width - padding * 2) / (data.length - 1);
    
    const points = data.map((point, index) => {
      const x = padding + index * xStep;
      const y = height - padding - ((point.value - minValue) / range) * (height - padding * 2);
      return `${x},${y}`;
    }).join(' ');

    const pathD = data.map((point, index) => {
      const x = padding + index * xStep;
      const y = height - padding - ((point.value - minValue) / range) * (height - padding * 2);
      return index === 0 ? `M ${x} ${y}` : `L ${x} ${y}`;
    }).join(' ');

    const areaD = `${pathD} L ${padding + (data.length - 1) * xStep} ${height - padding} L ${padding} ${height - padding} Z`;

    return (
      <svg width="100%" height={height} viewBox={`0 0 ${width} ${height}`} className="overflow-visible">
        {/* Grid lines */}
        <defs>
          <pattern id="grid" width="40" height="20" patternUnits="userSpaceOnUse">
            <path d="M 40 0 L 0 0 0 20" fill="none" stroke="#f1f5f9" strokeWidth="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
        
        {/* Area fill */}
        <motion.path
          d={areaD}
          className={colors.secondary}
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.3 }}
          transition={{ duration: 1, delay: 0.5 }}
        />
        
        {/* Line */}
        <motion.path
          d={pathD}
          fill="none"
          className={colors.primary}
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 2, ease: "easeInOut" }}
        />
        
        {/* Data points */}
        {data.map((point, index) => {
          const x = padding + index * xStep;
          const y = height - padding - ((point.value - minValue) / range) * (height - padding * 2);
          return (
            <motion.circle
              key={index}
              cx={x}
              cy={y}
              r="4"
              className={colors.primary.replace('stroke-', 'fill-')}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.3, delay: index * 0.1 + 1 }}
              whileHover={{ scale: 1.5 }}
            >
              <title>{`${point.date}: ${point.value}${unit}`}</title>
            </motion.circle>
          );
        })}
      </svg>
    );
  };

  const renderBarChart = () => {
    const barWidth = 100 / data.length - 2; // percentage width with spacing

    return (
      <div className="flex items-end space-x-1 h-32">
        {data.map((point, index) => {
          const height = ((point.value - minValue) / range) * 100;
          return (
            <motion.div
              key={index}
              className="flex flex-col items-center"
              style={{ width: `${barWidth}%` }}
            >
              <motion.div
                className={`${colors.bar} rounded-t-sm relative group cursor-pointer`}
                style={{ width: '100%' }}
                initial={{ height: 0 }}
                animate={{ height: `${height}%` }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ scale: 1.05 }}
              >
                {/* Tooltip */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  {point.value.toLocaleString()}{unit}
                </div>
              </motion.div>
              <span className="text-xs text-slate-500 mt-2 transform rotate-45 origin-left">
                {point.date}
              </span>
            </motion.div>
          );
        })}
      </div>
    );
  };

  return (
    <div className={`bg-white rounded-2xl border border-slate-200 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-slate-900">{title}</h3>
        {showTrend && (
          <div className={`flex items-center space-x-1 ${colors.trend}`}>
            {isPositiveTrend ? (
              <ArrowTrendingUpIcon className="w-4 h-4" />
            ) : (
              <ArrowTrendingDownIcon className="w-4 h-4" />
            )}
            <span className="text-sm font-medium">
              {isPositiveTrend ? '+' : ''}{trendPercentage.toFixed(1)}%
            </span>
          </div>
        )}
      </div>

      <div className="mb-4">
        {type === 'line' ? renderLineChart() : renderBarChart()}
      </div>

      {/* Summary stats */}
      <div className="grid grid-cols-3 gap-4 pt-4 border-t border-slate-100">
        <div className="text-center">
          <div className="text-sm text-slate-500">Current</div>
          <div className="text-lg font-semibold text-slate-900">
            {lastValue.toLocaleString()}{unit}
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm text-slate-500">Average</div>
          <div className="text-lg font-semibold text-slate-900">
            {Math.round(data.reduce((sum, d) => sum + d.value, 0) / data.length).toLocaleString()}{unit}
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm text-slate-500">Best</div>
          <div className="text-lg font-semibold text-slate-900">
            {maxValue.toLocaleString()}{unit}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressChart;