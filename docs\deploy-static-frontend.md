# Deploying the static Revisionary front-end to Cloud Run

This guide shows the quickest workflow for packaging the Vite front-end as static files and deploying it to Google Cloud Run using the lightweight `frontend/cloudbuild.static.yaml` pipeline.

## Prerequisites

1. Google Cloud SDK (`gcloud`) installed and initialised (`gcloud init`).
2. You are authenticated (`gcloud auth login`) and have set the default project & region:

   ```bash
   gcloud config set project <YOUR_PROJECT_ID>
   gcloud config set run/region us-central1     # or your preferred region
   ```

3. Node ≥ 18 and `pnpm` ≥ 8 globally available:

   ```bash
   npm install -g pnpm   # one-time setup
   ```

4. Logged into your conda environment (if you keep Node inside it):

   ```bash
   conda activate revisionary
   ```

## Step 1 – Build the static assets locally

```bash
cd frontend
pnpm install --frozen-lockfile   # installs deps according to pnpm-lock.yaml
pnpm run build                  # generates dist/ with production files
cd ..                            # return to repo root for the next step
```

> **Why local build?**  The `Dockerfile.static` merely copies `dist/` into an Nginx image.  It does not run the Node build itself.

## Step 2 – Submit the Cloud Build job & deploy

From the repository root:

```bash
# Upload current directory (filtered by .gcloudignore) and execute the pipeline
gcloud builds submit --config=frontend/cloudbuild.static.yaml .
```

What happens:

1. The local source (including `frontend/dist/**`) is uploaded to Cloud Build.
2. Cloud Build executes the single Docker build step defined in `cloudbuild.static.yaml`:
   • `Dockerfile.static` copies the pre-built `dist/` into an Nginx image.
3. The resulting image is pushed to Container Registry and immediately deployed to Cloud Run (`revisionary-frontend` service by default).

Once the command finishes you'll get the service URL in the terminal.

## Handy one-liner (inside `frontend/`)

```bash
pnpm install --frozen-lockfile && pnpm run build && cd .. && \
  gcloud builds submit --config=frontend/cloudbuild.static.yaml .
```

## Running the Development Server Locally

### For PowerShell users:

1. **Clean install (if having issues)**:
   ```powershell
   Remove-Item -Recurse -Force node_modules -ErrorAction SilentlyContinue
   Remove-Item -Force pnpm-lock.yaml -ErrorAction SilentlyContinue
   pnpm install
   ```

2. **Install missing dependencies**:
   ```powershell
   pnpm add axios  # Required dependency
   ```

3. **Run the development server**:
   ```powershell
   # Option 1: Direct execution
   .\node_modules\.bin\vite

   # Option 2: Configure pnpm for PowerShell and use npm scripts
   pnpm config set script-shell powershell
   pnpm run dev
   ```

### For Bash/Linux/WSL users:

```bash
cd frontend
pnpm install
pnpm add axios  # If not already in package.json
pnpm run dev
```

The development server will start at http://localhost:3000

## Troubleshooting

• **"vite is not recognized"** – For PowerShell: use `.\node_modules\.bin\vite` or configure pnpm with `pnpm config set script-shell powershell`
• **"axios dependency not found"** – run `pnpm add axios` to install the missing dependency
• **Empty site after deploy** – verify that `frontend/dist/index.html` exists **before** running `gcloud builds submit`
• **Want Cloud Build to build for you instead?**  Use `frontend/cloudbuild.yaml` (multi-stage Dockerfile) instead of the static pipeline; it compiles the app inside the builder stage 