-- Revisionary Cross-Reference and Citation Management Tables
-- Part 4 of the schema migration

-- 24. Entities Table
CREATE TABLE entities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(500) NOT NULL,
    entity_type entity_type_enum NOT NULL,
    aliases TEXT[] DEFAULT '{}',
    description TEXT,
    attributes JSONB DEFAULT '{}'::jsonb,
    physical_attributes JSONB DEFAULT '{}'::jsonb,
    personality_traits JSONB DEFAULT '{}'::jsonb,
    first_mentioned_block_id UUID REFERENCES blocks(id),
    last_mentioned_block_id UUID REFERENCES blocks(id),
    mention_count INTEGER DEFAULT 1,
    importance_score DECIMAL(3,2) DEFAULT 0.5,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT entities_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT entities_attributes_valid CHECK (jsonb_typeof(attributes) = 'object'),
    CONSTRAINT entities_physical_valid CHECK (jsonb_typeof(physical_attributes) = 'object'),
    CONSTRAINT entities_personality_valid CHECK (jsonb_typeof(personality_traits) = 'object'),
    CONSTRAINT entities_mention_count_positive CHECK (mention_count >= 0),
    CONSTRAINT entities_importance_range CHECK (importance_score >= 0 AND importance_score <= 1)
);

-- 25. Entity Relationships Table
CREATE TABLE entity_relationships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_entity_id UUID REFERENCES entities(id) ON DELETE CASCADE,
    target_entity_id UUID REFERENCES entities(id) ON DELETE CASCADE,
    relationship_type relationship_type_enum NOT NULL,
    relationship_name VARCHAR(255),
    description TEXT,
    strength DECIMAL(3,2) DEFAULT 0.5,
    is_bidirectional BOOLEAN DEFAULT FALSE,
    context_block_id UUID REFERENCES blocks(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT entity_relationships_different CHECK (source_entity_id != target_entity_id),
    CONSTRAINT entity_relationships_strength_range CHECK (strength >= 0 AND strength <= 1)
);

-- 26. Consistency Violations Table
CREATE TABLE consistency_violations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    entity_id UUID REFERENCES entities(id) ON DELETE CASCADE,
    violation_type violation_type_enum NOT NULL,
    severity consistency_severity_enum NOT NULL,
    description TEXT NOT NULL,
    conflicting_blocks UUID[] NOT NULL,
    suggested_resolution TEXT,
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMPTZ,
    resolved_by UUID REFERENCES users(id),
    resolution_method VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT consistency_violations_description_not_empty CHECK (LENGTH(TRIM(description)) > 0),
    CONSTRAINT consistency_violations_blocks_not_empty CHECK (array_length(conflicting_blocks, 1) > 0)
);

-- 27. Citations Table
CREATE TABLE citations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    citation_key VARCHAR(255) NOT NULL,
    citation_type citation_type_enum NOT NULL,
    title TEXT NOT NULL,
    authors TEXT[] NOT NULL,
    publication_year INTEGER,
    publisher VARCHAR(500),
    doi VARCHAR(255),
    url TEXT,
    page_numbers VARCHAR(50),
    volume VARCHAR(50),
    issue VARCHAR(50),
    journal_name VARCHAR(500),
    isbn VARCHAR(20),
    raw_citation TEXT,
    formatted_citation TEXT,
    citation_style citation_style_enum DEFAULT 'apa',
    access_date DATE,
    position JSONB,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_date TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT citations_key_not_empty CHECK (LENGTH(TRIM(citation_key)) > 0),
    CONSTRAINT citations_title_not_empty CHECK (LENGTH(TRIM(title)) > 0),
    CONSTRAINT citations_authors_not_empty CHECK (array_length(authors, 1) > 0),
    CONSTRAINT citations_year_reasonable CHECK (publication_year IS NULL OR (publication_year >= 1000 AND publication_year <= EXTRACT(YEAR FROM NOW()) + 5)),
    CONSTRAINT citations_position_valid CHECK (
        position IS NULL OR (
            jsonb_typeof(position) = 'object' AND
            position ? 'start' AND position ? 'end'
        )
    ),
    
    UNIQUE(document_id, citation_key)
);

-- 28. Reference Library Table
CREATE TABLE reference_library (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    authors TEXT[] NOT NULL,
    publication_year INTEGER,
    citation_type citation_type_enum NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    tags TEXT[] DEFAULT '{}',
    notes TEXT,
    file_url TEXT,
    doi VARCHAR(255),
    url TEXT,
    is_favorite BOOLEAN DEFAULT FALSE,
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT reference_library_title_not_empty CHECK (LENGTH(TRIM(title)) > 0),
    CONSTRAINT reference_library_authors_not_empty CHECK (array_length(authors, 1) > 0),
    CONSTRAINT reference_library_metadata_valid CHECK (jsonb_typeof(metadata) = 'object'),
    CONSTRAINT reference_library_usage_positive CHECK (usage_count >= 0)
);

-- 29. Export Jobs Table
CREATE TABLE export_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    format export_format_enum NOT NULL,
    status export_status_enum DEFAULT 'pending',
    options JSONB DEFAULT '{}'::jsonb,
    file_url TEXT,
    file_size INTEGER,
    error_message TEXT,
    processing_started_at TIMESTAMPTZ,
    processing_completed_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT export_jobs_file_size_positive CHECK (file_size >= 0),
    CONSTRAINT export_jobs_options_valid CHECK (jsonb_typeof(options) = 'object')
);

-- 30. Templates Table
CREATE TABLE templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category template_category_enum NOT NULL,
    document_type document_type_enum NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES users(id),
    structure JSONB NOT NULL, -- Template structure definition
    metadata JSONB DEFAULT '{}'::jsonb,
    usage_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2),
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT templates_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT templates_structure_valid CHECK (jsonb_typeof(structure) = 'object'),
    CONSTRAINT templates_metadata_valid CHECK (jsonb_typeof(metadata) = 'object'),
    CONSTRAINT templates_usage_count_positive CHECK (usage_count >= 0),
    CONSTRAINT templates_rating_range CHECK (rating IS NULL OR (rating >= 0 AND rating <= 5))
);