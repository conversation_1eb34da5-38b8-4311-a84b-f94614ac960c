import React, { useState, useEffect } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  HomeIcon,
  DocumentTextIcon,
  PencilSquareIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  FolderIcon,
  SparklesIcon,
  BoltIcon,
  XMarkIcon,
  Bars3Icon,
  CodeBracketIcon,
} from '@heroicons/react/24/outline';

const navigation = [
  { name: 'Dashboard', href: '/app/dashboard', icon: HomeIcon, badge: null },
  { name: 'New Document', href: '/app/editor', icon: PencilSquareIcon, badge: null },
  { name: 'My Documents', href: '/app/editor', icon: FolderIcon, badge: '12' },
  { name: 'Analytics', href: '/app/analytics', icon: ChartBarIcon, badge: 'New' },
  { name: 'Developers', href: '/app/developers', icon: CodeBracketIcon, badge: null },
  { name: 'Settings', href: '/app/settings', icon: Cog6ToothIcon, badge: null },
];

const recentDocs = [
  { id: 1, name: 'AI Research Paper', progress: 75, color: 'bg-blue-500' },
  { id: 2, name: 'Business Proposal', progress: 92, color: 'bg-green-500' },
  { id: 3, name: 'Creative Chapter', progress: 60, color: 'bg-purple-500' },
];

interface SidebarProps {
  isMobileMenuOpen?: boolean;
  onMobileMenuClose?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ 
  isMobileMenuOpen = false, 
  onMobileMenuClose 
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const location = useLocation();

  // Close mobile menu when route changes
  useEffect(() => {
    if (onMobileMenuClose) {
      onMobileMenuClose();
    }
  }, [location.pathname, onMobileMenuClose]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.classList.add('mobile-scroll-lock');
    } else {
      document.body.classList.remove('mobile-scroll-lock');
    }
    
    return () => {
      document.body.classList.remove('mobile-scroll-lock');
    };
  }, [isMobileMenuOpen]);

  return (
    <>
      {/* Desktop Sidebar */}
      <aside className="hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 lg:pt-16 lg:z-50">
        <div className="flex flex-col flex-grow backdrop-blur-xl bg-white/70 border-r border-slate-200/50 shadow-xl overflow-y-auto">
        {/* Logo section */}
        <div className="px-6 py-8 border-b border-slate-200/50">
          <div className="flex items-center group cursor-pointer">
            <div className="relative">
              <div className="w-12 h-12 rounded-2xl overflow-hidden shadow-lg group-hover:shadow-xl transition-shadow duration-200">
                <img 
                  src="/src/assets/logo.png" 
                  alt="Revisionary Logo" 
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
            </div>
            <div className="ml-4">
              <h1 className="text-xl font-bold text-slate-900 group-hover:text-purple-700 transition-colors">Revisionary</h1>
              <p className="text-sm text-slate-500 font-medium">AI Writing Studio</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2">
          {navigation.map((item) => (
            <NavLink
              key={item.name}
              to={item.href}
              className={({ isActive }) =>
                `group flex items-center justify-between px-4 py-3 text-sm font-semibold rounded-2xl transition-all duration-200 transform hover:scale-105 ${
                  isActive
                    ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg shadow-purple-500/25'
                    : 'text-slate-700 hover:text-slate-900 hover:bg-white/80 hover:shadow-md'
                }`
              }
            >
              {({ isActive }) => (
                <>
                  <div className="flex items-center">
                    <item.icon
                      className={`mr-4 h-5 w-5 flex-shrink-0 transition-colors ${
                        isActive ? 'text-white' : 'text-slate-600 group-hover:text-purple-600'
                      }`}
                      aria-hidden="true"
                    />
                    {item.name}
                  </div>
                  {item.badge && (
                    <span className={`px-2 py-1 text-xs font-bold rounded-full ${
                      isActive 
                        ? 'bg-white/20 text-white' 
                        : item.badge === 'New' 
                          ? 'bg-green-100 text-green-700'
                          : 'bg-slate-100 text-slate-600'
                    }`}>
                      {item.badge}
                    </span>
                  )}
                </>
              )}
            </NavLink>
          ))}
        </nav>

        {/* Quick Actions */}
        <div className="px-4 py-4 border-t border-slate-200/50">
          <h3 className="text-xs font-bold text-slate-500 uppercase tracking-wider mb-4">
            Quick Actions
          </h3>
          <div className="space-y-2">
            <button className="w-full flex items-center px-4 py-3 text-sm font-semibold text-white bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 group">
              <BoltIcon className="w-4 h-4 mr-3 group-hover:animate-bounce" />
              AI Suggestions
            </button>
            <button className="w-full flex items-center px-4 py-3 text-sm font-medium text-slate-700 bg-white/60 rounded-2xl shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-200 group border border-slate-200/50">
              <DocumentTextIcon className="w-4 h-4 mr-3 text-slate-500 group-hover:text-purple-600 transition-colors" />
              Templates
            </button>
          </div>
        </div>

        {/* Recent documents section */}
        <div className="px-4 py-4 border-t border-slate-200/50">
          <h3 className="text-xs font-bold text-slate-500 uppercase tracking-wider mb-4">
            Recent Work
          </h3>
          <div className="space-y-3">
            {recentDocs.map((doc) => (
              <div
                key={doc.id}
                className="group relative overflow-hidden bg-white/60 rounded-2xl p-4 border border-slate-200/50 hover:bg-white/80 hover:shadow-md cursor-pointer transition-all duration-200"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 ${doc.color} rounded-full mr-3 shadow-sm`}></div>
                    <span className="text-sm font-medium text-slate-700 truncate group-hover:text-purple-700 transition-colors">
                      {doc.name}
                    </span>
                  </div>
                  <span className="text-xs text-slate-500 font-medium">{doc.progress}%</span>
                </div>
                
                <div className="w-full bg-slate-200 rounded-full h-1.5 overflow-hidden">
                  <div 
                    className={`h-full bg-gradient-to-r ${doc.color.replace('bg-', 'from-')} to-transparent transition-all duration-500`}
                    style={{ width: `${doc.progress}%` }}
                  ></div>
                </div>
                
                {/* Hover effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer section */}
        <div className="px-4 py-4 border-t border-slate-200/50">
          <div className="flex items-center justify-between p-3 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl border border-slate-200/50">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
                <span className="text-xs font-bold text-white">DU</span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-semibold text-slate-900">Developer User</p>
                <p className="text-xs text-slate-500">Professional Plan</p>
              </div>
            </div>
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          </div>
        </div>
        </div>
      </aside>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="lg:hidden fixed inset-0 z-mobile-sidebar bg-black/50 backdrop-blur-sm"
            onClick={onMobileMenuClose}
          />
        )}
      </AnimatePresence>

      {/* Mobile Sidebar */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.aside
            initial={{ x: '-100%' }}
            animate={{ x: 0 }}
            exit={{ x: '-100%' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            className="lg:hidden fixed inset-y-0 left-0 z-mobile-sidebar w-80 max-w-[85vw] mobile-safe-area"
          >
            <div className="flex flex-col h-full bg-white shadow-2xl overflow-y-auto">
              {/* Mobile Header */}
              <div className="flex items-center justify-between p-4 border-b border-slate-200">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-lg overflow-hidden shadow-sm">
                    <img 
                      src="/src/assets/logo.png" 
                      alt="Revisionary Logo" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="ml-3">
                    <h1 className="text-lg font-bold text-slate-900">Revisionary</h1>
                    <p className="text-xs text-slate-500">AI Writing Studio</p>
                  </div>
                </div>
                <button
                  onClick={onMobileMenuClose}
                  className="mobile-icon-btn text-slate-600 hover:text-slate-900 hover:bg-slate-100"
                >
                  <XMarkIcon className="w-6 h-6" />
                </button>
              </div>

              {/* Mobile Navigation */}
              <nav className="flex-1 p-4 space-y-1">
                {navigation.map((item) => (
                  <NavLink
                    key={item.name}
                    to={item.href}
                    className={({ isActive }) =>
                      `group flex items-center justify-between mobile-btn rounded-xl transition-all duration-200 ${
                        isActive
                          ? 'bg-purple-600 text-white shadow-lg'
                          : 'text-slate-700 hover:text-slate-900 hover:bg-slate-50'
                      }`
                    }
                  >
                    {({ isActive }) => (
                      <>
                        <div className="flex items-center">
                          <item.icon
                            className={`mr-3 h-5 w-5 flex-shrink-0 transition-colors ${
                              isActive ? 'text-white' : 'text-slate-600 group-hover:text-purple-600'
                            }`}
                            aria-hidden="true"
                          />
                          <span className="font-medium">{item.name}</span>
                        </div>
                        {item.badge && (
                          <span className={`px-2 py-1 text-xs font-bold rounded-full ${
                            isActive 
                              ? 'bg-white/20 text-white' 
                              : item.badge === 'New' 
                                ? 'bg-green-100 text-green-700'
                                : 'bg-slate-100 text-slate-600'
                          }`}>
                            {item.badge}
                          </span>
                        )}
                      </>
                    )}
                  </NavLink>
                ))}
              </nav>

              {/* Mobile Quick Actions */}
              <div className="p-4 border-t border-slate-200">
                <h3 className="text-xs font-bold text-slate-500 uppercase tracking-wider mb-3">
                  Quick Actions
                </h3>
                <div className="space-y-2">
                  <button className="w-full flex items-center mobile-btn text-white bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl shadow-lg">
                    <BoltIcon className="w-4 h-4 mr-3" />
                    AI Suggestions
                  </button>
                  <button className="w-full flex items-center mobile-btn text-slate-700 bg-slate-50 rounded-xl border border-slate-200">
                    <DocumentTextIcon className="w-4 h-4 mr-3 text-slate-500" />
                    Templates
                  </button>
                </div>
              </div>

              {/* Mobile Recent Documents */}
              <div className="p-4 border-t border-slate-200">
                <h3 className="text-xs font-bold text-slate-500 uppercase tracking-wider mb-3">
                  Recent Work
                </h3>
                <div className="space-y-2">
                  {recentDocs.slice(0, 2).map((doc) => (
                    <div
                      key={doc.id}
                      className="flex items-center justify-between p-3 bg-slate-50 rounded-xl border border-slate-200 cursor-pointer hover:bg-slate-100 transition-colors"
                    >
                      <div className="flex items-center min-w-0 flex-1">
                        <div className={`w-2 h-2 ${doc.color} rounded-full mr-3 flex-shrink-0`}></div>
                        <span className="text-sm font-medium text-slate-700 truncate">
                          {doc.name}
                        </span>
                      </div>
                      <span className="text-xs text-slate-500 font-medium ml-2">{doc.progress}%</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Mobile User Section */}
              <div className="p-4 border-t border-slate-200">
                <div className="flex items-center p-3 bg-slate-50 rounded-xl">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-white">DU</span>
                  </div>
                  <div className="ml-3 min-w-0 flex-1">
                    <p className="text-sm font-semibold text-slate-900 truncate">Developer User</p>
                    <p className="text-xs text-slate-500">Professional Plan</p>
                  </div>
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>
          </motion.aside>
        )}
      </AnimatePresence>

      {/* Mobile Bottom Navigation */}
      <nav className="lg:hidden mobile-tab-bar">
        <div className="flex items-center justify-around px-4 py-2">
          {navigation.slice(0, 4).map((item) => (
            <NavLink
              key={item.name}
              to={item.href}
              className={({ isActive }) =>
                `relative flex flex-col items-center mobile-icon-btn rounded-xl transition-all duration-200 ${
                  isActive
                    ? 'text-purple-600'
                    : 'text-slate-600 hover:text-slate-900'
                }`
              }
            >
              {({ isActive }) => (
                <>
                  <item.icon className="w-6 h-6 mb-1" />
                  <span className="text-xs font-medium">{item.name.split(' ')[0]}</span>
                  {item.badge && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-white">!</span>
                    </div>
                  )}
                  {isActive && (
                    <motion.div
                      layoutId="mobile-nav-indicator"
                      className="absolute -bottom-2 w-1 h-1 bg-purple-600 rounded-full"
                      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                    />
                  )}
                </>
              )}
            </NavLink>
          ))}
          <button 
            onClick={() => {/* More menu handler */}}
            className="flex flex-col items-center mobile-icon-btn text-slate-600 hover:text-slate-900 rounded-xl transition-colors"
          >
            <Bars3Icon className="w-6 h-6 mb-1" />
            <span className="text-xs font-medium">More</span>
          </button>
        </div>
      </nav>
    </>
  );
};

export default Sidebar;