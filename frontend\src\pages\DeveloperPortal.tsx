import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  CodeBracketIcon, 
  DocumentTextIcon, 
  PlayIcon,
  CubeIcon,
  CommandLineIcon,
  BookOpenIcon,
  ClipboardDocumentIcon,
  ChartBarIcon,
  KeyIcon,
  CogIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { useUser } from '@/stores/authStore';
import DocumentationViewer from '@/components/developer/DocumentationViewer';
import ApiExplorer from '@/components/developer/ApiExplorer';

const DeveloperPortal: React.FC = () => {
  const user = useUser();
  const [mounted, setMounted] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    setMounted(true);
  }, []);

  const navigation = [
    { id: 'overview', name: 'Overview', icon: DocumentTextIcon },
    { id: 'api-explorer', name: 'API Explorer', icon: PlayIcon },
    { id: 'documentation', name: 'Documentation', icon: BookOpenIcon },
    { id: 'authentication', name: 'Authentication', icon: KeyIcon },
    { id: 'quota', name: 'Usage & Quota', icon: ChartBarIcon },
    { id: 'tools', name: 'Developer Tools', icon: CogIcon },
  ];

  const quickStartCards = [
    {
      title: 'Authentication Guide',
      description: 'Learn how to authenticate API requests with JWT tokens',
      icon: KeyIcon,
      color: 'from-blue-500 to-indigo-500',
      action: 'View Guide'
    },
    {
      title: 'API Reference',
      description: 'Complete documentation of all available endpoints',
      icon: BookOpenIcon,
      color: 'from-green-500 to-emerald-500',
      action: 'Browse API'
    },
    {
      title: 'Interactive Testing',
      description: 'Test API endpoints directly from your browser',
      icon: PlayIcon,
      color: 'from-purple-500 to-violet-500',
      action: 'Start Testing'
    },
    {
      title: 'Code Examples',
      description: 'Copy-paste code samples in multiple languages',
      icon: CodeBracketIcon,
      color: 'from-orange-500 to-red-500',
      action: 'Get Samples'
    }
  ];

  const apiEndpoints = [
    {
      method: 'GET',
      path: '/api/v1/analytics/token-usage',
      description: 'Get current token usage and quota information',
      status: 'stable'
    },
    {
      method: 'GET',
      path: '/api/v1/documents',
      description: 'List user documents with pagination',
      status: 'stable'
    },
    {
      method: 'POST',
      path: '/api/v1/documents',
      description: 'Create a new document',
      status: 'stable'
    },
    {
      method: 'GET',
      path: '/api/v1/auth/profile',
      description: 'Get current user profile information',
      status: 'stable'
    },
    {
      method: 'POST',
      path: '/api/v1/blocks/generate',
      description: 'Generate AI content blocks',
      status: 'beta'
    }
  ];

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'bg-blue-100 text-blue-800';
      case 'POST': return 'bg-green-100 text-green-800';
      case 'PUT': return 'bg-yellow-100 text-yellow-800';
      case 'DELETE': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'stable': return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
      case 'beta': return <ExclamationTriangleIcon className="w-4 h-4 text-yellow-500" />;
      case 'deprecated': return <InformationCircleIcon className="w-4 h-4 text-red-500" />;
      default: return <InformationCircleIcon className="w-4 h-4 text-gray-500" />;
    }
  };

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-slate-900 via-purple-900 to-slate-900 rounded-2xl p-8 text-white">
        <div className="flex items-center mb-4">
          <CodeBracketIcon className="w-8 h-8 mr-3 text-purple-300" />
          <h1 className="text-3xl font-bold">Developer Portal</h1>
        </div>
        <p className="text-lg text-white/80 mb-6">
          Welcome to the Revisionary API documentation and testing portal. 
          Build powerful writing applications with our comprehensive API.
        </p>
        <div className="flex flex-wrap gap-4">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100/20 text-green-300">
            <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
            All Systems Operational
          </span>
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100/20 text-blue-300">
            API Version 1.0
          </span>
        </div>
      </div>

      {/* Quick Start Cards */}
      <div>
        <h2 className="text-2xl font-bold text-slate-900 mb-6">Quick Start</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickStartCards.map((card, index) => {
            const Icon = card.icon;
            return (
              <div
                key={card.title}
                className="group relative overflow-hidden bg-white rounded-xl p-6 shadow-md hover:shadow-xl border border-slate-100 transform hover:scale-105 transition-all duration-300"
              >
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${card.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-slate-900 mb-2">{card.title}</h3>
                <p className="text-sm text-slate-600 mb-4">{card.description}</p>
                <button className="text-sm font-medium text-purple-600 hover:text-purple-700 flex items-center">
                  {card.action}
                  <PlayIcon className="w-4 h-4 ml-1" />
                </button>
              </div>
            );
          })}
        </div>
      </div>

      {/* API Endpoints Preview */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-slate-900">Popular Endpoints</h2>
          <button 
            onClick={() => setActiveTab('api-explorer')}
            className="text-purple-600 hover:text-purple-700 font-medium flex items-center"
          >
            View All Endpoints
            <CommandLineIcon className="w-4 h-4 ml-1" />
          </button>
        </div>
        <div className="bg-white rounded-xl border border-slate-200 overflow-hidden">
          {apiEndpoints.slice(0, 5).map((endpoint, index) => (
            <div key={`${endpoint.method}-${endpoint.path}`} className={`p-4 border-b border-slate-100 hover:bg-slate-50 transition-colors ${index === apiEndpoints.length - 1 ? 'border-b-0' : ''}`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className={`px-2 py-1 rounded text-xs font-mono font-semibold ${getMethodColor(endpoint.method)}`}>
                    {endpoint.method}
                  </span>
                  <code className="text-sm font-mono text-slate-700">{endpoint.path}</code>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(endpoint.status)}
                  <span className="text-xs text-slate-500 capitalize">{endpoint.status}</span>
                </div>
              </div>
              <p className="text-sm text-slate-600 mt-2">{endpoint.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderApiExplorer = () => <ApiExplorer />;

  const renderDocumentation = () => <DocumentationViewer />;

  const renderAuthentication = () => (
    <div className="space-y-8">
      <div className="text-center">
        <KeyIcon className="w-16 h-16 text-purple-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-slate-900 mb-2">Authentication</h2>
        <p className="text-slate-600">Authentication guides and token management coming soon</p>
      </div>
    </div>
  );

  const renderQuota = () => (
    <div className="space-y-8">
      <div className="text-center">
        <ChartBarIcon className="w-16 h-16 text-purple-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-slate-900 mb-2">Usage & Quota</h2>
        <p className="text-slate-600">API usage monitoring and quota management coming soon</p>
      </div>
    </div>
  );

  const renderTools = () => (
    <div className="space-y-8">
      <div className="text-center">
        <CogIcon className="w-16 h-16 text-purple-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-slate-900 mb-2">Developer Tools</h2>
        <p className="text-slate-600">SDK downloads, code generators, and testing tools coming soon</p>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview': return renderOverview();
      case 'api-explorer': return renderApiExplorer();
      case 'documentation': return renderDocumentation();
      case 'authentication': return renderAuthentication();
      case 'quota': return renderQuota();
      case 'tools': return renderTools();
      default: return renderOverview();
    }
  };

  return (
    <div className="mobile-full-height bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.03),transparent_50%)] hidden md:block"></div>
      
      <div className="relative z-10 max-w-7xl mx-auto mobile-px sm:px-8 lg:px-10 py-4 lg:py-8 lg:pl-80">
        
        {/* Navigation Tabs */}
        <div className={`mb-8 transform transition-all duration-1000 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <div className="border-b border-slate-200">
            <nav className="-mb-px flex space-x-8 overflow-x-auto">
              {navigation.map((tab) => {
                const Icon = tab.icon;
                const isActive = activeTab === tab.id;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                      isActive
                        ? 'border-purple-500 text-purple-600'
                        : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                    }`}
                  >
                    <Icon className={`w-5 h-5 mr-2 ${isActive ? 'text-purple-500' : 'text-slate-400 group-hover:text-slate-500'}`} />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className={`transform transition-all duration-1000 delay-200 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

export default DeveloperPortal;