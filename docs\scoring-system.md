# Revisionary - Writing Quality Scoring System

## 1. Overview

The Revisionary Scoring System provides consistent, objective evaluation of writing quality across all document types. It tracks improvement over time, offers personalized feedback, and gamifies the writing improvement process.

## 2. Core Scoring Framework

### 2.1 Universal Scoring Dimensions

All documents are evaluated across four core dimensions, weighted differently based on document type:

```yaml
Core Dimensions:
  Grammar & Mechanics: 0-25 points
    - Spelling accuracy
    - Grammar correctness
    - Punctuation usage
    - Syntax structure
    
  Style & Clarity: 0-25 points
    - Readability level
    - Word choice precision
    - Sentence variety
    - Conciseness
    
  Structure & Organization: 0-25 points
    - Logical flow
    - Paragraph transitions
    - Section coherence
    - Overall architecture
    
  Content Quality: 0-25 points
    - Depth of analysis
    - Evidence support
    - Consistency
    - Purpose alignment

Total Score: 0-100 points
```

### 2.2 Document Type Weightings

Different writing types emphasize different aspects:

```yaml
Creative Writing:
  Grammar & Mechanics: 15%
  Style & Clarity: 35%
  Structure & Organization: 25%
  Content Quality: 25%

Academic Writing:
  Grammar & Mechanics: 20%
  Style & Clarity: 20%
  Structure & Organization: 30%
  Content Quality: 30%

Professional Writing:
  Grammar & Mechanics: 25%
  Style & Clarity: 30%
  Structure & Organization: 25%
  Content Quality: 20%

Technical Writing:
  Grammar & Mechanics: 30%
  Style & Clarity: 25%
  Structure & Organization: 25%
  Content Quality: 20%

General Writing:
  Grammar & Mechanics: 25%
  Style & Clarity: 25%
  Structure & Organization: 25%
  Content Quality: 25%
```

## 3. Detailed Scoring Criteria

### 3.1 Grammar & Mechanics (0-25 points)

**Excellent (22-25 points):**
- 0-1 grammar errors per 1000 words
- Perfect spelling and punctuation
- Sophisticated syntax usage
- No run-on sentences or fragments

**Good (18-21 points):**
- 2-3 grammar errors per 1000 words
- Minor spelling/punctuation issues
- Generally correct syntax
- Occasional awkward constructions

**Fair (14-17 points):**
- 4-6 grammar errors per 1000 words
- Noticeable spelling/punctuation errors
- Some syntax problems
- Multiple awkward sentences

**Poor (0-13 points):**
- 7+ grammar errors per 1000 words
- Frequent spelling/punctuation errors
- Major syntax problems
- Difficult to understand

### 3.2 Style & Clarity (0-25 points)

**Metrics Evaluated:**
- **Readability Score**: Flesch-Kincaid, Gunning Fog Index
- **Vocabulary Diversity**: Type-Token Ratio, unique words
- **Sentence Variety**: Length variation, structure diversity
- **Conciseness**: Unnecessary words, redundancy
- **Tone Consistency**: Appropriate for audience and purpose

**Excellent (22-25 points):**
- Optimal readability for target audience
- Rich, precise vocabulary
- Excellent sentence variety
- Clear, concise expression
- Perfect tone consistency

**Algorithm Example:**
```python
def calculate_style_score(text, document_type, target_audience):
    scores = {
        'readability': assess_readability(text, target_audience),
        'vocabulary': assess_vocabulary_diversity(text),
        'sentence_variety': assess_sentence_variety(text),
        'conciseness': assess_conciseness(text),
        'tone': assess_tone_consistency(text, document_type)
    }
    
    weights = get_style_weights(document_type)
    return sum(score * weight for score, weight in zip(scores.values(), weights))
```

### 3.3 Structure & Organization (0-25 points)

**Document Type Specific Criteria:**

**Academic Papers:**
- Clear thesis statement
- Logical argument progression
- Proper section organization (Introduction → Methods → Results → Discussion)
- Effective transitions between ideas
- Strong conclusion that ties back to thesis

**Creative Writing:**
- Compelling opening hook
- Appropriate story structure (3-act, hero's journey, etc.)
- Character development consistency
- Scene transitions and pacing
- Satisfying resolution

**Professional Documents:**
- Executive summary (if applicable)
- Clear action items and recommendations
- Logical presentation of data/findings
- Appropriate use of headers and formatting
- Clear next steps

**Scoring Algorithm:**
```python
def calculate_structure_score(document):
    structure_elements = analyze_document_structure(document)
    
    scores = {
        'introduction_strength': assess_opening(document),
        'logical_flow': assess_paragraph_transitions(document),
        'section_organization': assess_section_order(document),
        'conclusion_effectiveness': assess_conclusion(document),
        'overall_coherence': assess_document_coherence(document)
    }
    
    return weighted_average(scores, document.type)
```

### 3.4 Content Quality (0-25 points)

**Universal Criteria:**
- **Depth of Analysis**: Surface-level vs. thorough exploration
- **Evidence Support**: Claims backed by appropriate evidence
- **Logical Consistency**: No contradictions or gaps
- **Originality**: Fresh insights vs. clichéd content
- **Purpose Fulfillment**: Meets stated objectives

**Domain-Specific Additions:**

**Academic:**
- Citation quality and relevance
- Methodology appropriateness
- Data interpretation accuracy
- Literature review comprehensiveness

**Creative:**
- Character development depth
- Plot consistency and pacing
- Theme development
- Emotional resonance

**Professional:**
- Data accuracy and relevance
- Actionable recommendations
- Risk assessment inclusion
- Stakeholder consideration

## 4. Real-time Scoring Engine

### 4.1 Scoring Architecture

```python
class WritingScorer:
    def __init__(self):
        self.grammar_analyzer = GrammarAnalyzer()
        self.style_analyzer = StyleAnalyzer()
        self.structure_analyzer = StructureAnalyzer()
        self.content_analyzer = ContentAnalyzer()
    
    async def score_document(self, document: Document) -> DocumentScore:
        # Run parallel analysis
        tasks = [
            self.grammar_analyzer.analyze(document),
            self.style_analyzer.analyze(document),
            self.structure_analyzer.analyze(document),
            self.content_analyzer.analyze(document)
        ]
        
        grammar, style, structure, content = await asyncio.gather(*tasks)
        
        # Apply document type weights
        weights = self.get_weights(document.type)
        
        total_score = (
            grammar.score * weights.grammar +
            style.score * weights.style +
            structure.score * weights.structure +
            content.score * weights.content
        )
        
        return DocumentScore(
            total=total_score,
            grammar=grammar,
            style=style,
            structure=structure,
            content=content,
            timestamp=datetime.utcnow()
        )
```

### 4.2 Incremental Scoring

For real-time feedback during editing:

```python
class IncrementalScorer:
    async def score_block_change(self, block: Block, change: TextChange):
        # Only re-score affected dimensions
        affected_scores = []
        
        if change.affects_grammar():
            affected_scores.append('grammar')
        
        if change.affects_style():
            affected_scores.append('style')
        
        # Update only necessary scores
        updated_scores = await self.update_scores(block, affected_scores)
        
        # Recalculate document total
        return await self.recalculate_document_score(block.document_id)
```

## 5. Progress Tracking System

### 5.1 Database Schema

```sql
-- Document scores over time
CREATE TABLE document_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    total_score DECIMAL(5,2) NOT NULL,
    grammar_score DECIMAL(5,2) NOT NULL,
    style_score DECIMAL(5,2) NOT NULL,
    structure_score DECIMAL(5,2) NOT NULL,
    content_score DECIMAL(5,2) NOT NULL,
    word_count INTEGER NOT NULL,
    document_type document_type_enum NOT NULL,
    detailed_breakdown JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT document_scores_total_range CHECK (total_score >= 0 AND total_score <= 100),
    CONSTRAINT document_scores_component_range CHECK (
        grammar_score >= 0 AND grammar_score <= 25 AND
        style_score >= 0 AND style_score <= 25 AND
        structure_score >= 0 AND structure_score <= 25 AND
        content_score >= 0 AND content_score <= 25
    )
);

-- User progress aggregations
CREATE TABLE user_writing_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    document_type document_type_enum NOT NULL,
    date DATE NOT NULL,
    average_score DECIMAL(5,2) NOT NULL,
    documents_scored INTEGER NOT NULL,
    total_words INTEGER NOT NULL,
    improvement_trend DECIMAL(5,2), -- Change from previous period
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id, document_type, date)
);

-- Scoring milestones and achievements
CREATE TABLE writing_achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    achievement_type VARCHAR(50) NOT NULL,
    achievement_name VARCHAR(100) NOT NULL,
    description TEXT,
    score_requirement DECIMAL(5,2),
    earned_at TIMESTAMPTZ DEFAULT NOW(),
    document_id UUID REFERENCES documents(id) ON DELETE SET NULL
);
```

### 5.2 Progress Analytics

```python
class ProgressTracker:
    async def get_user_progress(self, user_id: str, timeframe: str = '30d'):
        # Get score history
        scores = await self.db.query("""
            SELECT 
                date_trunc('day', created_at) as date,
                document_type,
                AVG(total_score) as avg_score,
                COUNT(*) as documents_scored
            FROM document_scores 
            WHERE user_id = $1 
                AND created_at >= NOW() - INTERVAL %s
            GROUP BY date, document_type
            ORDER BY date DESC
        """, user_id, timeframe)
        
        # Calculate trends
        trends = self.calculate_trends(scores)
        
        # Generate insights
        insights = self.generate_insights(scores, trends)
        
        return UserProgress(
            scores=scores,
            trends=trends,
            insights=insights,
            achievements=await self.get_recent_achievements(user_id)
        )
```

## 6. Gamification & Motivation

### 6.1 Achievement System

```yaml
Writing Achievements:
  Grammar Master:
    - Bronze: Average 18+ grammar score for 5 documents
    - Silver: Average 20+ grammar score for 10 documents  
    - Gold: Average 22+ grammar score for 20 documents
    - Platinum: Average 24+ grammar score for 50 documents
  
  Style Savant:
    - Bronze: Average 18+ style score for 5 documents
    - Silver: Average 20+ style score for 10 documents
    - Gold: Average 22+ style score for 20 documents
    - Platinum: Average 24+ style score for 50 documents
  
  Structure Specialist:
    - Bronze: Average 18+ structure score for 5 documents
    - Silver: Average 20+ structure score for 10 documents
    - Gold: Average 22+ structure score for 20 documents
    - Platinum: Average 24+ structure score for 50 documents
  
  Content Creator:
    - Bronze: Average 18+ content score for 5 documents
    - Silver: Average 20+ content score for 10 documents
    - Gold: Average 22+ content score for 20 documents
    - Platinum: Average 24+ content score for 50 documents

Improvement Achievements:
  Rising Star: 10+ point improvement in 30 days
  Consistent Improver: Positive trend for 7 consecutive documents
  Perfect Score: Achieve 95+ total score on any document
  Prolific Writer: Score 50+ documents in a month
  
Document Type Specialists:
  Academic Scholar: Average 85+ on 10 academic documents
  Creative Genius: Average 85+ on 10 creative documents  
  Professional Powerhouse: Average 85+ on 10 professional documents
```

### 6.2 Daily/Weekly Challenges

```python
class WritingChallenges:
    def generate_daily_challenge(self, user: User) -> Challenge:
        # Analyze user's weakest area
        weak_area = self.identify_weak_area(user)
        
        challenges = {
            'grammar': [
                "Write 500 words with zero grammar errors",
                "Use at least 5 complex sentence structures",
                "Perfect punctuation in a 1000-word piece"
            ],
            'style': [
                "Write using exactly 15 different sentence lengths",
                "Use 50+ unique vocabulary words in 500 words",
                "Achieve Flesch Reading Ease score of 60-70"
            ],
            'structure': [
                "Write with perfect paragraph transitions",
                "Create a document with 5-part structure",
                "Write compelling intro and conclusion"
            ],
            'content': [
                "Include 3 strong supporting arguments",
                "Write with zero logical inconsistencies",
                "Create original insights on familiar topic"
            ]
        }
        
        return random.choice(challenges[weak_area])
```

## 7. User Interface Integration

### 7.1 Score Display Components

**Document Score Card:**
```typescript
interface DocumentScore {
  total: number;
  breakdown: {
    grammar: number;
    style: number;
    structure: number;
    content: number;
  };
  trend: 'improving' | 'declining' | 'stable';
  lastUpdated: Date;
  suggestions: string[];
}

const ScoreCard: React.FC<{score: DocumentScore}> = ({ score }) => (
  <div className="score-card">
    <CircularProgress value={score.total} max={100} />
    <div className="breakdown">
      {Object.entries(score.breakdown).map(([category, value]) => (
        <ProgressBar 
          key={category}
          label={category}
          value={value}
          max={25}
          color={getScoreColor(value)}
        />
      ))}
    </div>
    <TrendIndicator trend={score.trend} />
    <ImprovementSuggestions suggestions={score.suggestions} />
  </div>
);
```

**Progress Dashboard:**
```typescript
const ProgressDashboard: React.FC = () => {
  const { progress } = useUserProgress();
  
  return (
    <Dashboard>
      <ScoreTrendChart data={progress.scoreHistory} />
      <AchievementGallery achievements={progress.achievements} />
      <WeakAreaAnalysis areas={progress.weakAreas} />
      <DailyChallenges challenges={progress.challenges} />
      <WritingStreak streak={progress.currentStreak} />
    </Dashboard>
  );
};
```

### 7.2 Real-time Score Updates

```typescript
const useDocumentScore = (documentId: string) => {
  const [score, setScore] = useState<DocumentScore | null>(null);
  
  useEffect(() => {
    // Subscribe to score updates
    const subscription = subscribe(`score:${documentId}`, (newScore) => {
      setScore(newScore);
    });
    
    // Get initial score
    fetchDocumentScore(documentId).then(setScore);
    
    return () => subscription.unsubscribe();
  }, [documentId]);
  
  return score;
};
```

## 8. API Endpoints

### 8.1 Scoring Endpoints

```python
@app.get("/api/v1/documents/{document_id}/score")
async def get_document_score(document_id: str, user: User = Depends(get_user)):
    """Get current document score with breakdown"""
    score = await scoring_service.get_document_score(document_id)
    return {
        "total": score.total,
        "breakdown": score.breakdown,
        "last_updated": score.timestamp,
        "suggestions": score.improvement_suggestions
    }

@app.post("/api/v1/documents/{document_id}/score/refresh")
async def refresh_document_score(document_id: str, user: User = Depends(get_user)):
    """Recalculate document score"""
    score = await scoring_service.calculate_score(document_id)
    await scoring_service.save_score(score)
    return score

@app.get("/api/v1/users/{user_id}/progress")
async def get_user_progress(
    user_id: str, 
    timeframe: str = "30d",
    document_type: Optional[str] = None,
    user: User = Depends(get_user)
):
    """Get user writing progress and trends"""
    progress = await progress_tracker.get_user_progress(
        user_id, timeframe, document_type
    )
    return progress

@app.get("/api/v1/users/{user_id}/achievements")
async def get_user_achievements(user_id: str, user: User = Depends(get_user)):
    """Get user achievements and progress toward goals"""
    achievements = await achievement_service.get_user_achievements(user_id)
    return achievements
```

## 9. Scoring Algorithm Details

### 9.1 Grammar Scoring Algorithm

```python
async def score_grammar(text: str) -> GrammarScore:
    # Use language tool for grammar checking
    errors = await grammar_checker.check(text)
    
    # Calculate error density
    word_count = len(text.split())
    error_density = len(errors) / (word_count / 1000)  # errors per 1000 words
    
    # Score based on error density
    if error_density <= 1:
        base_score = 25
    elif error_density <= 3:
        base_score = 21
    elif error_density <= 6:
        base_score = 17
    else:
        base_score = max(0, 15 - error_density)
    
    # Adjust for error severity
    severity_penalty = sum(error.severity_weight for error in errors)
    final_score = max(0, base_score - severity_penalty)
    
    return GrammarScore(
        score=final_score,
        error_count=len(errors),
        error_density=error_density,
        errors=errors
    )
```

### 9.2 Style Scoring Algorithm

```python
async def score_style(text: str, document_type: str) -> StyleScore:
    metrics = {
        'readability': calculate_readability(text),
        'vocabulary_diversity': calculate_vocabulary_diversity(text),
        'sentence_variety': calculate_sentence_variety(text),
        'conciseness': calculate_conciseness(text),
        'tone_consistency': await assess_tone_consistency(text, document_type)
    }
    
    # Weighted average based on document type
    weights = get_style_weights(document_type)
    final_score = sum(
        metrics[metric] * weights[metric] 
        for metric in metrics
    )
    
    return StyleScore(
        score=final_score,
        metrics=metrics,
        recommendations=generate_style_recommendations(metrics)
    )
```

This comprehensive scoring system provides users with detailed, actionable feedback on their writing while gamifying the improvement process. It adapts to different writing types while maintaining consistency and helping users track their progress over time.