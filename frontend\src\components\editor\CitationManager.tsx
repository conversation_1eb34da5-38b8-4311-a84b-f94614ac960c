import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BookOpenIcon,
  PencilSquareIcon,
  TrashIcon,
  DocumentDuplicateIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  ShareIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

interface Citation {
  id: string;
  title: string;
  authors: string[];
  year: number;
  source: string;
  type: 'journal' | 'book' | 'website' | 'report' | 'article';
  doi?: string;
  url?: string;
  abstract?: string;
  tags?: string[];
  style: 'apa' | 'mla' | 'chicago';
  usageCount: number;
  dateAdded: Date;
}

interface CitationManagerProps {
  isOpen: boolean;
  onClose: () => void;
  citations: Citation[];
  onAddCitation: () => void;
  onEditCitation: (citation: Citation) => void;
  onDeleteCitation: (id: string) => void;
  onInsertCitation: (citation: Citation) => void;
  onExportBibliography: (style: string, citations: Citation[]) => void;
}

const CitationManager: React.FC<CitationManagerProps> = ({
  isOpen,
  onClose,
  citations,
  onAddCitation,
  onEditCitation,
  onDeleteCitation,
  onInsertCitation,
  onExportBibliography,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'title' | 'year' | 'usage' | 'date'>('title');
  const [selectedCitations, setSelectedCitations] = useState<Set<string>>(new Set());
  const [citationStyle, setCitationStyle] = useState<'apa' | 'mla' | 'chicago'>('apa');

  const filteredCitations = citations
    .filter(citation => {
      const matchesSearch = searchQuery === '' || 
        citation.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        citation.authors.some(author => author.toLowerCase().includes(searchQuery.toLowerCase())) ||
        citation.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesType = filterType === 'all' || citation.type === filterType;
      
      return matchesSearch && matchesType;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'title':
          return a.title.localeCompare(b.title);
        case 'year':
          return b.year - a.year;
        case 'usage':
          return b.usageCount - a.usageCount;
        case 'date':
          return b.dateAdded.getTime() - a.dateAdded.getTime();
        default:
          return 0;
      }
    });

  const handleSelectCitation = (id: string) => {
    const newSelected = new Set(selectedCitations);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedCitations(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedCitations.size === filteredCitations.length) {
      setSelectedCitations(new Set());
    } else {
      setSelectedCitations(new Set(filteredCitations.map(c => c.id)));
    }
  };

  const formatCitation = (citation: Citation, style: string) => {
    const authors = citation.authors.join(', ');
    switch (style) {
      case 'apa':
        return `${authors} (${citation.year}). ${citation.title}. ${citation.source}.`;
      case 'mla':
        return `${authors}. "${citation.title}." ${citation.source}, ${citation.year}.`;
      case 'chicago':
        return `${authors}. "${citation.title}." ${citation.source} (${citation.year}).`;
      default:
        return `${authors} (${citation.year}). ${citation.title}. ${citation.source}.`;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'journal': return '📄';
      case 'book': return '📚';
      case 'website': return '🌐';
      case 'article': return '📰';
      case 'report': return '📊';
      default: return '📄';
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-[9998]"
            onClick={onClose}
          />

          {/* Panel */}
          <motion.div
            initial={{ opacity: 0, x: 400 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 400 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            className="fixed right-0 top-0 bottom-0 w-[600px] bg-white/95 backdrop-blur-xl border-l border-slate-200/50 shadow-2xl z-[9999] flex flex-col"
          >
            {/* Header */}
            <div className="px-6 py-4 border-b border-slate-200/50 bg-gradient-to-r from-blue-50/50 to-purple-50/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                    <BookOpenIcon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg font-bold text-slate-900">Citation Manager</h2>
                    <p className="text-sm text-slate-600">{citations.length} citations</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>

              {/* Actions Bar */}
              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={onAddCitation}
                    className="flex items-center px-3 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg text-sm font-medium hover:shadow-md transition-all"
                  >
                    <PlusIcon className="w-4 h-4 mr-2" />
                    Add Citation
                  </button>

                  {selectedCitations.size > 0 && (
                    <button
                      onClick={() => {
                        const selected = citations.filter(c => selectedCitations.has(c.id));
                        onExportBibliography(citationStyle, selected);
                      }}
                      className="flex items-center px-3 py-2 bg-white border border-slate-200 text-slate-700 rounded-lg text-sm font-medium hover:bg-slate-50 transition-colors"
                    >
                      <ShareIcon className="w-4 h-4 mr-2" />
                      Export ({selectedCitations.size})
                    </button>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <select
                    value={citationStyle}
                    onChange={(e) => setCitationStyle(e.target.value as any)}
                    className="px-3 py-2 text-sm border border-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="apa">APA</option>
                    <option value="mla">MLA</option>
                    <option value="chicago">Chicago</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="px-6 py-4 border-b border-slate-200/50 space-y-3">
              {/* Search */}
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <input
                  type="text"
                  placeholder="Search citations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-slate-50/50 border border-slate-200 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Filters */}
              <div className="flex items-center space-x-3">
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="flex-1 px-3 py-2 text-sm border border-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Types</option>
                  <option value="journal">Journals</option>
                  <option value="book">Books</option>
                  <option value="website">Websites</option>
                  <option value="article">Articles</option>
                  <option value="report">Reports</option>
                </select>

                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="flex-1 px-3 py-2 text-sm border border-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="title">Sort by Title</option>
                  <option value="year">Sort by Year</option>
                  <option value="usage">Sort by Usage</option>
                  <option value="date">Sort by Date Added</option>
                </select>

                <button
                  onClick={handleSelectAll}
                  className="px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                >
                  {selectedCitations.size === filteredCitations.length ? 'Deselect All' : 'Select All'}
                </button>
              </div>
            </div>

            {/* Citations List */}
            <div className="flex-1 overflow-y-auto">
              {filteredCitations.length > 0 ? (
                <div className="p-4 space-y-3">
                  {filteredCitations.map((citation, index) => (
                    <motion.div
                      key={citation.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className={`bg-white rounded-xl border-2 p-4 transition-all duration-200 hover:shadow-md ${
                        selectedCitations.has(citation.id)
                          ? 'border-blue-200 bg-blue-50/50'
                          : 'border-slate-200 hover:border-slate-300'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <input
                          type="checkbox"
                          checked={selectedCitations.has(citation.id)}
                          onChange={() => handleSelectCitation(citation.id)}
                          className="mt-1 w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500"
                        />

                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <span className="text-lg">{getTypeIcon(citation.type)}</span>
                              <span className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                                {citation.type}
                              </span>
                              {citation.usageCount > 0 && (
                                <span className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                                  Used {citation.usageCount}x
                                </span>
                              )}
                            </div>
                            <div className="flex items-center space-x-1">
                              <button
                                onClick={() => onInsertCitation(citation)}
                                className="p-1.5 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
                                title="Insert Citation"
                              >
                                <DocumentDuplicateIcon className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => onEditCitation(citation)}
                                className="p-1.5 text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
                                title="Edit Citation"
                              >
                                <PencilSquareIcon className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => onDeleteCitation(citation.id)}
                                className="p-1.5 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
                                title="Delete Citation"
                              >
                                <TrashIcon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>

                          <h4 className="font-semibold text-slate-900 mb-1 line-clamp-2">
                            {citation.title}
                          </h4>

                          <p className="text-sm text-slate-600 mb-2">
                            {citation.authors.join(', ')} ({citation.year})
                          </p>

                          <p className="text-sm text-slate-500 mb-2">{citation.source}</p>

                          {citation.abstract && (
                            <p className="text-xs text-slate-500 line-clamp-2 mb-2">
                              {citation.abstract}
                            </p>
                          )}

                          {citation.tags && citation.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mb-3">
                              {citation.tags.map((tag) => (
                                <span
                                  key={tag}
                                  className="px-2 py-1 text-xs bg-slate-100 text-slate-600 rounded-md"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                          )}

                          {/* Formatted Citation Preview */}
                          <div className="mt-3 p-3 bg-slate-50 rounded-lg">
                            <p className="text-xs font-medium text-slate-700 mb-1">
                              {citationStyle.toUpperCase()} Format:
                            </p>
                            <p className="text-xs text-slate-600 italic">
                              {formatCitation(citation, citationStyle)}
                            </p>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                  <BookOpenIcon className="w-16 h-16 text-slate-300 mb-4" />
                  <h3 className="text-lg font-semibold text-slate-900 mb-2">
                    {searchQuery || filterType !== 'all' ? 'No matching citations' : 'No citations yet'}
                  </h3>
                  <p className="text-slate-600 mb-6">
                    {searchQuery || filterType !== 'all' 
                      ? 'Try adjusting your search or filters'
                      : 'Start by adding your first citation'
                    }
                  </p>
                  <button
                    onClick={onAddCitation}
                    className="flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg font-medium hover:shadow-md transition-all"
                  >
                    <PlusIcon className="w-4 h-4 mr-2" />
                    Add Citation
                  </button>
                </div>
              )}
            </div>

            {/* Footer Stats */}
            {citations.length > 0 && (
              <div className="px-6 py-3 bg-slate-50/50 border-t border-slate-200/50">
                <div className="flex items-center justify-between text-xs text-slate-500">
                  <span>
                    Showing {filteredCitations.length} of {citations.length} citations
                  </span>
                  <span>
                    {selectedCitations.size > 0 && `${selectedCitations.size} selected`}
                  </span>
                </div>
              </div>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default CitationManager;