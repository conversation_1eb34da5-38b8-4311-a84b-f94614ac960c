/**
 * Custom Agents API Service
 * 
 * Handles all custom agent-related API calls including:
 * - Agent CRUD operations
 * - Agent execution and management
 * - Style rules configuration
 * - Performance analytics and optimization
 */

import { baseApi, ApiResponse } from './baseApi';

export interface CustomAgent {
  id: string;
  user_id: string;
  name: string;
  description: string;
  type: 'grammar' | 'style' | 'structure' | 'content' | 'research' | 'creative' | 'technical' | 'custom';
  capabilities: string[];
  style_rules: {
    tone_guidelines: string[];
    vocabulary_preferences: {
      preferred_words: string[];
      avoid_words: string[];
      formality_level: 'casual' | 'professional' | 'academic' | 'creative';
    };
    structure_preferences: {
      sentence_length: 'short' | 'medium' | 'long' | 'varied';
      paragraph_style: 'concise' | 'detailed' | 'flowing';
      heading_style: string;
    };
    content_focus: string[];
    brand_voice?: {
      characteristics: string[];
      do_list: string[];
      dont_list: string[];
    };
  };
  execution_config: {
    model_preference: 'fast' | 'balanced' | 'thorough';
    context_window: 'small' | 'medium' | 'large';
    creativity_level: number; // 1-10
    confidence_threshold: number; // 0-1
    max_suggestions: number;
  };
  priority: number; // 1-100, higher = more important
  is_active: boolean;
  is_template: boolean;
  usage_stats: {
    total_executions: number;
    avg_execution_time: number;
    suggestion_acceptance_rate: number;
    last_used: string;
  };
  performance_metrics: {
    effectiveness_score: number;
    user_satisfaction: number;
    improvement_impact: number;
  };
  created_at: string;
  updated_at: string;
}

export interface AgentCreate {
  name: string;
  description: string;
  type: CustomAgent['type'];
  capabilities: string[];
  style_rules: CustomAgent['style_rules'];
  execution_config?: Partial<CustomAgent['execution_config']>;
  priority?: number;
}

export interface AgentUpdate {
  name?: string;
  description?: string;
  capabilities?: string[];
  style_rules?: Partial<CustomAgent['style_rules']>;
  execution_config?: Partial<CustomAgent['execution_config']>;
  priority?: number;
  is_active?: boolean;
}

export interface AgentExecution {
  id: string;
  agent_id: string;
  user_id: string;
  text_input: string;
  context: {
    document_type?: string;
    target_audience?: string;
    writing_goal?: string;
    previous_suggestions?: string[];
  };
  results: {
    suggestions: Array<{
      id: string;
      type: 'addition' | 'replacement' | 'deletion' | 'restructure';
      priority: 'low' | 'medium' | 'high';
      confidence: number;
      title: string;
      description: string;
      location?: { start: number; end: number };
      original_text?: string;
      suggested_text?: string;
      reasoning: string;
      category: string;
    }>;
    overall_assessment: {
      score: number;
      strengths: string[];
      areas_for_improvement: string[];
    };
    metadata: {
      processing_time: number;
      model_used: string;
      tokens_consumed: number;
      confidence_score: number;
    };
  };
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  execution_time: number;
  error_message?: string;
  created_at: string;
  completed_at?: string;
}

export interface AgentExecutionRequest {
  text: string;
  context?: AgentExecution['context'];
  options?: {
    execution_mode?: 'quick' | 'standard' | 'thorough';
    max_suggestions?: number;
    focus_areas?: string[];
    exclude_categories?: string[];
  };
}

export interface AgentBatchRequest {
  agent_ids: string[];
  text: string;
  context?: AgentExecution['context'];
  options?: AgentExecutionRequest['options'] & {
    parallel_execution?: boolean;
    conflict_resolution?: 'merge' | 'priority' | 'user_choice';
    max_total_suggestions?: number;
  };
}

export interface AgentBatchResponse {
  executions: AgentExecution[];
  consolidated_results: {
    merged_suggestions: AgentExecution['results']['suggestions'];
    conflict_analysis: Array<{
      suggestion_1: string;
      suggestion_2: string;
      conflict_type: 'overlapping' | 'contradictory' | 'redundant';
      resolution: string;
    }>;
    overall_assessment: AgentExecution['results']['overall_assessment'];
  };
  processing_summary: {
    total_agents: number;
    successful_executions: number;
    failed_executions: number;
    total_processing_time: number;
    tokens_consumed: number;
  };
}

export interface AgentTemplate {
  id: string;
  name: string;
  description: string;
  type: CustomAgent['type'];
  category: 'beginner' | 'intermediate' | 'advanced' | 'specialized';
  use_cases: string[];
  template_data: Omit<AgentCreate, 'name' | 'description'>;
  preview_config: {
    sample_input: string;
    expected_output: string;
  };
  is_premium: boolean;
  rating: number;
  usage_count: number;
}

export interface AgentAnalytics {
  agent_id: string;
  performance_summary: {
    total_executions: number;
    success_rate: number;
    avg_execution_time: number;
    avg_suggestions_per_execution: number;
    suggestion_acceptance_rate: number;
  };
  usage_patterns: {
    most_active_hours: number[];
    most_common_document_types: string[];
    frequent_suggestion_categories: string[];
    user_interaction_patterns: Record<string, number>;
  };
  effectiveness_metrics: {
    user_satisfaction_score: number;
    improvement_impact_score: number;
    consistency_score: number;
    accuracy_score: number;
  };
  comparison_data: {
    vs_other_user_agents: number;
    vs_template_agents: number;
    vs_system_agents: number;
  };
  recommendations: Array<{
    area: 'performance' | 'configuration' | 'usage';
    suggestion: string;
    expected_impact: 'low' | 'medium' | 'high';
  }>;
  trend_data: Array<{
    date: string;
    executions: number;
    avg_score: number;
    acceptance_rate: number;
  }>;
}

class AgentsApiService {
  /**
   * Get list of user's custom agents
   */
  async getAgents(params?: {
    type?: CustomAgent['type'];
    is_active?: boolean;
    include_templates?: boolean;
    sort?: 'name' | 'created_at' | 'priority' | 'usage';
  }): Promise<CustomAgent[]> {
    const response = await baseApi.get<CustomAgent[]>('/agents', { params });
    return response.data;
  }

  /**
   * Get a specific agent by ID
   */
  async getAgent(id: string): Promise<CustomAgent> {
    const response = await baseApi.get<CustomAgent>(`/agents/${id}`);
    return response.data;
  }

  /**
   * Create a new custom agent
   */
  async createAgent(agent: AgentCreate): Promise<CustomAgent> {
    const response = await baseApi.post<CustomAgent>('/agents', agent);
    return response.data;
  }

  /**
   * Update an existing agent
   */
  async updateAgent(id: string, updates: AgentUpdate): Promise<CustomAgent> {
    const response = await baseApi.put<CustomAgent>(`/agents/${id}`, updates);
    return response.data;
  }

  /**
   * Delete/deactivate an agent
   */
  async deleteAgent(id: string): Promise<{ success: boolean }> {
    const response = await baseApi.delete(`/agents/${id}`);
    return response.data;
  }

  /**
   * Execute a single agent
   */
  async executeAgent(agentId: string, request: AgentExecutionRequest): Promise<AgentExecution> {
    const response = await baseApi.post<AgentExecution>(`/agents/${agentId}/execute`, request);
    return response.data;
  }

  /**
   * Execute multiple agents in batch
   */
  async executeBatch(request: AgentBatchRequest): Promise<AgentBatchResponse> {
    const response = await baseApi.post<AgentBatchResponse>('/agents/execute-batch', request);
    return response.data;
  }

  /**
   * Get execution history for an agent
   */
  async getExecutionHistory(agentId: string, params?: {
    limit?: number;
    offset?: number;
    status?: AgentExecution['status'];
    date_range?: { start: string; end: string };
  }): Promise<{
    executions: AgentExecution[];
    total: number;
    has_more: boolean;
  }> {
    const response = await baseApi.get(`/agents/${agentId}/executions`, { params });
    return response.data;
  }

  /**
   * Get agent analytics and performance data
   */
  async getAgentAnalytics(agentId: string, timeRange?: 'week' | 'month' | 'quarter' | 'year'): Promise<AgentAnalytics> {
    const response = await baseApi.get<AgentAnalytics>(`/agents/${agentId}/analytics`, {
      params: { time_range: timeRange }
    });
    return response.data;
  }

  /**
   * Get available agent templates
   */
  async getAgentTemplates(category?: AgentTemplate['category']): Promise<AgentTemplate[]> {
    const response = await baseApi.get<AgentTemplate[]>('/agents/templates', {
      params: { category }
    });
    return response.data;
  }

  /**
   * Create agent from template
   */
  async createFromTemplate(templateId: string, customization: {
    name: string;
    description?: string;
    modifications?: Partial<AgentCreate>;
  }): Promise<CustomAgent> {
    const response = await baseApi.post<CustomAgent>(`/agents/templates/${templateId}/instantiate`, customization);
    return response.data;
  }

  /**
   * Test agent configuration
   */
  async testAgent(agentData: AgentCreate, testText: string): Promise<{
    test_execution: AgentExecution;
    performance_prediction: {
      estimated_effectiveness: number;
      strengths: string[];
      potential_issues: string[];
      optimization_suggestions: string[];
    };
    configuration_feedback: Array<{
      area: string;
      suggestion: string;
      impact: 'low' | 'medium' | 'high';
    }>;
  }> {
    const response = await baseApi.post('/agents/test', {
      agent_data: agentData,
      test_text: testText
    });
    return response.data;
  }

  /**
   * Optimize agent configuration
   */
  async optimizeAgent(agentId: string): Promise<{
    current_performance: AgentAnalytics['effectiveness_metrics'];
    optimization_suggestions: Array<{
      parameter: string;
      current_value: any;
      suggested_value: any;
      expected_improvement: number;
      confidence: number;
    }>;
    auto_apply_safe_optimizations?: boolean;
  }> {
    const response = await baseApi.post(`/agents/${agentId}/optimize`);
    return response.data;
  }

  /**
   * Duplicate agent with modifications
   */
  async duplicateAgent(id: string, modifications?: {
    name: string;
    changes?: Partial<AgentCreate>;
  }): Promise<CustomAgent> {
    const response = await baseApi.post<CustomAgent>(`/agents/${id}/duplicate`, modifications);
    return response.data;
  }

  /**
   * Update agent priority
   */
  async updateAgentPriority(agentId: string, priority: number): Promise<CustomAgent> {
    const response = await baseApi.patch<CustomAgent>(`/agents/${agentId}/priority`, {
      priority
    });
    return response.data;
  }

  /**
   * Bulk update agent priorities
   */
  async updateAgentPriorities(priorities: Array<{ agent_id: string; priority: number }>): Promise<CustomAgent[]> {
    const response = await baseApi.post<CustomAgent[]>('/agents/bulk-priority-update', {
      priorities
    });
    return response.data;
  }

  /**
   * Get agent conflicts analysis
   */
  async analyzeAgentConflicts(agentIds: string[]): Promise<{
    conflicts: Array<{
      agent_1: string;
      agent_2: string;
      conflict_type: 'style' | 'priority' | 'suggestion_overlap';
      severity: 'low' | 'medium' | 'high';
      description: string;
      resolution_suggestion: string;
    }>;
    optimization_recommendations: string[];
    execution_order_suggestion: string[];
  }> {
    const response = await baseApi.post('/agents/analyze-conflicts', {
      agent_ids: agentIds
    });
    return response.data;
  }

  /**
   * Export agent configuration
   */
  async exportAgent(agentId: string): Promise<{
    export_data: CustomAgent;
    export_url: string;
    expires_at: string;
  }> {
    const response = await baseApi.post(`/agents/${agentId}/export`);
    return response.data;
  }

  /**
   * Import agent configuration
   */
  async importAgent(importData: any): Promise<CustomAgent> {
    const response = await baseApi.post<CustomAgent>('/agents/import', {
      agent_data: importData
    });
    return response.data;
  }

  /**
   * Get agent suggestions based on user's writing patterns
   */
  async getAgentSuggestions(params?: {
    document_types?: string[];
    writing_goals?: string[];
    current_agents?: string[];
  }): Promise<Array<{
    template: AgentTemplate;
    relevance_score: number;
    reasons: string[];
    configuration_suggestions: Record<string, any>;
  }>> {
    const response = await baseApi.get('/agents/suggestions', { params });
    return response.data;
  }
}

// Export singleton instance
export const agentsApi = new AgentsApiService();
export default agentsApi;