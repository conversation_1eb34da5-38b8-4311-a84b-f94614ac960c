"""
Developer Portal API Endpoints

Provides endpoints for:
- Serving documentation files
- OpenAPI specification
- API metadata and examples
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import JSONResponse
from fastapi.openapi.utils import get_openapi

from api.auth import get_current_user
from core.database import get_database

router = APIRouter(prefix="/developer", tags=["Developer Portal"])

# Get the docs directory path
DOCS_DIR = Path(__file__).parent.parent.parent / "docs"

# Allowed documentation files for security - ONLY public API docs
ALLOWED_DOCS = {
    "api-documentation.md"
}

@router.get("/docs/{filename}")
async def get_documentation(
    filename: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Serve documentation files from the docs directory.
    
    Security: Only serves predefined documentation files to prevent path traversal.
    """
    # Validate filename for security
    if filename not in ALLOWED_DOCS:
        raise HTTPException(
            status_code=404, 
            detail=f"Documentation file '{filename}' not found or not allowed"
        )
    
    file_path = DOCS_DIR / filename
    
    if not file_path.exists():
        raise HTTPException(
            status_code=404, 
            detail=f"Documentation file '{filename}' does not exist"
        )
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return {
            "success": True,
            "data": {
                "filename": filename,
                "content": content,
                "size": len(content),
                "last_modified": file_path.stat().st_mtime
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to read documentation file: {str(e)}"
        )

@router.get("/docs")
async def list_documentation(
    current_user: dict = Depends(get_current_user)
):
    """
    List available documentation files with metadata.
    """
    docs_list = []
    
    for filename in ALLOWED_DOCS:
        file_path = DOCS_DIR / filename
        if file_path.exists():
            stat = file_path.stat()
            
            # Get first few lines to extract title/description
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    first_lines = []
                    for i, line in enumerate(f):
                        if i >= 10:  # Read first 10 lines max
                            break
                        first_lines.append(line.strip())
                
                # Extract title (first # heading)
                title = filename.replace('.md', '').replace('-', ' ').title()
                description = "Documentation file"
                
                for line in first_lines:
                    if line.startswith('# '):
                        title = line[2:].strip()
                        break
                
                # Try to find a description from subsequent lines
                for line in first_lines:
                    if line and not line.startswith('#') and len(line) > 20:
                        description = line[:100] + ('...' if len(line) > 100 else '')
                        break
                
            except:
                title = filename.replace('.md', '').replace('-', ' ').title()
                description = "Documentation file"
            
            docs_list.append({
                "filename": filename,
                "title": title,
                "description": description,
                "size": stat.st_size,
                "last_modified": stat.st_mtime,
                "category": _get_doc_category(filename)
            })
    
    return {
        "success": True,
        "data": docs_list
    }

def _get_doc_category(filename: str) -> str:
    """Categorize documentation files."""
    if 'api' in filename:
        return 'API'
    elif 'backend' in filename or 'database' in filename:
        return 'Backend'
    elif 'frontend' in filename:
        return 'Frontend'
    elif 'architecture' in filename or 'system' in filename:
        return 'Architecture'
    elif 'deploy' in filename:
        return 'Deployment'
    else:
        return 'General'

@router.get("/openapi-spec")
async def get_openapi_spec(
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """
    Generate and return the OpenAPI specification for the API.
    """
    try:
        # Get the FastAPI app instance
        app = request.app
        
        # Generate OpenAPI spec
        openapi_schema = get_openapi(
            title="Revisionary API",
            version="1.0.0",
            description="""
            AI-powered writing assistance API providing comprehensive tools for document creation,
            editing, and enhancement with advanced analytics and persona-driven writing assistance.
            
            ## Authentication
            All endpoints require authentication via Bearer tokens obtained through Firebase ID token exchange.
            
            ## Rate Limiting
            - Free: 100 requests/hour
            - Professional: 1,000 requests/hour  
            - Studio: 10,000 requests/hour
            - Enterprise: Unlimited
            
            ## Base URL
            - Production: `https://api.revisionary.app/v1`
            - Development: `http://localhost:8000/api/v1`
            """,
            routes=app.routes,
            contact={
                "name": "Revisionary API Support",
                "email": "<EMAIL>",
                "url": "https://revisionary.app/support"
            },
            license_info={
                "name": "Proprietary",
                "url": "https://revisionary.app/terms"
            }
        )
        
        # Add security schemes
        if "components" not in openapi_schema:
            openapi_schema["components"] = {}
        
        openapi_schema["components"]["securitySchemes"] = {
            "bearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT",
                "description": "Enter your JWT token obtained from the auth endpoint"
            }
        }
        
        # Add global security requirement
        openapi_schema["security"] = [{"bearerAuth": []}]
        
        # Add servers
        openapi_schema["servers"] = [
            {
                "url": "https://api.revisionary.app/v1",
                "description": "Production server"
            },
            {
                "url": "http://localhost:8000/api/v1", 
                "description": "Development server"
            }
        ]
        
        return openapi_schema
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate OpenAPI specification: {str(e)}"
        )

@router.get("/api-stats")
async def get_api_stats(
    current_user: dict = Depends(get_current_user)
):
    """
    Get API statistics and metadata for the developer portal.
    """
    try:
        # In a real implementation, you'd query your analytics database
        # For now, return mock data based on your actual API structure
        
        stats = {
            "total_endpoints": 45,
            "stable_endpoints": 38,
            "beta_endpoints": 6,
            "deprecated_endpoints": 1,
            "categories": {
                "Authentication": 4,
                "Documents": 12,
                "Analytics": 8,
                "AI Generation": 7,
                "Personas": 5,
                "Agents": 4,
                "Health": 3,
                "Admin": 2
            },
            "latest_version": "1.0.0",
            "uptime": "99.9%",
            "avg_response_time": "125ms"
        }
        
        return {
            "success": True,
            "data": stats
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get API statistics: {str(e)}"
        )

@router.get("/code-examples/{language}")
async def get_code_examples(
    language: str,
    endpoint: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Generate code examples for API endpoints in different languages.
    """
    supported_languages = ["javascript", "python", "curl", "php", "go"]
    
    if language not in supported_languages:
        raise HTTPException(
            status_code=400,
            detail=f"Language '{language}' not supported. Supported: {supported_languages}"
        )
    
    # Example templates for different languages
    examples = {
        "javascript": {
            "get_documents": """
// Get user documents
const response = await fetch('/api/v1/documents', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data);
""",
            "create_document": """
// Create a new document  
const response = await fetch('/api/v1/documents', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    title: 'My New Document',
    type: 'blog',
    content: 'Document content here...'
  })
});

const data = await response.json();
console.log(data);
""",
            "token_usage": """
// Get token usage information
const response = await fetch('/api/v1/analytics/token-usage', {
  method: 'GET', 
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const usage = await response.json();
console.log(`Used: ${usage.data.total_tokens_used}/${usage.data.tokens_limit}`);
"""
        },
        "python": {
            "get_documents": """
import requests

# Get user documents
response = requests.get(
    'http://localhost:8000/api/v1/documents',
    headers={
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
)

data = response.json()
print(data)
""",
            "create_document": """
import requests

# Create a new document
response = requests.post(
    'http://localhost:8000/api/v1/documents',
    headers={
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    },
    json={
        'title': 'My New Document',
        'type': 'blog', 
        'content': 'Document content here...'
    }
)

data = response.json()
print(data)
""",
            "token_usage": """
import requests

# Get token usage information
response = requests.get(
    'http://localhost:8000/api/v1/analytics/token-usage',
    headers={
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
)

usage = response.json()
print(f"Used: {usage['data']['total_tokens_used']}/{usage['data']['tokens_limit']}")
"""
        },
        "curl": {
            "get_documents": """
# Get user documents
curl -X GET "http://localhost:8000/api/v1/documents" \\
  -H "Authorization: Bearer $TOKEN" \\
  -H "Content-Type: application/json"
""",
            "create_document": """
# Create a new document
curl -X POST "http://localhost:8000/api/v1/documents" \\
  -H "Authorization: Bearer $TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{
    "title": "My New Document",
    "type": "blog",
    "content": "Document content here..."
  }'
""",
            "token_usage": """
# Get token usage information  
curl -X GET "http://localhost:8000/api/v1/analytics/token-usage" \\
  -H "Authorization: Bearer $TOKEN" \\
  -H "Content-Type: application/json"
"""
        }
    }
    
    language_examples = examples.get(language, {})
    
    if endpoint and endpoint in language_examples:
        return {
            "success": True,
            "data": {
                "language": language,
                "endpoint": endpoint,
                "code": language_examples[endpoint]
            }
        }
    
    return {
        "success": True,
        "data": {
            "language": language,
            "examples": language_examples
        }
    }