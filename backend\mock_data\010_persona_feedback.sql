-- Revisionary Mock Data - Part 10: Persona Feedback
-- Effectiveness scoring for user personas

DO $$
DECLARE
    -- Get user IDs from firebase_uid (from 001_core_data.sql)
    sarah_id UUID;
    alex_id UUID;
    kim_id UUID;
    maya_id UUID;
    james_id UUID;
    
    -- Get block IDs for feedback
    sarah_climate_para1_id UUID;
    alex_cafe_para1_id UUID;
    kim_neural_para_id UUID;
    maya_chap1_para1_id UUID;
    james_q4_para_id UUID;
    
    -- Get document IDs
    sarah_climate_doc_id UUID;
    alex_cafe_doc_id UUID;
    kim_neural_doc_id UUID;
    maya_chap1_doc_id UUID;
    james_q4_doc_id UUID;
    
BEGIN
    -- Get user IDs from existing users
    SELECT id INTO sarah_id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1;
    SELECT id INTO alex_id FROM users WHERE firebase_uid = 'firebase_user_002' LIMIT 1;
    SELECT id INTO kim_id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1;
    SELECT id INTO maya_id FROM users WHERE firebase_uid = 'firebase_user_004' LIMIT 1;
    SELECT id INTO james_id FROM users WHERE firebase_uid = 'firebase_user_005' LIMIT 1;
    
    -- Get document IDs from existing documents
    SELECT id INTO sarah_climate_doc_id FROM documents WHERE title = 'Climate Change Research Paper' LIMIT 1;
    SELECT id INTO alex_cafe_doc_id FROM documents WHERE title = 'The Midnight Café' LIMIT 1;
    SELECT id INTO kim_neural_doc_id FROM documents WHERE title = 'Neural Networks in Medical Diagnosis' LIMIT 1;
    SELECT id INTO maya_chap1_doc_id FROM documents WHERE title = 'Chronicles of Aetheria: Chapter 1' LIMIT 1;
    SELECT id INTO james_q4_doc_id FROM documents WHERE title = 'Q4 Marketing Strategy Report' LIMIT 1;
    
    -- Get block IDs
    SELECT id INTO sarah_climate_para1_id FROM blocks WHERE content LIKE 'Climate change represents one of the most pressing%' AND document_id = sarah_climate_doc_id LIMIT 1;
    SELECT id INTO alex_cafe_para1_id FROM blocks WHERE content LIKE 'Elena had walked past the corner%' AND document_id = alex_cafe_doc_id LIMIT 1;
    SELECT id INTO kim_neural_para_id FROM blocks WHERE content LIKE 'The integration of artificial neural networks%' AND document_id = kim_neural_doc_id LIMIT 1;
    SELECT id INTO maya_chap1_para1_id FROM blocks WHERE content LIKE 'The ancient crown of Aetheria lay in fragments%' AND document_id = maya_chap1_doc_id LIMIT 1;
    SELECT id INTO james_q4_para_id FROM blocks WHERE content LIKE 'Q4 2024 demonstrated exceptional growth%' AND document_id = james_q4_doc_id LIMIT 1;

    -- =================================================================
    -- PERSONA FEEDBACK - Effectiveness scoring for user personas
    -- =================================================================
    
    INSERT INTO persona_feedback (block_id, persona_id, session_id, comprehension_score, engagement_score, emotional_score, style_score, overall_score, feedback_text, specific_issues, suggestions, confidence, processing_time_ms, model_used, tokens_used) 
    VALUES
    (kim_neural_para_id, (SELECT id FROM personas WHERE name = 'Medical Research Scholar' AND user_id = kim_id LIMIT 1), gen_random_uuid(), 0.94, 0.88, 0.82, 0.96, 0.90, 
     'Excellent technical accuracy and methodology focus. Well-suited for peer review submission. Could benefit from more clinical implementation examples.',
     '["accessibility for practitioners", "implementation cost considerations"]'::jsonb, 
     '["Include more clinical implementation examples", "Add cost-benefit analysis perspective", "Consider interdisciplinary collaboration angles"]'::jsonb, 
     0.94, 2850, 'gpt-4', 145),
    
    (maya_chap1_para1_id, (SELECT id FROM personas WHERE name = 'Epic Fantasy Narrator' AND user_id = maya_id LIMIT 1), gen_random_uuid(), 0.89, 0.92, 0.87, 0.91, 0.90, 
     'Strong world-building depth and magical consistency. Narrative voice is compelling. Character emotional depth could be enhanced.',
     '["character emotional development", "pacing variation needed"]'::jsonb, 
     '["Develop character internal monologue", "Add more sensory world-building details", "Include foreshadowing elements"]'::jsonb, 
     0.89, 2150, 'gpt-4', 128),
    
    (james_q4_para_id, (SELECT id FROM personas WHERE name = 'Executive Strategist' AND user_id = james_id LIMIT 1), gen_random_uuid(), 0.92, 0.86, 0.85, 0.94, 0.89, 
     'Strong ROI focus and strategic clarity. Data presentation is excellent. Risk analysis and competitive context need strengthening.',
     '["risk analysis depth", "competitive landscape missing"]'::jsonb, 
     '["Add competitive differentiation analysis", "Include risk mitigation strategies", "Expand market opportunity sizing"]'::jsonb, 
     0.92, 1950, 'gpt-4', 108),
    
    (sarah_climate_para1_id, (SELECT id FROM personas WHERE name = 'Environmental Researcher' AND user_id = sarah_id LIMIT 1), gen_random_uuid(), 0.87, 0.84, 0.89, 0.85, 0.86, 
     'Good policy relevance and evidence-based approach. Solution focus and stakeholder engagement could be improved.',
     '["solution orientation lacking", "stakeholder perspective needed"]'::jsonb, 
     '["Include more solution-oriented content", "Add stakeholder impact analysis", "Consider implementation pathways"]'::jsonb, 
     0.87, 2200, 'gpt-4', 132),
    
    (alex_cafe_para1_id, (SELECT id FROM personas WHERE name = 'Urban Fantasy Storyteller' AND user_id = alex_id LIMIT 1), gen_random_uuid(), 0.91, 0.93, 0.88, 0.89, 0.90, 
     'Excellent atmospheric building and mystery development. Contemporary setting works well. Character development and supernatural rules need clarification.',
     '["character development depth", "supernatural system rules"]'::jsonb, 
     '["Develop Elena personal stakes", "Establish supernatural world rules", "Add more sensory atmosphere details"]'::jsonb, 
     0.91, 1850, 'gpt-4', 115);

END $$;