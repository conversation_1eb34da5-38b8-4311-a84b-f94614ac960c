-- Revisionary Row Level Security (RLS) Policies
-- Part 6 of the schema migration - CRITICAL FOR SECURITY

-- =================================================================
-- ENABLE ROW LEVEL SECURITY
-- =================================================================

-- Enable RLS on all user data tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE suggestions ENABLE ROW LEVEL SECURITY;
ALTER TABLE summaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE collaborations ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_writing_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE writing_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE writing_challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE token_usage_daily ENABLE ROW LEVEL SECURITY;
ALTER TABLE personas ENABLE ROW LEVEL SECURITY;
ALTER TABLE persona_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE audience_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_style_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_usage_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE health_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE health_issues ENABLE ROW LEVEL SECURITY;
ALTER TABLE entities ENABLE ROW LEVEL SECURITY;
ALTER TABLE entity_relationships ENABLE ROW LEVEL SECURITY;
ALTER TABLE consistency_violations ENABLE ROW LEVEL SECURITY;
ALTER TABLE citations ENABLE ROW LEVEL SECURITY;
ALTER TABLE reference_library ENABLE ROW LEVEL SECURITY;
ALTER TABLE export_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE templates ENABLE ROW LEVEL SECURITY;

-- =================================================================
-- RLS POLICIES
-- =================================================================

-- Users: Users can only see their own data
CREATE POLICY users_own_data ON users
    FOR ALL USING (firebase_uid = auth.jwt() ->> 'sub');

-- Documents: Users can access their own documents and public documents
CREATE POLICY documents_owner_access ON documents
    FOR ALL USING (
        owner_id IN (
            SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
        )
    );

CREATE POLICY documents_public_read ON documents
    FOR SELECT USING (is_public = TRUE AND deleted_at IS NULL);

-- Documents: Collaborators can access shared documents
CREATE POLICY documents_collaborator_access ON documents
    FOR SELECT USING (
        id IN (
            SELECT document_id FROM collaborations 
            WHERE user_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            ) AND status = 'active'
        )
    );

-- Blocks: Access through document ownership or collaboration
CREATE POLICY blocks_document_access ON blocks
    FOR ALL USING (
        document_id IN (
            SELECT id FROM documents WHERE owner_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            )
        ) OR
        document_id IN (
            SELECT document_id FROM collaborations 
            WHERE user_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            ) AND status = 'active'
        )
    );

-- Versions: Access through block ownership
CREATE POLICY versions_block_access ON versions
    FOR ALL USING (
        block_id IN (
            SELECT id FROM blocks WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );

-- Suggestions: Access through block ownership
CREATE POLICY suggestions_block_access ON suggestions
    FOR ALL USING (
        block_id IN (
            SELECT id FROM blocks WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );

-- Summaries: Access through block ownership
CREATE POLICY summaries_block_access ON summaries
    FOR ALL USING (
        block_id IN (
            SELECT id FROM blocks WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );

-- Collaborations: Users can see collaborations they're part of or own
CREATE POLICY collaborations_access ON collaborations
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub') OR
        document_id IN (
            SELECT id FROM documents WHERE owner_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            )
        )
    );

-- Comments: Access through collaboration or ownership
CREATE POLICY comments_collaboration_access ON comments
    FOR ALL USING (
        block_id IN (
            SELECT b.id FROM blocks b
            JOIN documents d ON b.document_id = d.id
            JOIN collaborations c ON d.id = c.document_id
            WHERE c.user_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            ) AND c.status = 'active'
        ) OR
        block_id IN (
            SELECT id FROM blocks WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );

-- Document Scores: Users can only see their own scores
CREATE POLICY document_scores_own_data ON document_scores
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );

-- User Writing Stats: Users can only see their own stats
CREATE POLICY user_writing_stats_own_data ON user_writing_stats
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );

-- Writing Achievements: Users can only see their own achievements
CREATE POLICY writing_achievements_own_data ON writing_achievements
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );

-- Writing Challenges: Users can only see their own challenges
CREATE POLICY writing_challenges_own_data ON writing_challenges
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );

-- Usage Events: Users can only see their own events
CREATE POLICY usage_events_own_data ON usage_events
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );

-- Token Usage: Users can only see their own usage
CREATE POLICY token_usage_daily_own_data ON token_usage_daily
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );

-- Personas: Users can access their own personas and public templates
CREATE POLICY personas_own_access ON personas
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub') OR
        is_template = TRUE
    );

-- Persona Feedback: Access through block ownership
CREATE POLICY persona_feedback_block_access ON persona_feedback
    FOR ALL USING (
        block_id IN (
            SELECT id FROM blocks WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );

-- Audience Analysis: Access through document ownership
CREATE POLICY audience_analysis_document_access ON audience_analysis
    FOR ALL USING (
        document_id IN (
            SELECT id FROM documents WHERE owner_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            )
        )
    );

-- Custom Agents: Users can access their own agents and public templates
CREATE POLICY custom_agents_own_access ON custom_agents
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub') OR
        is_template = TRUE
    );

-- Agent Style Rules: Access through agent ownership
CREATE POLICY agent_style_rules_agent_access ON agent_style_rules
    FOR ALL USING (
        agent_id IN (
            SELECT id FROM custom_agents WHERE user_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            ) OR is_template = TRUE
        )
    );

-- Agent Usage Stats: Users can only see their own stats
CREATE POLICY agent_usage_stats_own_data ON agent_usage_stats
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );

-- Health Metrics: Users can only see their own data
CREATE POLICY health_metrics_own_data ON health_metrics
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );

-- Health Issues: Access through block ownership
CREATE POLICY health_issues_block_access ON health_issues
    FOR ALL USING (
        block_id IN (
            SELECT id FROM blocks WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );

-- Entities: Access through document ownership
CREATE POLICY entities_document_access ON entities
    FOR ALL USING (
        document_id IN (
            SELECT id FROM documents WHERE owner_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            )
        )
    );

-- Entity Relationships: Access through entity ownership
CREATE POLICY entity_relationships_entity_access ON entity_relationships
    FOR ALL USING (
        source_entity_id IN (
            SELECT id FROM entities WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );

-- Consistency Violations: Access through document ownership
CREATE POLICY consistency_violations_document_access ON consistency_violations
    FOR ALL USING (
        document_id IN (
            SELECT id FROM documents WHERE owner_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            )
        )
    );

-- Citations: Access through document ownership
CREATE POLICY citations_document_access ON citations
    FOR ALL USING (
        document_id IN (
            SELECT id FROM documents WHERE owner_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            )
        )
    );

-- Reference Library: Users can only see their own library
CREATE POLICY reference_library_own_data ON reference_library
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );

-- Export Jobs: Users can only see their own export jobs
CREATE POLICY export_jobs_own_data ON export_jobs
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );

-- Templates: Users can see public templates and their own templates
CREATE POLICY templates_public_read ON templates
    FOR SELECT USING (is_public = TRUE);

CREATE POLICY templates_own_access ON templates
    FOR ALL USING (
        created_by IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );

-- =================================================================
-- ADMIN POLICIES (For service role access)
-- =================================================================

-- Allow service role to bypass RLS for administrative tasks
-- This should only be used by the backend service with proper authentication

-- Note: Service role policies would go here, but they're typically
-- handled through Supabase dashboard or service key usage
-- For production, ensure service key is properly secured and used
-- only by authenticated backend services

-- =================================================================
-- VERIFICATION
-- =================================================================

-- Query to verify RLS is enabled on all tables
-- SELECT schemaname, tablename, rowsecurity 
-- FROM pg_tables 
-- WHERE schemaname = 'public' AND rowsecurity = true;

-- Query to list all policies
-- SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
-- FROM pg_policies 
-- WHERE schemaname = 'public';

COMMENT ON SCHEMA public IS 'Revisionary database schema with comprehensive Row Level Security policies';
COMMENT ON TABLE users IS 'User accounts with Firebase authentication integration';
COMMENT ON TABLE documents IS 'Document storage with hierarchical block structure';
COMMENT ON TABLE blocks IS 'Hierarchical document content blocks with version control';
COMMENT ON TABLE suggestions IS 'AI-generated suggestions for content improvement';
COMMENT ON TABLE personas IS 'Reader personas for multi-audience feedback analysis';
COMMENT ON TABLE custom_agents IS 'User-defined AI agents with custom rules and capabilities';