# Frontend Environment Configuration
# Copy this file to .env.local for local development

# API Configuration
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000

# Firebase Configuration
VITE_FIREBASE_API_KEY=your-firebase-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
VITE_FIREBASE_APP_ID=your-app-id
VITE_FIREBASE_MEASUREMENT_ID=your-measurement-id

# Development Mode
VITE_DEV_MODE=false
VITE_DEV_USER_EMAIL=<EMAIL>
VITE_DEV_USER_NAME=Developer User

# Feature Flags
VITE_ENABLE_WEBSOCKET=true
VITE_ENABLE_PERSONAS=true
VITE_ENABLE_AGENTS=true
VITE_ENABLE_HEALTH_ANALYSIS=true
VITE_ENABLE_CITATIONS=true
VITE_ENABLE_ANALYTICS=true

# API Keys (if needed for direct client calls)
VITE_OPENAI_API_KEY=
VITE_GOOGLE_AI_API_KEY=

# Application Settings
VITE_APP_NAME=Revisionary
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# Monitoring and Analytics
VITE_SENTRY_DSN=
VITE_GA_MEASUREMENT_ID=

# Cache Settings
VITE_CACHE_DURATION=300000  # 5 minutes in milliseconds
VITE_HEALTH_CACHE_DURATION=300000
VITE_API_TIMEOUT=30000  # 30 seconds

# WebSocket Settings
VITE_WS_RECONNECT_ATTEMPTS=5
VITE_WS_RECONNECT_DELAY=1000  # 1 second

# Rate Limiting
VITE_API_RATE_LIMIT=100  # requests per minute
VITE_WS_RATE_LIMIT=50  # messages per minute