import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { personas<PERSON><PERSON>, <PERSON><PERSON> as Api<PERSON><PERSON><PERSON>, <PERSON>aTemplate as ApiPersonaTemplate } from '../services/api/personasApi';
import {
  PersonaStore,
  ReaderPersona,
  PersonaTemplate,
  PersonaFeedback,
  PersonaInsights,
  PersonaCategory,
  PersonaReaction,
  CommentType,
  FeedbackCategory,
} from '../types/persona';

// Interface adapters to bridge API types with UI types
const transformApiPersona = (apiPersona: ApiPersona): ReaderPersona => ({
  id: apiPersona.id,
  name: apiPersona.name,
  description: apiPersona.description,
  category: apiPersona.type as PersonaCategory,
  demographics: {
    age: parseInt(apiPersona.demographics.age_range?.split('-')[0] || '30'),
    education: apiPersona.demographics.education_level || 'bachelors',
    profession: apiPersona.demographics.profession,
    interests: apiPersona.demographics.interests || [],
  },
  readingPreferences: {
    complexity: apiPersona.reading_preferences.complexity_level === 'expert' ? 'complex' : 
                apiPersona.reading_preferences.complexity_level === 'advanced' ? 'complex' :
                apiPersona.reading_preferences.complexity_level === 'intermediate' ? 'moderate' : 'simple',
    readingSpeed: apiPersona.reading_preferences.reading_speed,
    attentionSpan: apiPersona.reading_preferences.attention_span === 'high' ? 'long' : 
                  apiPersona.reading_preferences.attention_span === 'medium' ? 'medium' : 'short',
    preferredLength: apiPersona.reading_preferences.preferred_length === 'long' ? 'detailed' :
                    apiPersona.reading_preferences.preferred_length === 'medium' ? 'medium' : 'brief',
  },
  personality: {
    traits: Object.entries(apiPersona.personality_traits)
      .filter(([_, value]) => value > 7)
      .map(([key, _]) => key),
    criticalThinking: apiPersona.personality_traits.analytical > 7 ? 'high' : 
                     apiPersona.personality_traits.analytical > 4 ? 'medium' : 'low',
    emotionalSensitivity: apiPersona.personality_traits.emotional > 7 ? 'high' :
                         apiPersona.personality_traits.emotional > 4 ? 'medium' : 'low',
  },
  context: {
    purpose: 'evaluation',
    expertise: apiPersona.reading_preferences.complexity_level === 'expert' ? 'expert' :
              apiPersona.reading_preferences.complexity_level === 'advanced' ? 'specialist' : 'novice',
    relationship: 'colleague',
  },
  feedbackStyle: {
    tone: apiPersona.feedback_style.criticism_style === 'direct' ? 'harsh' :
          apiPersona.feedback_style.criticism_style === 'gentle' ? 'encouraging' : 'constructive',
    focus: apiPersona.feedback_style.focus_areas.includes('content') ? 'content' :
           apiPersona.feedback_style.focus_areas.includes('structure') ? 'structure' : 'technical',
    detail: apiPersona.feedback_style.depth === 'deep' ? 'specific' :
            apiPersona.feedback_style.depth === 'moderate' ? 'moderate' : 'high-level',
    priorities: apiPersona.feedback_style.focus_areas,
  },
  isBuiltIn: apiPersona.is_template,
  isActive: apiPersona.is_active,
  tags: [apiPersona.type],
  color: 'purple', // Default color
  createdAt: new Date(apiPersona.created_at),
  updatedAt: new Date(apiPersona.updated_at),
});

const transformApiTemplate = (apiTemplate: ApiPersonaTemplate): PersonaTemplate => ({
  id: apiTemplate.id,
  name: apiTemplate.name,
  description: apiTemplate.description,
  category: apiTemplate.type as PersonaCategory,
  isPopular: apiTemplate.rating > 4,
  usageCount: apiTemplate.usage_count,
  template: {
    category: apiTemplate.type as PersonaCategory,
    demographics: {
      education: apiTemplate.template_data.demographics.education_level || 'bachelors',
      profession: apiTemplate.template_data.demographics.profession,
    },
    readingPreferences: {
      complexity: apiTemplate.template_data.reading_preferences.complexity_level === 'expert' ? 'complex' : 
                  apiTemplate.template_data.reading_preferences.complexity_level === 'advanced' ? 'complex' :
                  apiTemplate.template_data.reading_preferences.complexity_level === 'intermediate' ? 'moderate' : 'simple',
      readingSpeed: apiTemplate.template_data.reading_preferences.reading_speed,
      attentionSpan: apiTemplate.template_data.reading_preferences.attention_span === 'high' ? 'long' : 
                    apiTemplate.template_data.reading_preferences.attention_span === 'medium' ? 'medium' : 'short',
      preferredLength: apiTemplate.template_data.reading_preferences.preferred_length === 'long' ? 'detailed' :
                      apiTemplate.template_data.reading_preferences.preferred_length === 'medium' ? 'medium' : 'brief',
    },
    personality: {
      traits: Object.entries(apiTemplate.template_data.personality_traits)
        .filter(([_, value]) => value > 7)
        .map(([key, _]) => key),
      criticalThinking: apiTemplate.template_data.personality_traits.analytical > 7 ? 'high' : 
                       apiTemplate.template_data.personality_traits.analytical > 4 ? 'medium' : 'low',
      emotionalSensitivity: apiTemplate.template_data.personality_traits.emotional > 7 ? 'high' :
                           apiTemplate.template_data.personality_traits.emotional > 4 ? 'medium' : 'low',
    },
    context: {
      purpose: 'evaluation',
      expertise: apiTemplate.template_data.reading_preferences.complexity_level === 'expert' ? 'expert' :
                apiTemplate.template_data.reading_preferences.complexity_level === 'advanced' ? 'specialist' : 'novice',
      relationship: 'colleague',
    },
    feedbackStyle: {
      tone: apiTemplate.template_data.feedback_style.criticism_style === 'direct' ? 'harsh' :
            apiTemplate.template_data.feedback_style.criticism_style === 'gentle' ? 'encouraging' : 'constructive',
      focus: apiTemplate.template_data.feedback_style.focus_areas.includes('content') ? 'content' :
             apiTemplate.template_data.feedback_style.focus_areas.includes('structure') ? 'structure' : 'technical',
      detail: apiTemplate.template_data.feedback_style.depth === 'deep' ? 'specific' :
              apiTemplate.template_data.feedback_style.depth === 'moderate' ? 'moderate' : 'high-level',
      priorities: apiTemplate.template_data.feedback_style.focus_areas,
    },
    color: 'purple', // Default color
  },
});

// API loading functions will replace hardcoded data

// Helper function to map numeric score to reaction
const mapScoreToReaction = (score: number): PersonaReaction => {
  if (score >= 9) return 'love';
  if (score >= 7) return 'like';
  if (score >= 5) return 'neutral';
  if (score >= 3) return 'confused';
  return 'bored';
};

// Mock feedback generator (would be replaced with actual AI service)
const generateMockFeedback = async (
  documentId: string,
  content: string,
  personas: ReaderPersona[]
): Promise<PersonaFeedback[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  const wordCount = content.trim().split(/\s+/).filter(Boolean).length;
  const sentences = content.split(/[.!?]+/).filter(Boolean).length;
  const avgWordsPerSentence = sentences > 0 ? wordCount / sentences : 0;
  
  return personas.map(persona => {
    // Generate feedback based on persona characteristics
    const engagementScore = Math.floor(Math.random() * 40) + 60; // 60-100
    const comprehensionScore = persona.readingPreferences.complexity === 'complex' 
      ? Math.floor(Math.random() * 30) + 70
      : Math.floor(Math.random() * 20) + 80;
    
    const reactions: PersonaReaction[] = ['love', 'like', 'neutral', 'confused', 'bored'];
    const overallReaction = reactions[Math.floor(Math.random() * reactions.length)];
    
    const feedback: PersonaFeedback = {
      personaId: persona.id,
      documentId,
      overallReaction,
      engagementScore,
      comprehensionScore,
      feedback: [
        {
          id: `feedback-${Date.now()}-${Math.random()}`,
          type: 'suggestion',
          severity: 'medium',
          title: `${persona.name}'s feedback`,
          message: generatePersonaSpecificFeedback(persona, content, avgWordsPerSentence),
          category: 'engagement',
          tags: [persona.category],
          lineNumber: Math.floor(Math.random() * 10) + 1,
        },
      ],
      predictions: {
        willFinishReading: engagementScore,
        wouldRecommend: Math.floor(Math.random() * 30) + 70,
        emotionalImpact: 'medium',
        memorability: 'memorable',
      },
      confidence: Math.floor(Math.random() * 20) + 80,
      generatedAt: new Date(),
    };
    
    return feedback;
  });
};

const generatePersonaSpecificFeedback = (
  persona: ReaderPersona,
  content: string,
  avgWordsPerSentence: number
): string => {
  const wordCount = content.trim().split(/\s+/).filter(Boolean).length;
  
  if (persona.category === 'academic') {
    return `As an academic reviewer, I notice this text needs more supporting evidence and citations. The argument structure could be strengthened with clearer thesis statements.`;
  } else if (persona.category === 'professional') {
    if (wordCount > 500) {
      return `This is too long for executive consumption. Get to the key points faster and use bullet points for better scanning.`;
    }
    return `Good professional tone, but consider adding more concrete data and actionable recommendations.`;
  } else if (persona.category === 'creative') {
    return `The narrative voice is engaging, but the pacing feels uneven. Consider adding more dialogue to break up exposition.`;
  } else if (persona.category === 'personal') {
    return `I love the personal touch in your writing! It feels authentic and relatable. Maybe add a few more specific examples.`;
  } else if (persona.category === 'age-specific' && persona.demographics.age && persona.demographics.age < 20) {
    return `This speaks to me! The language feels natural and the topics are relevant. Maybe add some social media references?`;
  }
  
  return `This is well-written overall. Consider adjusting the complexity level to better match your target audience.`;
};

export const usePersonaStore = create<PersonaStore>()(
  persist(
    (set, get) => ({
      // State
      personas: [], // Will be loaded from API
      selectedPersonas: [],
      templates: [], // Will be loaded from API
      feedback: [],
      insights: null,
      activeFeedbackMode: 'onDemand',
      maxConcurrentPersonas: 5,
      showConsensusOnly: false,
      lastUpdated: new Date(),
      isLoading: false,
      error: null,

      // Persona Management
      createPersona: (personaData) => {
        const newPersona: ReaderPersona = {
          ...personaData,
          id: `persona-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          isBuiltIn: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          personas: [...state.personas, newPersona],
          lastUpdated: new Date(),
        }));
        
        return newPersona;
      },

      updatePersona: (id, updates) => {
        set((state) => ({
          personas: state.personas.map((persona) =>
            persona.id === id
              ? { ...persona, ...updates, updatedAt: new Date() }
              : persona
          ),
          lastUpdated: new Date(),
        }));
      },

      deletePersona: (id) => {
        set((state) => ({
          personas: state.personas.filter((persona) => persona.id !== id && !persona.isBuiltIn),
          selectedPersonas: state.selectedPersonas.filter((personaId) => personaId !== id),
          lastUpdated: new Date(),
        }));
      },

      duplicatePersona: (id) => {
        const persona = get().personas.find((p) => p.id === id);
        if (persona) {
          const duplicated: ReaderPersona = {
            ...persona,
            id: `persona-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            name: `${persona.name} (Copy)`,
            isBuiltIn: false,
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          set((state) => ({
            personas: [...state.personas, duplicated],
            lastUpdated: new Date(),
          }));
        }
      },

      togglePersonaActive: (id) => {
        set((state) => ({
          personas: state.personas.map((persona) =>
            persona.id === id ? { ...persona, isActive: !persona.isActive } : persona
          ),
          lastUpdated: new Date(),
        }));
      },

      // Selection
      selectPersona: (id) => {
        set((state) => {
          const maxPersonas = state.maxConcurrentPersonas;
          const selected = state.selectedPersonas;
          
          if (selected.includes(id)) return state;
          
          const newSelected = selected.length >= maxPersonas
            ? [...selected.slice(1), id] // Remove oldest, add new
            : [...selected, id];
          
          return {
            selectedPersonas: newSelected,
            lastUpdated: new Date(),
          };
        });
      },

      toggleSelectedPersona: (id) => {
        set((state) => {
          const selected = state.selectedPersonas;
          const isSelected = selected.includes(id);
          
          const newSelected = isSelected
            ? selected.filter(personaId => personaId !== id)
            : selected.length >= state.maxConcurrentPersonas
              ? [...selected.slice(1), id] // Remove oldest, add new
              : [...selected, id];
          
          return {
            selectedPersonas: newSelected,
            lastUpdated: new Date(),
          };
        });
      },

      deselectPersona: (id) => {
        set((state) => ({
          selectedPersonas: state.selectedPersonas.filter((personaId) => personaId !== id),
          lastUpdated: new Date(),
        }));
      },

      selectAllPersonas: () => {
        set((state) => {
          const activePersonas = state.personas
            .filter(persona => persona.isActive)
            .slice(0, state.maxConcurrentPersonas)
            .map(persona => persona.id);
          
          return {
            selectedPersonas: activePersonas,
            lastUpdated: new Date(),
          };
        });
      },

      deselectAllPersonas: () => {
        set({
          selectedPersonas: [],
          lastUpdated: new Date(),
        });
      },

      // Templates
      createTemplate: (templateData) => {
        const newTemplate: PersonaTemplate = {
          ...templateData,
          id: `template-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          usageCount: 0,
        };

        set((state) => ({
          templates: [...state.templates, newTemplate],
          lastUpdated: new Date(),
        }));
      },

      useTemplate: (templateId, customizations = {}) => {
        const template = get().templates.find(t => t.id === templateId);
        if (!template) return;

        const newPersona: ReaderPersona = {
          id: `persona-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          name: customizations.name || template.name,
          description: customizations.description || template.description,
          category: template.category,
          demographics: { ...template.template.demographics, ...customizations.demographics },
          readingPreferences: { ...template.template.readingPreferences, ...customizations.readingPreferences },
          personality: { ...template.template.personality, ...customizations.personality },
          context: { ...template.template.context, ...customizations.context },
          feedbackStyle: { ...template.template.feedbackStyle, ...customizations.feedbackStyle },
          isBuiltIn: false,
          isActive: true,
          tags: customizations.tags || [template.category],
          color: customizations.color || template.template.color || 'gray',
          createdAt: new Date(),
          updatedAt: new Date(),
          ...customizations,
        };

        // Increment template usage
        set((state) => ({
          personas: [...state.personas, newPersona],
          templates: state.templates.map(t => 
            t.id === templateId ? { ...t, usageCount: t.usageCount + 1 } : t
          ),
          lastUpdated: new Date(),
        }));
      },

      // Feedback Generation
      generateFeedback: async (documentId, content, selectedPersonas) => {
        const state = get();
        const personaIds = selectedPersonas || state.selectedPersonas;
        
        if (personaIds.length === 0) return [];
        
        try {
          // Use the API service for multi-persona feedback
          const response = await personasApi.requestMultiFeedback({
            text: content,
            persona_ids: personaIds,
            context: {
              document_type: 'general',
              purpose: 'feedback',
            },
            options: {
              feedback_depth: 'standard',
              include_emotional_analysis: true,
              include_suggestions: true,
              enable_cross_analysis: true,
            },
          });
          
          // Map API response to our PersonaFeedback format
          const feedback = response.feedback_results.map(result => ({
            personaId: result.persona_id,
            documentId,
            overallReaction: mapScoreToReaction(result.scores.overall),
            engagementScore: result.scores.engagement * 10, // Convert 1-10 to percentage
            comprehensionScore: result.scores.comprehension * 10,
            feedback: result.specific_comments.map(comment => ({
              id: `feedback-${Date.now()}-${Math.random()}`,
              type: comment.type as CommentType,
              severity: comment.severity || 'medium',
              title: comment.comment,
              message: comment.comment,
              category: 'engagement' as FeedbackCategory,
              tags: [comment.type],
              lineNumber: comment.location?.start || 0,
            })),
            predictions: {
              willFinishReading: result.scores.engagement * 10,
              wouldRecommend: result.scores.overall * 10,
              emotionalImpact: result.emotional_impact.emotional_intensity > 7 ? 'high' : 
                               result.emotional_impact.emotional_intensity > 4 ? 'medium' : 'low',
              memorability: result.scores.overall > 7 ? 'memorable' : 'average',
            },
            confidence: 85, // Default confidence
            generatedAt: new Date(result.created_at),
          }));
          
          set((state) => ({
            feedback: [
              ...state.feedback.filter(f => f.documentId !== documentId),
              ...feedback,
            ],
            lastUpdated: new Date(),
          }));
          
          return feedback;
        } catch (error) {
          console.error('Error generating persona feedback:', error);
          // Fallback to mock feedback if API fails
          const personas = state.personas.filter(p => personaIds.includes(p.id));
          return await generateMockFeedback(documentId, content, personas);
        }
      },

      generateInsights: async (documentId) => {
        const state = get();
        const documentFeedback = state.feedback.filter(f => f.documentId === documentId);
        
        if (documentFeedback.length === 0) return null;
        
        // Generate mock insights
        const insights: PersonaInsights = {
          documentId,
          activePersonas: documentFeedback.map(f => f.personaId),
          consensus: {
            agreements: [],
            disagreements: [],
            universalIssues: [],
            polarizingElements: [],
          },
          audienceCompatibility: {
            primaryAudience: {
              personaId: documentFeedback[0].personaId,
              compatibilityScore: Math.floor(Math.random() * 30) + 70,
            },
            crossAudienceAppeal: Math.floor(Math.random() * 40) + 60,
            audienceConflicts: [],
          },
          optimizations: [],
          generatedAt: new Date(),
        };
        
        set((state) => ({
          insights,
          lastUpdated: new Date(),
        }));
        
        return insights;
      },

      clearFeedback: (documentId) => {
        set((state) => ({
          feedback: documentId 
            ? state.feedback.filter(f => f.documentId !== documentId)
            : [],
          insights: documentId && state.insights?.documentId === documentId ? null : state.insights,
          lastUpdated: new Date(),
        }));
      },

      // Import/Export
      exportPersonas: (personaIds) => {
        const state = get();
        const personasToExport = personaIds
          ? state.personas.filter((persona) => personaIds.includes(persona.id))
          : state.personas.filter((persona) => !persona.isBuiltIn);

        return JSON.stringify(personasToExport, null, 2);
      },

      importPersonas: (data) => {
        try {
          const importedPersonas: ReaderPersona[] = JSON.parse(data);
          
          set((state) => ({
            personas: [
              ...state.personas,
              ...importedPersonas.map((persona) => ({
                ...persona,
                id: `persona-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                isBuiltIn: false,
                createdAt: new Date(),
                updatedAt: new Date(),
              })),
            ],
            lastUpdated: new Date(),
          }));
        } catch (error) {
          console.error('Failed to import personas:', error);
        }
      },

      // Settings
      updateSettings: (settings) => {
        set((state) => ({
          ...state,
          ...settings,
          lastUpdated: new Date(),
        }));
      },

      // Getters
      getPersona: (id) => {
        return get().personas.find((persona) => persona.id === id);
      },

      getActivePersonas: () => {
        return get().personas.filter((persona) => persona.isActive);
      },

      getPersonasByCategory: (category) => {
        return get().personas.filter((persona) => persona.category === category);
      },

      getFeedbackForDocument: (documentId) => {
        return get().feedback.filter((feedback) => feedback.documentId === documentId);
      },
    }),
    {
      name: 'persona-store',
      version: 1,
    }
  )
);

// Computed selectors
export const usePersonaSelectors = () => {
  const store = usePersonaStore();
  
  return {
    // Get selected personas with full data
    getSelectedPersonas: () => {
      return store.personas.filter(p => store.selectedPersonas.includes(p.id));
    },
    
    // Get feedback summary for document
    getFeedbackSummary: (documentId: string) => {
      const feedback = store.getFeedbackForDocument(documentId);
      return {
        totalFeedback: feedback.length,
        averageEngagement: feedback.reduce((acc, f) => acc + f.engagementScore, 0) / (feedback.length || 1),
        averageComprehension: feedback.reduce((acc, f) => acc + f.comprehensionScore, 0) / (feedback.length || 1),
        reactions: feedback.map(f => f.overallReaction),
      };
    },
    
    // Get personas by category with counts
    getCategoryCounts: () => {
      const categories: Record<PersonaCategory, number> = {
        academic: 0,
        professional: 0,
        creative: 0,
        personal: 0,
        technical: 0,
        cultural: 0,
        'age-specific': 0,
        custom: 0,
      };
      
      store.personas.forEach(persona => {
        categories[persona.category]++;
      });
      
      return categories;
    },
    
    // Check if can select more personas
    canSelectMore: () => {
      return store.selectedPersonas.length < store.maxConcurrentPersonas;
    },
  };
};