// Authentication types
export interface User {
  id: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  subscriptionTier: 'free' | 'professional' | 'studio' | 'enterprise';
  tokenUsage: {
    used: number;
    limit: number;
    resetDate: Date;
  };
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    defaultDocumentType: string;
  };
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  lastTokenCheck: Date | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignupCredentials extends LoginCredentials {
  displayName: string;
}