# Backend Refactoring Plan: Remove Hardcoded Mock Data

This document outlines the necessary steps to refactor the backend API to use the mock-data-populated database instead of hardcoded mock data functions. This is a critical step to ensure the backend is testable and behaves consistently.

## Problem

Currently, a significant portion of the backend API endpoints return hardcoded mock data directly from functions within the `mock_data` package. This approach is inconsistent and prevents true integration testing, as it bypasses the database entirely.

**Affected Files:**

- `backend/api/agent_management.py`
- `backend/api/auth.py`
- `backend/api/blocks.py`
- `backend/api/health.py`
- `backend/core/multi_agent_engine.py`

## Instructions for the Developer

Your task is to refactor the backend services to remove all hardcoded mock data and connect the API endpoints to the existing database, which has been populated with a comprehensive set of mock data.

### 1. Connect to the Database

- All API endpoints that currently return hardcoded data must be modified to query the database instead.
- Use the existing database connection service in `core/database.py` for all database interactions.

### 2. Use the Data Mapping Document

- Refer to `backend/docs/frontend_data_mapping_detailed.md` to understand which database tables and columns correspond to the data structures expected by the frontend.
- This document provides a clear mapping between the frontend's data stores and the backend's database schema.

### 3. Remove Mock Data Functions

- Once the API endpoints are connected to the database, remove the now-unused mock data functions from the `mock_data` package.
- The `MultiAgentEngine.is_mock_mode()` check and similar conditional blocks for mock data should be removed from the API endpoints. The application should operate as if it's in a real environment, but with the database providing the mock data.

### Example: Refactoring `list_user_agents`

**Current (in `api/agent_management.py`):**

```python
# In mock mode, return mock agents
if MultiAgentEngine.is_mock_mode():
    mock_agents = get_mock_custom_agents()
    # ... filtering and pagination logic ...
    return AgentListResponse(...)

# Get agents from database
agents = await MultiAgentEngine.list_user_agents(...)
```

**Refactored:**

The `if MultiAgentEngine.is_mock_mode():` block should be removed. The code should now exclusively call `MultiAgentEngine.list_user_agents(...)`, which in turn should query the `custom_agents` table in the database.

## Acceptance Criteria

- All API endpoints in the affected files must fetch data from the database.
- The hardcoded mock data functions in the `mock_data` package should be removed.
- The application should function correctly using the data from the mock-populated database.
