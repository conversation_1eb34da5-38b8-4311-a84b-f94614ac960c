import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Bars3Icon, 
  BellIcon, 
  UserCircleIcon,
  CreditCardIcon,
  MagnifyingGlassIcon,
  CommandLineIcon,
  XMarkIcon,
  EllipsisVerticalIcon
} from '@heroicons/react/24/outline';
import { useAuth, useAuthActions, useTokenUsage } from '@/stores/authStore';
import { motion, AnimatePresence } from 'framer-motion';

interface HeaderProps {
  onMobileMenuOpen?: () => void;
}

const Header: React.FC<HeaderProps> = ({ onMobileMenuOpen }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { signOut } = useAuthActions();
  const tokenUsage = useTokenUsage();
  const [showMobileSearch, setShowMobileSearch] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/login');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const getTokenUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-500 bg-red-50';
    if (percentage >= 75) return 'text-yellow-500 bg-yellow-50';
    return 'text-green-500 bg-green-50';
  };

  const tokenPercentage = tokenUsage ? (tokenUsage.used / tokenUsage.limit) * 100 : 0;

  return (
    <header className="fixed top-0 left-0 right-0 z-mobile-header backdrop-blur-xl bg-white/90 border-b border-slate-200/50 shadow-sm mobile-safe-area">
      <div className="px-4 lg:px-8 py-3 lg:py-4">
        <div className="flex items-center justify-between">
          {/* Left section */}
          <div className="flex items-center space-x-3 lg:space-x-6">
            {/* Mobile menu button */}
            <button 
              onClick={onMobileMenuOpen}
              className="lg:hidden mobile-icon-btn text-slate-600 hover:text-slate-900 hover:bg-white/60 transition-all duration-200"
            >
              <Bars3Icon className="h-6 w-6" />
            </button>
            
            {/* Logo for mobile */}
            <div className="lg:hidden flex items-center">
              <div className="w-8 h-8 rounded-lg overflow-hidden shadow-sm">
                <img 
                  src="/src/assets/logo.png" 
                  alt="Revisionary Logo" 
                  className="w-full h-full object-cover"
                />
              </div>
              <h1 className="ml-2 text-lg font-bold text-slate-900">Revisionary</h1>
            </div>
            
            {/* Desktop Search */}
            <div className="hidden lg:flex items-center">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-4 w-4 text-slate-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search..."
                  className="block w-64 pl-9 pr-12 py-2 bg-slate-50/50 border border-slate-200/50 rounded-xl text-sm placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500/30 transition-all duration-200"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <div className="flex items-center space-x-1 bg-slate-200/50 px-1.5 py-0.5 rounded">
                    <CommandLineIcon className="h-3 w-3 text-slate-400" />
                    <span className="text-xs text-slate-400 font-medium">K</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right section */}
          <div className="flex items-center space-x-2 lg:space-x-4">
            {/* Mobile Search Button */}
            <button 
              onClick={() => setShowMobileSearch(true)}
              className="lg:hidden mobile-icon-btn text-slate-600 hover:text-slate-900 hover:bg-white/60 transition-all duration-200"
            >
              <MagnifyingGlassIcon className="h-5 w-5" />
            </button>
            {/* Token usage indicator */}
            {tokenUsage && (
              <div className={`hidden md:flex items-center space-x-2 lg:space-x-3 px-3 lg:px-4 py-2 rounded-xl lg:rounded-2xl border text-xs lg:text-sm ${getTokenUsageColor(tokenPercentage)} border-current/20 shadow-sm`}>
                <CreditCardIcon className="h-4 w-4 lg:h-5 lg:w-5" />
                <div className="hidden lg:flex lg:flex-col">
                  <span className="text-xs font-semibold uppercase tracking-wider opacity-80">Tokens</span>
                  <span className="text-sm font-bold">
                    {tokenUsage.used.toLocaleString()}/{tokenUsage.limit.toLocaleString()}
                  </span>
                </div>
                <div className="lg:hidden">
                  <span className="font-bold">
                    {Math.round((tokenUsage.used / tokenUsage.limit) * 100)}%
                  </span>
                </div>
                <div className="w-8 lg:w-12 h-2 bg-current/20 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-current rounded-full transition-all duration-500"
                    style={{ width: `${Math.min(tokenPercentage, 100)}%` }}
                  ></div>
                </div>
              </div>
            )}

            {/* Notifications */}
            <button className="relative mobile-icon-btn lg:p-3 rounded-xl lg:rounded-2xl text-slate-600 hover:text-slate-900 hover:bg-white/60 transition-all duration-200 group">
              <BellIcon className="h-5 w-5 lg:h-6 lg:w-6 group-hover:scale-110 transition-transform" />
              {/* Notification badge */}
              <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center animate-pulse">
                3
              </span>
            </button>

            {/* User menu */}
            <div className="relative">
              <button 
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-2 lg:space-x-3 p-2 lg:p-3 rounded-xl lg:rounded-2xl text-slate-700 hover:text-slate-900 hover:bg-white/60 transition-all duration-200 group border border-slate-200/50 shadow-sm"
              >
                {user?.photoURL ? (
                  <img
                    src={user.photoURL}
                    alt={user.displayName || user.email}
                    className="h-7 w-7 lg:h-8 lg:w-8 rounded-full ring-2 ring-purple-500/20"
                  />
                ) : (
                  <div className="h-7 w-7 lg:h-8 lg:w-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                    <span className="text-xs lg:text-sm font-bold text-white">
                      {(user?.displayName || user?.email || 'U').charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
                <div className="hidden md:block text-left">
                  <p className="text-sm font-semibold">{user?.displayName || 'Developer User'}</p>
                  <p className="text-xs text-slate-500 capitalize font-medium">{user?.subscriptionTier || 'Professional'}</p>
                </div>
                <EllipsisVerticalIcon className="md:hidden h-4 w-4 text-slate-400" />
              </button>
              
              {/* User menu dropdown */}
              <AnimatePresence>
                {showUserMenu && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95, y: -10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.95, y: -10 }}
                    className="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-50"
                  >
                    <div className="px-4 py-2 border-b border-slate-200">
                      <p className="text-sm font-semibold text-slate-900">{user?.displayName || 'Developer User'}</p>
                      <p className="text-xs text-slate-500">{user?.email}</p>
                    </div>
                    <button
                      onClick={handleSignOut}
                      className="w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors"
                    >
                      Sign Out
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Desktop Sign out */}
            <button
              onClick={handleSignOut}
              className="hidden xl:flex items-center px-4 py-2 text-sm font-semibold text-slate-600 hover:text-slate-900 hover:bg-white/60 rounded-2xl transition-all duration-200 border border-slate-200/50"
            >
              Sign Out
            </button>
          </div>
        </div>
      </div>
      
      {/* Mobile Search Modal */}
      <AnimatePresence>
        {showMobileSearch && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="lg:hidden fixed inset-0 z-mobile-modal bg-black/50 backdrop-blur-sm"
            onClick={() => setShowMobileSearch(false)}
          >
            <motion.div
              initial={{ y: -100, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -100, opacity: 0 }}
              className="bg-white mobile-safe-area"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-4 border-b border-slate-200">
                <div className="flex items-center space-x-3">
                  <div className="relative flex-1">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <MagnifyingGlassIcon className="h-5 w-5 text-slate-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search documents, templates..."
                      autoFocus
                      className="block w-full pl-10 pr-4 py-3 bg-slate-50 border border-slate-200 rounded-xl text-base placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                  <button
                    onClick={() => setShowMobileSearch(false)}
                    className="mobile-icon-btn text-slate-600 hover:text-slate-900 hover:bg-slate-100"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>
              
              {/* Search suggestions/results would go here */}
              <div className="p-4">
                <p className="text-sm text-slate-500 text-center py-8">
                  Start typing to search your documents, templates, and more...
                </p>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
};

export default Header;