// Cross-Reference Intelligence Types

export interface CrossReferenceEntity {
  id: string;
  name: string;
  type: EntityType;
  aliases: string[];
  attributes: Record<string, any>;
  firstMention: TextPosition;
  mentions: EntityMention[];
  confidence: number; // 0-100 confidence that this is a real entity
  verified: boolean; // Whether user has confirmed this entity
  tags: string[];
  category?: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export type EntityType = 
  | 'character'
  | 'location'
  | 'organization'
  | 'concept'
  | 'event'
  | 'object'
  | 'terminology'
  | 'date'
  | 'custom';

export interface EntityMention {
  id: string;
  entityId: string;
  documentId: string;
  position: TextPosition;
  context: string; // Surrounding text
  excerpt: string; // The actual mention text
  mentionType: 'direct' | 'indirect' | 'pronoun' | 'description';
  attributes: Record<string, any>; // Attributes mentioned in this specific mention
  isInconsistent: boolean;
  inconsistencies?: Inconsistency[];
  confidence: number;
  timestamp: Date;
}

export interface TextPosition {
  documentId: string;
  startOffset: number;
  endOffset: number;
  lineNumber: number;
  characterPosition: number;
  paragraph: number;
}

export interface Inconsistency {
  id: string;
  type: InconsistencyType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  conflictingMentions: string[]; // EntityMention IDs
  suggestedResolution?: string;
  autoResolvable: boolean;
  category: string;
  detectedAt: Date;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: 'user' | 'auto';
}

export type InconsistencyType =
  | 'attribute-conflict'
  | 'timeline-conflict'
  | 'name-variation'
  | 'description-conflict'
  | 'relationship-conflict'
  | 'location-conflict'
  | 'state-conflict'
  | 'terminology-conflict';

export interface Timeline {
  id: string;
  name: string;
  description?: string;
  events: TimelineEvent[];
  startDate?: Date | string;
  endDate?: Date | string;
  isRelative: boolean; // Whether dates are relative (Day 1, Chapter 2) or absolute
  unit: 'minutes' | 'hours' | 'days' | 'weeks' | 'months' | 'years' | 'custom';
  customUnit?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TimelineEvent {
  id: string;
  timelineId: string;
  name: string;
  description?: string;
  date: Date | string | number; // Can be absolute date, relative string, or number
  duration?: number; // In timeline units
  entities: string[]; // Entity IDs involved in this event
  mentions: string[]; // EntityMention IDs that reference this event
  position: TextPosition;
  isInferred: boolean; // Whether this was auto-detected vs manually added
  confidence: number;
  tags: string[];
  relationships: EventRelationship[];
}

export interface EventRelationship {
  type: 'before' | 'after' | 'during' | 'causes' | 'caused-by' | 'concurrent';
  targetEventId: string;
  confidence: number;
  isInferred: boolean;
}

export interface WorldBuildingRule {
  id: string;
  name: string;
  description: string;
  category: string;
  ruleType: 'physical-law' | 'social-rule' | 'magical-system' | 'technology' | 'custom';
  entities: string[]; // Entities this rule applies to
  constraints: RuleConstraint[];
  violations: RuleViolation[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface RuleConstraint {
  type: 'must' | 'must-not' | 'should' | 'should-not';
  condition: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
}

export interface RuleViolation {
  id: string;
  ruleId: string;
  description: string;
  position: TextPosition;
  severity: 'minor' | 'major' | 'critical';
  suggestion?: string;
  detectedAt: Date;
  resolved: boolean;
}

export interface ConsistencyReport {
  documentId: string;
  generatedAt: Date;
  overallScore: number; // 0-100
  scores: {
    character: number;
    timeline: number;
    worldBuilding: number;
    terminology: number;
  };
  totalEntities: number;
  totalInconsistencies: number;
  inconsistenciesByType: Record<InconsistencyType, number>;
  inconsistenciesBySeverity: Record<'low' | 'medium' | 'high' | 'critical', number>;
  topIssues: Inconsistency[];
  suggestions: ConsistencySuggestion[];
}

export interface ConsistencySuggestion {
  id: string;
  type: 'merge-entities' | 'split-entity' | 'verify-timeline' | 'add-attributes' | 'resolve-conflict';
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  effort: 'easy' | 'moderate' | 'difficult';
  impact: 'minor' | 'moderate' | 'major';
  affectedEntities: string[];
  autoResolvable: boolean;
}

// Store types
export interface CrossReferenceState {
  entities: CrossReferenceEntity[];
  mentions: EntityMention[];
  timelines: Timeline[];
  rules: WorldBuildingRule[];
  inconsistencies: Inconsistency[];
  
  // UI State
  selectedEntity?: string;
  selectedTimeline?: string;
  activeFilters: ConsistencyFilter[];
  showResolved: boolean;
  autoDetectionEnabled: boolean;
  
  // Analysis State
  lastAnalysis?: Date;
  analysisInProgress: boolean;
  currentReport?: ConsistencyReport;
  
  // Settings
  detectionSettings: DetectionSettings;
}

export interface ConsistencyFilter {
  type: 'entity-type' | 'severity' | 'status' | 'category';
  value: string;
  enabled: boolean;
}

export interface DetectionSettings {
  minConfidence: number; // Minimum confidence to auto-create entities
  entityTypes: EntityType[]; // Which types to auto-detect
  nameVariationTolerance: number; // How tolerant to be of name variations
  contextWindow: number; // Words around mention to include as context
  realTimeAnalysis: boolean;
  smartSuggestions: boolean;
}

export interface CrossReferenceActions {
  // Entity Management
  createEntity: (entity: Omit<CrossReferenceEntity, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateEntity: (id: string, updates: Partial<CrossReferenceEntity>) => void;
  deleteEntity: (id: string) => void;
  mergeEntities: (sourceId: string, targetId: string) => void;
  splitEntity: (entityId: string, mentionIds: string[], newEntityData: Partial<CrossReferenceEntity>) => void;
  
  // Mention Management
  addMention: (mention: Omit<EntityMention, 'id' | 'timestamp'>) => void;
  updateMention: (id: string, updates: Partial<EntityMention>) => void;
  deleteMention: (id: string) => void;
  
  // Timeline Management
  createTimeline: (timeline: Omit<Timeline, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateTimeline: (id: string, updates: Partial<Timeline>) => void;
  deleteTimeline: (id: string) => void;
  addTimelineEvent: (timelineId: string, event: Omit<TimelineEvent, 'id' | 'timelineId'>) => void;
  
  // World Building Rules
  createRule: (rule: Omit<WorldBuildingRule, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateRule: (id: string, updates: Partial<WorldBuildingRule>) => void;
  deleteRule: (id: string) => void;
  
  // Inconsistency Management
  resolveInconsistency: (id: string, resolution: 'accept' | 'reject' | 'merge' | 'custom', customData?: any) => void;
  markInconsistencyResolved: (id: string) => void;
  
  // Analysis
  analyzeDocument: (documentId: string, content: string) => Promise<ConsistencyReport>;
  analyzeText: (text: string, position: TextPosition) => Promise<EntityMention[]>;
  generateReport: (documentId: string) => ConsistencyReport;
  
  // Import/Export
  exportData: (entityIds?: string[]) => string;
  importData: (data: string) => void;
  
  // Settings
  updateSettings: (settings: Partial<DetectionSettings>) => void;
  
  // Getters
  getEntity: (id: string) => CrossReferenceEntity | undefined;
  getEntitiesByType: (type: EntityType) => CrossReferenceEntity[];
  getMentionsForEntity: (entityId: string) => EntityMention[];
  getInconsistenciesForEntity: (entityId: string) => Inconsistency[];
  getUnresolvedInconsistencies: () => Inconsistency[];
  getTimelineEvents: (timelineId: string) => TimelineEvent[];
  searchEntities: (query: string) => CrossReferenceEntity[];
}

export type CrossReferenceStore = CrossReferenceState & CrossReferenceActions;

// Analysis and Detection Types
export interface DetectionResult {
  entities: Partial<CrossReferenceEntity>[];
  mentions: Partial<EntityMention>[];
  inconsistencies: Inconsistency[];
  confidence: number;
  processingTime: number;
}

export interface EntityPattern {
  type: EntityType;
  regex: RegExp;
  contextClues: string[];
  confidence: number;
  priority: number;
}

export interface ConsistencyBadge {
  entityId: string;
  mentionId: string;
  type: 'verified' | 'inconsistent' | 'new' | 'suggestion';
  severity?: 'low' | 'medium' | 'high';
  message: string;
  position: TextPosition;
  onClick?: () => void;
}

// Export event types for real-time updates
export interface CrossReferenceEvent {
  type: 'entity-created' | 'entity-updated' | 'inconsistency-detected' | 'analysis-complete';
  data: any;
  timestamp: Date;
  documentId?: string;
}