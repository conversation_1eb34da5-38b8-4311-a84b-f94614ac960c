-- Revisionary Mock Data - Part 6: Health Metrics
-- Document health and quality analysis

DO $$
DECLARE
    -- Get user IDs from firebase_uid (from 001_core_data.sql)
    sarah_id UUID;
    alex_id UUID;
    kim_id UUID;
    maya_id UUID;
    james_id UUID;
    
    -- Get document IDs by title (from 001_core_data.sql)
    sarah_climate_doc_id UUID;
    alex_cafe_doc_id UUID;
    kim_neural_doc_id UUID;
    maya_chap1_doc_id UUID;
    james_q4_doc_id UUID;
    
    -- Get block IDs for health references
    sarah_climate_para1_id UUID;
    alex_cafe_para1_id UUID;
    kim_neural_para_id UUID;
    maya_chap1_para1_id UUID;
    james_q4_para_id UUID;
    
BEGIN
    -- Get user IDs from existing users
    SELECT id INTO sarah_id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1;
    SELECT id INTO alex_id FROM users WHERE firebase_uid = 'firebase_user_002' LIMIT 1;
    SELECT id INTO kim_id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1;
    SELECT id INTO maya_id FROM users WHERE firebase_uid = 'firebase_user_004' LIMIT 1;
    SELECT id INTO james_id FROM users WHERE firebase_uid = 'firebase_user_005' LIMIT 1;
    
    -- Get document IDs from existing documents
    SELECT id INTO sarah_climate_doc_id FROM documents WHERE title = 'Climate Change Research Paper' LIMIT 1;
    SELECT id INTO alex_cafe_doc_id FROM documents WHERE title = 'The Midnight Café' LIMIT 1;
    SELECT id INTO kim_neural_doc_id FROM documents WHERE title = 'Neural Networks in Medical Diagnosis' LIMIT 1;
    SELECT id INTO maya_chap1_doc_id FROM documents WHERE title = 'Chronicles of Aetheria: Chapter 1' LIMIT 1;
    SELECT id INTO james_q4_doc_id FROM documents WHERE title = 'Q4 Marketing Strategy Report' LIMIT 1;
    
    -- Get block IDs for health and entity references
    SELECT id INTO sarah_climate_para1_id FROM blocks WHERE content LIKE 'Climate change represents one of the most pressing%' AND document_id = sarah_climate_doc_id LIMIT 1;
    SELECT id INTO alex_cafe_para1_id FROM blocks WHERE content LIKE 'Elena had walked past the corner%' AND document_id = alex_cafe_doc_id LIMIT 1;
    SELECT id INTO kim_neural_para_id FROM blocks WHERE content LIKE 'The integration of artificial neural networks%' AND document_id = kim_neural_doc_id LIMIT 1;
    SELECT id INTO maya_chap1_para1_id FROM blocks WHERE content LIKE 'The ancient crown of Aetheria lay in fragments%' AND document_id = maya_chap1_doc_id LIMIT 1;
    SELECT id INTO james_q4_para_id FROM blocks WHERE content LIKE 'Q4 2024 demonstrated exceptional growth%' AND document_id = james_q4_doc_id LIMIT 1;

    -- =================================================================
    -- HEALTH METRICS - Document health and quality analysis
    -- =================================================================
    
    -- Sarah's climate research paper health metrics
    INSERT INTO health_metrics (block_id, user_id, overall_score, readability_score, clarity_score, voice_consistency_score, brand_alignment_score, flesch_kincaid_grade, flesch_reading_ease, passive_voice_percentage, avg_sentence_length, complex_words_percentage, processing_time_ms) 
    VALUES
    (sarah_climate_para1_id, sarah_id, 83.8, 78.5, 88.0, 85.0, NULL, 12.8, 48.5, 15.2, 22.4, 18.7, 2150),
    
    -- Alex's creative writing health metrics  
    (alex_cafe_para1_id, alex_id, 89.3, 85.0, 94.0, 89.0, NULL, 9.2, 65.8, 8.3, 18.6, 12.4, 1890),
    
    -- Dr. Kim's medical research health metrics
    (kim_neural_para_id, kim_id, 90.7, 82.0, 96.0, 94.0, NULL, 14.6, 42.1, 18.9, 25.8, 22.3, 3200),
    
    -- Maya's fantasy novel health metrics
    (maya_chap1_para1_id, maya_id, 90.0, 87.0, 91.0, 92.0, NULL, 8.4, 68.2, 6.7, 16.9, 11.8, 1650),
    
    -- James's business report health metrics
    (james_q4_para_id, james_id, 91.7, 89.0, 95.0, 91.0, 94.0, 11.5, 52.4, 12.1, 19.8, 15.6, 1420);

END $$;