"""
LLM Service for Revisionary

Purpose: Centralized service for managing LLM API calls with cost optimization
- Supports OpenAI (GPT-4.1 models) and Google Gemini
- Implements aggressive caching to reduce costs
- Tracks token usage and costs in real-time
- Provides model routing based on agent type

Features:
- Multi-provider support (OpenAI, Google)
- Context caching for repeated queries
- Cost tracking and budget enforcement
- Automatic model downgrade on budget limits
- Batch processing support
"""

import os
import json
import hashlib
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import structlog
from openai import AsyncOpenAI
import google.generativeai as genai

from core.redis_service import get_redis
from core.settings import settings

logger = structlog.get_logger(__name__)


class LLMService:
    """Centralized LLM service with cost optimization and caching."""
    
    def __init__(self):
        """Initialize LLM service with provider clients."""
        # Initialize OpenAI client
        self.openai_client = AsyncOpenAI(
            api_key=os.getenv('OPENAI_API_KEY', settings.openai_api_key)
        )
        
        # Initialize Gemini
        genai.configure(api_key=os.getenv('GOOGLE_AI_API_KEY', settings.google_ai_api_key))
        
        # Load model configurations
        self.model_configs = self._load_model_configs()
        
        # Cost tracking
        self.monthly_budget = float(os.getenv('MAX_TOKEN_COST_MONTH', '50'))
        self.alert_threshold = float(os.getenv('ALERT_THRESHOLD_5MIN', '1'))
        
        # Concurrency control
        self.concurrent_limiter = asyncio.Semaphore(
            int(os.getenv('MAX_CONCURRENT_LLM_CALLS', '5'))
        )
        
        # Initialize Redis
        self.redis = None
        
        logger.info("LLM Service initialized", 
                   monthly_budget=self.monthly_budget,
                   providers=["openai", "google"])
    
    async def initialize(self):
        """Initialize async resources."""
        self.redis = await get_redis()
        logger.info("LLM Service async resources initialized")
    
    def _load_model_configs(self) -> Dict[str, Dict]:
        """Load model configurations from environment variables."""
        return {
            'grammar': {
                'provider': os.getenv('GRAMMAR_MODEL_PROVIDER', 'openai'),
                'model': os.getenv('GRAMMAR_MODEL_NAME', 'gpt-4.1-nano-2025-04-14'),
                'max_tokens': int(os.getenv('GRAMMAR_MODEL_MAX_TOKENS', '200')),
                'cost_per_1k_input': float(os.getenv('GRAMMAR_MODEL_COST_PER_1K_INPUT', '0.0001')),
                'cost_per_1k_output': float(os.getenv('GRAMMAR_MODEL_COST_PER_1K_OUTPUT', '0.0004')),
            },
            'style': {
                'provider': os.getenv('STYLE_MODEL_PROVIDER', 'openai'),
                'model': os.getenv('STYLE_MODEL_NAME', 'gpt-4.1-mini-2025-04-14'),
                'max_tokens': int(os.getenv('STYLE_MODEL_MAX_TOKENS', '400')),
                'cost_per_1k_input': float(os.getenv('STYLE_MODEL_COST_PER_1K_INPUT', '0.0004')),
                'cost_per_1k_output': float(os.getenv('STYLE_MODEL_COST_PER_1K_OUTPUT', '0.0016')),
            },
            'structure': {
                'provider': os.getenv('STRUCTURE_MODEL_PROVIDER', 'openai'),
                'model': os.getenv('STRUCTURE_MODEL_NAME', 'gpt-4.1-2025-04-14'),
                'max_tokens': int(os.getenv('STRUCTURE_MODEL_MAX_TOKENS', '800')),
                'cost_per_1k_input': float(os.getenv('STRUCTURE_MODEL_COST_PER_1K_INPUT', '0.002')),
                'cost_per_1k_output': float(os.getenv('STRUCTURE_MODEL_COST_PER_1K_OUTPUT', '0.008')),
            },
            'content': {
                'provider': os.getenv('CONTENT_MODEL_PROVIDER', 'google'),
                'model': os.getenv('CONTENT_MODEL_NAME', 'gemini-2.5-flash-lite-preview'),
                'max_tokens': int(os.getenv('CONTENT_MODEL_MAX_TOKENS', '1500')),
                'cost_per_1k_input': float(os.getenv('CONTENT_MODEL_COST_PER_1K_INPUT', '0.0001')),
                'cost_per_1k_output': float(os.getenv('CONTENT_MODEL_COST_PER_1K_OUTPUT', '0.0004')),
            }
        }
    
    async def get_completion(
        self, 
        agent_type: str, 
        prompt: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Get completion from appropriate LLM with caching and cost tracking.
        
        Args:
            agent_type: Type of agent (grammar, style, structure, content)
            prompt: The prompt to send to the LLM
            context: Optional context for the request
            
        Returns:
            Dict containing text response and usage information
        """
        if not self.redis:
            await self.initialize()
        
        # Check budget before making call
        await self._check_budget()
        
        # Generate cache key
        cache_key = self._generate_cache_key(agent_type, prompt)
        
        # Check Redis cache first (2 hour TTL)
        cached = await self._get_cached_response(cache_key)
        if cached:
            logger.info(f"Cache hit for {agent_type}", cache_key=cache_key[:16])
            return cached
        
        # Get model configuration
        config = self.model_configs.get(agent_type)
        if not config:
            raise ValueError(f"Unknown agent type: {agent_type}")
        
        # Use semaphore to limit concurrent calls
        async with self.concurrent_limiter:
            # Track start time for latency monitoring
            start_time = datetime.utcnow()
            
            # Call appropriate provider
            if config['provider'] == 'openai':
                result = await self._call_openai(config, prompt, context)
            elif config['provider'] == 'google':
                result = await self._call_gemini(config, prompt, context)
            else:
                raise ValueError(f"Unknown provider: {config['provider']}")
            
            # Calculate latency
            latency = (datetime.utcnow() - start_time).total_seconds()
            result['latency'] = latency
            
            # Cache result
            await self._cache_response(cache_key, result)
            
            # Track costs
            await self._track_cost(agent_type, config, result['usage'])
            
            logger.info(
                f"LLM call completed",
                agent_type=agent_type,
                provider=config['provider'],
                model=config['model'],
                latency=latency,
                tokens_in=result['usage']['input_tokens'],
                tokens_out=result['usage']['output_tokens'],
                cached_tokens=result['usage'].get('cached_tokens', 0)
            )
            
            return result
    
    async def _call_openai(
        self, 
        config: Dict, 
        prompt: str, 
        context: Optional[Dict]
    ) -> Dict[str, Any]:
        """Call OpenAI API with caching optimization."""
        try:
            # Prepare messages
            messages = [
                {"role": "system", "content": "You are a helpful writing assistant."},
                {"role": "user", "content": prompt}
            ]
            
            # Add context if provided
            if context and context.get('previous_text'):
                messages.insert(1, {
                    "role": "assistant", 
                    "content": f"Context: {context['previous_text'][:500]}"
                })
            
            # Call OpenAI with caching-friendly parameters
            response = await self.openai_client.chat.completions.create(
                model=config['model'],
                messages=messages,
                max_tokens=config['max_tokens'],
                temperature=0.3,  # Lower temperature for consistency
                seed=12345,  # Fixed seed for better caching
                stream=False,
                presence_penalty=0,
                frequency_penalty=0
            )
            
            # Extract usage information
            usage = {
                'input_tokens': response.usage.prompt_tokens,
                'output_tokens': response.usage.completion_tokens,
                'total_tokens': response.usage.total_tokens,
                'cached_tokens': 0  # OpenAI doesn't report cached tokens yet
            }
            
            # Check if response indicates caching (future feature)
            if hasattr(response.usage, 'prompt_tokens_details'):
                usage['cached_tokens'] = getattr(
                    response.usage.prompt_tokens_details, 
                    'cached_tokens', 
                    0
                )
            
            return {
                'text': response.choices[0].message.content,
                'usage': usage,
                'model': config['model'],
                'provider': 'openai'
            }
            
        except Exception as e:
            logger.error(f"OpenAI API error", error=str(e), model=config['model'])
            raise
    
    async def _call_gemini(
        self, 
        config: Dict, 
        prompt: str, 
        context: Optional[Dict]
    ) -> Dict[str, Any]:
        """Call Gemini API with context caching for long documents."""
        try:
            # Initialize model
            model = genai.GenerativeModel(config['model'])
            
            # For long contexts, use caching
            if len(prompt) > 5000 and context and context.get('document_id'):
                # Create cached context for document
                cache_display_name = f"doc_{context['document_id']}_{datetime.utcnow().strftime('%Y%m%d%H')}"
                
                try:
                    # Try to create cached content
                    cache = genai.caching.CachedContent.create(
                        model=config['model'],
                        display_name=cache_display_name,
                        contents=[prompt],
                        ttl=timedelta(minutes=60)  # 1 hour cache
                    )
                    
                    # Use cached model
                    model = genai.GenerativeModel.from_cached_content(cached_content=cache)
                    response = await model.generate_content_async("Analyze and improve this text.")
                    
                    logger.info("Using Gemini cached context", cache_name=cache_display_name)
                    
                except Exception as cache_error:
                    logger.warning(f"Gemini caching failed, using direct call", error=str(cache_error))
                    response = await model.generate_content_async(prompt)
            else:
                # Direct call for short prompts
                response = await model.generate_content_async(prompt)
            
            # Extract usage information
            usage_metadata = response.usage_metadata
            usage = {
                'input_tokens': usage_metadata.prompt_token_count,
                'output_tokens': usage_metadata.candidates_token_count,
                'total_tokens': usage_metadata.total_token_count,
                'cached_tokens': getattr(usage_metadata, 'cached_content_token_count', 0)
            }
            
            return {
                'text': response.text,
                'usage': usage,
                'model': config['model'],
                'provider': 'google'
            }
            
        except Exception as e:
            logger.error(f"Gemini API error", error=str(e), model=config['model'])
            raise
    
    def _generate_cache_key(self, agent_type: str, prompt: str) -> str:
        """Generate a cache key for the prompt."""
        # Create hash of prompt for cache key
        prompt_hash = hashlib.sha256(prompt.encode()).hexdigest()[:16]
        return f"llm:cache:{agent_type}:{prompt_hash}"
    
    async def _get_cached_response(self, cache_key: str) -> Optional[Dict]:
        """Get cached response from Redis."""
        try:
            cached = await self.redis.get(cache_key)
            if cached:
                return json.loads(cached)
        except Exception as e:
            logger.warning(f"Cache retrieval failed", error=str(e))
        return None
    
    async def _cache_response(self, cache_key: str, response: Dict):
        """Cache response in Redis with 2 hour TTL."""
        try:
            # Remove latency from cached response
            cache_data = response.copy()
            cache_data.pop('latency', None)
            
            # Cache for 2 hours
            await self.redis.set(
                cache_key, 
                json.dumps(cache_data), 
                ttl=7200  # 2 hours
            )
        except Exception as e:
            logger.warning(f"Cache storage failed", error=str(e))
    
    async def _check_budget(self):
        """Check if we're within budget before making API call."""
        # Get current month's spend
        monthly_key = f"cost:monthly:{datetime.utcnow().strftime('%Y%m')}"
        current_spend = await self.redis.get(monthly_key)
        
        if current_spend:
            spend_amount = float(current_spend)
            
            # Check if we're at 90% of budget
            if spend_amount >= self.monthly_budget * 0.9:
                logger.warning(
                    "Approaching monthly budget limit",
                    current_spend=spend_amount,
                    budget=self.monthly_budget
                )
                
                # Check if we should force downgrade
                if os.getenv('FORCE_MODEL_DOWNGRADE', 'false').lower() == 'true':
                    raise Exception("Monthly budget exceeded - model downgrade required")
    
    async def _track_cost(self, agent_type: str, config: Dict, usage: Dict):
        """Track token usage and costs."""
        # Calculate costs
        input_tokens = usage['input_tokens']
        cached_tokens = usage.get('cached_tokens', 0)
        output_tokens = usage['output_tokens']
        
        # Apply caching discount (75% off for cached tokens)
        billable_input = input_tokens - (cached_tokens * 0.75)
        
        # Calculate costs in dollars
        input_cost = (billable_input / 1000) * config['cost_per_1k_input']
        output_cost = (output_tokens / 1000) * config['cost_per_1k_output']
        total_cost = input_cost + output_cost
        
        # Update Redis counters
        await self._update_cost_counters(total_cost)
        
        # Check alert thresholds
        await self._check_cost_alerts()
        
        logger.info(
            "Token usage tracked",
            agent_type=agent_type,
            input_tokens=input_tokens,
            cached_tokens=cached_tokens,
            output_tokens=output_tokens,
            cost=f"${total_cost:.4f}"
        )
    
    async def _update_cost_counters(self, cost: float):
        """Update cost tracking counters in Redis."""
        now = datetime.utcnow()
        
        # Define counter keys
        monthly_key = f"cost:monthly:{now.strftime('%Y%m')}"
        daily_key = f"cost:daily:{now.strftime('%Y%m%d')}"
        window_key = f"cost:5min:{now.strftime('%Y%m%d%H%M')}"
        
        # Update counters atomically
        pipeline = self.redis.redis_client.pipeline()
        pipeline.incrbyfloat(monthly_key, cost)
        pipeline.incrbyfloat(daily_key, cost)
        pipeline.incrbyfloat(window_key, cost)
        pipeline.expire(window_key, 300)  # 5 minute expiry
        pipeline.expire(daily_key, 86400)  # 1 day expiry
        pipeline.expire(monthly_key, 2592000)  # 30 day expiry
        await pipeline.execute()
    
    async def _check_cost_alerts(self):
        """Check if we need to send cost alerts."""
        # Check 5-minute window
        now = datetime.utcnow()
        window_key = f"cost:5min:{now.strftime('%Y%m%d%H%M')}"
        window_cost = await self.redis.get(window_key)
        
        if window_cost and float(window_cost) > self.alert_threshold:
            logger.error(
                "High cost alert!",
                window_cost=float(window_cost),
                threshold=self.alert_threshold
            )
            # TODO: Send actual alert (email, Slack, etc.)
    
    async def get_monthly_spend(self) -> float:
        """Get current month's total spend."""
        monthly_key = f"cost:monthly:{datetime.utcnow().strftime('%Y%m')}"
        spend = await self.redis.get(monthly_key)
        return float(spend) if spend else 0.0
    
    async def get_daily_spend(self) -> float:
        """Get today's total spend."""
        daily_key = f"cost:daily:{datetime.utcnow().strftime('%Y%m%d')}"
        spend = await self.redis.get(daily_key)
        return float(spend) if spend else 0.0


# Global instance
_llm_service: Optional[LLMService] = None


async def get_llm_service() -> LLMService:
    """Get or create LLM service instance."""
    global _llm_service
    if _llm_service is None:
        _llm_service = LLMService()
        await _llm_service.initialize()
    return _llm_service