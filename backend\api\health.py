"""
Writing Health Analysis API Endpoints

Purpose: Provides REST API endpoints for writing health analysis including:
- Real-time text health analysis and scoring
- Historical health metrics and trends
- Health issue detection and resolution tracking
- Personalized health insights and recommendations

Responsibilities:
- Text health analysis request handling
- Health metrics calculation and storage
- Health trends and analytics generation
- Issue categorization and resolution guidance
- User health preferences management

Used by: Frontend health dashboard, real-time health workers, analytics components
Dependencies: core.multi_agent_engine, health analysis workers, core.auth
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import asyncio
import structlog

from api.auth import get_current_user
from core.auth import UserClaims
from core.queue_manager import get_queue_manager
from core.redis_service import get_redis
from core.debounced_health_service import get_debounced_health_service
from core.database import get_database, DatabaseService

# Configure logging
logger = structlog.get_logger(__name__)

# Create router
router = APIRouter()

# Request/Response Models
class HealthAnalysisRequest(BaseModel):
    """Request model for text health analysis."""
    text: str = Field(..., min_length=1, description="Text to analyze")
    block_id: Optional[str] = Field(None, description="Associated block ID")
    document_id: Optional[str] = Field(None, description="Associated document ID")
    analysis_type: str = Field(default="comprehensive", description="Type of analysis to perform")
    preferences: Optional[Dict[str, Any]] = Field(default_factory=dict)

class BatchHealthAnalysisRequest(BaseModel):
    """Request model for batch health analysis."""
    texts: List[str] = Field(..., min_items=1, max_items=50)
    block_ids: Optional[List[str]] = None
    document_id: Optional[str] = None
    analysis_type: str = Field(default="comprehensive")

class HealthIssueResolution(BaseModel):
    """Request model for resolving a health issue."""
    issue_id: str
    resolution_type: str = Field(..., description="How the issue was resolved")
    user_feedback: Optional[str] = Field(None, max_length=500)

class HealthPreferencesUpdate(BaseModel):
    """Request model for updating health preferences."""
    focus_areas: Optional[List[str]] = None
    scoring_weights: Optional[Dict[str, float]] = None
    notification_threshold: Optional[int] = Field(None, ge=0, le=100)
    auto_analysis: Optional[bool] = None

class HealthResponse(BaseModel):
    """Response model for health analysis."""
    success: bool
    data: Dict[str, Any]

class HealthListResponse(BaseModel):
    """Response model for health data lists."""
    success: bool
    data: List[Dict[str, Any]]
    meta: Dict[str, Any]

@router.post("/analyze", response_model=HealthResponse)
async def analyze_text_health(
    analysis_request: HealthAnalysisRequest,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Analyze text health metrics and provide improvement suggestions.
    
    Args:
        analysis_request: Text analysis request data
        current_user: Current authenticated user
        
    Returns:
        HealthResponse: Health analysis results
    """
    try:
        logger.info(
            "Analyzing text health",
            user_id=current_user.user_id,
            text_length=len(analysis_request.text),
            analysis_type=analysis_request.analysis_type,
            block_id=analysis_request.block_id
        )
        
        # Initialize cache options
        cache_options = {
            'analysis_type': analysis_request.analysis_type,
            'focus_areas': analysis_request.preferences.get('focus_areas'),
            'user_preferences': analysis_request.preferences
        }
        
        # Try to get queue manager and Redis, but don't fail if unavailable
        try:
            queue_manager = await get_queue_manager()
            redis_client = await get_redis()
            
            cached_result = await _get_cached_health_result(analysis_request.text, cache_options)
            if cached_result:
                logger.info(
                    "Health analysis served from cache",
                    text_length=len(analysis_request.text),
                    cache_hit=True
                )
                
                # Add request-specific metadata to cached result
                cached_result.update({
                    "block_id": analysis_request.block_id,
                    "document_id": analysis_request.document_id,
                    "analysis_type": analysis_request.analysis_type,
                    "user_id": current_user.user_id,
                    "cached": True
                })
                
                return HealthResponse(
                    success=True,
                    data=cached_result
                )
        except Exception as e:
            logger.warning("Cache check failed, continuing without cache", error=str(e))
            queue_manager = None
            redis_client = None
        
        # Check if we should use real worker or fallback to mock
        use_real_worker = not analysis_request.preferences.get('use_mock', False)
        
        if use_real_worker and queue_manager:
            try:
                # Prepare job data for health worker
                job_data = {
                    'text': analysis_request.text,
                    'block_id': analysis_request.block_id,
                    'document_id': analysis_request.document_id,
                    'options': {
                        'analysis_type': analysis_request.analysis_type,
                        'focus_areas': analysis_request.preferences.get('focus_areas'),
                        'user_preferences': analysis_request.preferences
                    }
                }
                
                # Enqueue health analysis job
                job_id = await queue_manager.enqueue_job(
                    agent_type='health',
                    job_data=job_data,
                    user_tier=current_user.subscription_tier or 'professional',
                    user_id=current_user.user_id
                )
                
                # Wait for job completion (with timeout)
                health_analysis = await _wait_for_health_result(job_id, queue_manager, timeout=10)
            except Exception as e:
                logger.warning("Failed to use real worker, falling back", error=str(e))
                health_analysis = None
            
            if not health_analysis or not health_analysis.get('success'):
                logger.warning(
                    "Health worker failed, falling back to mock data",
                    job_id=job_id,
                    error=health_analysis.get('error') if health_analysis else 'timeout'
                )
                # Fallback to database lookup for cached analysis
                try:
                    db = await get_database()
                    fallback_query = """
                        SELECT * FROM health_metrics 
                        WHERE text_hash = $1 
                        ORDER BY created_at DESC 
                        LIMIT 1
                    """
                    import hashlib
                    text_hash = hashlib.md5(analysis_request.text.encode()).hexdigest()
                    async with db.get_connection() as conn:
                        cached_row = await conn.fetchrow(fallback_query, text_hash)
                    cached_analysis = dict(cached_row) if cached_row else None
                except Exception as e:
                    logger.warning("Database fallback failed", error=str(e))
                    cached_analysis = None
                
                if cached_analysis:
                    health_analysis = dict(cached_analysis)
                    health_analysis['fallback_used'] = True
                    health_analysis['fallback_reason'] = 'worker_failure_cached'
                else:
                    # Create basic analysis if no cache available
                    health_analysis = {
                        "overall_score": 85,
                        "metrics": {"readability": 82, "clarity": 88, "voice": 85, "brand": 87},
                        "issues": [],
                        "suggestions": [],
                        "analysis_timestamp": datetime.utcnow().isoformat() + "Z"
                    }
                    health_analysis['fallback_used'] = True
                    health_analysis['fallback_reason'] = 'worker_failure_default'
            
        else:
            # Use database lookup for development/testing
            try:
                db = await get_database()
                import hashlib
                text_hash = hashlib.md5(analysis_request.text.encode()).hexdigest()
                
                # Try to find existing analysis by text_hash
                cached_query = """
                    SELECT * FROM health_metrics 
                    WHERE text_hash = $1 
                    ORDER BY created_at DESC 
                    LIMIT 1
                """
                async with db.get_connection() as conn:
                    cached_row = await conn.fetchrow(cached_query, text_hash)
                cached_analysis = dict(cached_row) if cached_row else None
            except Exception as e:
                logger.warning("Database lookup failed", error=str(e))
                cached_analysis = None
            
            if cached_analysis:
                health_analysis = dict(cached_analysis)
                health_analysis['from_cache'] = True
            else:
                # Create new basic analysis
                health_analysis = {
                    "overall_score": 85,
                    "metrics": {"readability": 82, "clarity": 88, "voice": 85, "brand": 87},
                    "issues": [],
                    "suggestions": [],
                    "analysis_timestamp": datetime.utcnow().isoformat() + "Z"
                }
                health_analysis['development_mode'] = True
        
        # Add request-specific metadata
        text_words = len(analysis_request.text.split())
        text_chars = len(analysis_request.text)
        
        health_analysis.update({
            "block_id": analysis_request.block_id,
            "document_id": analysis_request.document_id,
            "analysis_type": analysis_request.analysis_type,
            "user_id": current_user.user_id,
            "text_stats": {
                "word_count": text_words,
                "character_count": text_chars,
                "sentence_count": analysis_request.text.count('.') + analysis_request.text.count('!') + analysis_request.text.count('?'),
                "paragraph_count": analysis_request.text.count('\n\n') + 1
            }
        })
        
        # Cache the result if it came from real worker and wasn't fallback
        if use_real_worker and not health_analysis.get('fallback_used', False):
            await _cache_health_result(analysis_request.text, cache_options, health_analysis)
        
        # Publish real-time health update via Redis pub/sub
        if analysis_request.document_id and redis_client:
            try:
                await redis_client.publish_document_update(
                    analysis_request.document_id,
                    {
                        'type': 'health_update',
                        'block_id': analysis_request.block_id,
                        'health_data': {
                            'overall_score': health_analysis.get('overall_score', 0),
                            'metrics': health_analysis.get('metrics', {}),
                            'issue_count': len(health_analysis.get('issues', []))
                        },
                        'timestamp': datetime.utcnow().isoformat() + "Z"
                    }
                )
            except Exception as e:
                logger.warning("Failed to publish health update", error=str(e))
        
        logger.info(
            "Text health analysis completed",
            overall_score=health_analysis.get("overall_score", 0),
            issues_found=len(health_analysis.get("issues", [])),
            real_worker_used=use_real_worker and not health_analysis.get('fallback_used', False)
        )
        
        return HealthResponse(
            success=True,
            data=health_analysis
        )
        
    except Exception as e:
        import traceback
        error_details = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "traceback": traceback.format_exc(),
            "user_id": current_user.user_id,
            "text_length": len(analysis_request.text) if analysis_request else "unknown",
            "analysis_type": analysis_request.analysis_type if analysis_request else "unknown"
        }
        logger.error("Failed to analyze text health - DETAILED ERROR", **error_details)
        print(f"HEALTH ENDPOINT ERROR: {error_details}")  # Also print to console for debugging
        raise HTTPException(
            status_code=500,
            detail=f"Failed to analyze text health: {type(e).__name__}: {str(e)}"
        )


@router.post("/batch-analyze", response_model=HealthResponse)
async def batch_analyze_health(
    batch_request: BatchHealthAnalysisRequest,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Perform batch health analysis on multiple texts.
    
    Args:
        batch_request: Batch analysis request data
        current_user: Current authenticated user
        
    Returns:
        HealthResponse: Batch analysis results
    """
    try:
        logger.info(
            "Performing batch health analysis",
            user_id=current_user.user_id,
            text_count=len(batch_request.texts),
            document_id=batch_request.document_id
        )
        
        # Get queue manager
        queue_manager = await get_queue_manager()
        
        # Process each text (can be parallelized in future)
        results = []
        total_score = 0
        total_issues = 0
        job_ids = []
        
        # Enqueue all jobs first
        for i, text in enumerate(batch_request.texts):
            block_id = batch_request.block_ids[i] if batch_request.block_ids and i < len(batch_request.block_ids) else None
            
            job_data = {
                'text': text,
                'block_id': block_id,
                'document_id': batch_request.document_id,
                'options': {
                    'analysis_type': batch_request.analysis_type,
                    'batch_index': i
                }
            }
            
            # Enqueue health analysis job
            job_id = await queue_manager.enqueue_job(
                agent_type='health',
                job_data=job_data,
                user_tier=current_user.subscription_tier or 'professional',
                user_id=current_user.user_id
            )
            
            job_ids.append((job_id, i, text, block_id))
        
        # Wait for all jobs to complete
        for job_id, text_index, text, block_id in job_ids:
            try:
                # Wait for individual job completion
                health_analysis = await _wait_for_health_result(job_id, queue_manager, timeout=15)
                
                if not health_analysis or not health_analysis.get('success'):
                    # Fallback to database lookup
                    db = await get_database()
                    import hashlib
                    text_hash = hashlib.md5(text.encode()).hexdigest()
                    
                    cached_query = """
                        SELECT * FROM health_metrics 
                        WHERE text_hash = $1 
                        ORDER BY created_at DESC 
                        LIMIT 1
                    """
                    cached_analysis = await db.execute_query(cached_query, [text_hash], fetch="one")
                    
                    if cached_analysis:
                        health_analysis = dict(cached_analysis)
                        health_analysis['fallback_used'] = True
                        health_analysis['fallback_reason'] = 'worker_failure_cached'
                    else:
                        health_analysis = {
                            "overall_score": 85,
                            "metrics": {"readability": 82, "clarity": 88, "voice": 85, "brand": 87},
                            "issues": [],
                            "suggestions": [],
                            "analysis_timestamp": datetime.utcnow().isoformat() + "Z"
                        }
                        health_analysis['fallback_used'] = True
                        health_analysis['fallback_reason'] = 'worker_failure_default'
                
                # Add batch-specific metadata
                text_words = len(text.split())
                health_analysis.update({
                    "block_id": block_id,
                    "document_id": batch_request.document_id,
                    "text_index": text_index,
                    "text_stats": {
                        "word_count": text_words,
                        "character_count": len(text)
                    }
                })
                
                results.append(health_analysis)
                total_score += health_analysis.get("overall_score", 0)
                total_issues += len(health_analysis.get("issues", []))
                
            except Exception as e:
                logger.error(f"Failed to process batch job {job_id}", error=str(e))
                # Add fallback result
                fallback_analysis = {
                    "overall_score": 85,
                    "metrics": {"readability": 82, "clarity": 88, "voice": 85, "brand": 87},
                    "issues": [],
                    "suggestions": [],
                    "analysis_timestamp": datetime.utcnow().isoformat() + "Z",
                    "block_id": block_id,
                    "document_id": batch_request.document_id,
                    "text_index": text_index,
                    "fallback_used": True,
                    "fallback_reason": "processing_error"
                }
                results.append(fallback_analysis)
                total_score += fallback_analysis["overall_score"]
                total_issues += len(fallback_analysis["issues"])
        
        # Calculate batch summary
        texts_analyzed = len(results)
        average_score = round(total_score / max(texts_analyzed, 1), 1)
        
        batch_summary = {
            "batch_id": f"batch_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            "document_id": batch_request.document_id,
            "analysis_timestamp": datetime.utcnow().isoformat() + "Z",
            "texts_analyzed": texts_analyzed,
            "average_score": average_score,
            "total_issues": total_issues,
            "results": results,
            "job_ids": [job_id for job_id, _, _, _ in job_ids],
            "execution_time": len(batch_request.texts) * 0.8  # Estimated time
        }
        
        logger.info(
            "Batch health analysis completed",
            texts_analyzed=texts_analyzed,
            average_score=average_score,
            successful_jobs=len([r for r in results if not r.get('fallback_used', False)])
        )
        
        return HealthResponse(
            success=True,
            data=batch_summary
        )
        
    except Exception as e:
        logger.error("Failed to perform batch health analysis", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to perform batch health analysis"
        )

@router.get("/trends", response_model=HealthResponse)
async def get_health_trends(
    current_user: UserClaims = Depends(get_current_user),
    days: int = Query(default=30, ge=1, le=365),
    document_id: Optional[str] = Query(default=None),
    metric_type: Optional[str] = Query(default=None)
):
    """
    Get health trends and analytics for the user.
    
    Args:
        current_user: Current authenticated user
        days: Number of days to include in trends
        document_id: Filter by specific document
        metric_type: Filter by specific metric type
        
    Returns:
        HealthResponse: Health trends data
    """
    try:
        logger.info(
            "Getting health trends",
            user_id=current_user.user_id,
            days=days,
            document_id=document_id,
            metric_type=metric_type
        )
        
        # Get trend data from database
        db = await get_database()
        
        # Query health metrics for the specified period
        query = """
            SELECT 
                DATE(created_at) as date,
                AVG(overall_score) as overall_score,
                AVG(readability_score) as readability_score,
                AVG(clarity_score) as clarity_score,
                AVG(voice_score) as voice_score,
                AVG(brand_score) as brand_score,
                COUNT(*) as analyses_count
            FROM health_metrics 
            WHERE user_id = $1 
                AND created_at >= CURRENT_DATE - INTERVAL '%s days'
        """ % days
        
        params = [current_user.user_id]
        
        if document_id:
            query += " AND document_id = $2"
            params.append(document_id)
        
        query += " GROUP BY DATE(created_at) ORDER BY date"
        
        trend_data_raw = await db.execute_query(query, params, fetch="all")
        
        # Format trend data
        trend_data = []
        for row in trend_data_raw:
            trend_data.append({
                "date": row['date'].strftime("%Y-%m-%d"),
                "overall_score": round(float(row['overall_score']), 1),
                "readability_score": round(float(row['readability_score']), 1),
                "clarity_score": round(float(row['clarity_score']), 1),
                "voice_score": round(float(row['voice_score']), 1),
                "brand_score": round(float(row['brand_score']), 1),
                "analyses_count": int(row['analyses_count']),
                "issues_resolved": 0  # Would need separate query for resolved issues
            })
        
        # Fill in missing days with no data
        if not trend_data:
            base_date = datetime.utcnow() - timedelta(days=days)
            for i in range(min(days, 7)):  # Only show last 7 days if no data
                date = base_date + timedelta(days=i)
                trend_data.append({
                    "date": date.strftime("%Y-%m-%d"),
                    "overall_score": 0,
                    "readability_score": 0,
                    "clarity_score": 0,
                    "voice_score": 0,
                    "brand_score": 0,
                    "analyses_count": 0,
                    "issues_resolved": 0
                })
        
        # Calculate summary statistics
        recent_scores = [d["overall_score"] for d in trend_data[-7:]]  # Last 7 days
        older_scores = [d["overall_score"] for d in trend_data[-14:-7]]  # Previous 7 days
        
        trends_summary = {
            "user_id": current_user.user_id,
            "time_period": f"{days}_days",
            "document_id": document_id,
            "metric_type": metric_type,
            "generated_at": datetime.utcnow().isoformat() + "Z",
            "summary": {
                "current_average": round(sum(recent_scores) / len(recent_scores), 1),
                "previous_average": round(sum(older_scores) / len(older_scores), 1) if older_scores else 0,
                "improvement": round(sum(recent_scores) / len(recent_scores) - (sum(older_scores) / len(older_scores) if older_scores else 75), 1),
                "total_analyses": sum(d["analyses_count"] for d in trend_data),
                "total_issues_resolved": sum(d["issues_resolved"] for d in trend_data),
                "best_day": max(trend_data, key=lambda x: x["overall_score"])["date"],
                "worst_day": min(trend_data, key=lambda x: x["overall_score"])["date"]
            },
            "daily_data": trend_data,
            "insights": [
                f"Average score over {days} days: {average_score}",
                f"Total analyses performed: {sum(d['analyses_count'] for d in trend_data)}",
                "Data retrieved from health metrics database",
                "Consider regular analysis for consistent improvement"
            ]
        }
        
        logger.info(
            "Health trends retrieved",
            current_average=trends_summary["summary"]["current_average"],
            improvement=trends_summary["summary"]["improvement"]
        )
        
        return HealthResponse(
            success=True,
            data=trends_summary
        )
        
    except Exception as e:
        logger.error("Failed to get health trends", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve health trends"
        )

@router.get("/issues/{block_id}", response_model=HealthListResponse)
async def get_block_health_issues(
    block_id: str,
    current_user: UserClaims = Depends(get_current_user),
    severity: Optional[str] = Query(default=None),
    status: Optional[str] = Query(default=None)
):
    """
    Get health issues for a specific block.
    
    Args:
        block_id: Block ID to get issues for
        current_user: Current authenticated user
        severity: Filter by issue severity
        status: Filter by issue status
        
    Returns:
        HealthListResponse: List of health issues
    """
    try:
        logger.info(
            "Getting block health issues",
            block_id=block_id,
            user_id=current_user.user_id,
            severity=severity,
            status=status
        )
        
        # Get health issues from database
        db = await get_database()
        
        query = """
            SELECT 
                id, block_id, type, severity, status, position, description,
                suggestion, impact, confidence, detected_at, resolved_at, category
            FROM health_issues 
            WHERE block_id = $1
        """
        params = [block_id]
        param_index = 2
        
        # Apply filters
        if severity:
            query += f" AND severity = ${param_index}"
            params.append(severity)
            param_index += 1
            
        if status:
            query += f" AND status = ${param_index}"
            params.append(status)
            param_index += 1
        
        query += " ORDER BY detected_at DESC"
        
        issues_data = await db.execute_query(query, params, fetch="all")
        filtered_issues = [dict(issue) for issue in issues_data]
        
        return HealthListResponse(
            success=True,
            data=filtered_issues,
            meta={
                "total": len(filtered_issues),
                "block_id": block_id,
                "active_issues": len([i for i in filtered_issues if i["status"] == "active"]),
                "resolved_issues": len([i for i in filtered_issues if i["status"] == "resolved"])
            }
        )
        
    except Exception as e:
        logger.error("Failed to get block health issues", error=str(e), block_id=block_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve block health issues"
        )

@router.post("/resolve-issue", response_model=HealthResponse)
async def resolve_health_issue(
    resolution: HealthIssueResolution,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Mark a health issue as resolved.
    
    Args:
        resolution: Issue resolution data
        current_user: Current authenticated user
        
    Returns:
        HealthResponse: Resolution confirmation
    """
    try:
        logger.info(
            "Resolving health issue",
            issue_id=resolution.issue_id,
            user_id=current_user.user_id,
            resolution_type=resolution.resolution_type
        )
        
        # Update issue in database
        db = await get_database()
        now = datetime.utcnow()
        
        update_query = """
            UPDATE health_issues 
            SET status = 'resolved', 
                resolution_type = $1,
                user_feedback = $2,
                resolved_at = $3,
                resolved_by = $4
            WHERE id = $5
            RETURNING *
        """
        
        updated_issue = await db.execute_query(
            update_query,
            [resolution.resolution_type, resolution.user_feedback, now, current_user.user_id, resolution.issue_id],
            fetch="one"
        )
        
        if not updated_issue:
            raise HTTPException(status_code=404, detail="Health issue not found")
        
        resolved_issue = dict(updated_issue)
        
        logger.info("Health issue resolved successfully", issue_id=resolution.issue_id)
        
        return HealthResponse(
            success=True,
            data={
                "message": "Health issue resolved successfully",
                "resolution": resolved_issue
            }
        )
        
    except Exception as e:
        logger.error("Failed to resolve health issue", error=str(e), issue_id=resolution.issue_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to resolve health issue"
        )

@router.get("/preferences", response_model=HealthResponse)
async def get_health_preferences(
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Get user's health analysis preferences.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        HealthResponse: User health preferences
    """
    try:
        logger.info("Getting health preferences", user_id=current_user.user_id)
        
        # Get user preferences from database
        db = await get_database()
        
        query = """
            SELECT 
                focus_areas, scoring_weights, notification_threshold, auto_analysis,
                analysis_frequency, detailed_explanations, show_confidence_scores,
                updated_at
            FROM user_health_preferences 
            WHERE user_id = $1
        """
        
        prefs_data = await db.execute_query(query, [current_user.user_id], fetch="one")
        
        if prefs_data:
            preferences = dict(prefs_data)
            preferences["user_id"] = current_user.user_id
        else:
            # Create default preferences
            preferences = {
                "user_id": current_user.user_id,
                "focus_areas": ["readability", "clarity", "voice_consistency"],
                "scoring_weights": {
                    "readability": 0.25,
                    "clarity": 0.25,
                    "voice_consistency": 0.25,
                    "brand_alignment": 0.25
                },
                "notification_threshold": 75,
                "auto_analysis": True,
                "analysis_frequency": "on_change",
                "detailed_explanations": True,
                "show_confidence_scores": True,
                "updated_at": datetime.utcnow().isoformat() + "Z"
            }
        
        return HealthResponse(
            success=True,
            data=preferences
        )
        
    except Exception as e:
        logger.error("Failed to get health preferences", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve health preferences"
        )

@router.get("/debounced-stats", response_model=HealthResponse)
async def get_debounced_health_stats(
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Get statistics for the debounced health analysis service.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        HealthResponse: Debounced service statistics
    """
    try:
        logger.info("Getting debounced health service stats", user_id=current_user.user_id)
        
        # Get debounced service
        debounced_service = await get_debounced_health_service()
        
        # Get service statistics
        stats = await debounced_service.get_service_stats()
        
        # Add timestamp
        stats['retrieved_at'] = datetime.utcnow().isoformat() + "Z"
        stats['user_id'] = current_user.user_id
        
        return HealthResponse(
            success=True,
            data=stats
        )
        
    except Exception as e:
        logger.error("Failed to get debounced health stats", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve debounced health statistics"
        )

@router.put("/preferences", response_model=HealthResponse)
async def update_health_preferences(
    preferences: HealthPreferencesUpdate,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Update user's health analysis preferences.
    
    Args:
        preferences: Updated preference data
        current_user: Current authenticated user
        
    Returns:
        HealthResponse: Updated preferences
    """
    try:
        logger.info("Updating health preferences", user_id=current_user.user_id)
        
        # Update preferences in database
        db = await get_database()
        now = datetime.utcnow()
        
        # Build dynamic update query
        update_fields = []
        params = []
        param_index = 1
        
        update_data = preferences.dict(exclude_unset=True)
        for field, value in update_data.items():
            if value is not None:
                update_fields.append(f"{field} = ${param_index}")
                params.append(value)
                param_index += 1
        
        # Add updated_at
        update_fields.append(f"updated_at = ${param_index}")
        params.append(now)
        param_index += 1
        
        # Add user_id for WHERE clause
        params.append(current_user.user_id)
        
        # Try to update existing preferences
        if update_fields:
            query = f"""
                UPDATE user_health_preferences 
                SET {', '.join(update_fields)}
                WHERE user_id = ${param_index}
                RETURNING *
            """
            
            result = await db.execute_query(query, params, fetch="one")
            
            if result:
                updated_preferences = dict(result)
                updated_preferences["user_id"] = current_user.user_id
            else:
                # Insert new preferences if none exist
                insert_query = """
                    INSERT INTO user_health_preferences (
                        user_id, focus_areas, scoring_weights, notification_threshold,
                        auto_analysis, updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6)
                    RETURNING *
                """
                
                insert_params = [
                    current_user.user_id,
                    preferences.focus_areas or ["readability", "clarity", "voice_consistency"],
                    preferences.scoring_weights or {
                        "readability": 0.25, "clarity": 0.25,
                        "voice_consistency": 0.25, "brand_alignment": 0.25
                    },
                    preferences.notification_threshold or 75,
                    preferences.auto_analysis if preferences.auto_analysis is not None else True,
                    now
                ]
                
                result = await db.execute_query(insert_query, insert_params, fetch="one")
                updated_preferences = dict(result)
                updated_preferences["user_id"] = current_user.user_id
        else:
            # No fields to update
            updated_preferences = {
                "user_id": current_user.user_id,
                "message": "No fields provided for update"
            }
        
        logger.info("Health preferences updated successfully", user_id=current_user.user_id)
        
        return HealthResponse(
            success=True,
            data={
                "message": "Health preferences updated successfully",
                "preferences": updated_preferences
            }
        )
        
    except Exception as e:
        logger.error("Failed to update health preferences", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to update health preferences"
        )


# Helper Functions

async def _wait_for_health_result(job_id: str, queue_manager, timeout: int = 10) -> Optional[Dict[str, Any]]:
    """
    Wait for health analysis job to complete and return result.
    
    Args:
        job_id: The job ID to wait for
        queue_manager: Queue manager instance
        timeout: Maximum time to wait in seconds
        
    Returns:
        Health analysis result or None if timeout/error
    """
    import asyncio
    
    start_time = datetime.utcnow()
    check_interval = 0.5  # Check every 500ms
    
    while (datetime.utcnow() - start_time).total_seconds() < timeout:
        try:
            # Check job status
            job_status = await queue_manager.get_job_status(job_id)
            
            if job_status:
                status = job_status.get('status')
                
                if status == 'completed':
                    # Job completed successfully
                    job_data = job_status.get('job_data', {})
                    if 'result' in job_data:
                        return job_data['result']
                    else:
                        # Try to get result from job data itself
                        return job_status.get('job_data', {})
                
                elif status == 'failed':
                    # Job failed
                    logger.warning(f"Health job {job_id} failed", error=job_status.get('error'))
                    return {
                        'success': False,
                        'error': job_status.get('error', 'Job failed')
                    }
            
            # Wait before next check
            await asyncio.sleep(check_interval)
            
        except Exception as e:
            logger.error(f"Error checking job status for {job_id}", error=str(e))
            await asyncio.sleep(check_interval)
    
    # Timeout reached
    logger.warning(f"Health job {job_id} timed out after {timeout} seconds")
    return {
        'success': False,
        'error': f'Job timed out after {timeout} seconds'
    }


async def _get_cached_health_result(text: str, options: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Get cached health analysis result if available.
    
    Args:
        text: Text to analyze
        options: Analysis options
        
    Returns:
        Cached result or None
    """
    try:
        import hashlib
        import json
        
        redis_client = await get_redis()
        
        # Create cache key
        text_hash = hashlib.md5(text.encode()).hexdigest()
        options_hash = hashlib.md5(json.dumps(options, sort_keys=True).encode()).hexdigest()
        cache_key = f"health:{text_hash}:{options_hash}"
        
        # Try to get cached result
        cached_data = await redis_client.get(cache_key)
        if cached_data:
            result = json.loads(cached_data)
            result['cached'] = True
            return result
            
    except Exception as e:
        logger.warning("Failed to get cached health result", error=str(e))
    
    return None


async def _cache_health_result(text: str, options: Dict[str, Any], result: Dict[str, Any], ttl: int = 3600):
    """
    Cache health analysis result.
    
    Args:
        text: Text that was analyzed
        options: Analysis options used
        result: Analysis result to cache
        ttl: Time to live in seconds (default 1 hour)
    """
    try:
        import hashlib
        import json
        
        redis_client = await get_redis()
        
        # Create cache key
        text_hash = hashlib.md5(text.encode()).hexdigest()
        options_hash = hashlib.md5(json.dumps(options, sort_keys=True).encode()).hexdigest()
        cache_key = f"health:{text_hash}:{options_hash}"
        
        # Remove non-serializable fields
        cache_data = {k: v for k, v in result.items() 
                     if k not in ['cached', 'job_id', 'user_id']}
        
        # Cache the result
        await redis_client.set(cache_key, json.dumps(cache_data), ttl=ttl)
        
    except Exception as e:
        logger.warning("Failed to cache health result", error=str(e))