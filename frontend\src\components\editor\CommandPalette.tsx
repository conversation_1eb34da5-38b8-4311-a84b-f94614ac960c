import React, { useState, useEffect, useRef } from 'react';
import {
  MagnifyingGlassIcon,
  CommandLineIcon,
  SparklesIcon,
  DocumentTextIcon,
  BookmarkIcon,
  ShareIcon,
  DocumentArrowDownIcon,
  PlusIcon,
  BoltIcon,
  PencilSquareIcon,
  CheckCircleIcon,
  UserGroupIcon,
  ChartBarIcon,
  BeakerIcon,
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

interface Command {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  category: string;
  shortcut?: string;
  action: () => void;
}

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
  onCommand: (commandId: string) => void;
}

const CommandPalette: React.FC<CommandPaletteProps> = ({ isOpen, onClose, onCommand }) => {
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);

  const commands: Command[] = [
    {
      id: 'ai-generate',
      title: 'Generate Content',
      description: 'Use AI to generate new content',
      icon: SparklesIcon,
      category: 'AI',
      shortcut: '⌘⇧A',
      action: () => onCommand('ai-generate'),
    },
    {
      id: 'ai-improve',
      title: 'Improve Writing',
      description: 'Enhance clarity and flow',
      icon: PencilSquareIcon,
      category: 'AI',
      action: () => onCommand('ai-improve'),
    },
    {
      id: 'ai-grammar',
      title: 'Check Grammar',
      description: 'Fix grammar and spelling',
      icon: CheckCircleIcon,
      category: 'AI',
      action: () => onCommand('ai-grammar'),
    },
    {
      id: 'save',
      title: 'Save Document',
      description: 'Save current document',
      icon: DocumentTextIcon,
      category: 'File',
      shortcut: '⌘S',
      action: () => onCommand('save'),
    },
    {
      id: 'share',
      title: 'Share Document',
      description: 'Share with collaborators',
      icon: ShareIcon,
      category: 'File',
      action: () => onCommand('share'),
    },
    {
      id: 'export',
      title: 'Export Document',
      description: 'Export to various formats',
      icon: DocumentArrowDownIcon,
      category: 'File',
      action: () => onCommand('export'),
    },
    {
      id: 'new-document',
      title: 'New Document',
      description: 'Create a new document',
      icon: PlusIcon,
      category: 'File',
      shortcut: '⌘N',
      action: () => onCommand('new-document'),
    },
    {
      id: 'bookmark',
      title: 'Bookmark Document',
      description: 'Add to bookmarks',
      icon: BookmarkIcon,
      category: 'Organization',
      action: () => onCommand('bookmark'),
    },
    {
      id: 'focus-mode',
      title: 'Toggle Focus Mode',
      description: 'Hide distractions',
      icon: BoltIcon,
      category: 'View',
      shortcut: '⌘⇧F',
      action: () => onCommand('focus-mode'),
    },
    {
      id: 'persona-feedback',
      title: 'Reader Persona Feedback',
      description: 'Get feedback from different audience personas',
      icon: UserGroupIcon,
      category: 'Analysis',
      shortcut: '⌘⇧P',
      action: () => onCommand('persona-feedback'),
    },
    {
      id: 'document-analytics',
      title: 'Document Analytics',
      description: 'View writing analytics and metrics',
      icon: ChartBarIcon,
      category: 'Analysis',
      action: () => onCommand('document-analytics'),
    },
    {
      id: 'writing-health',
      title: 'Writing Health Check',
      description: 'Analyze writing quality and readability',
      icon: BeakerIcon,
      category: 'Analysis',
      action: () => onCommand('writing-health'),
    },
  ];

  const filteredCommands = commands.filter(command =>
    command.title.toLowerCase().includes(query.toLowerCase()) ||
    command.description.toLowerCase().includes(query.toLowerCase()) ||
    command.category.toLowerCase().includes(query.toLowerCase())
  );

  const groupedCommands = filteredCommands.reduce((groups, command) => {
    const category = command.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(command);
    return groups;
  }, {} as Record<string, Command[]>);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < filteredCommands.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : filteredCommands.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredCommands[selectedIndex]) {
            filteredCommands[selectedIndex].action();
            onClose();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedIndex, filteredCommands, onClose]);

  useEffect(() => {
    setSelectedIndex(0);
  }, [query]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-start justify-center pt-20 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: -20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: -20 }}
          transition={{ type: 'spring', damping: 25, stiffness: 200 }}
          className="w-full max-w-2xl mx-4 bg-white rounded-2xl shadow-2xl border border-slate-200/50 overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Search Input */}
          <div className="relative p-6 border-b border-slate-200/50">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 text-slate-400">
                <CommandLineIcon className="w-5 h-5" />
                <MagnifyingGlassIcon className="w-5 h-5" />
              </div>
              <input
                ref={inputRef}
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Type a command or search..."
                className="flex-1 text-lg bg-transparent border-0 outline-none placeholder-slate-400 text-slate-900"
              />
              <div className="flex items-center space-x-1">
                <kbd className="px-2 py-1 text-xs font-mono bg-slate-100 text-slate-600 rounded">ESC</kbd>
              </div>
            </div>
          </div>

          {/* Commands List */}
          <div className="max-h-96 overflow-y-auto">
            {Object.keys(groupedCommands).length > 0 ? (
              <div className="p-2">
                {Object.entries(groupedCommands).map(([category, categoryCommands]) => (
                  <div key={category} className="mb-4 last:mb-0">
                    <div className="px-3 py-2">
                      <h3 className="text-xs font-semibold text-slate-500 uppercase tracking-wider">
                        {category}
                      </h3>
                    </div>
                    <div className="space-y-1">
                      {categoryCommands.map((command, index) => {
                        const globalIndex = filteredCommands.indexOf(command);
                        const isSelected = globalIndex === selectedIndex;
                        const IconComponent = command.icon;

                        return (
                          <motion.button
                            key={command.id}
                            onClick={() => {
                              command.action();
                              onClose();
                            }}
                            className={`w-full flex items-center justify-between p-3 rounded-xl transition-all duration-150 ${
                              isSelected
                                ? 'bg-purple-100 text-purple-900 shadow-sm'
                                : 'text-slate-700 hover:bg-slate-50'
                            }`}
                            whileHover={{ scale: 1.01 }}
                            whileTap={{ scale: 0.99 }}
                          >
                            <div className="flex items-center space-x-3">
                              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                                isSelected
                                  ? 'bg-purple-200 text-purple-700'
                                  : 'bg-slate-100 text-slate-600'
                              }`}>
                                <IconComponent className="w-4 h-4" />
                              </div>
                              <div className="text-left">
                                <div className="font-semibold">{command.title}</div>
                                <div className="text-sm opacity-75">{command.description}</div>
                              </div>
                            </div>
                            {command.shortcut && (
                              <div className="flex items-center space-x-1">
                                {command.shortcut.split('').map((char, i) => (
                                  <kbd
                                    key={i}
                                    className={`px-2 py-1 text-xs font-mono rounded ${
                                      isSelected
                                        ? 'bg-purple-200 text-purple-700'
                                        : 'bg-slate-200 text-slate-600'
                                    }`}
                                  >
                                    {char}
                                  </kbd>
                                ))}
                              </div>
                            )}
                          </motion.button>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-8 text-center">
                <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MagnifyingGlassIcon className="w-8 h-8 text-slate-400" />
                </div>
                <h3 className="text-lg font-semibold text-slate-900 mb-2">No commands found</h3>
                <p className="text-sm text-slate-500">
                  Try a different search term or browse all commands.
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="px-6 py-4 bg-slate-50 border-t border-slate-200/50">
            <div className="flex items-center justify-between text-xs text-slate-500">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <kbd className="px-1.5 py-0.5 bg-white border border-slate-200 rounded text-slate-600">↑↓</kbd>
                  <span>Navigate</span>
                </div>
                <div className="flex items-center space-x-1">
                  <kbd className="px-1.5 py-0.5 bg-white border border-slate-200 rounded text-slate-600">↵</kbd>
                  <span>Select</span>
                </div>
                <div className="flex items-center space-x-1">
                  <kbd className="px-1.5 py-0.5 bg-white border border-slate-200 rounded text-slate-600">ESC</kbd>
                  <span>Close</span>
                </div>
              </div>
              <div className="text-purple-600 font-medium">
                {filteredCommands.length} command{filteredCommands.length !== 1 ? 's' : ''}
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default CommandPalette;