import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuthStore } from '@/stores/authStore';

// Import assets
import heroBackground from '@/assets/images/hero-background.png';
import aiAgentsShowcase from '@/assets/images/ai-agents-showcase.png';
import aiNetworkBackground from '@/assets/images/ai-network-background.png';
import { 
  SparklesIcon, 
  RocketLaunchIcon,
  DocumentTextIcon,
  BeakerIcon,
  ChartBarIcon,
  UserGroupIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  PlayIcon,
  StarIcon,
  BoltIcon,
  AcademicCapIcon,
  BriefcaseIcon,
  PencilIcon,
  EyeIcon,
  ClockIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  FireIcon,
  MagnifyingGlassIcon,
  CpuChipIcon,
  LightBulbIcon,
  BookOpenIcon,
  Bars3Icon,
  XMarkIcon,
  UserCircleIcon
} from '@heroicons/react/24/outline';

const Home: React.FC = () => {
  const [mounted, setMounted] = useState(false);
  const [activeFeature, setActiveFeature] = useState(0);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  
  const { isAuthenticated, user } = useAuthStore();

  useEffect(() => {
    setMounted(true);
    
    // Auto-cycle through agent categories and agents
    const interval = setInterval(() => {
      setSelectedCategory(prev => (prev + 1) % agentCategories.length);
      setAgentIndex(prev => (prev + 1) % 4);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  // Infinite AI Agents - Core Categories
  const agentCategories = [
    {
      category: "Writing Quality",
      icon: CpuChipIcon,
      gradient: "from-blue-600 to-cyan-600",
      agents: [
        { name: "Grammar Agent", description: "Perfect grammar and syntax", specialty: "Linguistic accuracy" },
        { name: "Style Agent", description: "Voice and tone optimization", specialty: "Writing elegance" },
        { name: "Clarity Agent", description: "Clear communication", specialty: "Reader comprehension" },
        { name: "Flow Agent", description: "Sentence rhythm", specialty: "Reading fluency" }
      ]
    },
    {
      category: "Content Intelligence", 
      icon: LightBulbIcon,
      gradient: "from-purple-600 to-pink-600",
      agents: [
        { name: "Structure Agent", description: "Perfect organization", specialty: "Logical flow" },
        { name: "Argument Agent", description: "Compelling logic", specialty: "Persuasive reasoning" },
        { name: "Evidence Agent", description: "Supporting data", specialty: "Fact verification" },
        { name: "Coherence Agent", description: "Unified narrative", specialty: "Consistency checking" }
      ]
    },
    {
      category: "Domain Expertise",
      icon: AcademicCapIcon, 
      gradient: "from-green-600 to-emerald-600",
      agents: [
        { name: "Academic Agent", description: "Scholarly writing", specialty: "Research standards" },
        { name: "Business Agent", description: "Professional communication", specialty: "Corporate tone" },
        { name: "Creative Agent", description: "Artistic expression", specialty: "Storytelling craft" },
        { name: "Technical Agent", description: "Precise documentation", specialty: "Specification clarity" }
      ]
    },
    {
      category: "Enhancement",
      icon: SparklesIcon,
      gradient: "from-orange-600 to-red-600", 
      agents: [
        { name: "Engagement Agent", description: "Reader captivation", specialty: "Attention retention" },
        { name: "Brevity Agent", description: "Concise expression", specialty: "Efficient communication" },
        { name: "Elegance Agent", description: "Sophisticated prose", specialty: "Literary refinement" },
        { name: "Impact Agent", description: "Memorable messaging", specialty: "Persuasive power" }
      ]
    }
  ];

  const [selectedCategory, setSelectedCategory] = useState(0);
  const [agentIndex, setAgentIndex] = useState(0);

  const writingTypes = [
    { name: "Academic Papers", icon: AcademicCapIcon, color: "bg-blue-500" },
    { name: "Creative Writing", icon: PencilIcon, color: "bg-purple-500" },
    { name: "Business Documents", icon: BriefcaseIcon, color: "bg-green-500" },
    { name: "Technical Documentation", icon: DocumentTextIcon, color: "bg-orange-500" }
  ];

  const stats = [
    { value: "4", label: "AI Agents", suffix: "" },
    { value: "225K", label: "Monthly Credits", suffix: "" },
    { value: "30+", label: "Languages", suffix: "" },
    { value: "99.9%", label: "Uptime SLA", suffix: "" }
  ];

  const testimonials = [
    {
      quote: "Revisionary transformed my academic writing. The AI agents catch issues I never noticed and the quality scoring helped me improve dramatically.",
      author: "Dr. Sarah Chen",
      role: "Research Scientist",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b5b5?w=64&h=64&fit=crop&crop=face"
    },
    {
      quote: "As a novelist, the persona agents feature is game-changing. Getting feedback from different reader perspectives before publishing is invaluable.",
      author: "Marcus Rodriguez",
      role: "Best-selling Author",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=64&h=64&fit=crop&crop=face"
    },
    {
      quote: "Our marketing team's writing quality improved 40% after adopting Revisionary. The collaboration features are enterprise-grade.",
      author: "Lisa Park",
      role: "Marketing Director",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=64&h=64&fit=crop&crop=face"
    }
  ];

  const pricing = [
    {
      name: "Free",
      price: "$0",
      period: "/month",
      description: "Perfect for getting started",
      features: [
        "10,000 words/month",
        "Basic grammar checking",
        "Limited AI generations",
        "Standard support"
      ],
      cta: "Start Free",
      gradient: "from-slate-600 to-slate-700",
      popular: false
    },
    {
      name: "Professional", 
      price: "$19",
      period: "/month",
      description: "For serious writers",
      features: [
        "225,000 AI credits",
        "All agents enabled",
        "Version history",
        "Collaboration features",
        "Priority support"
      ],
      cta: "Start Professional",
      gradient: "from-purple-600 to-pink-600",
      popular: true
    },
    {
      name: "Studio",
      price: "$29", 
      period: "/month",
      description: "For power users",
      features: [
        "1,000,000 AI credits",
        "Priority processing",
        "Advanced analytics",
        "API access",
        "Premium support"
      ],
      cta: "Start Studio",
      gradient: "from-blue-600 to-cyan-600",
      popular: false
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.05),transparent_50%)]"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(236,72,153,0.05),transparent_50%)]"></div>
      
      {/* Navigation Bar */}
      <nav className="relative z-50 bg-white/80 backdrop-blur-md border-b border-slate-200/50">
        <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-10">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <Link to="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center">
                  <SparklesIcon className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold text-slate-900">Revisionary</span>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-slate-600 hover:text-slate-900 font-medium transition-colors">
                Features
              </a>
              <a href="#pricing" className="text-slate-600 hover:text-slate-900 font-medium transition-colors">
                Pricing
              </a>
              <a href="#testimonials" className="text-slate-600 hover:text-slate-900 font-medium transition-colors">
                Reviews
              </a>
              
              {/* Authentication Links */}
              {isAuthenticated ? (
                <div className="flex items-center space-x-4">
                  <Link
                    to="/app/dashboard"
                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-200"
                  >
                    <ChartBarIcon className="w-4 h-4 mr-2" />
                    Dashboard
                  </Link>
                  <div className="flex items-center space-x-2 text-slate-600">
                    <UserCircleIcon className="w-6 h-6" />
                    <span className="text-sm">{user?.displayName || user?.email}</span>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-4">
                  <Link
                    to="/login"
                    className="text-slate-600 hover:text-slate-900 font-medium transition-colors"
                  >
                    Sign In
                  </Link>
                  <Link
                    to="/signup"
                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-200"
                  >
                    Start Free
                  </Link>
                </div>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="p-2 rounded-lg text-slate-600 hover:text-slate-900 hover:bg-slate-100 transition-colors"
              >
                {mobileMenuOpen ? (
                  <XMarkIcon className="w-6 h-6" />
                ) : (
                  <Bars3Icon className="w-6 h-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden bg-white border-t border-slate-200"
            >
              <div className="px-6 py-4 space-y-4">
                <a
                  href="#features"
                  className="block text-slate-600 hover:text-slate-900 font-medium transition-colors"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Features
                </a>
                <a
                  href="#pricing"
                  className="block text-slate-600 hover:text-slate-900 font-medium transition-colors"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Pricing
                </a>
                <a
                  href="#testimonials"
                  className="block text-slate-600 hover:text-slate-900 font-medium transition-colors"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Reviews
                </a>
                
                {isAuthenticated ? (
                  <div className="pt-4 border-t border-slate-200">
                    <div className="flex items-center space-x-2 mb-4 text-slate-600">
                      <UserCircleIcon className="w-6 h-6" />
                      <span className="text-sm">{user?.displayName || user?.email}</span>
                    </div>
                    <Link
                      to="/app/dashboard"
                      className="block w-full text-center px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-lg"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Go to Dashboard
                    </Link>
                  </div>
                ) : (
                  <div className="pt-4 border-t border-slate-200 space-y-3">
                    <Link
                      to="/login"
                      className="block text-center px-4 py-2 text-slate-600 border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Sign In
                    </Link>
                    <Link
                      to="/signup"
                      className="block text-center px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-lg"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Start Free
                    </Link>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </nav>
      
      {/* Hero Section */}
      <section className="relative z-10 pt-20 pb-16 lg:pt-32 lg:pb-24 overflow-hidden">
        {/* Hero Background Image */}
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30"
          style={{ backgroundImage: `url(${heroBackground})` }}
        />
        <div className="absolute inset-0 bg-gradient-to-br from-white/60 via-white/40 to-white/60" />
        
        <div className="relative z-10 max-w-7xl mx-auto px-6 sm:px-8 lg:px-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-purple-100 to-pink-100 border border-purple-200 mb-8"
            >
              <SparklesIcon className="w-5 h-5 text-purple-600 mr-2" />
              <span className="text-sm font-semibold text-purple-700">
                AI-Powered Writing Revolution
              </span>
            </motion.div>

            {/* Main Headline */}
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.8 }}
              className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight"
            >
              <span className="bg-gradient-to-r from-slate-900 via-purple-900 to-slate-900 bg-clip-text text-transparent">
                Write
              </span>{" "}
              <span className="bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 bg-clip-text text-transparent">
                Brilliantly
              </span>
              <br />
              <span className="bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent">
                with AI
              </span>
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
              className="text-xl md:text-2xl text-slate-600 max-w-4xl mx-auto mb-12 leading-relaxed"
            >
              The world's most advanced AI writing assistant. Four specialized agents work together to help you create 
              <span className="font-semibold text-purple-700"> exceptional content</span> across any domain—from academic papers to creative fiction.
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.8 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
            >
              <Link
                to="/signup"
                className="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              >
                <RocketLaunchIcon className="w-6 h-6 mr-3" />
                Start Writing for Free
                <ArrowRightIcon className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>
              
              <button className="group inline-flex items-center px-8 py-4 bg-white border-2 border-slate-200 text-slate-700 font-semibold rounded-2xl shadow-md hover:shadow-lg hover:border-purple-300 transform hover:scale-105 transition-all duration-200">
                <PlayIcon className="w-5 h-5 mr-3" />
                Watch Demo
              </button>
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-3xl mx-auto"
            >
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-slate-900 mb-2">
                    {stat.value}{stat.suffix}
                  </div>
                  <div className="text-sm text-slate-600 font-medium">
                    {stat.label}
                  </div>
                </div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Infinite AI Agents Section */}
      <section id="features" className="py-32 relative overflow-hidden">
        {/* AI Network Background */}
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${aiNetworkBackground})` }}
        />
        <div className="absolute inset-0 bg-black/50" />
        
        {/* Floating particles */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-purple-400 rounded-full animate-ping"></div>
          <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-pink-400 rounded-full animate-pulse"></div>
          <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce"></div>
          <div className="absolute bottom-1/3 right-1/4 w-1 h-1 bg-cyan-400 rounded-full animate-ping"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-6 sm:px-8 lg:px-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-5xl md:text-7xl font-bold text-white mb-8">
              Infinite{" "}
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">
                AI Agents
              </span>
              <br />
              <span className="text-4xl md:text-5xl text-white/90">
                Specialized for Every Task
              </span>
            </h2>
            <p className="text-xl text-white/80 max-w-4xl mx-auto leading-relaxed">
              From grammar perfection to creative enhancement, our AI agents adapt and specialize 
              to meet your exact writing needs. Each agent is an expert in its domain.
            </p>
          </motion.div>

          {/* Agent Categories Grid */}
          <div className="grid lg:grid-cols-4 gap-8 mb-16">
            {agentCategories.map((category, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className={`relative group cursor-pointer transition-all duration-500 ${
                  selectedCategory === index 
                    ? 'scale-105' 
                    : 'hover:scale-102'
                }`}
                onClick={() => setSelectedCategory(index)}
              >
                <div className={`p-8 rounded-3xl backdrop-blur-sm border transition-all duration-300 ${
                  selectedCategory === index
                    ? 'bg-white/20 border-white/40 shadow-2xl'
                    : 'bg-white/10 border-white/20 hover:bg-white/15'
                }`}>
                  <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${category.gradient} flex items-center justify-center mb-6 shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                    {React.createElement(category.icon, { className: "w-8 h-8 text-white" })}
                  </div>
                  
                  <h3 className="text-2xl font-bold text-white mb-4">
                    {category.category}
                  </h3>
                  
                  <div className="space-y-3">
                    {category.agents.slice(0, 2).map((agent, agentIdx) => (
                      <div key={agentIdx} className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-white/60 rounded-full"></div>
                        <span className="text-white/80 text-sm font-medium">
                          {agent.name}
                        </span>
                      </div>
                    ))}
                    <div className="flex items-center space-x-3 text-white/60">
                      <div className="w-2 h-2 bg-white/40 rounded-full"></div>
                      <span className="text-sm">+{category.agents.length - 2} more</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Active Agent Showcase */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-white/10 backdrop-blur-md rounded-3xl p-10 border border-white/20 shadow-2xl"
          >
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* Agent Details */}
              <div>
                <AnimatePresence mode="wait">
                  <motion.div
                    key={`${selectedCategory}-${agentIndex}`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="flex items-center space-x-4 mb-8">
                      <div className={`w-20 h-20 rounded-3xl bg-gradient-to-r ${agentCategories[selectedCategory].gradient} flex items-center justify-center shadow-xl`}>
                        {React.createElement(agentCategories[selectedCategory].icon, { className: "w-10 h-10 text-white" })}
                      </div>
                      <div>
                        <h4 className="text-3xl font-bold text-white mb-2">
                          {agentCategories[selectedCategory].agents[agentIndex]?.name}
                        </h4>
                        <p className="text-white/70 text-lg">
                          {agentCategories[selectedCategory].category}
                        </p>
                      </div>
                    </div>

                    <p className="text-xl text-white/90 mb-6 leading-relaxed">
                      {agentCategories[selectedCategory].agents[agentIndex]?.description}
                    </p>

                    <div className="bg-white/10 rounded-2xl p-6 mb-6">
                      <h5 className="text-lg font-semibold text-white mb-3">Specialty:</h5>
                      <p className="text-white/80">
                        {agentCategories[selectedCategory].agents[agentIndex]?.specialty}
                      </p>
                    </div>

                    <div className="flex flex-wrap gap-3">
                      {agentCategories[selectedCategory].agents.map((agent, idx) => (
                        <button
                          key={idx}
                          onClick={() => setAgentIndex(idx)}
                          className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                            agentIndex === idx
                              ? 'bg-white/20 text-white border border-white/30'
                              : 'bg-white/10 text-white/70 hover:bg-white/15 hover:text-white'
                          }`}
                        >
                          {agent.name}
                        </button>
                      ))}
                    </div>
                  </motion.div>
                </AnimatePresence>
              </div>

              {/* Visual Showcase */}
              <div className="relative">
                {/* AI Agents Showcase Image */}
                <div className="mb-8 flex justify-center">
                  <img 
                    src={aiAgentsShowcase} 
                    alt="AI Agents: Grammar, Style, Structure, and Content"
                    className="max-w-full h-auto rounded-2xl shadow-2xl"
                  />
                </div>
                
                {/* Terminal Output */}
                <div className="bg-slate-900/70 rounded-2xl p-6 border border-white/10">
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    <span className="text-white/60 text-sm ml-4">Revisionary AI Engine</span>
                  </div>
                  <div className="font-mono text-sm">
                    <div className="text-green-400 mb-2">
                      $ revisionary deploy --agents=infinite --task=enhance
                    </div>
                    <div className="text-white/80 space-y-1">
                      <div>✓ {agentCategories[selectedCategory].agents[agentIndex]?.name}: Activated</div>
                      <div>✓ Analyzing content structure...</div>
                      <div>✓ Applying {agentCategories[selectedCategory].agents[agentIndex]?.specialty.toLowerCase()}</div>
                      <div className="text-cyan-400">→ Quality improved by 34%</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Writing Types Section */}
      <section className="py-24">
        <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-6xl font-bold text-slate-900 mb-6">
              Perfect for{" "}
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Every Writer
              </span>
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              From academic papers to creative fiction, our AI adapts to your writing domain and style
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {writingTypes.map((type, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="group p-8 bg-white rounded-3xl shadow-lg hover:shadow-xl border border-slate-100 transform hover:scale-105 transition-all duration-300 cursor-pointer"
              >
                <div className={`w-16 h-16 ${type.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  {React.createElement(type.icon, { className: "w-8 h-8 text-white" })}
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-4">
                  {type.name}
                </h3>
                <p className="text-slate-600 leading-relaxed">
                  Specialized assistance tailored to your domain's unique requirements and conventions
                </p>
                <div className="mt-6 flex items-center text-purple-600 font-medium">
                  Learn more
                  <ArrowRightIcon className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Quality Scoring Showcase */}
      <section className="py-24 bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-10">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
                Real-time{" "}
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Quality Scoring
                </span>
              </h2>
              <p className="text-xl text-slate-600 mb-8 leading-relaxed">
                Get instant feedback on your writing quality across four dimensions. 
                Track your improvement over time and achieve your writing goals faster.
              </p>
              
              <div className="space-y-6">
                {[
                  { name: "Grammar & Mechanics", score: 95, color: "bg-green-500" },
                  { name: "Style & Clarity", score: 88, color: "bg-blue-500" },
                  { name: "Structure & Organization", score: 92, color: "bg-purple-500" },
                  { name: "Content Quality", score: 87, color: "bg-pink-500" }
                ].map((metric, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.6 }}
                    viewport={{ once: true }}
                    className="flex items-center space-x-4"
                  >
                    <div className="flex-1">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-semibold text-slate-700">
                          {metric.name}
                        </span>
                        <span className="text-sm font-bold text-slate-900">
                          {metric.score}%
                        </span>
                      </div>
                      <div className="w-full bg-slate-200 rounded-full h-3 overflow-hidden">
                        <motion.div
                          initial={{ width: 0 }}
                          whileInView={{ width: `${metric.score}%` }}
                          transition={{ delay: index * 0.1 + 0.3, duration: 1 }}
                          viewport={{ once: true }}
                          className={`h-full ${metric.color} rounded-full`}
                        />
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              {/* Placeholder for quality scoring visualization */}
              <div className="bg-white rounded-3xl shadow-2xl p-8 border border-slate-200">
                <div className="text-center mb-8">
                  <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full mb-4">
                    <span className="text-3xl font-bold text-white">91</span>
                  </div>
                  <h3 className="text-2xl font-bold text-slate-900 mb-2">Overall Quality Score</h3>
                  <p className="text-slate-600">Excellent writing quality</p>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-green-50 rounded-xl border border-green-200">
                    <div className="flex items-center space-x-3">
                      <CheckCircleIcon className="w-6 h-6 text-green-600" />
                      <span className="font-medium text-green-800">Strong arguments</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-blue-50 rounded-xl border border-blue-200">
                    <div className="flex items-center space-x-3">
                      <LightBulbIcon className="w-6 h-6 text-blue-600" />
                      <span className="font-medium text-blue-800">Clear structure</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-purple-50 rounded-xl border border-purple-200">
                    <div className="flex items-center space-x-3">
                      <SparklesIcon className="w-6 h-6 text-purple-600" />
                      <span className="font-medium text-purple-800">Engaging style</span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="py-24">
        <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-6xl font-bold text-slate-900 mb-6">
              Trusted by{" "}
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Writers Worldwide
              </span>
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Join thousands of writers who have transformed their craft with Revisionary
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="bg-white rounded-3xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-shadow duration-300"
              >
                <div className="flex items-center space-x-1 mb-6">
                  {[...Array(5)].map((_, i) => (
                    <StarIcon key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <blockquote className="text-slate-700 mb-6 leading-relaxed">
                  "{testimonial.quote}"
                </blockquote>
                <div className="flex items-center space-x-4">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.author}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div>
                    <div className="font-semibold text-slate-900">
                      {testimonial.author}
                    </div>
                    <div className="text-sm text-slate-500">
                      {testimonial.role}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-24 bg-gradient-to-br from-slate-50 to-white">
        <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-6xl font-bold text-slate-900 mb-6">
              Choose Your{" "}
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Writing Journey
              </span>
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Start free and scale as you grow. All plans include our core AI agents.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {pricing.map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className={`relative bg-white rounded-3xl p-8 shadow-lg border ${
                  plan.popular 
                    ? 'border-purple-200 ring-4 ring-purple-100 scale-105' 
                    : 'border-slate-200'
                } hover:shadow-xl transition-all duration-300`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-slate-900 mb-2">
                    {plan.name}
                  </h3>
                  <p className="text-slate-600 mb-6">
                    {plan.description}
                  </p>
                  <div className="flex items-baseline justify-center">
                    <span className="text-5xl font-bold text-slate-900">
                      {plan.price}
                    </span>
                    <span className="text-xl text-slate-500 ml-1">
                      {plan.period}
                    </span>
                  </div>
                </div>

                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center space-x-3">
                      <CheckCircleIcon className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-slate-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Link
                  to="/signup"
                  className={`w-full inline-flex items-center justify-center px-6 py-3 rounded-2xl font-semibold transition-all duration-200 ${
                    plan.popular
                      ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:shadow-lg transform hover:scale-105'
                      : 'bg-slate-100 text-slate-700 hover:bg-slate-200'
                  }`}
                >
                  {plan.cta}
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-r from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20"></div>
        </div>
        
        <div className="relative z-10 max-w-4xl mx-auto px-6 sm:px-8 lg:px-10 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Ready to{" "}
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Transform
              </span>{" "}
              Your Writing?
            </h2>
            <p className="text-xl text-white/80 mb-12 max-w-2xl mx-auto leading-relaxed">
              Join thousands of writers who have already discovered the power of AI-assisted writing. 
              Start your free trial today—no credit card required.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                to="/signup"
                className="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              >
                <SparklesIcon className="w-6 h-6 mr-3" />
                Start Free Trial
                <ArrowRightIcon className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>
              
              <Link
                to="/login"
                className="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm border border-white/20 text-white font-semibold rounded-2xl hover:bg-white/20 transition-all duration-200"
              >
                Sign In
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;