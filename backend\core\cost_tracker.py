"""
Cost Tracker for Revisionary

Purpose: Centralized cost tracking and budget management
- Real-time token usage and cost monitoring
- Budget enforcement with automatic controls
- Cost alerts and notifications
- Emergency cost-saving mode activation

Features:
- Per-model cost calculation
- Monthly/daily/hourly budget tracking
- Automatic model downgrade on budget limits
- Cost analytics and reporting
"""

import os
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import structlog

from core.redis_service import get_redis
from core.settings import settings

logger = structlog.get_logger(__name__)


class CostTracker:
    """Centralized cost tracking and budget management."""
    
    def __init__(self):
        """Initialize cost tracker with model pricing."""
        self.model_costs = {
            # OpenAI GPT-4.1 models (per 1K tokens)
            'gpt-4.1-nano-2025-04-14': {
                'input': 0.0001,    # $0.10 per 1M tokens
                'output': 0.0004,   # $0.40 per 1M tokens
                'cached_input': 0.000025  # 75% discount
            },
            'gpt-4.1-mini-2025-04-14': {
                'input': 0.0004,    # $0.40 per 1M tokens
                'output': 0.0016,   # $1.60 per 1M tokens
                'cached_input': 0.0001  # 75% discount
            },
            'gpt-4.1-2025-04-14': {
                'input': 0.002,     # $2.00 per 1M tokens
                'output': 0.008,    # $8.00 per 1M tokens
                'cached_input': 0.0005  # 75% discount
            },
            # Google Gemini models (per 1K tokens)
            'gemini-2.5-flash-lite-preview': {
                'input': 0.0001,    # $0.10 per 1M tokens
                'output': 0.0004,   # $0.40 per 1M tokens
                'cached_input': 0.000025  # Estimated discount
            },
            'gemini-2.0-flash': {
                'input': 0.0001,    # $0.10 per 1M tokens
                'output': 0.0004,   # $0.40 per 1M tokens
                'cached_input': 0.000025  # Estimated discount
            }
        }
        
        # Budget settings
        self.monthly_budget = float(os.getenv('MAX_TOKEN_COST_MONTH', '50'))
        self.alert_threshold_5min = float(os.getenv('ALERT_THRESHOLD_5MIN', '1'))
        self.emergency_mode_threshold = self.monthly_budget * 0.95  # 95%
        
        # Redis client
        self.redis = None
        
        logger.info("Cost Tracker initialized", 
                   monthly_budget=self.monthly_budget,
                   models_configured=len(self.model_costs))
    
    async def initialize(self):
        """Initialize async resources."""
        self.redis = await get_redis()
        logger.info("Cost Tracker async resources initialized")
    
    async def track_usage(
        self, 
        model: str, 
        usage: Dict[str, int], 
        agent_type: str,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Track token usage and calculate costs.
        
        Args:
            model: Model name used
            usage: Token usage dict with input_tokens, output_tokens, cached_tokens
            agent_type: Type of agent (grammar, style, structure, content)
            user_id: Optional user ID for per-user tracking
            
        Returns:
            Dict with cost information and alerts
        """
        if not self.redis:
            await self.initialize()
        
        # Get model costs
        model_costs = self.model_costs.get(model)
        if not model_costs:
            logger.warning(f"Unknown model for cost calculation: {model}")
            model_costs = {'input': 0.001, 'output': 0.001, 'cached_input': 0.0001}
        
        # Calculate costs
        input_tokens = usage.get('input_tokens', 0)
        output_tokens = usage.get('output_tokens', 0)
        cached_tokens = usage.get('cached_tokens', 0)
        
        # Calculate billable input tokens (apply caching discount)
        billable_input = input_tokens - cached_tokens
        cached_input_cost = cached_tokens * model_costs['cached_input'] / 1000
        
        # Calculate total cost
        input_cost = billable_input * model_costs['input'] / 1000
        output_cost = output_tokens * model_costs['output'] / 1000
        total_cost = input_cost + output_cost + cached_input_cost
        
        # Create cost record
        cost_record = {
            'timestamp': datetime.utcnow().isoformat(),
            'model': model,
            'agent_type': agent_type,
            'user_id': user_id,
            'usage': usage,
            'costs': {
                'input_cost': input_cost,
                'output_cost': output_cost,
                'cached_input_cost': cached_input_cost,
                'total_cost': total_cost
            },
            'savings': {
                'cached_tokens': cached_tokens,
                'saved_cost': cached_tokens * (model_costs['input'] - model_costs['cached_input']) / 1000
            }
        }
        
        # Update cost counters
        await self._update_cost_counters(total_cost, agent_type, user_id)
        
        # Check thresholds and alerts
        alerts = await self._check_cost_thresholds()
        cost_record['alerts'] = alerts
        
        # Log the usage
        logger.info(
            "Token usage tracked",
            model=model,
            agent_type=agent_type,
            user_id=user_id,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            cached_tokens=cached_tokens,
            total_cost=f"${total_cost:.4f}",
            saved_cost=f"${cost_record['savings']['saved_cost']:.4f}"
        )
        
        return cost_record
    
    async def _update_cost_counters(
        self, 
        cost: float, 
        agent_type: str, 
        user_id: Optional[str]
    ):
        """Update various cost tracking counters in Redis."""
        now = datetime.utcnow()
        
        # Define counter keys
        monthly_key = f"cost:monthly:{now.strftime('%Y%m')}"
        daily_key = f"cost:daily:{now.strftime('%Y%m%d')}"
        hourly_key = f"cost:hourly:{now.strftime('%Y%m%d%H')}"
        window_5min_key = f"cost:5min:{now.strftime('%Y%m%d%H%M')}"
        agent_monthly_key = f"cost:agent:{agent_type}:{now.strftime('%Y%m')}"
        
        # User-specific tracking if provided
        user_keys = []
        if user_id:
            user_monthly_key = f"cost:user:{user_id}:{now.strftime('%Y%m')}"
            user_daily_key = f"cost:user:{user_id}:{now.strftime('%Y%m%d')}"
            user_keys = [user_monthly_key, user_daily_key]
        
        # Update all counters atomically
        pipeline = self.redis.redis_client.pipeline()
        
        # Global counters
        pipeline.incrbyfloat(monthly_key, cost)
        pipeline.incrbyfloat(daily_key, cost)
        pipeline.incrbyfloat(hourly_key, cost)
        pipeline.incrbyfloat(window_5min_key, cost)
        pipeline.incrbyfloat(agent_monthly_key, cost)
        
        # User-specific counters
        for key in user_keys:
            pipeline.incrbyfloat(key, cost)
        
        # Set expiration times
        pipeline.expire(window_5min_key, 300)      # 5 minutes
        pipeline.expire(hourly_key, 86400)         # 24 hours
        pipeline.expire(daily_key, 2592000)        # 30 days
        pipeline.expire(monthly_key, 7776000)      # 90 days
        pipeline.expire(agent_monthly_key, 7776000) # 90 days
        
        for key in user_keys:
            pipeline.expire(key, 2592000)  # 30 days
        
        await pipeline.execute()
    
    async def _check_cost_thresholds(self) -> List[Dict[str, Any]]:
        """Check cost thresholds and return any alerts."""
        alerts = []
        now = datetime.utcnow()
        
        # Check 5-minute window
        window_key = f"cost:5min:{now.strftime('%Y%m%d%H%M')}"
        window_cost = await self.redis.get(window_key)
        
        if window_cost and float(window_cost) > self.alert_threshold_5min:
            alerts.append({
                'type': 'high_spend_5min',
                'severity': 'critical',
                'amount': float(window_cost),
                'threshold': self.alert_threshold_5min,
                'message': f"High spending: ${float(window_cost):.2f} in 5 minutes"
            })
        
        # Check monthly budget
        monthly_key = f"cost:monthly:{now.strftime('%Y%m')}"
        monthly_cost = await self.redis.get(monthly_key)
        
        if monthly_cost:
            monthly_amount = float(monthly_cost)
            
            # 80% warning
            if monthly_amount > self.monthly_budget * 0.8:
                alerts.append({
                    'type': 'budget_warning',
                    'severity': 'warning',
                    'amount': monthly_amount,
                    'budget': self.monthly_budget,
                    'percentage': (monthly_amount / self.monthly_budget) * 100,
                    'message': f"Monthly budget {(monthly_amount / self.monthly_budget) * 100:.1f}% consumed"
                })
            
            # 95% critical - trigger emergency mode
            if monthly_amount > self.emergency_mode_threshold:
                await self._activate_emergency_mode()
                alerts.append({
                    'type': 'emergency_mode',
                    'severity': 'critical',
                    'amount': monthly_amount,
                    'budget': self.monthly_budget,
                    'message': "Emergency cost-saving mode activated"
                })
        
        return alerts
    
    async def _activate_emergency_mode(self):
        """Activate emergency cost-saving mode."""
        logger.critical("Activating emergency cost-saving mode")
        
        # Set emergency mode flag
        await self.redis.set("emergency:mode", "active", ttl=86400)  # 24 hours
        
        # Reduce worker limits
        await self.redis.set("emergency:max_workers_grammar", "1", ttl=86400)
        await self.redis.set("emergency:max_workers_style", "1", ttl=86400)
        await self.redis.set("emergency:max_workers_structure", "0", ttl=86400)  # Disable
        await self.redis.set("emergency:max_workers_content", "0", ttl=86400)    # Disable
        
        # Increase cache TTL
        await self.redis.set("emergency:cache_ttl", "14400", ttl=86400)  # 4 hours
        
        # Force model downgrades
        await self.redis.set("emergency:force_downgrade", "true", ttl=86400)
        
        # TODO: Send emergency alert to admin
    
    async def get_cost_summary(self, period: str = "month") -> Dict[str, Any]:
        """
        Get cost summary for specified period.
        
        Args:
            period: "month", "day", or "hour"
            
        Returns:
            Dict with cost breakdown and statistics
        """
        if not self.redis:
            await self.initialize()
        
        now = datetime.utcnow()
        
        if period == "month":
            key = f"cost:monthly:{now.strftime('%Y%m')}"
            agent_keys = [f"cost:agent:{agent}:{now.strftime('%Y%m')}" 
                         for agent in ['grammar', 'style', 'structure', 'content']]
        elif period == "day":
            key = f"cost:daily:{now.strftime('%Y%m%d')}"
            agent_keys = [f"cost:agent:{agent}:{now.strftime('%Y%m%d')}" 
                         for agent in ['grammar', 'style', 'structure', 'content']]
        elif period == "hour":
            key = f"cost:hourly:{now.strftime('%Y%m%d%H')}"
            agent_keys = []  # No hourly agent breakdown
        else:
            raise ValueError(f"Invalid period: {period}")
        
        # Get total cost
        total_cost = await self.redis.get(key)
        total_cost = float(total_cost) if total_cost else 0.0
        
        # Get agent breakdown
        agent_costs = {}
        for i, agent in enumerate(['grammar', 'style', 'structure', 'content']):
            if i < len(agent_keys):
                cost = await self.redis.get(agent_keys[i])
                agent_costs[agent] = float(cost) if cost else 0.0
            else:
                agent_costs[agent] = 0.0
        
        # Calculate statistics
        remaining_budget = max(0, self.monthly_budget - total_cost)
        budget_percentage = (total_cost / self.monthly_budget) * 100 if period == "month" else None
        
        summary = {
            'period': period,
            'total_cost': total_cost,
            'agent_breakdown': agent_costs,
            'budget': {
                'monthly_limit': self.monthly_budget,
                'remaining': remaining_budget,
                'percentage_used': budget_percentage
            },
            'top_agent': max(agent_costs.items(), key=lambda x: x[1])[0] if agent_costs else None,
            'timestamp': now.isoformat()
        }
        
        return summary
    
    async def get_user_cost_summary(self, user_id: str, period: str = "month") -> Dict[str, Any]:
        """Get cost summary for a specific user."""
        if not self.redis:
            await self.initialize()
        
        now = datetime.utcnow()
        
        if period == "month":
            key = f"cost:user:{user_id}:{now.strftime('%Y%m')}"
        elif period == "day":
            key = f"cost:user:{user_id}:{now.strftime('%Y%m%d')}"
        else:
            raise ValueError(f"Invalid period: {period}")
        
        user_cost = await self.redis.get(key)
        user_cost = float(user_cost) if user_cost else 0.0
        
        return {
            'user_id': user_id,
            'period': period,
            'total_cost': user_cost,
            'timestamp': now.isoformat()
        }
    
    async def is_emergency_mode_active(self) -> bool:
        """Check if emergency mode is currently active."""
        if not self.redis:
            await self.initialize()
        
        mode = await self.redis.get("emergency:mode")
        return mode == "active"
    
    async def deactivate_emergency_mode(self):
        """Manually deactivate emergency mode."""
        if not self.redis:
            await self.initialize()
        
        logger.info("Deactivating emergency cost-saving mode")
        
        # Remove emergency mode flags
        keys_to_delete = [
            "emergency:mode",
            "emergency:max_workers_grammar",
            "emergency:max_workers_style", 
            "emergency:max_workers_structure",
            "emergency:max_workers_content",
            "emergency:cache_ttl",
            "emergency:force_downgrade"
        ]
        
        for key in keys_to_delete:
            await self.redis.delete(key)


# Global instance
_cost_tracker: Optional[CostTracker] = None


async def get_cost_tracker() -> CostTracker:
    """Get or create cost tracker instance."""
    global _cost_tracker
    if _cost_tracker is None:
        _cost_tracker = CostTracker()
        await _cost_tracker.initialize()
    return _cost_tracker