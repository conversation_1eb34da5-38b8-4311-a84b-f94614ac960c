/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_URL: string
  readonly VITE_WS_URL: string
  readonly VITE_FIREBASE_API_KEY: string
  readonly VITE_FIREBASE_AUTH_DOMAIN: string
  readonly VITE_FIREBASE_PROJECT_ID: string
  readonly VITE_FIREBASE_STORAGE_BUCKET: string
  readonly VITE_FIREBASE_MESSAGING_SENDER_ID: string
  readonly VITE_FIREBASE_APP_ID: string
  readonly VITE_ENABLE_SENTRY: string
  readonly VITE_DEV_MODE: string
  readonly VITE_DEV_USER_EMAIL: string
  readonly VITE_DEV_USER_NAME: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}