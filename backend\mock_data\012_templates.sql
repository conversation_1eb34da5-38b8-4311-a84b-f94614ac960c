-- Revisionary Mock Data - Part 12: Templates
-- Reusable document templates

DO $$
DECLARE
    -- Get user IDs from firebase_uid (from 001_core_data.sql)
    sarah_id UUID;
    alex_id UUID;
    kim_id UUID;
    maya_id UUID;
    james_id UUID;
    
    -- No template IDs needed since we're not using RETURNING clauses
    
BEGIN
    -- Get user IDs from existing users
    SELECT id INTO sarah_id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1;
    SELECT id INTO alex_id FROM users WHERE firebase_uid = 'firebase_user_002' LIMIT 1;
    SELECT id INTO kim_id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1;
    SELECT id INTO maya_id FROM users WHERE firebase_uid = 'firebase_user_004' LIMIT 1;
    SELECT id INTO james_id FROM users WHERE firebase_uid = 'firebase_user_005' LIMIT 1;

    -- =================================================================
    -- TEMPLATES - Reusable document templates
    -- =================================================================
    
    INSERT INTO templates (name, description, category, document_type, structure, metadata, tags, created_by, is_public, usage_count) 
    VALUES
    ('Academic Research Paper Template', 'Standard structure for peer-reviewed academic papers with proper sections and citation format', 'research_paper', 'academic',
     '{"sections": ["abstract", "introduction", "literature_review", "methodology", "results", "discussion", "conclusion", "references"], "formatting": {"citation_style": "apa", "line_spacing": "double", "font": "Times New Roman 12pt"}}'::jsonb, 
     '{"target_audience": "academic", "estimated_length": "5000-8000 words", "complexity": "high", "discipline": "general"}'::jsonb, 
     '{"academic", "research", "peer-review", "apa", "methodology"}', kim_id, true, 23);
    
    INSERT INTO templates (name, description, category, document_type, structure, metadata, tags, created_by, is_public, usage_count)
    VALUES
    ('Business Strategy Report Template', 'Executive-level business strategy document with analysis and recommendations', 'business_plan', 'professional',
     '{"sections": ["executive_summary", "situation_analysis", "strategic_options", "recommendations", "implementation_plan", "risk_assessment", "appendices"], "formatting": {"style": "professional", "charts": "required", "executive_focus": true}}'::jsonb, 
     '{"target_audience": "executives", "estimated_length": "3000-5000 words", "complexity": "medium-high", "focus": "strategic"}'::jsonb, 
     '{"business", "strategy", "executive", "analysis", "recommendations"}', james_id, true, 18);
    
    INSERT INTO templates (name, description, category, document_type, structure, metadata, tags, created_by, is_public, usage_count)
    VALUES
    ('Epic Fantasy Novel Template', 'Chapter-based structure for epic fantasy novels with world-building elements', 'novel', 'creative',
     '{"structure": ["prologue", "part_1", "part_2", "part_3", "epilogue"], "chapters_per_part": 8, "elements": ["character_arcs", "world_building", "magic_system", "political_intrigue"], "formatting": {"style": "manuscript", "chapter_headers": true}}'::jsonb, 
     '{"target_audience": "fantasy_readers", "estimated_length": "************ words", "complexity": "medium", "subgenre": "epic_fantasy"}'::jsonb, 
     '{"fantasy", "novel", "epic", "world-building", "magic system"}', maya_id, true, 12);
    
    INSERT INTO templates (name, description, category, document_type, structure, metadata, tags, created_by, is_public, usage_count)
    VALUES
    ('Urban Fantasy Short Story Template', 'Structure for contemporary fantasy short stories in urban settings', 'short_story', 'creative',
     '{"structure": ["hook", "inciting_incident", "rising_action", "climax", "resolution"], "elements": ["modern_setting", "supernatural_element", "character_discovery", "atmospheric_description"], "formatting": {"style": "literary", "length": "short_form"}}'::jsonb, 
     '{"target_audience": "fantasy_readers", "estimated_length": "2000-5000 words", "complexity": "medium", "subgenre": "urban_fantasy"}'::jsonb, 
     '{"urban fantasy", "short story", "contemporary", "supernatural", "atmosphere"}', alex_id, true, 8),
    
    ('Climate Research Policy Brief', 'Template for translating climate research into policy recommendations', 'report', 'academic',
     '{"sections": ["key_findings", "policy_implications", "recommendations", "implementation_pathways", "supporting_evidence"], "formatting": {"style": "policy", "length": "brief", "audience": "policymakers"}}'::jsonb, 
     '{"target_audience": "policymakers", "estimated_length": "1500-3000 words", "complexity": "medium", "focus": "actionable"}'::jsonb, 
     '{"climate", "policy", "research", "brief", "recommendations"}', sarah_id, true, 15),
    
    ('Medical Conference Presentation', 'Template for medical research presentations at academic conferences', 'article', 'professional',
     '{"structure": ["title_slide", "background", "methods", "results", "clinical_implications", "future_research", "questions"], "formatting": {"style": "presentation", "slides": "15-20", "timing": "20_minutes"}}'::jsonb, 
     '{"target_audience": "medical_professionals", "estimated_duration": "20 minutes", "complexity": "high", "focus": "clinical"}'::jsonb, 
     '{"medical", "conference", "presentation", "research", "clinical"}', kim_id, false, 5);

END $$;