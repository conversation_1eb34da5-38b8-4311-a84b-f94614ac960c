{"permissions": {"allow": ["WebFetch(domain:sudowrite.com)", "<PERSON><PERSON>(mkdir:*)", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(mv:*)", "Bash(conda:*)", "Bash(pnpm add:*)", "Bash(rm:*)", "Bash(pnpm dev:*)", "Bash(npm run dev:*)", "Bash(npm install)", "Bash(cp:*)", "Bash(npm run type-check:*)", "<PERSON><PERSON>(gcloud auth:*)", "Bash(gcloud builds submit:*)", "Bash(gcloud projects get-iam-policy:*)", "Bash(gcloud projects add-iam-policy-binding:*)", "Bash(gcloud builds log:*)", "Bash(npm run build:*)", "Bash(find:*)", "Bash(gcloud run services describe:*)", "WebFetch(domain:revisionary-frontend-758179301459.us-central1.run.app)", "Bash(gcloud run services add-iam-policy-binding:*)", "Bash(ls:*)", "Bash(pnpm run:*)", "<PERSON><PERSON>(python:*)", "Bash(pip install:*)", "Bash(source ~/.bashrc)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(source:*)", "Bash(./venv/bin/pip install supabase)", "Bash(./venv/bin/python -m pip install asyncpg structlog python-dotenv)", "Bash(./venv/bin/python -c \"import asyncpg, structlog; from dotenv import load_dotenv; print('✅ All migration dependencies are now installed and working')\")", "Bash(./venv/bin/python run_migrations.py:*)", "Bash(./venv/bin/python -m pip install pydantic pydantic-settings)", "Bash(venv/bin/python:*)", "Bash(npm install:*)", "Bash(pnpm install:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(jq:*)"], "deny": []}}