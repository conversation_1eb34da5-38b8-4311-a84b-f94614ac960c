-- Revisionary Mock Data - Part 5A: Scoring Algorithms
-- Additional algorithm versions beyond v1.0

DO $$
DECLARE
    -- Variables not needed for this section
BEGIN
    -- =================================================================
    -- SCORING ALGORITHMS - Additional algorithm versions beyond v1.0
    -- =================================================================
    
    INSERT INTO scoring_algorithms (version, algorithm_name, description, grammar_weights, style_weights, structure_weights, content_weights, document_type_adjustments, is_active) 
    VALUES
    ('1.1', 'Enhanced Academic Scoring', 'Improved algorithm with better academic writing assessment', 
     '{"base_weight": 0.25, "citation_bonus": 0.05, "methodology_boost": 0.03}'::jsonb,
     '{"base_weight": 0.20, "passive_voice_penalty": -0.02, "technical_vocabulary_tolerance": 0.03}'::jsonb,
     '{"base_weight": 0.25, "logical_flow_emphasis": 0.04, "section_organization": 0.02}'::jsonb,
     '{"base_weight": 0.30, "evidence_strength": 0.05, "depth_analysis": 0.03}'::jsonb,
     '{"academic": {"multiplier": 1.1, "citation_requirement": true}, "research": {"multiplier": 1.05, "methodology_emphasis": true}}'::jsonb, false),
    
    ('1.2', 'Creative Writing Optimizer', 'Specialized scoring for creative and narrative content',
     '{"base_weight": 0.15, "dialogue_mechanics": 0.04, "narrative_grammar": 0.02}'::jsonb,
     '{"base_weight": 0.35, "voice_consistency": 0.08, "atmospheric_language": 0.06, "metaphor_quality": 0.04}'::jsonb,
     '{"base_weight": 0.25, "pacing_analysis": 0.05, "scene_structure": 0.03}'::jsonb,
     '{"base_weight": 0.25, "character_development": 0.06, "emotional_resonance": 0.05}'::jsonb,
     '{"creative": {"multiplier": 1.15, "atmosphere_bonus": 0.1}, "novel": {"multiplier": 1.1, "character_emphasis": true}}'::jsonb, false),
    
    ('2.0', 'AI-Enhanced Multi-Modal Scoring', 'Next-generation algorithm with AI-powered content analysis',
     '{"base_weight": 0.20, "semantic_analysis": 0.05, "context_grammar": 0.03}'::jsonb,
     '{"base_weight": 0.25, "ai_tone_detection": 0.07, "brand_voice_matching": 0.05}'::jsonb,
     '{"base_weight": 0.25, "ai_flow_analysis": 0.06, "audience_alignment": 0.04}'::jsonb,
     '{"base_weight": 0.30, "semantic_depth": 0.08, "contextual_relevance": 0.06}'::jsonb,
     '{"all": {"ai_enhancement": true, "semantic_bonus": 0.05}, "experimental": {"tone_detection": true, "brand_matching": true}}'::jsonb, false),
    
    ('1.3', 'Business Communication Specialist', 'Optimized for professional and business writing',
     '{"base_weight": 0.20, "business_language": 0.03, "clarity_emphasis": 0.04}'::jsonb,
     '{"base_weight": 0.30, "executive_tone": 0.06, "action_orientation": 0.05, "conciseness": 0.04}'::jsonb,
     '{"base_weight": 0.30, "roi_presentation": 0.05, "stakeholder_focus": 0.04}'::jsonb,
     '{"base_weight": 0.20, "data_integration": 0.05, "strategic_insight": 0.04}'::jsonb,
     '{"professional": {"multiplier": 1.1, "executive_bonus": 0.08}, "business": {"multiplier": 1.15, "roi_emphasis": true}}'::jsonb, false);

END $$;