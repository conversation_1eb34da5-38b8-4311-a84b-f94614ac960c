#!/usr/bin/env python3
"""
Test Error Handling Improvements

Purpose: Test the enhanced error handling system including:
- Custom exception classes with structured error codes
- Validation errors with field-level details
- HTTP status code mapping
- Global exception handlers
- Structured error responses

Features tested:
- ValidationException with field errors
- ResourceNotFoundException with resource details  
- AuthorizationException with permission details
- CitationException with operation context
- ServiceUnavailableException with retry information
"""

import asyncio
import json
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

from core.exceptions import (
    ValidationException,
    ResourceNotFoundException,
    AuthorizationException,
    CitationException,
    ServiceUnavailableException,
    DuplicateResourceException,
    to_http_exception,
    validate_required_fields,
    validate_field_length
)

def test_validation_exception():
    """Test validation exception with field errors."""
    print("🧪 Testing ValidationException")
    print("-" * 40)
    
    try:
        # Test field validation
        field_errors = {
            "title": ["Title is required"],
            "authors": ["At least one author is required"],
            "email": ["Invalid email format"]
        }
        
        raise ValidationException(
            message="Citation validation failed",
            field_errors=field_errors,
            details={"validation_context": "citation_creation"}
        )
    except ValidationException as e:
        print(f"✓ Exception caught: {e.message}")
        print(f"  Error Code: {e.error_code}")
        print(f"  Field Errors: {json.dumps(e.field_errors, indent=2)}")
        print(f"  Details: {json.dumps(e.details, indent=2)}")
        
        # Test HTTP conversion
        http_exc = to_http_exception(e)
        print(f"  HTTP Status: {http_exc.status_code}")
        print(f"  HTTP Detail: {json.dumps(http_exc.detail, indent=2)}")
    
    print()

def test_resource_not_found():
    """Test resource not found exception."""
    print("🧪 Testing ResourceNotFoundException")
    print("-" * 40)
    
    try:
        raise ResourceNotFoundException(
            resource_type="Citation",
            resource_id="citation-123"
        )
    except ResourceNotFoundException as e:
        print(f"✓ Exception caught: {e.message}")
        print(f"  Error Code: {e.error_code}")
        print(f"  Resource Type: {e.resource_type}")
        print(f"  Resource ID: {e.resource_id}")
        print(f"  Details: {json.dumps(e.details, indent=2)}")
        
        # Test HTTP conversion
        http_exc = to_http_exception(e)
        print(f"  HTTP Status: {http_exc.status_code}")
    
    print()

def test_authorization_exception():
    """Test authorization exception."""
    print("🧪 Testing AuthorizationException")
    print("-" * 40)
    
    try:
        raise AuthorizationException(
            message="Insufficient permissions to access document",
            details={
                "required_permission": "document:read",
                "user_permissions": ["document:create"],
                "resource_id": "doc-123"
            }
        )
    except AuthorizationException as e:
        print(f"✓ Exception caught: {e.message}")
        print(f"  Error Code: {e.error_code}")
        print(f"  Details: {json.dumps(e.details, indent=2)}")
        
        # Test HTTP conversion
        http_exc = to_http_exception(e)
        print(f"  HTTP Status: {http_exc.status_code}")
    
    print()

def test_citation_exception():
    """Test citation-specific exception."""
    print("🧪 Testing CitationException")
    print("-" * 40)
    
    try:
        raise CitationException(
            citation_id="smith2024",
            operation="bibliography_generation",
            message="Failed to format citation in APA style",
            details={
                "style": "apa",
                "csl_error": "Missing required field: publication_year",
                "attempted_fields": ["title", "authors", "journal"]
            }
        )
    except CitationException as e:
        print(f"✓ Exception caught: {e.message}")
        print(f"  Error Code: {e.error_code}")
        print(f"  Citation ID: {e.citation_id}")
        print(f"  Operation: {e.operation}")
        print(f"  Details: {json.dumps(e.details, indent=2)}")
        
        # Test HTTP conversion
        http_exc = to_http_exception(e)
        print(f"  HTTP Status: {http_exc.status_code}")
    
    print()

def test_service_unavailable():
    """Test service unavailable exception with retry information."""
    print("🧪 Testing ServiceUnavailableException")
    print("-" * 40)
    
    try:
        raise ServiceUnavailableException(
            service_name="OpenAI API",
            message="OpenAI API is temporarily unavailable due to rate limits",
            retry_after=60
        )
    except ServiceUnavailableException as e:
        print(f"✓ Exception caught: {e.message}")
        print(f"  Error Code: {e.error_code}")
        print(f"  Service: {e.service_name}")
        print(f"  Retry After: {e.retry_after} seconds")
        print(f"  Details: {json.dumps(e.details, indent=2)}")
        
        # Test HTTP conversion
        http_exc = to_http_exception(e)
        print(f"  HTTP Status: {http_exc.status_code}")
        print(f"  Headers: {http_exc.headers}")
    
    print()

def test_duplicate_resource():
    """Test duplicate resource exception."""
    print("🧪 Testing DuplicateResourceException")
    print("-" * 40)
    
    try:
        raise DuplicateResourceException(
            resource_type="Citation",
            identifier="smith2024",
            identifier_field="citation_key"
        )
    except DuplicateResourceException as e:
        print(f"✓ Exception caught: {e.message}")
        print(f"  Error Code: {e.error_code}")
        print(f"  Resource Type: {e.resource_type}")
        print(f"  Identifier: {e.identifier}")
        print(f"  Identifier Field: {e.identifier_field}")
        
        # Test HTTP conversion
        http_exc = to_http_exception(e)
        print(f"  HTTP Status: {http_exc.status_code}")
    
    print()

def test_validation_helpers():
    """Test validation helper functions."""
    print("🧪 Testing Validation Helpers")
    print("-" * 40)
    
    # Test required fields validation
    try:
        data = {
            "title": "Sample Title",
            "authors": [],  # Empty list should fail
            "citation_key": ""  # Empty string should fail
        }
        
        validate_required_fields(data, ["title", "authors", "citation_key"])
        print("❌ Should have failed validation")
    except ValidationException as e:
        print(f"✓ Required fields validation caught: {e.message}")
        print(f"  Field Errors: {json.dumps(e.field_errors, indent=2)}")
    
    # Test field length validation
    try:
        data = {"title": "x" * 1001}  # Too long
        validate_field_length(data, "title", min_length=1, max_length=1000)
        print("❌ Should have failed length validation")
    except ValidationException as e:
        print(f"✓ Field length validation caught: {e.message}")
        print(f"  Field Errors: {json.dumps(e.field_errors, indent=2)}")
    
    print()

def main():
    """Run all error handling tests."""
    print("🚀 REVISIONARY ERROR HANDLING TESTS")
    print("=" * 80)
    print("Testing enhanced error handling system...")
    print()
    
    # Test all exception types
    test_validation_exception()
    test_resource_not_found()
    test_authorization_exception()
    test_citation_exception()
    test_service_unavailable()
    test_duplicate_resource()
    test_validation_helpers()
    
    print("🎉 ALL ERROR HANDLING TESTS COMPLETED!")
    print("=" * 80)
    print("\n📋 ERROR HANDLING IMPROVEMENTS SUMMARY:")
    print("   ✓ Custom exception classes with structured error codes")
    print("   ✓ Field-level validation errors for forms")
    print("   ✓ Resource-specific exceptions with context")
    print("   ✓ Service availability exceptions with retry logic")
    print("   ✓ HTTP status code mapping")
    print("   ✓ Structured error responses")
    print("   ✓ Global exception handlers")
    print("   ✓ Validation helper functions")
    print("\n🔗 Exception Types Available:")
    print("   • ValidationException - Form validation errors")
    print("   • ResourceNotFoundException - Missing resources")
    print("   • AuthorizationException - Permission errors")
    print("   • CitationException - Citation processing errors")
    print("   • ServiceUnavailableException - External service failures")
    print("   • DuplicateResourceException - Conflict errors")
    print("   • DatabaseException - Database operation failures")
    print("   • LLMServiceException - AI service errors")
    print("   • ConfigurationException - Config errors")
    print("\n🎯 Ready for frontend error handling integration!")

if __name__ == "__main__":
    main()