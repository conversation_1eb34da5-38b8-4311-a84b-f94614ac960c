import React, { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CloudArrowUpIcon,
  DocumentTextIcon,
  PhotoIcon,
  FolderOpenIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  ArrowRightIcon,
  DocumentIcon,
  GlobeAltIcon,
  LinkIcon,
  EyeIcon,
  PencilSquareIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';

interface ImportFile {
  id: string;
  name: string;
  type: string;
  size: number;
  status: 'pending' | 'processing' | 'success' | 'error';
  preview?: string;
  extractedText?: string;
  error?: string;
  progress?: number;
}

interface ImportOptions {
  preserveFormatting: boolean;
  extractImages: boolean;
  mergeIntoDocument: boolean;
  createSeparateDocuments: boolean;
}

interface ImportWizardProps {
  isOpen: boolean;
  onClose: () => void;
  onImportComplete: (files: ImportFile[], options: ImportOptions) => void;
}

const supportedFormats = [
  { type: '.docx', label: 'Word Documents', icon: DocumentTextIcon, color: 'blue' },
  { type: '.pdf', label: 'PDF Files', icon: DocumentIcon, color: 'red' },
  { type: '.txt', label: 'Text Files', icon: DocumentTextIcon, color: 'gray' },
  { type: '.md', label: 'Markdown', icon: DocumentTextIcon, color: 'green' },
  { type: '.rtf', label: 'Rich Text', icon: DocumentTextIcon, color: 'purple' },
  { type: '.odt', label: 'OpenDocument', icon: DocumentTextIcon, color: 'orange' },
];

const ImportWizard: React.FC<ImportWizardProps> = ({
  isOpen,
  onClose,
  onImportComplete,
}) => {
  const [step, setStep] = useState<'upload' | 'options' | 'preview'>('upload');
  const [files, setFiles] = useState<ImportFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [importOptions, setImportOptions] = useState<ImportOptions>({
    preserveFormatting: true,
    extractImages: false,
    mergeIntoDocument: false,
    createSeparateDocuments: true,
  });
  const [urlInput, setUrlInput] = useState('');
  const [isProcessingUrl, setIsProcessingUrl] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const dragCounterRef = useRef(0);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current++;
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current--;
    if (dragCounterRef.current === 0) {
      setIsDragOver(false);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
    dragCounterRef.current = 0;

    const droppedFiles = Array.from(e.dataTransfer.files);
    handleFiles(droppedFiles);
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files);
      handleFiles(selectedFiles);
    }
  };

  const handleFiles = (fileList: File[]) => {
    const newFiles: ImportFile[] = fileList.map((file, index) => ({
      id: `${Date.now()}-${index}`,
      name: file.name,
      type: file.type || getFileExtension(file.name),
      size: file.size,
      status: 'pending',
    }));

    setFiles(prev => [...prev, ...newFiles]);
    
    // Simulate file processing
    newFiles.forEach(file => processFile(file, fileList.find(f => f.name === file.name)!));
  };

  const processFile = async (importFile: ImportFile, file: File) => {
    setFiles(prev => prev.map(f => 
      f.id === importFile.id ? { ...f, status: 'processing', progress: 0 } : f
    ));

    // Simulate processing with progress
    for (let progress = 0; progress <= 100; progress += 20) {
      await new Promise(resolve => setTimeout(resolve, 200));
      setFiles(prev => prev.map(f => 
        f.id === importFile.id ? { ...f, progress } : f
      ));
    }

    // Simulate extraction
    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        let extractedText = '';
        let preview = '';

        if (file.type.includes('text') || importFile.name.endsWith('.txt') || importFile.name.endsWith('.md')) {
          extractedText = result;
          preview = result.substring(0, 300) + (result.length > 300 ? '...' : '');
        } else {
          // For other file types, simulate extracted text
          extractedText = `Extracted content from ${file.name}.\n\nThis is simulated content extraction. In a real implementation, this would contain the actual text extracted from the document using appropriate libraries for each file format.\n\nThe document contains approximately ${Math.floor(Math.random() * 5000) + 1000} words across ${Math.floor(Math.random() * 20) + 1} pages.`;
          preview = extractedText.substring(0, 300) + '...';
        }

        setFiles(prev => prev.map(f => 
          f.id === importFile.id 
            ? { ...f, status: 'success', extractedText, preview, progress: 100 }
            : f
        ));
      };

      reader.onerror = () => {
        setFiles(prev => prev.map(f => 
          f.id === importFile.id 
            ? { ...f, status: 'error', error: 'Failed to read file' }
            : f
        ));
      };

      reader.readAsText(file);
    } catch (error) {
      setFiles(prev => prev.map(f => 
        f.id === importFile.id 
          ? { ...f, status: 'error', error: 'Processing failed' }
          : f
      ));
    }
  };

  const handleUrlImport = async () => {
    if (!urlInput.trim()) return;

    setIsProcessingUrl(true);
    
    // Simulate URL processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const urlFile: ImportFile = {
      id: `url-${Date.now()}`,
      name: new URL(urlInput).hostname,
      type: 'web',
      size: 0,
      status: 'success',
      extractedText: `Content extracted from ${urlInput}\n\nThis is simulated web content extraction. In a real implementation, this would fetch and extract the main content from the webpage, removing navigation, ads, and other non-content elements.\n\nThe page contains relevant information that can be used as reference material in your writing.`,
      preview: `Content from ${new URL(urlInput).hostname}...`,
    };

    setFiles(prev => [...prev, urlFile]);
    setUrlInput('');
    setIsProcessingUrl(false);
  };

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id));
  };

  const getFileExtension = (filename: string) => {
    return '.' + filename.split('.').pop()?.toLowerCase() || '';
  };

  const getFileIcon = (type: string) => {
    if (type === 'web') return GlobeAltIcon;
    if (type.includes('image')) return PhotoIcon;
    return DocumentTextIcon;
  };

  const getFileColor = (type: string) => {
    if (type === 'web') return 'purple';
    if (type.includes('pdf')) return 'red';
    if (type.includes('word') || type.includes('docx')) return 'blue';
    if (type.includes('text')) return 'gray';
    return 'slate';
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return 'N/A';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const canProceed = () => {
    return files.length > 0 && files.every(f => f.status === 'success' || f.status === 'error');
  };

  const handleNext = () => {
    if (step === 'upload' && canProceed()) {
      setStep('options');
    } else if (step === 'options') {
      setStep('preview');
    }
  };

  const handleImport = () => {
    onImportComplete(files.filter(f => f.status === 'success'), importOptions);
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="fixed inset-0 flex items-center justify-center z-mobile-modal mobile-p lg:p-4"
            onClick={e => e.stopPropagation()}
          >
            <div className="w-full max-w-4xl mobile-full-height lg:max-h-mobile-modal bg-white lg:bg-white/95 lg:backdrop-blur-xl rounded-none lg:rounded-2xl shadow-2xl border-0 lg:border lg:border-slate-200/50 overflow-hidden flex flex-col">
              {/* Header */}
              <div className="mobile-px lg:px-6 mobile-py lg:py-4 border-b border-slate-200/50 bg-gradient-to-r from-blue-50/50 to-purple-50/50 flex-shrink-0 mobile-safe-area">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 lg:w-10 lg:h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg lg:rounded-xl flex items-center justify-center">
                      <CloudArrowUpIcon className="w-4 h-4 lg:w-5 lg:h-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-base lg:text-lg font-bold text-slate-900">Import Documents</h2>
                      <p className="text-xs lg:text-sm text-slate-600">
                        Step {step === 'upload' ? '1' : step === 'options' ? '2' : '3'} of 3 • {step === 'upload' ? 'Upload Files' : step === 'options' ? 'Import Options' : 'Preview & Import'}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={onClose}
                    className="mobile-icon-btn text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
                  >
                    <XMarkIcon className="w-5 h-5 lg:w-6 lg:h-6" />
                  </button>
                </div>

                {/* Progress Steps */}
                <div className="flex items-center space-x-4 mt-4">
                  {['upload', 'options', 'preview'].map((stepName, index) => (
                    <div key={stepName} className="flex items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                        step === stepName
                          ? 'bg-blue-500 text-white'
                          : index < ['upload', 'options', 'preview'].indexOf(step)
                          ? 'bg-green-500 text-white'
                          : 'bg-slate-200 text-slate-600'
                      }`}>
                        {index < ['upload', 'options', 'preview'].indexOf(step) ? (
                          <CheckCircleIcon className="w-4 h-4" />
                        ) : (
                          index + 1
                        )}
                      </div>
                      {index < 2 && (
                        <div className={`w-12 h-0.5 mx-2 ${
                          index < ['upload', 'options', 'preview'].indexOf(step)
                            ? 'bg-green-500'
                            : 'bg-slate-200'
                        }`} />
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Content */}
              <div className="mobile-p lg:p-6 flex-1 overflow-y-auto">
                <AnimatePresence mode="wait">
                  {step === 'upload' && (
                    <motion.div
                      key="upload"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      className="space-y-6"
                    >
                      {/* Drag and Drop Area */}
                      <div
                        onDragEnter={handleDragEnter}
                        onDragLeave={handleDragLeave}
                        onDragOver={handleDragOver}
                        onDrop={handleDrop}
                        className={`border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-200 ${
                          isDragOver
                            ? 'border-blue-400 bg-blue-50'
                            : 'border-slate-300 hover:border-slate-400'
                        }`}
                      >
                        <CloudArrowUpIcon className={`w-16 h-16 mx-auto mb-4 ${
                          isDragOver ? 'text-blue-500' : 'text-slate-400'
                        }`} />
                        <h3 className="text-lg font-semibold text-slate-900 mb-2">
                          Drop files here or click to browse
                        </h3>
                        <p className="text-slate-600 mb-4">
                          Support for Word, PDF, text files and more
                        </p>
                        <button
                          onClick={() => fileInputRef.current?.click()}
                          className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-xl font-medium hover:shadow-lg transition-all"
                        >
                          Choose Files
                        </button>
                        <input
                          ref={fileInputRef}
                          type="file"
                          multiple
                          accept=".docx,.pdf,.txt,.md,.rtf,.odt"
                          onChange={handleFileSelect}
                          className="hidden"
                        />
                      </div>

                      {/* URL Import */}
                      <div className="border border-slate-200 rounded-xl p-4">
                        <h4 className="font-semibold text-slate-900 mb-3">Import from URL</h4>
                        <div className="flex space-x-3">
                          <div className="flex-1 relative">
                            <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                            <input
                              type="url"
                              placeholder="https://example.com/article"
                              value={urlInput}
                              onChange={(e) => setUrlInput(e.target.value)}
                              className="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                          </div>
                          <button
                            onClick={handleUrlImport}
                            disabled={!urlInput.trim() || isProcessingUrl}
                            className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium disabled:bg-slate-400 hover:bg-blue-700 transition-colors"
                          >
                            {isProcessingUrl ? 'Importing...' : 'Import'}
                          </button>
                        </div>
                      </div>

                      {/* Supported Formats */}
                      <div>
                        <h4 className="font-semibold text-slate-900 mb-3">Supported Formats</h4>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          {supportedFormats.map((format) => {
                            const IconComponent = format.icon;
                            return (
                              <div key={format.type} className="flex items-center p-3 bg-slate-50 rounded-lg">
                                <IconComponent className={`w-5 h-5 text-${format.color}-600 mr-3`} />
                                <div>
                                  <p className="text-sm font-medium text-slate-900">{format.type}</p>
                                  <p className="text-xs text-slate-600">{format.label}</p>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>

                      {/* Files List */}
                      {files.length > 0 && (
                        <div>
                          <h4 className="font-semibold text-slate-900 mb-3">
                            Uploaded Files ({files.length})
                          </h4>
                          <div className="space-y-2">
                            {files.map((file) => {
                              const IconComponent = getFileIcon(file.type);
                              const color = getFileColor(file.type);
                              
                              return (
                                <div key={file.id} className="flex items-center p-3 bg-white border border-slate-200 rounded-lg">
                                  <IconComponent className={`w-5 h-5 text-${color}-600 mr-3`} />
                                  <div className="flex-1">
                                    <p className="text-sm font-medium text-slate-900">{file.name}</p>
                                    <p className="text-xs text-slate-600">
                                      {formatFileSize(file.size)} • {file.type}
                                    </p>
                                    {file.status === 'processing' && file.progress !== undefined && (
                                      <div className="mt-1 w-full bg-slate-200 rounded-full h-1">
                                        <div 
                                          className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                                          style={{ width: `${file.progress}%` }}
                                        />
                                      </div>
                                    )}
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    {file.status === 'success' && (
                                      <CheckCircleIcon className="w-5 h-5 text-green-500" />
                                    )}
                                    {file.status === 'error' && (
                                      <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />
                                    )}
                                    {file.status === 'processing' && (
                                      <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                                    )}
                                    <button
                                      onClick={() => removeFile(file.id)}
                                      className="p-1 text-slate-400 hover:text-red-600 transition-colors"
                                    >
                                      <TrashIcon className="w-4 h-4" />
                                    </button>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </motion.div>
                  )}

                  {step === 'options' && (
                    <motion.div
                      key="options"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      className="space-y-6"
                    >
                      <h3 className="text-lg font-semibold text-slate-900">Import Options</h3>
                      
                      <div className="space-y-4">
                        <div className="flex items-start space-x-3">
                          <input
                            type="checkbox"
                            id="preserveFormatting"
                            checked={importOptions.preserveFormatting}
                            onChange={(e) => setImportOptions(prev => ({
                              ...prev,
                              preserveFormatting: e.target.checked
                            }))}
                            className="mt-1 w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500"
                          />
                          <div>
                            <label htmlFor="preserveFormatting" className="font-medium text-slate-900">
                              Preserve Formatting
                            </label>
                            <p className="text-sm text-slate-600">
                              Keep original text formatting like bold, italic, and headings
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start space-x-3">
                          <input
                            type="checkbox"
                            id="extractImages"
                            checked={importOptions.extractImages}
                            onChange={(e) => setImportOptions(prev => ({
                              ...prev,
                              extractImages: e.target.checked
                            }))}
                            className="mt-1 w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500"
                          />
                          <div>
                            <label htmlFor="extractImages" className="font-medium text-slate-900">
                              Extract Images
                            </label>
                            <p className="text-sm text-slate-600">
                              Import images and media from documents
                            </p>
                          </div>
                        </div>

                        <div className="border-t border-slate-200 pt-4">
                          <h4 className="font-medium text-slate-900 mb-3">Document Organization</h4>
                          
                          <div className="space-y-3">
                            <div className="flex items-start space-x-3">
                              <input
                                type="radio"
                                id="mergeIntoDocument"
                                name="organization"
                                checked={importOptions.mergeIntoDocument}
                                onChange={(e) => setImportOptions(prev => ({
                                  ...prev,
                                  mergeIntoDocument: e.target.checked,
                                  createSeparateDocuments: !e.target.checked
                                }))}
                                className="mt-1 w-4 h-4 text-blue-600 border-slate-300 focus:ring-blue-500"
                              />
                              <div>
                                <label htmlFor="mergeIntoDocument" className="font-medium text-slate-900">
                                  Merge into current document
                                </label>
                                <p className="text-sm text-slate-600">
                                  Add imported content to the end of your current document
                                </p>
                              </div>
                            </div>

                            <div className="flex items-start space-x-3">
                              <input
                                type="radio"
                                id="createSeparateDocuments"
                                name="organization"
                                checked={importOptions.createSeparateDocuments}
                                onChange={(e) => setImportOptions(prev => ({
                                  ...prev,
                                  createSeparateDocuments: e.target.checked,
                                  mergeIntoDocument: !e.target.checked
                                }))}
                                className="mt-1 w-4 h-4 text-blue-600 border-slate-300 focus:ring-blue-500"
                              />
                              <div>
                                <label htmlFor="createSeparateDocuments" className="font-medium text-slate-900">
                                  Create separate documents
                                </label>
                                <p className="text-sm text-slate-600">
                                  Import each file as a new document in your workspace
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {step === 'preview' && (
                    <motion.div
                      key="preview"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      className="space-y-6"
                    >
                      <h3 className="text-lg font-semibold text-slate-900">Preview & Import</h3>
                      
                      <div className="space-y-4">
                        {files.filter(f => f.status === 'success').map((file) => (
                          <div key={file.id} className="border border-slate-200 rounded-lg p-4">
                            <div className="flex items-center justify-between mb-3">
                              <h4 className="font-medium text-slate-900">{file.name}</h4>
                              <span className="text-xs text-slate-500">{formatFileSize(file.size)}</span>
                            </div>
                            {file.preview && (
                              <div className="bg-slate-50 rounded-lg p-3">
                                <p className="text-sm text-slate-700 line-clamp-4">{file.preview}</p>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>

                      <div className="bg-blue-50 rounded-lg p-4">
                        <h4 className="font-medium text-slate-900 mb-2">Import Summary</h4>
                        <ul className="text-sm text-slate-600 space-y-1">
                          <li>• {files.filter(f => f.status === 'success').length} files will be imported</li>
                          <li>• Formatting will be {importOptions.preserveFormatting ? 'preserved' : 'simplified'}</li>
                          <li>• Images will be {importOptions.extractImages ? 'extracted' : 'ignored'}</li>
                          <li>• Content will be {importOptions.mergeIntoDocument ? 'merged into current document' : 'created as separate documents'}</li>
                        </ul>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Footer */}
              <div className="mobile-px lg:px-6 mobile-py lg:py-4 border-t border-slate-200/50 bg-slate-50/50 flex-shrink-0 safe-bottom">
                <div className="flex flex-col space-y-3 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
                  <div className="text-xs lg:text-sm text-slate-600 text-center lg:text-left">
                    {step === 'upload' && `${files.length} file${files.length !== 1 ? 's' : ''} uploaded`}
                    {step === 'options' && 'Configure import settings'}
                    {step === 'preview' && `Ready to import ${files.filter(f => f.status === 'success').length} file${files.filter(f => f.status === 'success').length !== 1 ? 's' : ''}`}
                  </div>
                  
                  <div className="flex space-x-3 justify-center lg:justify-end">
                    {step !== 'upload' && (
                      <button
                        onClick={() => setStep(step === 'options' ? 'upload' : 'options')}
                        className="mobile-btn text-slate-700 border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors"
                      >
                        Back
                      </button>
                    )}
                    
                    {step === 'preview' ? (
                      <button
                        onClick={handleImport}
                        className="flex items-center justify-center mobile-btn bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg font-medium hover:shadow-lg transition-all"
                      >
                        Import Documents
                        <CheckCircleIcon className="w-4 h-4 ml-2" />
                      </button>
                    ) : (
                      <button
                        onClick={handleNext}
                        disabled={!canProceed()}
                        className="flex items-center justify-center mobile-btn bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg font-medium disabled:bg-slate-400 hover:shadow-lg transition-all"
                      >
                        {step === 'upload' ? 'Continue' : 'Next'}
                        <ArrowRightIcon className="w-4 h-4 ml-2" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default ImportWizard;