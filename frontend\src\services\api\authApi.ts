/**
 * Authentication API Service
 * 
 * Handles all authentication-related API calls including:
 * - Token exchange and refresh
 * - User profile management
 * - Session management
 * - Authentication status checks
 */

import baseApi from './baseApi';
import type { ApiResponse } from './baseApi';

export interface User {
  id: string;
  firebase_uid: string;
  email: string;
  display_name: string;
  avatar_url?: string;
  subscription_tier: 'free' | 'professional' | 'studio' | 'enterprise';
  subscription_status: 'active' | 'inactive' | 'cancelled' | 'past_due';
  onboarding_completed: boolean;
  preferences: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  user: User;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
    achievements: boolean;
    health_alerts: boolean;
  };
  editor: {
    font_size: number;
    font_family: string;
    line_height: number;
    auto_save: boolean;
    spell_check: boolean;
  };
  health: {
    auto_analysis: boolean;
    real_time_feedback: boolean;
    focus_areas: string[];
    target_score: number;
  };
}

class AuthApiService {
  /**
   * Exchange Firebase ID token for API access token
   */
  async exchangeToken(firebaseToken: string): Promise<TokenResponse> {
    const response = await baseApi.post<TokenResponse>('/auth/token', {
      firebase_token: firebaseToken,
    });
    return response.data;
  }

  /**
   * Refresh expired access token
   */
  async refreshToken(refreshToken: string): Promise<Omit<TokenResponse, 'user'>> {
    const response = await baseApi.post<Omit<TokenResponse, 'user'>>('/auth/refresh', {
      refresh_token: refreshToken,
    });
    return response.data;
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<User> {
    const response = await baseApi.get<User>('/auth/me');
    return response.data;
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: Partial<Pick<User, 'display_name' | 'avatar_url'>>): Promise<User> {
    const response = await baseApi.put<User>('/auth/me', updates);
    return response.data;
  }

  /**
   * Get user preferences
   */
  async getPreferences(): Promise<UserPreferences> {
    const response = await baseApi.get<UserPreferences>('/auth/preferences');
    return response.data;
  }

  /**
   * Update user preferences
   */
  async updatePreferences(preferences: Partial<UserPreferences>): Promise<UserPreferences> {
    const response = await baseApi.put<UserPreferences>('/auth/preferences', preferences);
    return response.data;
  }

  /**
   * Complete user onboarding
   */
  async completeOnboarding(onboardingData: {
    writing_goals: string[];
    preferred_styles: string[];
    experience_level: 'beginner' | 'intermediate' | 'advanced';
    content_types: string[];
  }): Promise<User> {
    const response = await baseApi.post<User>('/auth/onboarding/complete', onboardingData);
    return response.data;
  }

  /**
   * Get user subscription information
   */
  async getSubscription(): Promise<{
    tier: string;
    status: string;
    current_period_end: string;
    usage: {
      requests_this_month: number;
      requests_limit: number;
      tokens_used: number;
      tokens_limit: number;
    };
  }> {
    const response = await baseApi.get('/auth/subscription');
    return response.data;
  }

  /**
   * Update subscription
   */
  async updateSubscription(plan: string): Promise<{
    subscription_url: string;
    session_id: string;
  }> {
    const response = await baseApi.post('/auth/subscription/update', { plan });
    return response.data;
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(): Promise<{ success: boolean }> {
    const response = await baseApi.post('/auth/subscription/cancel');
    return response.data;
  }

  /**
   * Delete user account
   */
  async deleteAccount(): Promise<{ success: boolean }> {
    const response = await baseApi.delete('/auth/me');
    return response.data;
  }

  /**
   * Logout (revoke tokens)
   */
  async logout(): Promise<{ success: boolean }> {
    const response = await baseApi.post('/auth/logout');
    return response.data;
  }

  /**
   * Verify current session is valid
   */
  async verifySession(): Promise<{ valid: boolean; user?: User }> {
    try {
      const user = await this.getCurrentUser();
      return { valid: true, user };
    } catch (error) {
      return { valid: false };
    }
  }
}

// Export singleton instance
export const authApi = new AuthApiService();
export default authApi;