"""
Persona Feedback Worker

Purpose: Generates multi-reader persona feedback using LLM models to provide
audience-specific analysis and cross-audience insights.

Features:
- Single persona feedback generation
- Multi-persona parallel processing
- Cross-audience analysis and conflict detection
- Feedback aggregation and optimization suggestions
- Performance tracking and analytics

Model Usage:
- Primary: GPT-4.1 full for complex persona analysis
- Fallback: Gemini 2.5 Flash for long context scenarios
- Context: Full document + persona profiles
- Target Latency: <3000ms per persona set

Dependencies: BaseWorker, LLMService, database
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from uuid import UUID, uuid4
from datetime import datetime
import structlog

from .base_worker import BaseWorker
from core.llm_service import LLMService
from core.database import get_database

logger = structlog.get_logger(__name__)


class PersonaWorker(BaseWorker):
    """Worker specialized for persona feedback generation."""
    
    def __init__(self, worker_id: Optional[str] = None):
        super().__init__('persona', worker_id)
        self.llm_service = LLMService()
        self.max_text_length = 8000  # Longer context for comprehensive analysis
        self.cache_ttl = 1800  # 30-minute cache for persona results
        self.max_personas_per_batch = 10  # Limit for parallel processing
        
    async def process_job(self, job: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process persona feedback job.
        
        Args:
            job: Job data containing:
                - text: Text to analyze
                - personas: List of persona IDs or full persona data
                - analysis_type: 'single' or 'multi'
                - context: Additional context (optional)
                - options: Processing options (optional)
        
        Returns:
            Dict containing feedback results
        """
        try:
            start_time = datetime.utcnow()
            
            text = job.get('text', '')
            personas = job.get('personas', [])
            analysis_type = job.get('analysis_type', 'single')
            context = job.get('context', {})
            options = job.get('options', {})
            
            logger.info("Processing persona feedback job",
                       worker_id=self.worker_id,
                       analysis_type=analysis_type,
                       persona_count=len(personas),
                       text_length=len(text))
            
            # Validate inputs
            if not text or len(text.strip()) == 0:
                raise ValueError("Text content is required")
            
            if not personas:
                raise ValueError("At least one persona is required")
            
            if len(personas) > self.max_personas_per_batch:
                raise ValueError(f"Maximum {self.max_personas_per_batch} personas allowed per batch")
            
            # Truncate text if too long
            if len(text) > self.max_text_length:
                text = text[:self.max_text_length] + "..."
                logger.warning("Text truncated due to length",
                             original_length=len(job.get('text', '')),
                             truncated_length=len(text))
            
            # Load full persona data if needed
            persona_data = await self._load_persona_data(personas)
            
            # Generate feedback based on analysis type
            if analysis_type == 'single' or len(persona_data) == 1:
                result = await self._generate_single_feedback(text, persona_data[0], context, options)
            else:
                result = await self._generate_multi_feedback(text, persona_data, context, options)
            
            # Calculate processing time
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            result['processing_time_ms'] = processing_time
            result['worker_id'] = self.worker_id
            result['created_at'] = datetime.utcnow().isoformat() + 'Z'
            
            logger.info("Persona feedback completed",
                       worker_id=self.worker_id,
                       processing_time_ms=processing_time,
                       analysis_type=analysis_type)
            
            return result
            
        except Exception as e:
            logger.error("Error processing persona feedback job",
                        worker_id=self.worker_id,
                        error=str(e))
            return {
                'error': str(e),
                'worker_id': self.worker_id,
                'processing_time_ms': 0,
                'created_at': datetime.utcnow().isoformat() + 'Z'
            }
    
    async def _load_persona_data(self, personas: List[Any]) -> List[Dict[str, Any]]:
        """Load full persona data from database if needed."""
        if not personas:
            return []
        
        # If personas are already full objects, return them
        if isinstance(personas[0], dict) and 'feedback_style' in personas[0]:
            return personas
        
        # Otherwise, load from database
        try:
            db = get_database()
            persona_ids = [UUID(p) if isinstance(p, str) else p for p in personas]
            
            query = """
                SELECT id, feedback_style, effectiveness_score
                FROM personas 
                WHERE id = ANY($1) AND is_active = TRUE
            """
            
            async with db.get_connection() as conn:
                rows = await conn.fetch(query, persona_ids)
            
            return [dict(row) for row in rows]
            
        except Exception as e:
            logger.error("Error loading persona data", error=str(e))
            raise ValueError(f"Failed to load persona data: {str(e)}")
    
    async def _generate_single_feedback(
        self, 
        text: str, 
        persona: Dict[str, Any], 
        context: Dict[str, Any],
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate feedback from a single persona."""
        
        # Build persona context
        persona_context = self._build_persona_context(persona)
        
        # Build prompt
        prompt = self._build_single_feedback_prompt(text, persona_context, context, options)
        
        # Generate feedback using LLM
        try:
            response = await self.llm_service.get_completion(
                agent_type='persona',
                prompt=prompt,
                context={
                    'max_tokens': 800,
                    'temperature': 0.3,
                    'model_preference': 'gpt-4.1-full'
                }
            )
            
            # Parse response
            feedback_data = self._parse_feedback_response(response.content, persona)
            
            # Add metadata
            feedback_data.update({
                'persona_id': str(persona['id']),
                'persona_name': persona['name'],
                'persona_type': persona['type'],
                'model_used': response.model,
                'tokens_used': response.tokens_used,
                'confidence': min(0.95, max(0.60, response.confidence or 0.85))
            })
            
            return {
                'feedback': feedback_data,
                'analysis_type': 'single'
            }
            
        except Exception as e:
            logger.error("Error generating single persona feedback", error=str(e))
            raise
    
    async def _generate_multi_feedback(
        self, 
        text: str, 
        personas: List[Dict[str, Any]], 
        context: Dict[str, Any],
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate feedback from multiple personas in parallel."""
        
        # Generate individual feedback in parallel
        feedback_tasks = [
            self._generate_single_feedback(text, persona, context, options)
            for persona in personas
        ]
        
        feedback_results = await asyncio.gather(*feedback_tasks, return_exceptions=True)
        
        # Process results and handle any exceptions
        successful_feedback = []
        failed_personas = []
        
        for i, result in enumerate(feedback_results):
            if isinstance(result, Exception):
                logger.error("Failed to generate feedback for persona",
                           persona_id=str(personas[i]['id']),
                           error=str(result))
                failed_personas.append({
                    'persona_id': str(personas[i]['id']),
                    'persona_name': personas[i]['name'],
                    'error': str(result)
                })
            else:
                successful_feedback.append(result['feedback'])
        
        if not successful_feedback:
            raise ValueError("Failed to generate feedback from any persona")
        
        # Generate cross-audience analysis if enabled
        cross_analysis = None
        include_cross_analysis = options.get('include_cross_analysis', True)
        
        if include_cross_analysis and len(successful_feedback) > 1:
            cross_analysis = await self._generate_cross_audience_analysis(
                text, successful_feedback, personas, context
            )
        
        return {
            'session_id': str(uuid4()),
            'feedback': successful_feedback,
            'personas_analyzed': len(successful_feedback),
            'failed_personas': failed_personas,
            'cross_audience_analysis': cross_analysis,
            'analysis_type': 'multi'
        }
    
    async def _generate_cross_audience_analysis(
        self,
        text: str,
        feedback_results: List[Dict[str, Any]],
        personas: List[Dict[str, Any]],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate cross-audience analysis and conflict detection."""
        
        # Build cross-analysis prompt
        prompt = self._build_cross_analysis_prompt(text, feedback_results, personas, context)
        
        try:
            response = await self.llm_service.get_completion(
                agent_type='persona',
                prompt=prompt,
                context={
                    'max_tokens': 600,
                    'temperature': 0.2,
                    'model_preference': 'gpt-4.1-full'
                }
            )
            
            # Parse cross-analysis response
            analysis = self._parse_cross_analysis_response(response.content, feedback_results)
            
            # Add metadata
            analysis.update({
                'model_used': response.model,
                'tokens_used': response.tokens_used,
                'confidence': response.confidence or 0.85
            })
            
            return analysis
            
        except Exception as e:
            logger.error("Error generating cross-audience analysis", error=str(e))
            # Return minimal analysis on error
            return {
                'overall_appeal_score': sum(f['overall_score'] for f in feedback_results) / len(feedback_results),
                'consistency_score': 0.8,
                'conflicts': [],
                'optimization_suggestions': [],
                'error': str(e)
            }
    
    def _build_persona_context(self, persona: Dict[str, Any]) -> str:
        """Build persona context string for prompts."""
        context_parts = [
            f"Persona ID: {persona['id']}"
        ]
        
        if persona.get('feedback_style'):
            style = persona['feedback_style']
            style_str = ', '.join([f"{k}: {v}" for k, v in style.items() if v])
            if style_str:
                context_parts.append(f"Feedback Style: {style_str}")
        
        return '\n'.join(context_parts)
    
    def _build_single_feedback_prompt(
        self, 
        text: str, 
        persona_context: str, 
        context: Dict[str, Any],
        options: Dict[str, Any]
    ) -> str:
        """Build prompt for single persona feedback."""
        
        analysis_depth = options.get('analysis_type', 'standard')
        
        prompt = f"""You are providing feedback on the following text from the perspective of this reader persona:

{persona_context}

Your task is to analyze the text and provide detailed, constructive feedback as this persona would. Consider how well the content would resonate with this specific reader type.

TEXT TO ANALYZE:
{text}

Provide your feedback in this JSON format:
{{
    "comprehension_score": 0.85,
    "engagement_score": 0.90,
    "emotional_score": 0.82,
    "style_score": 0.88,
    "overall_score": 0.86,
    "feedback_text": "Detailed feedback from persona perspective...",
    "specific_issues": ["Issue 1", "Issue 2"],
    "suggestions": ["Suggestion 1", "Suggestion 2", "Suggestion 3"]
}}

Scoring Guidelines:
- comprehension_score: How easy is it for this persona to understand? (0.0-1.0)
- engagement_score: How engaging/interesting is it for this persona? (0.0-1.0)
- emotional_score: How well does it connect emotionally with this persona? (0.0-1.0)
- style_score: How well does the writing style match this persona's preferences? (0.0-1.0)
- overall_score: Overall effectiveness for this persona (0.0-1.0)

Provide thoughtful, specific feedback that this persona would actually give. Consider their expertise level, preferences, and communication style."""

        return prompt
    
    def _build_cross_analysis_prompt(
        self,
        text: str,
        feedback_results: List[Dict[str, Any]],
        personas: List[Dict[str, Any]],
        context: Dict[str, Any]
    ) -> str:
        """Build prompt for cross-audience analysis."""
        
        # Summarize individual feedback
        feedback_summary = []
        for i, feedback in enumerate(feedback_results):
            persona_name = feedback.get('persona_name', f"Persona {i+1}")
            feedback_summary.append(
                f"{persona_name}: Overall {feedback['overall_score']:.2f}, "
                f"Key concerns: {', '.join(feedback.get('specific_issues', [])[:2])}"
            )
        
        feedback_text = '\n'.join(feedback_summary)
        
        prompt = f"""You are analyzing how well content performs across different reader personas. Here's the feedback from multiple personas:

INDIVIDUAL FEEDBACK SUMMARY:
{feedback_text}

ORIGINAL TEXT:
{text}

Analyze the cross-audience appeal and identify any conflicts between different persona needs. Provide your analysis in this JSON format:

{{
    "overall_appeal_score": 0.85,
    "consistency_score": 0.80,
    "conflicts": [
        {{
            "type": "tone_mismatch",
            "description": "Professional personas prefer formal tone while creative personas prefer casual",
            "affected_personas": ["Professional Reader", "Creative Writer"],
            "severity": "medium"
        }}
    ],
    "optimization_suggestions": [
        {{
            "suggestion": "Add audience-specific sections",
            "impact": "Addresses different expertise levels",
            "priority": "high"
        }}
    ],
    "audience_alignment": {{
        "primary_audience": "mixed",
        "compatibility_score": 0.85
    }}
}}

Focus on identifying real conflicts between persona needs and practical suggestions for improvement."""

        return prompt
    
    def _parse_feedback_response(self, response: str, persona: Dict[str, Any]) -> Dict[str, Any]:
        """Parse LLM feedback response into structured data."""
        try:
            # Try to parse as JSON first
            if response.strip().startswith('{'):
                data = json.loads(response.strip())
                
                # Validate required fields
                required_fields = ['comprehension_score', 'engagement_score', 'emotional_score', 
                                 'style_score', 'overall_score', 'feedback_text']
                
                for field in required_fields:
                    if field not in data:
                        raise ValueError(f"Missing required field: {field}")
                
                # Ensure scores are in valid range
                for score_field in ['comprehension_score', 'engagement_score', 'emotional_score', 
                                  'style_score', 'overall_score']:
                    if score_field in data:
                        data[score_field] = max(0.0, min(1.0, float(data[score_field])))
                
                # Ensure arrays exist
                data.setdefault('specific_issues', [])
                data.setdefault('suggestions', [])
                
                return data
            
            else:
                # Fallback parsing for non-JSON responses
                logger.warning("Non-JSON response from persona feedback, using fallback parsing")
                return self._create_fallback_feedback(response, persona)
                
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            logger.warning("Error parsing persona feedback response", error=str(e))
            return self._create_fallback_feedback(response, persona)
    
    def _parse_cross_analysis_response(self, response: str, feedback_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Parse cross-audience analysis response."""
        try:
            if response.strip().startswith('{'):
                data = json.loads(response.strip())
                
                # Validate and set defaults
                data.setdefault('overall_appeal_score', 
                               sum(f['overall_score'] for f in feedback_results) / len(feedback_results))
                data.setdefault('consistency_score', 0.8)
                data.setdefault('conflicts', [])
                data.setdefault('optimization_suggestions', [])
                data.setdefault('audience_alignment', {'primary_audience': 'mixed', 'compatibility_score': 0.8})
                
                return data
            
            else:
                # Fallback for non-JSON
                return {
                    'overall_appeal_score': sum(f['overall_score'] for f in feedback_results) / len(feedback_results),
                    'consistency_score': 0.8,
                    'conflicts': [],
                    'optimization_suggestions': [],
                    'audience_alignment': {'primary_audience': 'mixed', 'compatibility_score': 0.8}
                }
                
        except (json.JSONDecodeError, ValueError) as e:
            logger.warning("Error parsing cross-analysis response", error=str(e))
            return {
                'overall_appeal_score': sum(f['overall_score'] for f in feedback_results) / len(feedback_results),
                'consistency_score': 0.8,
                'conflicts': [],
                'optimization_suggestions': [],
                'audience_alignment': {'primary_audience': 'mixed', 'compatibility_score': 0.8}
            }
    
    def _create_fallback_feedback(self, response: str, persona: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback feedback when parsing fails."""
        # Generate reasonable default scores
        base_score = 0.75
        
        return {
            'comprehension_score': base_score,
            'engagement_score': base_score,
            'emotional_score': base_score,
            'style_score': base_score,
            'overall_score': base_score,
            'feedback_text': "This content shows good potential for general readers. " + 
                           (response[:200] + "..." if len(response) > 200 else response),
            'specific_issues': ["Response parsing failed - manual review recommended"],
            'suggestions': ["Consider revising based on persona preferences", "Review content clarity and engagement"]
        }