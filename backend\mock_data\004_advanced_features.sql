-- Revisionary Mock Data - Advanced Features
-- Part 4: Personas, custom agents, style rules, and usage tracking
-- Depends on 001_core_data.sql for user and document references

DO $$
DECLARE
    -- Get user IDs from firebase_uid (from 001_core_data.sql)
    sarah_id UUID;
    alex_id UUID;
    kim_id UUID;
    maya_id UUID;
    james_id UUID;
    
    -- Get document IDs by title (from 001_core_data.sql)
    sarah_climate_doc_id UUID;
    alex_cafe_doc_id UUID;
    kim_neural_doc_id UUID;
    maya_chap1_doc_id UUID;
    maya_world_doc_id UUID;
    james_q4_doc_id UUID;
    
    -- For capturing new agent IDs
    medical_agent_id UUID;
    structure_agent_id UUID;
    worldbuilding_agent_id UUID;
    dialogue_agent_id UUID;
    executive_agent_id UUID;
    data_agent_id UUID;
    climate_agent_id UUID;
    atmosphere_agent_id UUID;
    
BEGIN
    -- Get user IDs from existing users
    SELECT id INTO sarah_id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1;
    SELECT id INTO alex_id FROM users WHERE firebase_uid = 'firebase_user_002' LIMIT 1;
    SELECT id INTO kim_id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1;
    SELECT id INTO maya_id FROM users WHERE firebase_uid = 'firebase_user_004' LIMIT 1;
    SELECT id INTO james_id FROM users WHERE firebase_uid = 'firebase_user_005' LIMIT 1;
    
    -- Get document IDs from existing documents
    SELECT id INTO sarah_climate_doc_id FROM documents WHERE title = 'Climate Change Research Paper' LIMIT 1;
    SELECT id INTO alex_cafe_doc_id FROM documents WHERE title = 'The Midnight Café' LIMIT 1;
    SELECT id INTO kim_neural_doc_id FROM documents WHERE title = 'Neural Networks in Medical Diagnosis' LIMIT 1;
    SELECT id INTO maya_chap1_doc_id FROM documents WHERE title = 'Chronicles of Aetheria: Chapter 1' LIMIT 1;
    SELECT id INTO maya_world_doc_id FROM documents WHERE title = 'World Building Bible' LIMIT 1;
    SELECT id INTO james_q4_doc_id FROM documents WHERE title = 'Q4 Marketing Strategy Report' LIMIT 1;

    -- =================================================================
    -- PERSONAS - Writing personas for different audiences and contexts
    -- =================================================================
    
    -- Dr. Kim's academic personas
    INSERT INTO personas (user_id, name, description, type, demographics, reading_preferences, personality_traits, feedback_style, is_active, is_template, usage_count, effectiveness_score) 
    VALUES
    (kim_id, 'Medical Research Scholar', 'Formal academic persona for peer-reviewed medical research papers', 'academic', 
     '{"profession": "medical researcher", "education": "PhD", "experience": "15+ years", "specialization": "AI in healthcare"}'::jsonb,
     '{"format": "peer-reviewed journals", "length": "detailed comprehensive", "style": "formal academic", "citations": "IEEE preferred"}'::jsonb,
     '{"analytical": true, "detail_oriented": true, "methodical": true, "objective": true, "precision_focused": true}'::jsonb,
     '{"tone": "constructive", "focus": "accuracy and methodology", "style": "detailed explanations", "criticism_approach": "evidence-based"}'::jsonb,
     true, false, 15, 0.94);
    
    INSERT INTO personas (user_id, name, description, type, demographics, reading_preferences, personality_traits, feedback_style, is_active, usage_count, effectiveness_score)
    VALUES
    (kim_id, 'Conference Presenter', 'Engaging academic persona for conference presentations and public speaking', 'professional', 
     '{"profession": "academic presenter", "audience": "medical professionals", "setting": "conferences", "experience": "senior level"}'::jsonb,
     '{"format": "presentations", "length": "concise impactful", "style": "engaging professional", "visual_aids": "preferred"}'::jsonb,
     '{"confident": true, "engaging": true, "authoritative": true, "accessible": true, "inspiring": true}'::jsonb,
     '{"tone": "encouraging", "focus": "clarity and impact", "style": "practical suggestions", "engagement": "interactive"}'::jsonb,
     true, 8, 0.87);
    
    -- Maya's creative personas
    INSERT INTO personas (user_id, name, description, type, demographics, reading_preferences, personality_traits, feedback_style, is_active, is_template, usage_count, effectiveness_score)
    VALUES
    (maya_id, 'Epic Fantasy Narrator', 'Immersive storytelling persona for fantasy novels with rich world-building', 'creative', 
     '{"age_range": "25-45", "education": "varies", "interests": "fantasy literature", "reading_level": "advanced", "genre_familiarity": "high"}'::jsonb,
     '{"genre": "epic fantasy", "length": "full novels", "complexity": "high", "world_building": "detailed", "character_development": "deep"}'::jsonb,
     '{"imaginative": true, "detail_oriented": true, "patient": true, "emotionally_engaged": true, "analytical_about_craft": true}'::jsonb,
     '{"tone": "enthusiastic", "focus": "world-building and character consistency", "style": "detailed creative guidance", "encouragement": "high"}'::jsonb,
     true, true, 42, 0.92);
    
    INSERT INTO personas (user_id, name, description, type, demographics, reading_preferences, personality_traits, feedback_style, is_active, usage_count, effectiveness_score)
    VALUES
    (maya_id, 'Writing Mentor', 'Supportive and instructional persona for writing advice and tutorial content', 'personal', 
     '{"age_range": "18-35", "education": "high school to college", "experience": "beginner to intermediate", "goals": "skill improvement"}'::jsonb,
     '{"format": "tutorials and guides", "length": "accessible chunks", "style": "instructional", "examples": "practical"}'::jsonb,
     '{"eager_to_learn": true, "appreciates_encouragement": true, "needs_practical_advice": true, "values_examples": true}'::jsonb,
     '{"tone": "encouraging and supportive", "focus": "practical skills and techniques", "style": "step-by-step guidance", "patience": "high"}'::jsonb,
     true, 12, 0.89);
    
    -- James's business personas
    INSERT INTO personas (user_id, name, description, type, demographics, reading_preferences, personality_traits, feedback_style, is_active, is_template, usage_count, effectiveness_score)
    VALUES
    (james_id, 'Executive Strategist', 'Authoritative business persona for C-suite communications and strategic documents', 'professional', 
     '{"position": "C-suite executives", "industry": "technology/business", "education": "MBA preferred", "experience": "senior leadership"}'::jsonb,
     '{"format": "executive summaries", "length": "concise strategic", "data": "metrics-driven", "presentation": "professional"}'::jsonb,
     '{"results_oriented": true, "time_conscious": true, "strategic_thinking": true, "decision_focused": true, "ROI_aware": true}'::jsonb,
     '{"tone": "direct and authoritative", "focus": "strategic implications and ROI", "style": "action-oriented recommendations", "efficiency": "high"}'::jsonb,
     true, true, 28, 0.95);
    
    INSERT INTO personas (user_id, name, description, type, demographics, reading_preferences, personality_traits, feedback_style, is_active, usage_count, effectiveness_score)
    VALUES
    (james_id, 'Team Communicator', 'Collaborative and clear persona for internal team communications and guidelines', 'professional', 
     '{"position": "team members", "departments": "various", "education": "college level", "work_style": "collaborative"}'::jsonb,
     '{"format": "guidelines and communications", "length": "clear and comprehensive", "style": "accessible professional", "actionability": "high"}'::jsonb,
     '{"team_oriented": true, "values_clarity": true, "appreciates_support": true, "needs_actionable_guidance": true}'::jsonb,
     '{"tone": "supportive and clear", "focus": "team effectiveness and clarity", "style": "practical guidance", "collaboration": "emphasized"}'::jsonb,
     true, 19, 0.91);
    
    -- Sarah's academic personas
    INSERT INTO personas (user_id, name, description, type, demographics, reading_preferences, personality_traits, feedback_style, is_active, is_template, usage_count, effectiveness_score)
    VALUES
    (sarah_id, 'Environmental Researcher', 'Focused academic persona for climate change and environmental research', 'academic', 
     '{"profession": "environmental scientists", "audience": "policy makers", "education": "graduate level", "urgency": "climate action"}'::jsonb,
     '{"format": "research papers and policy briefs", "length": "evidence-based", "style": "academic with policy relevance", "urgency": "appropriate"}'::jsonb,
     '{"evidence_focused": true, "solution_oriented": true, "urgency_aware": true, "policy_conscious": true, "collaborative": true}'::jsonb,
     '{"tone": "urgent but professional", "focus": "evidence and policy implications", "style": "actionable recommendations", "urgency": "balanced"}'::jsonb,
     true, true, 6, 0.88);
    
    -- Alex's creative personas
    INSERT INTO personas (user_id, name, description, type, demographics, reading_preferences, personality_traits, feedback_style, is_active, is_template, usage_count, effectiveness_score)
    VALUES
    (alex_id, 'Urban Fantasy Storyteller', 'Atmospheric persona for modern fantasy set in contemporary urban environments', 'creative', 
     '{"age_range": "20-40", "interests": "urban fantasy", "reading_level": "intermediate to advanced", "genre_familiarity": "contemporary fantasy"}'::jsonb,
     '{"genre": "urban fantasy", "setting": "contemporary", "length": "short stories to novels", "atmosphere": "mysterious and immersive"}'::jsonb,
     '{"atmosphere_focused": true, "mystery_appreciating": true, "contemporary_aware": true, "emotionally_engaged": true, "detail_oriented": true}'::jsonb,
     '{"tone": "encouraging and atmospheric", "focus": "mood and mystery", "style": "immersive feedback", "creativity": "supported"}'::jsonb,
     true, true, 18, 0.90);

    -- =================================================================
    -- CUSTOM AGENTS - User-configured AI writing assistants
    -- =================================================================
    
    -- Dr. Kim's specialized agents
    INSERT INTO custom_agents (user_id, name, description, type, capabilities, priority, is_active, is_template, model_preference, max_context_tokens, temperature, execution_timeout) 
    VALUES
    (kim_id, 'Medical Terminology Validator', 'Specialized agent for medical terminology accuracy and consistency', 'technical', 
     '{"edit", "add", "consistency_check", "fact_check"}'::agent_capability_enum[], 70, true, false, 'gpt-4', 8000, 0.2, 45000)
    RETURNING id INTO medical_agent_id;
    
    INSERT INTO custom_agents (user_id, name, description, type, capabilities, priority, is_active, model_preference, max_context_tokens, temperature, execution_timeout)
    VALUES
    (kim_id, 'Research Structure Optimizer', 'Agent focused on improving academic paper structure and logical flow', 'structure', 
     '{"restructure", "add", "consistency_check", "readability_optimization"}'::agent_capability_enum[], 60, true, 'gpt-4', 6000, 0.3, 40000)
    RETURNING id INTO structure_agent_id;
    
    -- Maya's creative writing agents
    INSERT INTO custom_agents (user_id, name, description, type, capabilities, priority, is_active, is_template, model_preference, max_context_tokens, temperature, execution_timeout)
    VALUES
    (maya_id, 'World-Building Consistency Checker', 'Agent specialized in maintaining consistency across fantasy world elements', 'content', 
     '{"consistency_check", "find_duplicates", "fact_check", "expand"}'::agent_capability_enum[], 80, true, true, 'gpt-4', 10000, 0.4, 50000)
    RETURNING id INTO worldbuilding_agent_id;
    
    INSERT INTO custom_agents (user_id, name, description, type, capabilities, priority, is_active, model_preference, max_context_tokens, temperature, execution_timeout)
    VALUES
    (maya_id, 'Dialogue Enhancement Specialist', 'Agent focused on improving character dialogue and voice consistency', 'style', 
     '{"edit", "tone_adjustment", "voice_consistency"}'::agent_capability_enum[], 65, true, 'gpt-4', 7000, 0.5, 35000)
    RETURNING id INTO dialogue_agent_id;
    
    -- James's business agents
    INSERT INTO custom_agents (user_id, name, description, type, capabilities, priority, is_active, is_template, model_preference, max_context_tokens, temperature, execution_timeout)
    VALUES
    (james_id, 'Executive Summary Generator', 'Agent specialized in creating compelling executive summaries and strategic communications', 'content', 
     '{"condense", "add", "restructure", "tone_adjustment"}'::agent_capability_enum[], 85, true, true, 'gpt-4', 8000, 0.25, 30000)
    RETURNING id INTO executive_agent_id;
    
    INSERT INTO custom_agents (user_id, name, description, type, capabilities, priority, is_active, model_preference, max_context_tokens, temperature, execution_timeout)
    VALUES
    (james_id, 'Data Storytelling Assistant', 'Agent focused on transforming data and metrics into compelling business narratives', 'content', 
     '{"expand", "add", "restructure", "readability_optimization"}'::agent_capability_enum[], 70, true, 'gpt-4', 9000, 0.35, 40000)
    RETURNING id INTO data_agent_id;
    
    -- Sarah's environmental research agents
    INSERT INTO custom_agents (user_id, name, description, type, capabilities, priority, is_active, model_preference, max_context_tokens, temperature, execution_timeout)
    VALUES
    (sarah_id, 'Climate Data Interpreter', 'Agent specialized in climate science communication and data interpretation', 'research', 
     '{"fact_check", "citation_validation", "add", "readability_optimization"}'::agent_capability_enum[], 75, true, 'gpt-4', 7000, 0.2, 35000)
    RETURNING id INTO climate_agent_id;
    
    -- Alex's creative agents
    INSERT INTO custom_agents (user_id, name, description, type, capabilities, priority, is_active, model_preference, max_context_tokens, temperature, execution_timeout)
    VALUES
    (alex_id, 'Atmospheric Enhancement Agent', 'Agent specialized in enhancing atmospheric descriptions and mood in creative writing', 'creative', 
     '{"edit", "expand", "tone_adjustment", "add"}'::agent_capability_enum[], 55, true, 'gpt-4', 6000, 0.6, 30000)
    RETURNING id INTO atmosphere_agent_id;

    -- =================================================================
    -- AGENT STYLE RULES - Specific grammar and style preferences for agents
    -- =================================================================
    
    -- Medical Terminology Validator rules
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority) 
    VALUES
    (medical_agent_id, 'grammar', 'Medical Abbreviation Format', 
     '{"pattern": "AI", "replacement": "artificial intelligence (AI)", "context": "first_use_expansion", "severity": "medium"}'::jsonb, 
     true, 60);
    
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (medical_agent_id, 'vocabulary', 'Neural Network Precision', 
     '{"pattern": "neural networks", "replacement": "convolutional neural networks (CNNs)", "context": "specificity_required", "severity": "high"}'::jsonb, 
     true, 80);
    
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (medical_agent_id, 'style_preferences', 'Passive Voice in Methods', 
     '{"pattern": "was analyzed", "replacement": "we analyzed", "context": "methodology_sections", "severity": "low"}'::jsonb, 
     true, 30);
    
    -- Research Structure Optimizer rules  
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (structure_agent_id, 'sentence_structure', 'Transition Sentence Enhancement', 
     '{"pattern": "The results show", "replacement": "Building on these findings, the results demonstrate", "context": "section_transitions", "severity": "medium"}'::jsonb, 
     true, 70);
    
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (structure_agent_id, 'formatting', 'Section Header Consistency', 
     '{"pattern": "Results and Discussion", "style": "title_case", "consistency": "document_wide", "severity": "low"}'::jsonb, 
     true, 40);
    
    -- World-Building Consistency Checker rules
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (worldbuilding_agent_id, 'vocabulary', 'Magic System Terminology', 
     '{"pattern": "magical energy", "replacement": "ethereal essence", "context": "world_building", "severity": "high"}'::jsonb, 
     true, 90);
    
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (worldbuilding_agent_id, 'vocabulary', 'Character Title Consistency', 
     '{"pattern": "Princess Lyralei", "replacement": "Princess Lyralei of Aetheria", "context": "character_references", "severity": "medium"}'::jsonb, 
     true, 75);
    
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (worldbuilding_agent_id, 'vocabulary', 'Geographic Consistency', 
     '{"pattern": "the seven realms", "replacement": "the Seven Realms of Aetheria", "context": "world_geography", "severity": "medium"}'::jsonb, 
     true, 65);
    
    -- Dialogue Enhancement Specialist rules
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (dialogue_agent_id, 'style_preferences', 'Character Voice Distinction', 
     '{"pattern": "\"The magic is strong,\"", "replacement": "\"The ethereal essence flows powerfully through this place,\"", "context": "character_dialogue", "severity": "high"}'::jsonb, 
     true, 85);
    
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (dialogue_agent_id, 'punctuation', 'Dialogue Tag Variation', 
     '{"pattern": "she said", "alternatives": ["she whispered", "she murmured", "she declared"], "context": "dialogue_tags", "severity": "low"}'::jsonb, 
     true, 35);
    
    -- Executive Summary Generator rules
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (executive_agent_id, 'tone', 'Executive Authority', 
     '{"pattern": "we think", "replacement": "our analysis indicates", "context": "executive_communication", "severity": "high"}'::jsonb, 
     true, 90);
    
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (executive_agent_id, 'formatting', 'Metric Presentation', 
     '{"pattern": "147%", "replacement": "147% year-over-year growth", "context": "metrics", "severity": "medium"}'::jsonb, 
     true, 70);
    
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (executive_agent_id, 'tone', 'Action-Oriented Language', 
     '{"pattern": "consider implementing", "replacement": "implement immediately", "context": "recommendations", "severity": "medium"}'::jsonb, 
     true, 75);
    
    -- Data Storytelling Assistant rules
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (data_agent_id, 'style_preferences', 'Data Context Enhancement', 
     '{"pattern": "23% reduction", "replacement": "23% reduction in customer acquisition costs", "context": "metrics", "severity": "high"}'::jsonb, 
     true, 85);
    
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (data_agent_id, 'style_preferences', 'Story Arc Structure', 
     '{"pattern": "The data shows", "replacement": "This performance transformation demonstrates", "context": "data_narrative", "severity": "medium"}'::jsonb, 
     true, 60);
    
    -- Climate Data Interpreter rules
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (climate_agent_id, 'vocabulary', 'Climate Data Sourcing', 
     '{"pattern": "sea levels are rising", "replacement": "sea levels are rising at 3.3mm per year (NOAA, 2023)", "context": "climate_data", "severity": "high"}'::jsonb, 
     true, 95);
    
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (climate_agent_id, 'tone', 'Urgency Balance', 
     '{"pattern": "climate change may affect", "replacement": "climate change will significantly impact", "context": "climate_urgency", "severity": "medium"}'::jsonb, 
     true, 70);
    
    -- Atmospheric Enhancement Agent rules
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (atmosphere_agent_id, 'style_preferences', 'Sensory Detail Enhancement', 
     '{"pattern": "the café glowed", "replacement": "the café glowed with warm amber light, its windows humming with ethereal energy", "context": "atmospheric_description", "severity": "medium"}'::jsonb, 
     true, 65);
    
    INSERT INTO agent_style_rules (agent_id, category, rule_name, rule_value, is_enabled, priority)
    VALUES
    (atmosphere_agent_id, 'style_preferences', 'Mystery Preservation', 
     '{"pattern": "appeared suddenly", "replacement": "materialized as if conjured from the liminal space between dreams and reality", "context": "mystery_elements", "severity": "high"}'::jsonb, 
     true, 80);

    -- =================================================================
    -- USAGE EVENTS - User interaction tracking across the platform
    -- =================================================================
    
    -- Dr. Kim's recent activity (professional user - high engagement)
    INSERT INTO usage_events (user_id, document_id, event_type, event_data, tokens_used, model_used, processing_time_ms) 
    VALUES
    (kim_id, kim_neural_doc_id, 'document_created', 
     '{"document_type": "academic", "initial_word_count": 0, "template_used": false}'::jsonb, 
     0, NULL, NULL);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data, tokens_used, model_used, processing_time_ms)
    VALUES
    (kim_id, kim_neural_doc_id, 'ai_suggestion_generated', 
     '{"agent_type": "content", "suggestion_category": "clarity", "confidence": 0.89}'::jsonb, 
     68, 'gpt-4', 160);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data, tokens_used, model_used, processing_time_ms)
    VALUES
    (kim_id, kim_neural_doc_id, 'suggestion_accepted', 
     '{"improvement_type": "style", "user_satisfaction": "high", "suggestion_category": "dialogue"}'::jsonb, 
     0, NULL, NULL);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data, tokens_used, model_used, processing_time_ms)
    VALUES
    (kim_id, kim_neural_doc_id, 'document_scored', 
     '{"total_score": 94.0, "improvement": 2.5, "scoring_version": "1.2"}'::jsonb, 
     45, 'gpt-4', 850);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data, tokens_used, model_used, processing_time_ms)
    VALUES
    (kim_id, NULL, 'agent_configured', 
     ('{"agent_id": "' || medical_agent_id::text || '", "configuration_changes": ["terminology_precision", "citation_style"]}')::jsonb, 
     0, NULL, NULL);
    
    -- Maya's creative writing activity (professional user - creative focus)
    INSERT INTO usage_events (user_id, document_id, event_type, event_data)
    VALUES
    (maya_id, maya_chap1_doc_id, 'document_opened', 
     '{"session_duration_start": true, "document_type": "creative"}'::jsonb);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data)
    VALUES
    (maya_id, maya_chap1_doc_id, 'writing_session_started', 
     '{"target_word_count": 1000, "session_goal": "chapter_completion", "focus_mode": true}'::jsonb);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data, tokens_used, model_used, processing_time_ms)
    VALUES
    (maya_id, maya_chap1_doc_id, 'ai_suggestion_generated', 
     '{"agent_type": "structure", "confidence": 0.74, "suggestion_category": "pacing"}'::jsonb, 
     85, 'gpt-4', 200);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data)
    VALUES
    (maya_id, maya_chap1_doc_id, 'writing_session_ended', 
     '{"words_written": 847, "session_duration": 3600, "goal_completion": 0.85}'::jsonb);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data)
    VALUES
    (maya_id, maya_chap1_doc_id, 'persona_applied', 
     '{"persona_name": "Epic Fantasy Narrator", "document_section": "chapter_opening", "style_adjustments": 5}'::jsonb);
    
    -- James's business activity (studio user - collaborative work)
    INSERT INTO usage_events (user_id, document_id, event_type, event_data)
    VALUES
    (james_id, james_q4_doc_id, 'collaboration_invited', 
     '{"invitee_role": "editor", "collaboration_type": "business_review"}'::jsonb);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data)
    VALUES
    (james_id, james_q4_doc_id, 'document_exported', 
     '{"format": "pdf", "stakeholder_distribution": true, "export_type": "executive_summary"}'::jsonb);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data, tokens_used, model_used, processing_time_ms)
    VALUES
    (james_id, james_q4_doc_id, 'ai_agent_used', 
     '{"agent_name": "Executive Summary Generator", "task": "executive_summary_generation", "input_length": 3800, "output_quality": 0.94}'::jsonb, 
     320, 'gpt-4', 2100);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data)
    VALUES
    (james_id, NULL, 'achievement_earned', 
     '{"achievement_type": "professional_powerhouse", "level": "platinum", "document_count": 8}'::jsonb);
    
    -- Sarah's academic activity (free user - learning and improvement)
    INSERT INTO usage_events (user_id, document_id, event_type, event_data)
    VALUES
    (sarah_id, sarah_climate_doc_id, 'suggestion_feedback', 
     '{"feedback": "helpful", "learning_value": "high", "suggestion_category": "grammar"}'::jsonb);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data)
    VALUES
    (sarah_id, NULL, 'challenge_progress', 
     '{"challenge_type": "grammar_perfection", "progress_update": 0.6, "milestone_reached": "grammar_improvement"}'::jsonb);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data)
    VALUES
    (sarah_id, NULL, 'tutorial_completed', 
     '{"tutorial_type": "citation_formatting", "completion_time": 1200, "quiz_score": 0.85}'::jsonb);
    
    -- Alex's creative activity (free user - atmospheric writing focus)
    INSERT INTO usage_events (user_id, document_id, event_type, event_data)
    VALUES
    (alex_id, alex_cafe_doc_id, 'document_revision', 
     '{"revision_type": "style_enhancement", "sections_modified": 3, "focus": "atmosphere"}'::jsonb);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data, tokens_used, model_used, processing_time_ms)
    VALUES
    (alex_id, alex_cafe_doc_id, 'ai_suggestion_generated', 
     '{"agent_type": "style", "focus": "atmospheric_enhancement", "suggestion_category": "word_choice"}'::jsonb, 
     52, 'gpt-4', 140);
    
    INSERT INTO usage_events (user_id, document_id, event_type, event_data)
    VALUES
    (alex_id, alex_cafe_doc_id, 'comment_received', 
     '{"commenter": "Maya Thompson", "sentiment": "positive", "actionable": true, "comment_type": "general"}'::jsonb);

END $$;