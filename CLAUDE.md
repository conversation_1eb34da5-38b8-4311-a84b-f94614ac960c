# Revisionary Development Guidelines

## Core Principles

**ENTERPRISE READY**: No shortcuts. Write production-grade code with proper error handling, logging, and monitoring.

**SIMPLICITY FIRST**: Prefer simple solutions over complex architectures. Fewer moving parts = fewer bugs.

**DOCUMENTATION DRIVEN**: Always read the architecture docs before coding. If unclear, ask—never guess.

**NO HARDCODED DATA**: All secrets, URLs, model names go in environment variables or config files.

**FAIL FAST**: Explicit errors over silent failures. Make problems obvious immediately.

---

## Development Rules

### Before You Code
1. **Activate environment**: `conda activate revisionary` (our dedicated conda environment)
2. **Read the docs** in `/docs/` - they define the contract
3. **Check `/infra/models.yaml`** for AI model configurations  
4. **Follow existing patterns** - don't reinvent what's already working

### Code Structure
```
/backend/
  api/               # FastAPI routes
  workers/           # Agent containers  
  core/              # Shared utilities
  tests/             # ALL test scripts go here
/frontend/src/
/infra/
  models.yaml        # Single source of truth for LLM configs
/docs/               # Architecture specifications
```

### Configuration
- **Environment Variables**: Use `pydantic.BaseSettings` for all config
- **No Secrets in Code**: Everything goes in `.env` files or environment
- **Redis Keys**: Follow patterns in `backend-architecture.md`
- **Model Names**: Reference `/infra/models.yaml` only

### Testing Requirements
- **All tests** in `/tests/` directory matching package structure
- **Mock external services**: Redis, Supabase, OpenAI, Google AI
- **Clean up after tests**: No leftover data in Redis/DB
- **Use pytest fixtures** for reusable test components

### Code Quality
- **Type annotations** on all public functions
- **Async/await** for IO operations (DB, Redis, LLM calls)
- **Return structured data**: Use Pydantic models, not raw dicts
- **Error handling**: Specific exceptions with clear messages
- **Firebase optimization**: Minimize auth calls in React context - cache user state efficiently

---

## Anti-Patterns to Avoid

❌ **Don't**: Create microservices for single endpoints  
✅ **Do**: Add modules in `/api/` until load requires separation

❌ **Don't**: Abstract everything into "manager" classes  
✅ **Do**: Keep functions simple and direct

❌ **Don't**: Hardcode model names, API keys, URLs  
✅ **Do**: Use environment variables and config files

❌ **Don't**: Leave test data in Redis/DB after tests  
✅ **Do**: Clean up in test teardown

❌ **Don't**: Ignore the architecture docs  
✅ **Do**: Follow documented patterns and data flows

---

## Quick Checklist

Before any code change:
- [ ] Architecture docs reviewed for this feature
- [ ] No hardcoded secrets/URLs/model names
- [ ] Tests added and pass
- [ ] Redis/DB cleaned up after tests
- [ ] Following existing code patterns
- [ ] Simple solution chosen over complex one

**Remember**: This system prioritizes cost efficiency and simplicity. Every abstraction layer costs money and complexity.