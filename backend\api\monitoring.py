"""
Monitoring and Metrics API Endpoints

Purpose: Provides production monitoring endpoints for system health and metrics
- Health check endpoints for load balancers
- Performance metrics for monitoring tools
- Error tracking and alerting
- System status dashboard data

Features:
- Health status with detailed service checks
- Performance metrics aggregation
- Error rate monitoring
- LLM usage tracking
- Citation processing metrics
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import structlog

from api.auth import get_current_user
from core.auth import UserClaims
from core.database import get_database
from core.redis_service import get_redis
from core.logging import (
    metrics_collector,
    get_system_metrics,
    get_error_summary,
    log_business_event
)
from core.settings import settings

# Configure logging
logger = structlog.get_logger(__name__)

# Create router
router = APIRouter()


@router.get("/health")
async def health_check():
    """
    Comprehensive health check endpoint for load balancers and monitoring.
    
    Returns:
        Dict: System health status with service checks
    """
    try:
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "environment": settings.env,
            "services": {}
        }
        
        # Check database health if not in mock mode
        if not settings.mock.use_mock_data:
            try:
                db = await get_database()
                db_health = await db.health_check()
                health_status["services"]["database"] = db_health
                
                if not all(db_health.values()):
                    health_status["status"] = "degraded"
                    
            except Exception as e:
                logger.error("Database health check failed", error=str(e))
                health_status["services"]["database"] = {"error": str(e)}
                health_status["status"] = "degraded"
        else:
            health_status["services"]["database"] = {"status": "mocked"}
        
        # Check Redis health if not in mock mode
        if not settings.mock.use_mock_redis:
            try:
                redis = await get_redis()
                redis_health = await redis.health_check()
                health_status["services"]["redis"] = redis_health
                
                if not all(value for key, value in redis_health.items() if key != "details"):
                    health_status["status"] = "degraded"
                    
            except Exception as e:
                logger.error("Redis health check failed", error=str(e))
                health_status["services"]["redis"] = {"error": str(e)}
                health_status["status"] = "degraded"
        else:
            health_status["services"]["redis"] = {"status": "mocked"}
        
        # Get application-level health from metrics
        app_health = metrics_collector.get_health_status()
        health_status["services"]["application"] = app_health
        
        if app_health["status"] != "healthy":
            health_status["status"] = app_health["status"]
        
        # Overall health status
        if health_status["status"] != "healthy":
            health_status["success"] = False
        else:
            health_status["success"] = True
        
        return health_status
        
    except Exception as e:
        logger.error("Health check failed", error=str(e), exc_info=True)
        return {
            "status": "unhealthy",
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@router.get("/health/ready")
async def readiness_check():
    """
    Readiness check for Kubernetes and container orchestration.
    
    Returns:
        Dict: Service readiness status
    """
    try:
        # Check if all critical services are ready
        ready = True
        services = {}
        
        # Check database readiness
        if not settings.mock.use_mock_data:
            try:
                db = await get_database()
                # Simple query to check if database is responsive
                await db.execute("SELECT 1")
                services["database"] = {"ready": True}
            except Exception as e:
                services["database"] = {"ready": False, "error": str(e)}
                ready = False
        else:
            services["database"] = {"ready": True, "mocked": True}
        
        # Check Redis readiness
        if not settings.mock.use_mock_redis:
            try:
                redis = await get_redis()
                await redis.ping()
                services["redis"] = {"ready": True}
            except Exception as e:
                services["redis"] = {"ready": False, "error": str(e)}
                ready = False
        else:
            services["redis"] = {"ready": True, "mocked": True}
        
        return {
            "ready": ready,
            "services": services,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        return {
            "ready": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@router.get("/health/live")
async def liveness_check():
    """
    Liveness check for Kubernetes and container orchestration.
    
    Returns:
        Dict: Basic application liveness status
    """
    return {
        "alive": True,
        "timestamp": datetime.now().isoformat(),
        "uptime": "N/A"  # Could be enhanced to track actual uptime
    }


@router.get("/metrics")
async def get_metrics(
    minutes: int = Query(15, ge=1, le=60, description="Time window in minutes")
):
    """
    Get system performance metrics for monitoring tools.
    
    Args:
        minutes: Time window for metrics aggregation
        
    Returns:
        Dict: Comprehensive system metrics
    """
    try:
        metrics = get_system_metrics()
        
        # Add time window information
        metrics["time_window_minutes"] = minutes
        
        # Get performance metrics for specified time window
        performance_metrics = metrics_collector.get_performance_metrics(minutes=minutes)
        metrics["performance"] = [m.to_dict() for m in performance_metrics]
        
        log_business_event("metrics_requested", {"time_window": minutes})
        
        return metrics
        
    except Exception as e:
        logger.error("Error getting metrics", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")


@router.get("/metrics/errors")
async def get_error_metrics():
    """
    Get error metrics and summaries for monitoring.
    
    Returns:
        Dict: Error statistics and summaries
    """
    try:
        error_summary = get_error_summary()
        
        log_business_event("error_metrics_requested", {
            "total_errors": error_summary["total_errors"]
        })
        
        return error_summary
        
    except Exception as e:
        logger.error("Error getting error metrics", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get error metrics: {str(e)}")


@router.get("/metrics/performance")
async def get_performance_metrics(
    minutes: int = Query(15, ge=1, le=60),
    endpoint: Optional[str] = Query(None, description="Filter by specific endpoint")
):
    """
    Get detailed performance metrics.
    
    Args:
        minutes: Time window for metrics
        endpoint: Optional endpoint filter
        
    Returns:
        Dict: Detailed performance metrics
    """
    try:
        performance_metrics = metrics_collector.get_performance_metrics(minutes=minutes)
        
        # Filter by endpoint if specified
        if endpoint:
            performance_metrics = [m for m in performance_metrics if endpoint in m.endpoint]
        
        return {
            "metrics": [m.to_dict() for m in performance_metrics],
            "time_window_minutes": minutes,
            "endpoint_filter": endpoint,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("Error getting performance metrics", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get performance metrics: {str(e)}")


@router.get("/status")
async def get_system_status():
    """
    Get comprehensive system status for dashboard.
    
    Returns:
        Dict: Complete system status information
    """
    try:
        # Get health status
        health = await health_check()
        
        # Get recent metrics
        metrics = get_system_metrics()
        
        # Get error summary
        errors = get_error_summary()
        
        # Get performance metrics
        performance_metrics = metrics_collector.get_performance_metrics(minutes=5)
        
        # Calculate summary statistics
        total_requests = sum(m.request_count for m in performance_metrics)
        avg_response_time = (
            sum(m.avg_response_time for m in performance_metrics) / len(performance_metrics)
            if performance_metrics else 0.0
        )
        
        return {
            "status": health["status"],
            "health": health,
            "summary": {
                "requests_last_5_minutes": total_requests,
                "avg_response_time_ms": round(avg_response_time, 2),
                "total_errors_last_hour": errors["total_errors"],
                "error_rate": errors["total_errors"] / total_requests if total_requests > 0 else 0.0
            },
            "services": health["services"],
            "environment": settings.env,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("Error getting system status", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get system status: {str(e)}")


@router.post("/events")
async def log_custom_event(
    event_data: Dict[str, Any],
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Log a custom business event for monitoring.
    
    Args:
        event_data: Event data to log
        current_user: Current authenticated user
        
    Returns:
        Dict: Event logging confirmation
    """
    try:
        event_type = event_data.get("type", "custom_event")
        details = event_data.get("details", {})
        
        log_business_event(
            event_type=event_type,
            details=details,
            user_id=current_user.user_id
        )
        
        return {
            "success": True,
            "message": "Event logged successfully",
            "event_type": event_type,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("Error logging custom event", error=str(e), user_id=current_user.user_id)
        raise HTTPException(status_code=500, detail=f"Failed to log event: {str(e)}")


# Prometheus-style metrics endpoint (for Prometheus/Grafana integration)
@router.get("/metrics/prometheus")
async def get_prometheus_metrics():
    """
    Get metrics in Prometheus format for monitoring integration.
    
    Returns:
        str: Metrics in Prometheus format
    """
    try:
        performance_metrics = metrics_collector.get_performance_metrics(minutes=5)
        health_status = metrics_collector.get_health_status()
        
        # Generate Prometheus-style metrics
        metrics_output = []
        
        # Health status metric
        health_value = 1 if health_status["status"] == "healthy" else 0
        metrics_output.append(f"revisionary_health_status {health_value}")
        
        # Request metrics
        for metric in performance_metrics:
            endpoint_label = metric.endpoint.replace(" ", "_").replace("/", "_").lower()
            metrics_output.append(f'revisionary_requests_total{{endpoint="{endpoint_label}"}} {metric.request_count}')
            metrics_output.append(f'revisionary_request_duration_ms{{endpoint="{endpoint_label}"}} {metric.avg_response_time}')
            metrics_output.append(f'revisionary_request_errors_total{{endpoint="{endpoint_label}"}} {metric.error_count}')
        
        # Overall metrics
        total_requests = sum(m.request_count for m in performance_metrics)
        total_errors = sum(m.error_count for m in performance_metrics)
        metrics_output.append(f"revisionary_requests_per_minute {total_requests}")
        metrics_output.append(f"revisionary_errors_per_minute {total_errors}")
        
        return "\n".join(metrics_output)
        
    except Exception as e:
        logger.error("Error generating Prometheus metrics", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to generate Prometheus metrics: {str(e)}")