"""
Document Management API Endpoints

Purpose: Provides REST API endpoints for document operations including:
- Creating, reading, updating, and deleting documents
- Document metadata and version management
- Document sharing and collaboration features
- Document organization and search capabilities

Responsibilities:
- Document CRUD operations via HTTP endpoints
- Document version control and history management
- Document sharing and permission management
- Document search and filtering capabilities
- File upload and import handling

Used by: Frontend document management UI, editor components
Dependencies: core.database, core.auth, document storage services
"""

from fastapi import APIRouter, HTTPException, Depends, Query, UploadFile, File
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime
import structlog

from api.auth import get_current_user
from core.auth import UserClaims

# Configure logging
logger = structlog.get_logger(__name__)

# Create router
router = APIRouter()

# Request/Response Models
class DocumentCreate(BaseModel):
    """Request model for creating a document."""
    title: str = Field(..., min_length=1, max_length=500)
    type: str = Field(..., description="Document type: creative, academic, professional, etc.")
    description: Optional[str] = Field(None, max_length=1000)
    content: Optional[str] = Field(default="", description="Initial document content")
    tags: List[str] = Field(default_factory=list)
    is_public: bool = Field(default=False)
    language: str = Field(default="en")

class DocumentUpdate(BaseModel):
    """Request model for updating a document."""
    title: Optional[str] = Field(None, min_length=1, max_length=500)
    description: Optional[str] = Field(None, max_length=1000)
    tags: Optional[List[str]] = None
    is_public: Optional[bool] = None
    status: Optional[str] = None

class DocumentResponse(BaseModel):
    """Response model for document data."""
    success: bool
    data: Dict[str, Any]

class DocumentListResponse(BaseModel):
    """Response model for document list."""
    success: bool
    data: List[Dict[str, Any]]
    meta: Dict[str, Any]

class CollaboratorResponse(BaseModel):
    """Response model for document collaborators."""
    success: bool
    data: List[Dict[str, Any]]

class Suggestion(BaseModel):
    """Response model for a single suggestion."""
    id: str
    block_id: str
    agent_type: str
    severity: str
    category: Optional[str] = None
    original_text: str
    suggested_text: str
    explanation: Optional[str] = None
    confidence: float
    position: dict
    original_hash: str
    status: str = "pending"
    user_feedback: Optional[str] = None
    feedback_note: Optional[str] = None
    tokens_used: int = 0
    model_used: Optional[str] = None
    processing_time_ms: Optional[int] = None
    created_at: str
    resolved_at: Optional[str] = None
    resolved_by: Optional[str] = None
    expires_at: Optional[str] = None

class SuggestionsResponse(BaseModel):
    """Response model for document suggestions."""
    success: bool
    data: List[Suggestion]

class Comment(BaseModel):
    """Response model for a single comment."""
    id: str
    block_id: str
    author_id: str
    author_name: str
    author_email: str
    parent_id: Optional[str] = None
    content: str
    position: Optional[dict] = None
    type: str = "general"
    status: str = "open"
    resolved_by: Optional[str] = None
    resolved_at: Optional[str] = None
    created_at: str
    updated_at: str
    deleted_at: Optional[str] = None

class CommentsResponse(BaseModel):
    """Response model for document comments."""
    success: bool
    data: List[Comment]

@router.get("/", response_model=DocumentListResponse)
async def list_user_documents(
    current_user: UserClaims = Depends(get_current_user),
    limit: int = Query(default=20, le=100),
    offset: int = Query(default=0, ge=0),
    type: Optional[str] = Query(default=None),
    status: Optional[str] = Query(default=None),
    search: Optional[str] = Query(default=None)
):
    """
    List all documents for the authenticated user.
    
    Args:
        current_user: Current authenticated user
        limit: Maximum number of documents to return
        offset: Number of documents to skip for pagination
        type: Filter by document type
        status: Filter by document status
        search: Search query for document titles/content
        
    Returns:
        DocumentListResponse: List of user's documents
    """
    try:
        logger.info(
            "Listing user documents",
            user_id=current_user.user_id,
            limit=limit,
            offset=offset,
            type=type,
            status=status,
            search=search
        )
        
        # Query documents from database
        from core.database import get_database
        db = await get_database()
        
        query = """
            SELECT 
                id, owner_id, title, type, status, description,
                word_count, character_count, version, tags, is_public,
                language, created_at, updated_at, published_at
            FROM documents 
            WHERE owner_id = $1 AND deleted_at IS NULL
        """
        params = [current_user.user_id]
        param_count = 2
        
        # Add filters
        if type:
            query += f" AND type = ${param_count}"
            params.append(type)
            param_count += 1
            
        if status:
            query += f" AND status = ${param_count}"
            params.append(status)
            param_count += 1
            
        if search:
            query += f" AND (title ILIKE ${param_count} OR description ILIKE ${param_count})"
            params.append(f"%{search}%")
            param_count += 1
        
        # Add ordering and pagination
        query += f" ORDER BY updated_at DESC LIMIT ${param_count} OFFSET ${param_count + 1}"
        params.extend([limit, offset])
        
        async with db.get_connection() as conn:
            rows = await conn.fetch(query, *params)
            documents = [dict(row) for row in rows]
        
        # Get total count for pagination
        count_query = """
            SELECT COUNT(*) FROM documents 
            WHERE owner_id = $1 AND deleted_at IS NULL
        """
        count_params = [current_user.user_id]
        
        if type:
            count_query += " AND type = $2"
            count_params.append(type)
        if status:
            param_idx = len(count_params) + 1
            count_query += f" AND status = ${param_idx}"
            count_params.append(status)
        if search:
            param_idx = len(count_params) + 1
            count_query += f" AND (title ILIKE ${param_idx} OR description ILIKE ${param_idx})"
            count_params.append(f"%{search}%")
            
        async with db.get_connection() as conn:
            total_documents = await conn.fetchval(count_query, *count_params) or 0
        
        return DocumentListResponse(
            success=True,
            data=documents,
            meta={
                "total": total_documents,
                "limit": limit,
                "offset": offset,
                "has_more": offset + limit < total_documents
            }
        )
        
    except Exception as e:
        logger.error("Failed to list documents", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve documents"
        )

@router.post("/", response_model=DocumentResponse)
async def create_document(
    document_data: DocumentCreate,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Create a new document for the authenticated user.
    
    Args:
        document_data: Document creation data
        current_user: Current authenticated user
        
    Returns:
        DocumentResponse: Created document data
    """
    try:
        logger.info(
            "Creating document",
            user_id=current_user.user_id,
            title=document_data.title,
            doc_type=document_data.type
        )
        
        # Create document in database
        from core.database import get_database
        db = await get_database()
        
        now = datetime.utcnow()
        
        insert_query = """
            INSERT INTO documents (
                owner_id, title, type, status, description, word_count,
                character_count, version, tags, is_public, language,
                is_template, metadata, settings,
                created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $15
            ) RETURNING *
        """
        
        word_count = len(document_data.content.split()) if document_data.content else 0
        character_count = len(document_data.content) if document_data.content else 0
        
        import json
        
        params = [
            current_user.user_id, document_data.title, document_data.type,
            "draft", document_data.description, word_count, character_count,
            1, document_data.tags, document_data.is_public, document_data.language,
            False, json.dumps({}), json.dumps({}),
            now
        ]
        
        async with db.get_connection() as conn:
            document_row = await conn.fetchrow(insert_query, *params)
            
        if not document_row:
            raise HTTPException(status_code=500, detail="Failed to create document in database")
        
        document = dict(document_row)
        
        # If there's initial content, create the first block
        if document_data.content:
            block_query = """
                INSERT INTO blocks (
                    document_id, content, type, position, created_at, updated_at
                ) VALUES (
                    $1, $2, 'paragraph', 0, $3, $3
                )
            """
            async with db.get_connection() as conn:
                await conn.execute(block_query, document['id'], document_data.content, now)
        
        logger.info("Document created successfully", document_id=document["id"])
        
        return DocumentResponse(
            success=True,
            data=document
        )
        
    except Exception as e:
        import traceback
        error_details = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "traceback": traceback.format_exc(),
            "user_id": current_user.user_id,
            "document_title": document_data.title if document_data else "unknown",
            "document_type": document_data.type if document_data else "unknown"
        }
        logger.error("Failed to create document - DETAILED ERROR", **error_details)
        print(f"DOCUMENTS ENDPOINT ERROR: {error_details}")  # Also print to console for debugging
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create document: {type(e).__name__}: {str(e)}"
        )

@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: str,
    current_user: UserClaims = Depends(get_current_user),
    include_content: bool = Query(default=True)
):
    """
    Get a specific document by ID.
    
    Args:
        document_id: Document ID to retrieve
        current_user: Current authenticated user
        include_content: Whether to include document content
        
    Returns:
        DocumentResponse: Document data
    """
    try:
        logger.info("Getting document", document_id=document_id, user_id=current_user.user_id)
        
        # Get document from database
        from core.database import get_database
        db = await get_database()
        
        query = """
            SELECT 
                id, owner_id, title, type, status, description,
                word_count, character_count, version, tags, is_public,
                language, created_at, updated_at, published_at
            FROM documents 
            WHERE id = $1 AND owner_id = $2 AND deleted_at IS NULL
        """
        
        async with db.get_connection() as conn:
            document_row = await conn.fetchrow(query, document_id, current_user.user_id)
        
        if not document_row:
            raise HTTPException(status_code=404, detail="Document not found")
        
        document_dict = dict(document_row)
        document_dict["last_opened"] = datetime.utcnow().isoformat() + "Z"
        
        if include_content:
            # Get document content from blocks
            blocks_query = """
                SELECT content FROM blocks 
                WHERE document_id = $1 
                ORDER BY position ASC
            """
            async with db.get_connection() as conn:
                blocks = await conn.fetch(blocks_query, document_id)
            content_parts = [block['content'] for block in blocks if block['content']]
            document_dict["content"] = "\n\n".join(content_parts)
        
        return DocumentResponse(
            success=True,
            data=document_dict
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get document", error=str(e), document_id=document_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve document"
        )

@router.put("/{document_id}", response_model=DocumentResponse)
async def update_document(
    document_id: str,
    document_updates: DocumentUpdate,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Update a document.
    
    Args:
        document_id: Document ID to update
        document_updates: Document update data
        current_user: Current authenticated user
        
    Returns:
        DocumentResponse: Updated document data
    """
    try:
        logger.info("Updating document", document_id=document_id, user_id=current_user.user_id)
        
        # Update document in database
        from core.database import get_database
        db = await get_database()
        
        # Build dynamic update query
        update_fields = []
        params = []
        param_index = 1
        
        update_data = document_updates.dict(exclude_unset=True)
        for field, value in update_data.items():
            if value is not None:
                update_fields.append(f"{field} = ${param_index}")
                params.append(value)
                param_index += 1
        
        if not update_fields:
            raise HTTPException(
                status_code=400,
                detail="No valid fields to update"
            )
        
        # Increment version and update timestamp
        update_fields.extend([f"version = version + 1", f"updated_at = ${param_index}"])
        params.append(datetime.utcnow())
        param_index += 1
        
        # Add WHERE conditions
        params.extend([document_id, current_user.user_id])
        
        query = f"""
            UPDATE documents 
            SET {', '.join(update_fields)}
            WHERE id = ${param_index - 1} AND owner_id = ${param_index} AND deleted_at IS NULL
            RETURNING *
        """
        
        document = await db.execute_query(query, params, fetch="one")
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        document_dict = dict(document)
        document_dict["last_opened"] = datetime.utcnow().isoformat() + "Z"
        
        logger.info("Document updated successfully", document_id=document_id)
        
        return DocumentResponse(
            success=True,
            data=document_dict
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update document", error=str(e), document_id=document_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to update document"
        )

@router.delete("/{document_id}")
async def delete_document(
    document_id: str,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Delete a document.
    
    Args:
        document_id: Document ID to delete
        current_user: Current authenticated user
        
    Returns:
        dict: Success response
    """
    try:
        logger.info("Deleting document", document_id=document_id, user_id=current_user.user_id)
        
        # Soft delete document in database
        from core.database import get_database
        db = await get_database()
        
        query = """
            UPDATE documents 
            SET deleted_at = $1, updated_at = $1
            WHERE id = $2 AND owner_id = $3 AND deleted_at IS NULL
            RETURNING id
        """
        
        result = await db.execute_query(query, [datetime.utcnow(), document_id, current_user.user_id], fetch="one")
        
        if not result:
            raise HTTPException(status_code=404, detail="Document not found")
        
        logger.info("Document deleted successfully", document_id=document_id)
        
        return {"success": True, "message": "Document deleted successfully"}
        
    except Exception as e:
        logger.error("Failed to delete document", error=str(e), document_id=document_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to delete document"
        )

@router.post("/{document_id}/duplicate", response_model=DocumentResponse)
async def duplicate_document(
    document_id: str,
    current_user: UserClaims = Depends(get_current_user),
    new_title: Optional[str] = Query(default=None)
):
    """
    Create a duplicate of an existing document.
    
    Args:
        document_id: Document ID to duplicate
        current_user: Current authenticated user
        new_title: Optional new title for the duplicate
        
    Returns:
        DocumentResponse: Duplicated document data
    """
    try:
        logger.info("Duplicating document", document_id=document_id, user_id=current_user.user_id)
        
        # Duplicate document in database
        from core.database import get_database
        db = await get_database()
        
        # First, get the original document
        original_query = """
            SELECT 
                title, type, description, tags, is_public, language
            FROM documents 
            WHERE id = $1 AND owner_id = $2 AND deleted_at IS NULL
        """
        
        original = await db.execute_query(original_query, [document_id, current_user.user_id], fetch="one")
        
        if not original:
            raise HTTPException(status_code=404, detail="Original document not found")
        
        # Create duplicate document
        now = datetime.utcnow()
        duplicate_title = new_title or f"Copy of {original['title']}"
        
        insert_query = """
            INSERT INTO documents (
                owner_id, title, type, status, description, word_count,
                character_count, version, tags, is_public, language,
                is_template, metadata, settings,
                created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $15
            ) RETURNING *
        """
        
        # Copy tags and add 'copy' tag
        duplicate_tags = list(original['tags']) if original['tags'] else []
        if 'copy' not in duplicate_tags:
            duplicate_tags.append('copy')
        
        import json
        
        params = [
            current_user.user_id, duplicate_title, original['type'],
            "draft", f"Duplicated from original document", 0, 0,
            1, duplicate_tags, False, original['language'],
            False, json.dumps({}), json.dumps({}), now
        ]
        
        duplicated_document = await db.execute_query(insert_query, params, fetch="one")
        
        if not duplicated_document:
            raise HTTPException(status_code=500, detail="Failed to create duplicate document")
        
        # Copy blocks from original document
        blocks_query = """
            SELECT content, type, position, metadata
            FROM blocks 
            WHERE document_id = $1 
            ORDER BY position ASC
        """
        
        original_blocks = await db.execute_query(blocks_query, [document_id], fetch="all")
        
        for block in original_blocks:
            block_insert_query = """
                INSERT INTO blocks (
                    document_id, content, type, position, metadata, created_at, updated_at
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $6
                )
            """
            await db.execute_query(
                block_insert_query,
                [duplicated_document['id'], block['content'], block['type'], 
                 block['position'], block['metadata'], now],
                fetch="none"
            )
        
        duplicate_dict = dict(duplicated_document)
        duplicate_dict["last_opened"] = now.isoformat() + "Z"
        
        logger.info("Document duplicated successfully", 
                   original_id=document_id, 
                   duplicate_id=duplicate_dict["id"])
        
        return DocumentResponse(
            success=True,
            data=duplicate_dict
        )
        
    except Exception as e:
        logger.error("Failed to duplicate document", error=str(e), document_id=document_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to duplicate document"
        )


@router.get("/{document_id}/collaborators", response_model=CollaboratorResponse)
async def get_document_collaborators(
    document_id: str,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Get all collaborators for a document.
    
    Args:
        document_id: Document ID to get collaborators for
        current_user: Current authenticated user
        
    Returns:
        CollaboratorResponse: List of collaborators with their details
    """
    try:
        logger.info("Getting document collaborators", document_id=document_id, user_id=current_user.user_id)
        
        from core.database import get_database
        db = await get_database()
        
        # First verify user has access to the document
        doc_access_query = """
            SELECT id FROM documents 
            WHERE id = $1 AND (
                owner_id = $2 OR 
                id IN (
                    SELECT document_id FROM collaborations 
                    WHERE user_id = $2 AND status = 'active'
                )
            ) AND deleted_at IS NULL
        """
        
        has_access = await db.execute_query(doc_access_query, [document_id, current_user.user_id], fetch="one")
        
        if not has_access:
            raise HTTPException(status_code=404, detail="Document not found or access denied")
        
        # Get collaborators with user details
        collaborators_query = """
            SELECT 
                c.id,
                c.role,
                c.status,
                c.permissions,
                c.created_at,
                c.updated_at,
                c.expires_at,
                u.id as user_id,
                u.display_name,
                u.email,
                u.avatar_url,
                invited_by_user.display_name as invited_by_name
            FROM collaborations c
            JOIN users u ON c.user_id = u.id
            LEFT JOIN users invited_by_user ON c.invited_by = invited_by_user.id
            WHERE c.document_id = $1 
            ORDER BY c.created_at DESC
        """
        
        collaborators = await db.execute_query(collaborators_query, [document_id], fetch="all")
        
        # Format collaborators data
        collaborators_data = []
        for collab in collaborators:
            collaborators_data.append({
                "id": collab["id"],
                "user": {
                    "id": collab["user_id"],
                    "display_name": collab["display_name"],
                    "email": collab["email"],
                    "avatar_url": collab["avatar_url"]
                },
                "role": collab["role"],
                "status": collab["status"],
                "permissions": collab["permissions"] or {},
                "invited_by": collab["invited_by_name"],
                "created_at": collab["created_at"].isoformat() + "Z" if collab["created_at"] else None,
                "updated_at": collab["updated_at"].isoformat() + "Z" if collab["updated_at"] else None,
                "expires_at": collab["expires_at"].isoformat() + "Z" if collab["expires_at"] else None
            })
        
        logger.info("Document collaborators retrieved successfully", 
                   document_id=document_id, 
                   collaborator_count=len(collaborators_data))
        
        return CollaboratorResponse(
            success=True,
            data=collaborators_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get document collaborators", error=str(e), document_id=document_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to get document collaborators"
        )

@router.get("/{document_id}/suggestions", response_model=SuggestionsResponse)
async def get_document_suggestions(
    document_id: UUID,
    status: Optional[str] = Query("pending", description="Filter by suggestion status"),
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Get all suggestions for a specific document.
    
    Args:
        document_id: UUID of the document
        status: Filter suggestions by status (pending, accepted, rejected)
        current_user: Current authenticated user
        
    Returns:
        SuggestionsResponse: List of suggestions for the document
    """
    try:
        logger.info(
            "Getting document suggestions",
            document_id=document_id,
            user_id=current_user.user_id,
            status_filter=status
        )
        
        from core.database import get_database
        db = await get_database()
        
        # First verify the user has access to this document
        document_access_query = """
            SELECT id FROM documents 
            WHERE id = $1 AND owner_id = $2 AND deleted_at IS NULL
        """
        
        document_exists = await db.execute_query(
            document_access_query, 
            [document_id, current_user.user_id], 
            fetch="one"
        )
        
        if not document_exists:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Get suggestions for the document (through blocks)
        suggestions_query = """
            SELECT 
                s.id, s.block_id, s.agent_type, s.severity, s.category,
                s.original_text, s.suggested_text, s.explanation,
                s.confidence, s.position, s.original_hash, s.status,
                s.user_feedback, s.feedback_note, s.tokens_used,
                s.model_used, s.processing_time_ms, s.created_at,
                s.resolved_at, s.resolved_by, s.expires_at
            FROM suggestions s
            JOIN blocks b ON s.block_id = b.id
            WHERE b.document_id = $1
        """
        params = [document_id]
        
        # Add status filter if provided
        if status and status != "all":
            suggestions_query += " AND s.status = $2"
            params.append(status)
            
        suggestions_query += " ORDER BY s.created_at DESC"
        
        suggestions = await db.execute_query(suggestions_query, params, fetch="all")
        
        # Format suggestions data
        suggestions_data = []
        for suggestion in suggestions:
            suggestions_data.append(Suggestion(
                id=str(suggestion["id"]),
                block_id=str(suggestion["block_id"]),
                agent_type=suggestion["agent_type"],
                severity=suggestion["severity"],
                category=suggestion["category"],
                original_text=suggestion["original_text"],
                suggested_text=suggestion["suggested_text"],
                explanation=suggestion["explanation"],
                confidence=float(suggestion["confidence"]) if suggestion["confidence"] else 0.0,
                position=suggestion["position"] if suggestion["position"] else {},
                original_hash=suggestion["original_hash"],
                status=suggestion["status"],
                user_feedback=suggestion["user_feedback"],
                feedback_note=suggestion["feedback_note"],
                tokens_used=suggestion["tokens_used"] or 0,
                model_used=suggestion["model_used"],
                processing_time_ms=suggestion["processing_time_ms"],
                created_at=suggestion["created_at"].isoformat() + "Z",
                resolved_at=suggestion["resolved_at"].isoformat() + "Z" if suggestion["resolved_at"] else None,
                resolved_by=str(suggestion["resolved_by"]) if suggestion["resolved_by"] else None,
                expires_at=suggestion["expires_at"].isoformat() + "Z" if suggestion["expires_at"] else None
            ))
        
        logger.info(
            "Document suggestions retrieved successfully", 
            document_id=document_id,
            suggestion_count=len(suggestions_data)
        )
        
        return SuggestionsResponse(
            success=True,
            data=suggestions_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get document suggestions", error=str(e), document_id=document_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to get document suggestions"
        )

@router.get("/{document_id}/comments", response_model=CommentsResponse)
async def get_document_comments(
    document_id: UUID,
    include_resolved: bool = Query(True, description="Include resolved comments"),
    include_deleted: bool = Query(False, description="Include deleted comments"),
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Get all comments for a specific document.
    
    Args:
        document_id: UUID of the document
        include_resolved: Whether to include resolved comments
        include_deleted: Whether to include deleted comments
        current_user: Current authenticated user
        
    Returns:
        CommentsResponse: List of comments for the document
    """
    try:
        logger.info(
            "Getting document comments",
            document_id=document_id,
            user_id=current_user.user_id,
            include_resolved=include_resolved,
            include_deleted=include_deleted
        )
        
        from core.database import get_database
        db = await get_database()
        
        # First verify the user has access to this document
        document_access_query = """
            SELECT id FROM documents 
            WHERE id = $1 AND owner_id = $2 AND deleted_at IS NULL
        """
        
        document_exists = await db.execute_query(
            document_access_query, 
            [document_id, current_user.user_id], 
            fetch="one"
        )
        
        if not document_exists:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Build comments query (through blocks)
        comments_query = """
            SELECT 
                c.id, c.block_id, c.author_id, c.parent_id, c.content,
                c.position, c.type, c.status, c.resolved_by, c.resolved_at,
                c.created_at, c.updated_at, c.deleted_at,
                u.display_name as author_name, u.email as author_email
            FROM comments c
            JOIN blocks b ON c.block_id = b.id
            JOIN users u ON c.author_id = u.id
            WHERE b.document_id = $1
        """
        params = [document_id]
        
        # Add filters
        if not include_resolved:
            comments_query += " AND c.resolved_at IS NULL"
            
        if not include_deleted:
            comments_query += " AND c.deleted_at IS NULL"
            
        comments_query += " ORDER BY c.created_at ASC"
        
        comments = await db.execute_query(comments_query, params, fetch="all")
        
        # Format comments data
        comments_data = []
        for comment in comments:
            comments_data.append(Comment(
                id=str(comment["id"]),
                block_id=str(comment["block_id"]),
                author_id=str(comment["author_id"]),
                author_name=comment["author_name"] or "Unknown User",
                author_email=comment["author_email"] or "",
                parent_id=str(comment["parent_id"]) if comment["parent_id"] else None,
                content=comment["content"],
                position=comment["position"] if comment["position"] else None,
                type=comment["type"],
                status=comment["status"],
                resolved_by=str(comment["resolved_by"]) if comment["resolved_by"] else None,
                resolved_at=comment["resolved_at"].isoformat() + "Z" if comment["resolved_at"] else None,
                created_at=comment["created_at"].isoformat() + "Z",
                updated_at=comment["updated_at"].isoformat() + "Z",
                deleted_at=comment["deleted_at"].isoformat() + "Z" if comment["deleted_at"] else None
            ))
        
        logger.info(
            "Document comments retrieved successfully", 
            document_id=document_id,
            comment_count=len(comments_data)
        )
        
        return CommentsResponse(
            success=True,
            data=comments_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get document comments", error=str(e), document_id=document_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to get document comments"
        )