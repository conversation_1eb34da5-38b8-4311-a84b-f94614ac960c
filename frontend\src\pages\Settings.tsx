import React, { useState, useEffect } from 'react';
import { useUser } from '@/stores/authStore';
import { 
  UserIcon, 
  AdjustmentsHorizontalIcon, 
  CreditCardIcon,
  CommandLineIcon,
  ChartBarIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import AgentManager from '../components/settings/AgentManager';
import { useAnalyticsStore } from '../stores/analyticsStore';

type SettingsTab = 'account' | 'preferences' | 'subscription' | 'agents' | 'analytics';

const Settings: React.FC = () => {
  const user = useUser();
  const [activeTab, setActiveTab] = useState<SettingsTab>('account');
  const [displayName, setDisplayName] = useState(user?.displayName || '');
  const [theme, setTheme] = useState('system');
  const [defaultDocType, setDefaultDocType] = useState('general');
  const [showResetConfirm, setShowResetConfirm] = useState(false);
  
  // Analytics store for settings
  const analyticsStore = useAnalyticsStore();

  // Check URL for tab parameter on mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab') as SettingsTab;
    if (tab && ['account', 'preferences', 'subscription', 'agents', 'analytics'].includes(tab)) {
      setActiveTab(tab);
    }
  }, []);

  // Update displayName when user changes
  useEffect(() => {
    setDisplayName(user?.displayName || '');
  }, [user?.displayName]);

  const tabs = [
    {
      id: 'account' as const,
      label: 'Account',
      icon: UserIcon,
    },
    {
      id: 'preferences' as const,
      label: 'Preferences',
      icon: AdjustmentsHorizontalIcon,
    },
    {
      id: 'agents' as const,
      label: 'Agents',
      icon: CommandLineIcon,
    },
    {
      id: 'analytics' as const,
      label: 'Analytics',
      icon: ChartBarIcon,
    },
    {
      id: 'subscription' as const,
      label: 'Subscription',
      icon: CreditCardIcon,
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'account':
        return (
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Account Settings</h2>
            </div>
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Display Name
                </label>
                <input
                  type="text"
                  value={displayName}
                  onChange={(e) => setDisplayName(e.target.value)}
                  className="input-field"
                  placeholder="Your display name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  value={user?.email || ''}
                  className="input-field"
                  disabled
                />
                <p className="text-xs text-gray-500 mt-1">
                  Email cannot be changed. Contact support if needed.
                </p>
              </div>
              <div className="pt-4 flex justify-end">
                <button className="btn-primary">
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        );

      case 'preferences':
        return (
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Preferences</h2>
            </div>
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Theme
                </label>
                <select 
                  value={theme}
                  onChange={(e) => setTheme(e.target.value)}
                  className="input-field"
                >
                  <option value="system">System</option>
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Default Document Type
                </label>
                <select 
                  value={defaultDocType}
                  onChange={(e) => setDefaultDocType(e.target.value)}
                  className="input-field"
                >
                  <option value="general">General</option>
                  <option value="academic">Academic</option>
                  <option value="creative">Creative</option>
                  <option value="professional">Professional</option>
                  <option value="technical">Technical</option>
                </select>
              </div>
              <div className="pt-4 flex justify-end">
                <button className="btn-primary">
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        );

      case 'agents':
        return <AgentManager />;

      case 'analytics':
        return (
          <div className="space-y-6">
            {/* Analytics Overview */}
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Analytics & Privacy</h2>
                <p className="text-sm text-gray-600 mt-1">
                  Manage your writing analytics data and privacy settings
                </p>
              </div>
              <div className="p-6 space-y-6">
                {/* Data Summary */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-purple-50 rounded-lg p-4">
                    <div className="text-sm text-purple-600 font-medium">Writing Sessions</div>
                    <div className="text-2xl font-bold text-purple-900">
                      {analyticsStore.sessions.length}
                    </div>
                  </div>
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="text-sm text-blue-600 font-medium">Daily Stats Records</div>
                    <div className="text-2xl font-bold text-blue-900">
                      {analyticsStore.dailyStats.length}
                    </div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="text-sm text-green-600 font-medium">Achievements</div>
                    <div className="text-2xl font-bold text-green-900">
                      {analyticsStore.achievements.filter(a => a.unlockedAt).length}
                    </div>
                  </div>
                </div>

                {/* Privacy Settings */}
                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-md font-semibold text-gray-900 mb-4">Privacy Settings</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">
                          Track Writing Analytics
                        </label>
                        <p className="text-xs text-gray-500">
                          Allow Revisionary to track your writing progress and habits
                        </p>
                      </div>
                      <input
                        type="checkbox"
                        defaultChecked={true}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">
                          AI Usage Analytics
                        </label>
                        <p className="text-xs text-gray-500">
                          Track AI suggestion acceptance rates and patterns
                        </p>
                      </div>
                      <input
                        type="checkbox"
                        defaultChecked={true}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">
                          Achievement Notifications
                        </label>
                        <p className="text-xs text-gray-500">
                          Show notifications when you unlock new achievements
                        </p>
                      </div>
                      <input
                        type="checkbox"
                        defaultChecked={true}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                    </div>
                  </div>
                </div>

                {/* Data Management */}
                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-md font-semibold text-gray-900 mb-4">Data Management</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <div className="text-sm font-medium text-gray-700">Export Analytics Data</div>
                        <div className="text-xs text-gray-500">
                          Download all your writing analytics data as JSON
                        </div>
                      </div>
                      <button 
                        onClick={() => {
                          const data = {
                            dailyStats: analyticsStore.dailyStats,
                            sessions: analyticsStore.sessions,
                            goals: analyticsStore.goals,
                            achievements: analyticsStore.achievements,
                            writingStreak: analyticsStore.writingStreak,
                            aiUsageStats: analyticsStore.aiUsageStats,
                            exportedAt: new Date().toISOString()
                          };
                          const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                          const url = URL.createObjectURL(blob);
                          const link = document.createElement('a');
                          link.href = url;
                          link.download = `revisionary-analytics-${new Date().toISOString().split('T')[0]}.json`;
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                          URL.revokeObjectURL(url);
                        }}
                        className="inline-flex items-center px-3 py-2 text-sm bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                      >
                        <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
                        Export Data
                      </button>
                    </div>
                    
                    {!showResetConfirm ? (
                      <div className="flex items-center justify-between p-4 bg-red-50 rounded-lg border border-red-200">
                        <div>
                          <div className="text-sm font-medium text-red-700">Reset Analytics Data</div>
                          <div className="text-xs text-red-600">
                            Permanently delete all analytics data including stats, goals, and achievements
                          </div>
                        </div>
                        <button 
                          onClick={() => setShowResetConfirm(true)}
                          className="inline-flex items-center px-3 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                        >
                          <TrashIcon className="w-4 h-4 mr-2" />
                          Reset Data
                        </button>
                      </div>
                    ) : (
                      <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                        <div className="flex items-center mb-3">
                          <ExclamationTriangleIcon className="w-5 h-5 text-red-500 mr-2" />
                          <div className="text-sm font-medium text-red-700">
                            Are you sure you want to reset all analytics data?
                          </div>
                        </div>
                        <div className="text-xs text-red-600 mb-4">
                          This action cannot be undone. All your writing statistics, goals, achievements, 
                          and session data will be permanently deleted.
                        </div>
                        <div className="flex space-x-3">
                          <button 
                            onClick={() => {
                              analyticsStore.resetAllData();
                              setShowResetConfirm(false);
                            }}
                            className="px-3 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                          >
                            Yes, Reset All Data
                          </button>
                          <button 
                            onClick={() => setShowResetConfirm(false)}
                            className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Data Retention */}
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Data Retention</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Analytics Data Retention Period
                    </label>
                    <select className="input-field">
                      <option value="forever">Keep Forever</option>
                      <option value="1year">1 Year</option>
                      <option value="6months">6 Months</option>
                      <option value="3months">3 Months</option>
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      Older data will be automatically deleted based on this setting
                    </p>
                  </div>
                  <div className="pt-4">
                    <button className="btn-primary">
                      Save Settings
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'subscription':
        return (
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Subscription</h2>
            </div>
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900 capitalize">
                    {user?.subscriptionTier} Plan
                  </p>
                  <p className="text-sm text-gray-500">
                    {user?.tokenUsage?.used.toLocaleString()} / {user?.tokenUsage?.limit.toLocaleString()} tokens used this month
                  </p>
                </div>
                {user?.subscriptionTier === 'free' && (
                  <button className="btn-primary text-sm">
                    Upgrade Plan
                  </button>
                )}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="mobile-p lg:p-6 lg:pl-64">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 mb-6 lg:mb-8">Settings</h1>
        
        {/* Tab Navigation */}
        <div className="mb-6 lg:mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-4 lg:space-x-8 overflow-x-auto">
              {tabs.map((tab) => {
                const IconComponent = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center py-2 px-1 border-b-2 font-medium text-xs lg:text-sm transition-colors duration-200 whitespace-nowrap ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <IconComponent className="w-4 h-4 lg:w-5 lg:h-5 mr-1 lg:mr-2" />
                    <span className="hidden sm:inline">{tab.label}</span>
                    <span className="sm:hidden">{tab.id.charAt(0).toUpperCase()}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {renderTabContent()}
      </div>
    </div>
  );
};

export default Settings;