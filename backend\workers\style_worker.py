"""
Style Worker for Revisionary

Purpose: Specialized worker for style analysis and improvement
- Uses GPT-4.1-mini for deeper style analysis
- Focuses on tone, voice, clarity, readability
- Provides style suggestions and improvements
- Supports brand voice alignment

Features:
- Style consistency analysis
- Tone and voice evaluation
- Readability scoring
- Brand alignment checking
- Writing style recommendations
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import structlog

from workers.base_worker import BaseWorker

logger = structlog.get_logger(__name__)


class StyleWorker(BaseWorker):
    """Worker specialized for style analysis and improvement."""
    
    def __init__(self, worker_id: Optional[str] = None):
        """Initialize style worker."""
        super().__init__('style', worker_id)
        
        # Style-specific configuration
        self.max_text_length = 2000  # More context needed for style analysis
        self.prompt_templates = self._load_prompt_templates()
        
        logger.info(f"Style worker {self.worker_id} initialized")
    
    def _load_prompt_templates(self) -> Dict[str, str]:
        """Load optimized prompt templates for style analysis."""
        return {
            'style_analysis': """Analyze the writing style and provide specific suggestions for improvement.

Text: {text}

Analyze for:
1. Tone and voice consistency
2. Clarity and readability
3. Sentence structure variety
4. Word choice and vocabulary
5. Overall flow and rhythm

Provide actionable suggestions in this format:
Style Score: [1-10]

Suggestions:
- [specific suggestion 1]
- [specific suggestion 2]
- [specific suggestion 3]

Strengths:
- [what works well]""",
            
            'brand_voice_check': """Check if this text aligns with the specified brand voice and provide suggestions.

Text: {text}

Brand Voice Guidelines: {brand_voice}

Analysis:
1. Brand alignment score (1-10)
2. Specific misalignments
3. Suggestions to better match brand voice

Format:
Brand Alignment: [1-10]
Issues: [list specific problems]
Recommendations: [specific fixes]""",
            
            'readability_analysis': """Analyze text readability and suggest improvements for the target audience.

Text: {text}

Target Audience: {audience_level}

Provide:
1. Readability score and grade level
2. Sentence length analysis
3. Vocabulary complexity assessment
4. Specific improvements for target audience

Format:
Readability Grade: [grade level]
Complexity Score: [1-10]
Improvements:
- [specific suggestion 1]
- [specific suggestion 2]""",
            
            'tone_adjustment': """Adjust the tone of this text to match the specified target tone.

Original Text: {text}

Target Tone: {target_tone}

Provide:
1. Current tone assessment
2. Rewritten text with target tone
3. Explanation of changes made

Format:
Current Tone: [assessment]

Revised Text:
[rewritten text]

Changes Made:
- [explanation of changes]""",
            
            'conciseness_check': """Analyze text for conciseness and suggest ways to make it more direct and impactful.

Text: {text}

Provide:
1. Wordiness assessment
2. Redundancy identification
3. More concise version
4. Impact improvement suggestions

Format:
Wordiness Score: [1-10, 10 being very wordy]
Redundancies: [list identified redundancies]

Concise Version:
[shorter, more direct version]

Impact Improvements:
- [suggestion 1]
- [suggestion 2]"""
        }
    
    async def process_job(self, job: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a style analysis job.
        
        Args:
            job: Job data containing text and style analysis options
            
        Returns:
            Dict with style analysis results and suggestions
        """
        job_id = job.get('job_id', 'unknown')
        text = job.get('text', '')
        options = job.get('options', {})
        
        # Validate input
        if not text or not text.strip():
            return {
                'job_id': job_id,
                'success': False,
                'error': 'Empty text provided'
            }
        
        # Limit text length for cost control
        original_text = text
        if len(text) > self.max_text_length:
            text = text[:self.max_text_length] + "..."
            logger.info(f"Text truncated for cost control", 
                       original_length=len(original_text),
                       truncated_length=len(text))
        
        try:
            # Determine analysis type
            analysis_type = options.get('analysis_type', 'style_analysis')
            
            # Choose appropriate prompt
            if analysis_type == 'brand_voice':
                prompt_type = 'brand_voice_check'
                brand_voice = options.get('brand_voice', 'professional and friendly')
                prompt = self.prompt_templates[prompt_type].format(
                    text=text, 
                    brand_voice=brand_voice
                )
            elif analysis_type == 'readability':
                prompt_type = 'readability_analysis'
                audience_level = options.get('audience_level', 'general adult')
                prompt = self.prompt_templates[prompt_type].format(
                    text=text, 
                    audience_level=audience_level
                )
            elif analysis_type == 'tone_adjustment':
                prompt_type = 'tone_adjustment'
                target_tone = options.get('target_tone', 'professional')
                prompt = self.prompt_templates[prompt_type].format(
                    text=text, 
                    target_tone=target_tone
                )
            elif analysis_type == 'conciseness':
                prompt_type = 'conciseness_check'
                prompt = self.prompt_templates[prompt_type].format(text=text)
            else:
                prompt_type = 'style_analysis'
                prompt = self.prompt_templates[prompt_type].format(text=text)
            
            # Get completion from LLM service
            result = await self.llm_service.get_completion(
                agent_type='style',
                prompt=prompt,
                context={
                    'job_id': job_id,
                    'text_length': len(text),
                    'analysis_type': analysis_type,
                    'options': options
                }
            )
            
            # Parse the response
            response_text = result['text'].strip()
            analysis_result = self._parse_style_response(response_text, analysis_type)
            
            # Track cost
            await self.cost_tracker.track_usage(
                model=result['model'],
                usage=result['usage'],
                agent_type='style',
                user_id=job.get('user_id')
            )
            
            # Build result
            style_result = {
                'job_id': job_id,
                'success': True,
                'original_text': original_text,
                'analysis_type': analysis_type,
                'analysis_result': analysis_result,
                'processing_time': result.get('latency', 0),
                'tokens_used': result['usage']['input_tokens'] + result['usage']['output_tokens'],
                'cached_tokens': result['usage'].get('cached_tokens', 0),
                'model_used': result['model']
            }
            
            # Calculate confidence score
            style_result['confidence'] = self._calculate_confidence(result, analysis_type)
            
            logger.info(
                f"Style job {job_id} completed",
                analysis_type=analysis_type,
                tokens_used=style_result['tokens_used'],
                processing_time=style_result['processing_time']
            )
            
            return style_result
            
        except Exception as e:
            logger.error(f"Style job {job_id} failed", error=str(e), exc_info=True)
            return {
                'job_id': job_id,
                'success': False,
                'error': str(e),
                'original_text': original_text,
                'analysis_type': analysis_type
            }
    
    async def _process_job_batch(self, jobs: List[Dict[str, Any]]):
        """Process multiple style jobs in a single API call."""
        job_ids = [job.get('job_id', 'unknown') for job in jobs]
        
        logger.info(f"Batch processing {len(jobs)} style jobs", job_ids=job_ids)
        
        try:
            # Group jobs by analysis type for efficiency
            job_groups = {}
            for job in jobs:
                analysis_type = job.get('options', {}).get('analysis_type', 'style_analysis')
                if analysis_type not in job_groups:
                    job_groups[analysis_type] = []
                job_groups[analysis_type].append(job)
            
            # Process each group
            all_results = []
            for analysis_type, group_jobs in job_groups.items():
                if len(group_jobs) == 1:
                    # Single job, process normally
                    result = await self.process_job(group_jobs[0])
                    all_results.append(result)
                else:
                    # Batch process similar jobs
                    batch_results = await self._process_batch_by_type(group_jobs, analysis_type)
                    all_results.extend(batch_results)
            
            # Acknowledge all jobs and publish results
            for job, result in zip(jobs, all_results):
                message_id = job.get('message_id')
                if message_id:
                    await self.redis.redis_client.xack(
                        self.queue_name,
                        self.consumer_group,
                        message_id
                    )
                
                # Publish result if needed
                if job.get('response_channel'):
                    await self.redis.publish(
                        job['response_channel'], 
                        json.dumps(result)
                    )
            
            logger.info(f"Batch style processing completed", 
                       jobs_processed=len(all_results))
            
        except Exception as e:
            logger.error(f"Batch style processing failed", error=str(e), exc_info=True)
            
            # Process jobs individually as fallback
            for job in jobs:
                await self._process_single_job(job)
    
    async def _process_batch_by_type(self, jobs: List[Dict[str, Any]], analysis_type: str) -> List[Dict[str, Any]]:
        """Process a batch of jobs of the same analysis type."""
        try:
            # Prepare batch texts
            batch_texts = []
            for i, job in enumerate(jobs):
                text = job.get('text', '')[:800]  # Limit each text in batch
                batch_texts.append(f"{i+1}. {text}")
            
            # Build batch prompt based on analysis type
            batch_text_str = '\n\n'.join(batch_texts)
            
            if analysis_type == 'style_analysis':
                prompt = f"""Analyze the style of each numbered text and provide suggestions.

{batch_text_str}

For each text, provide:
Style Score: [1-10]
Suggestions: [list]
Strengths: [list]

Format as:
1. [analysis for first text]
2. [analysis for second text]
etc."""
            else:
                # For other types, fall back to individual processing
                results = []
                for job in jobs:
                    result = await self.process_job(job)
                    results.append(result)
                return results
            
            # Get completion
            result = await self.llm_service.get_completion(
                agent_type='style',
                prompt=prompt,
                context={
                    'batch': True,
                    'analysis_type': analysis_type,
                    'job_count': len(jobs),
                    'batch_ids': [job.get('job_id', 'unknown') for job in jobs]
                }
            )
            
            # Parse batch response
            batch_results = self._parse_batch_response(result['text'], jobs, analysis_type)
            
            # Track cost for batch
            await self.cost_tracker.track_usage(
                model=result['model'],
                usage=result['usage'],
                agent_type='style',
                user_id=jobs[0].get('user_id') if jobs else None
            )
            
            return batch_results
            
        except Exception as e:
            logger.error(f"Batch processing by type failed", error=str(e), exc_info=True)
            # Fall back to individual processing
            results = []
            for job in jobs:
                result = await self.process_job(job)
                results.append(result)
            return results
    
    def _parse_style_response(self, response: str, analysis_type: str) -> Dict[str, Any]:
        """Parse the style analysis response."""
        analysis_result = {
            'raw_response': response,
            'analysis_type': analysis_type
        }
        
        try:
            lines = response.split('\n')
            current_section = None
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # Parse different sections based on analysis type
                if 'Score:' in line:
                    score_text = line.split('Score:')[-1].strip()
                    try:
                        analysis_result['score'] = int(score_text.split()[0])
                    except (ValueError, IndexError):
                        analysis_result['score'] = 5  # Default
                
                elif 'Suggestions:' in line:
                    current_section = 'suggestions'
                    analysis_result['suggestions'] = []
                
                elif 'Strengths:' in line:
                    current_section = 'strengths'
                    analysis_result['strengths'] = []
                
                elif 'Issues:' in line:
                    current_section = 'issues'
                    analysis_result['issues'] = []
                
                elif 'Recommendations:' in line:
                    current_section = 'recommendations'
                    analysis_result['recommendations'] = []
                
                elif 'Revised Text:' in line:
                    current_section = 'revised_text'
                    analysis_result['revised_text'] = ""
                
                elif 'Changes Made:' in line:
                    current_section = 'changes_made'
                    analysis_result['changes_made'] = []
                
                elif line.startswith('-') and current_section:
                    # Extract bullet point
                    item = line[1:].strip()
                    if current_section in ['suggestions', 'strengths', 'issues', 'recommendations', 'changes_made']:
                        if current_section not in analysis_result:
                            analysis_result[current_section] = []
                        analysis_result[current_section].append(item)
                
                elif current_section == 'revised_text':
                    # Accumulate revised text
                    if 'revised_text' not in analysis_result:
                        analysis_result['revised_text'] = ""
                    analysis_result['revised_text'] += line + " "
            
            # Clean up revised text
            if 'revised_text' in analysis_result:
                analysis_result['revised_text'] = analysis_result['revised_text'].strip()
        
        except Exception as e:
            logger.warning(f"Failed to parse style response", error=str(e))
            analysis_result['parse_error'] = str(e)
        
        return analysis_result
    
    def _parse_batch_response(self, response: str, jobs: List[Dict[str, Any]], analysis_type: str) -> List[Dict[str, Any]]:
        """Parse batch response and create individual results."""
        results = []
        
        # Split response by numbers (1., 2., etc.)
        analyses = []
        lines = response.split('\n')
        current_analysis = ""
        
        for line in lines:
            line = line.strip()
            if line and any(line.startswith(f"{i}.") for i in range(1, len(jobs) + 1)):
                if current_analysis:
                    analyses.append(current_analysis.strip())
                current_analysis = line[2:].strip()  # Remove number prefix
            elif current_analysis:
                current_analysis += "\n" + line
        
        # Add the last analysis
        if current_analysis:
            analyses.append(current_analysis.strip())
        
        # Create results for each job
        for i, job in enumerate(jobs):
            original_text = job.get('text', '')
            analysis_text = analyses[i] if i < len(analyses) else "Analysis not available"
            
            analysis_result = self._parse_style_response(analysis_text, analysis_type)
            
            result = {
                'job_id': job.get('job_id', 'unknown'),
                'success': True,
                'original_text': original_text,
                'analysis_type': analysis_type,
                'analysis_result': analysis_result,
                'processing_time': 0,  # Batch processing time divided later
                'batch_processed': True
            }
            
            results.append(result)
        
        return results
    
    def _calculate_confidence(self, llm_result: Dict, analysis_type: str) -> float:
        """Calculate confidence score for the style analysis."""
        confidence = 0.85  # Base confidence for style analysis
        
        # Adjust based on analysis type complexity
        if analysis_type in ['brand_voice', 'tone_adjustment']:
            confidence *= 0.9  # More subjective analysis
        elif analysis_type == 'readability':
            confidence *= 1.1  # More objective analysis
        
        # Adjust based on cached tokens
        cached_ratio = llm_result['usage'].get('cached_tokens', 0) / max(llm_result['usage']['input_tokens'], 1)
        if cached_ratio > 0.5:
            confidence = min(0.98, confidence * 1.1)
        
        return round(confidence, 2)
    
    async def get_style_stats(self) -> Dict[str, Any]:
        """Get style-specific worker statistics."""
        base_stats = await self.get_worker_stats()
        
        # Add style-specific metrics
        style_stats = {
            **base_stats,
            'max_text_length': self.max_text_length,
            'analysis_types': ['style_analysis', 'brand_voice', 'readability', 'tone_adjustment', 'conciseness'],
            'supports_batch': True,
            'model_used': 'gpt-4.1-mini-2025-04-14'
        }
        
        return style_stats


# Standalone function to run the worker
async def run_style_worker():
    """Run a style worker instance."""
    worker = StyleWorker()
    await worker.initialize()
    await worker.start()


if __name__ == "__main__":
    import asyncio
    asyncio.run(run_style_worker())