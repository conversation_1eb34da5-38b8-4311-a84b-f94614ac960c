import React, { useState } from 'react';
import {
  BoldIcon,
  ItalicIcon,
  UnderlineIcon,
  ListBulletIcon,
  NumberedListIcon,
  LinkIcon,
  PhotoIcon,
  ChatBubbleLeftRightIcon,
  CodeBracketIcon,
  TableCellsIcon,
  CheckIcon,
  PlusIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

interface EditorToolbarProps {
  onFormatClick: (format: string) => void;
  activeFormats: Set<string>;
}

const formatGroups = [
  {
    name: 'Text',
    items: [
      { id: 'bold', icon: BoldIcon, label: 'Bold', shortcut: '⌘B' },
      { id: 'italic', icon: ItalicIcon, label: 'Italic', shortcut: '⌘I' },
      { id: 'underline', icon: UnderlineIcon, label: 'Underline', shortcut: '⌘U' },
    ],
  },
  {
    name: 'Lists',
    items: [
      { id: 'bullet-list', icon: ListBulletIcon, label: 'Bullet List' },
      { id: 'numbered-list', icon: NumberedListIcon, label: 'Numbered List' },
      { id: 'checklist', icon: CheckIcon, label: 'Checklist' },
    ],
  },
  {
    name: 'Insert',
    items: [
      { id: 'link', icon: LinkIcon, label: 'Link', shortcut: '⌘K' },
      { id: 'image', icon: PhotoIcon, label: 'Image' },
      { id: 'table', icon: TableCellsIcon, label: 'Table' },
      { id: 'code', icon: CodeBracketIcon, label: 'Code Block' },
    ],
  },
  {
    name: 'AI',
    items: [
      { id: 'ai-write', icon: SparklesIcon, label: 'AI Generate', shortcut: '⌘⇧A' },
      { id: 'ai-comment', icon: ChatBubbleLeftRightIcon, label: 'AI Comment' },
    ],
  },
];

const EditorToolbar: React.FC<EditorToolbarProps> = ({ onFormatClick, activeFormats }) => {
  const [showMore, setShowMore] = useState(false);

  return (
    <div className="sticky top-0 z-10 bg-white/95 backdrop-blur-xl border-b border-slate-200/50 shadow-sm">
      <div className="flex items-center justify-between px-6 py-3">
        <div className="flex items-center space-x-1">
          {formatGroups.map((group, groupIndex) => (
            <div key={group.name} className="flex items-center">
              {groupIndex > 0 && (
                <div className="w-px h-6 bg-slate-300 mx-3" />
              )}
              <div className="flex items-center space-x-1">
                {group.items.map((item) => (
                  <motion.button
                    key={item.id}
                    onClick={() => onFormatClick(item.id)}
                    className={`group relative p-2 rounded-xl transition-all duration-200 ${
                      activeFormats.has(item.id)
                        ? 'bg-purple-100 text-purple-700 shadow-sm'
                        : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    title={`${item.label}${item.shortcut ? ` (${item.shortcut})` : ''}`}
                  >
                    <item.icon className="w-5 h-5" />
                    
                    {/* Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                      {item.label}
                      {item.shortcut && (
                        <span className="ml-1 text-slate-400">{item.shortcut}</span>
                      )}
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-l-transparent border-r-transparent border-t-slate-900"></div>
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* More options */}
        <div className="relative">
          <motion.button
            onClick={() => setShowMore(!showMore)}
            className={`p-2 rounded-xl transition-all duration-200 ${
              showMore
                ? 'bg-purple-100 text-purple-700'
                : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
            }`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <PlusIcon className={`w-5 h-5 transition-transform duration-200 ${showMore ? 'rotate-45' : ''}`} />
          </motion.button>

          <AnimatePresence>
            {showMore && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -10 }}
                className="absolute top-12 right-0 bg-white rounded-2xl shadow-2xl border border-slate-200/50 p-2 min-w-48 z-30"
              >
                <div className="space-y-1">
                  <button className="w-full flex items-center p-3 text-sm text-slate-700 hover:bg-slate-50 rounded-xl transition-colors">
                    <SparklesIcon className="w-4 h-4 mr-2" />
                    More AI Options
                  </button>
                  <button className="w-full flex items-center p-3 text-sm text-slate-700 hover:bg-slate-50 rounded-xl transition-colors">
                    <TableCellsIcon className="w-4 h-4 mr-2" />
                    Advanced Table
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

export default EditorToolbar;