# Revisionary - Database Schema

## 1. Overview

The Revisionary database schema is designed for scalability, performance, and multi-tenancy. Built on PostgreSQL with Supabase, it supports real-time subscriptions, row-level security, and vector operations for semantic search.

## 2. Schema Design Principles

- **Hierarchical Document Structure**: Documents → Blocks → Versions
- **Optimistic Concurrency**: Hash-based conflict detection
- **Soft Deletes**: Retain data for recovery and audit trails
- **Audit Logging**: Track all changes for security and debugging
- **Denormalization**: Strategic denormalization for read performance
- **Multi-tenancy**: Row-level security for data isolation

## 3. Core Tables

### 3.1 Users Table

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    firebase_uid VARCHAR(128) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    avatar_url TEXT,
    subscription_tier subscription_tier_enum NOT NULL DEFAULT 'free',
    subscription_status subscription_status_enum NOT NULL DEFAULT 'active',
    subscription_period_start TIMESTAMPTZ,
    subscription_period_end TIMESTAMPTZ,
    onboarding_completed BOOLEAN DEFAULT FALSE,
    preferences JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_active_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT users_email_valid CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT users_preferences_valid CHECK (jsonb_typeof(preferences) = 'object')
);

-- Enums
CREATE TYPE subscription_tier_enum AS ENUM ('free', 'professional', 'studio', 'enterprise');
CREATE TYPE subscription_status_enum AS ENUM ('active', 'canceled', 'past_due', 'paused');

-- Indexes
CREATE INDEX idx_users_firebase_uid ON users(firebase_uid);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_subscription ON users(subscription_tier, subscription_status);
CREATE INDEX idx_users_last_active ON users(last_active_at) WHERE deleted_at IS NULL;

-- Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

CREATE POLICY users_own_data ON users
    FOR ALL USING (firebase_uid = auth.jwt() ->> 'sub');
```

### 3.2 Documents Table

```sql
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    type document_type_enum NOT NULL,
    status document_status_enum DEFAULT 'draft',
    description TEXT,
    word_count INTEGER DEFAULT 0,
    character_count INTEGER DEFAULT 0,
    version INTEGER DEFAULT 1,
    is_template BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    language VARCHAR(10) DEFAULT 'en',
    metadata JSONB DEFAULT '{}'::jsonb,
    settings JSONB DEFAULT '{}'::jsonb,
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    published_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ,
    
    -- Computed columns
    slug VARCHAR(255) GENERATED ALWAYS AS (
        LOWER(REGEXP_REPLACE(title, '[^a-zA-Z0-9]+', '-', 'g'))
    ) STORED,
    
    -- Constraints
    CONSTRAINT documents_title_not_empty CHECK (LENGTH(TRIM(title)) > 0),
    CONSTRAINT documents_word_count_positive CHECK (word_count >= 0),
    CONSTRAINT documents_character_count_positive CHECK (character_count >= 0),
    CONSTRAINT documents_metadata_valid CHECK (jsonb_typeof(metadata) = 'object'),
    CONSTRAINT documents_settings_valid CHECK (jsonb_typeof(settings) = 'object')
);

-- Enums
CREATE TYPE document_type_enum AS ENUM (
    'creative', 'academic', 'professional', 'general', 'technical', 'legal'
);

CREATE TYPE document_status_enum AS ENUM (
    'draft', 'editing', 'review', 'published', 'archived'
);

-- Indexes
CREATE INDEX idx_documents_owner ON documents(owner_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_documents_type ON documents(type) WHERE deleted_at IS NULL;
CREATE INDEX idx_documents_status ON documents(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_documents_updated ON documents(updated_at DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_documents_tags ON documents USING gin(tags);
CREATE INDEX idx_documents_metadata ON documents USING gin(metadata);
CREATE INDEX idx_documents_search ON documents USING gin(
    to_tsvector('english', title || ' ' || COALESCE(description, ''))
);

-- Row Level Security
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;

CREATE POLICY documents_owner_access ON documents
    FOR ALL USING (
        owner_id IN (
            SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
        )
    );

CREATE POLICY documents_public_read ON documents
    FOR SELECT USING (is_public = TRUE AND deleted_at IS NULL);
```

### 3.3 Blocks Table (Hierarchical Document Structure)

```sql
CREATE TABLE blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    type block_type_enum NOT NULL,
    position INTEGER NOT NULL,
    depth INTEGER DEFAULT 0,
    path LTREE, -- Materialized path for efficient tree queries
    content TEXT,
    content_hash VARCHAR(64), -- SHA256 hash for conflict detection
    content_length INTEGER GENERATED ALWAYS AS (LENGTH(content)) STORED,
    word_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT blocks_position_positive CHECK (position >= 0),
    CONSTRAINT blocks_depth_positive CHECK (depth >= 0),
    CONSTRAINT blocks_word_count_positive CHECK (word_count >= 0),
    CONSTRAINT blocks_content_hash_valid CHECK (content_hash ~ '^[a-f0-9]{64}$'),
    CONSTRAINT blocks_metadata_valid CHECK (jsonb_typeof(metadata) = 'object'),
    
    -- Tree structure constraints
    CONSTRAINT blocks_parent_different CHECK (id != parent_id),
    CONSTRAINT blocks_root_no_parent CHECK (
        (type = 'document' AND parent_id IS NULL) OR 
        (type != 'document' AND parent_id IS NOT NULL)
    )
);

-- Enums
CREATE TYPE block_type_enum AS ENUM (
    'document', 'chapter', 'section', 'subsection', 'paragraph', 'heading', 'list', 'quote', 'code'
);

-- Indexes
CREATE UNIQUE INDEX idx_blocks_document_position ON blocks(document_id, parent_id, position) 
    WHERE deleted_at IS NULL;
CREATE INDEX idx_blocks_parent ON blocks(parent_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_blocks_path ON blocks USING gist(path) WHERE deleted_at IS NULL;
CREATE INDEX idx_blocks_type ON blocks(type) WHERE deleted_at IS NULL;
CREATE INDEX idx_blocks_hash ON blocks(content_hash);
CREATE INDEX idx_blocks_updated ON blocks(updated_at DESC);
CREATE INDEX idx_blocks_content_search ON blocks USING gin(
    to_tsvector('english', content)
) WHERE content IS NOT NULL AND deleted_at IS NULL;

-- Triggers for maintaining tree structure
CREATE OR REPLACE FUNCTION update_block_path()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.parent_id IS NULL THEN
        NEW.path = NEW.id::text::ltree;
        NEW.depth = 0;
    ELSE
        SELECT path || NEW.id::text, depth + 1
        INTO NEW.path, NEW.depth
        FROM blocks
        WHERE id = NEW.parent_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_block_path
    BEFORE INSERT OR UPDATE OF parent_id
    ON blocks
    FOR EACH ROW
    EXECUTE FUNCTION update_block_path();

-- Row Level Security
ALTER TABLE blocks ENABLE ROW LEVEL SECURITY;

CREATE POLICY blocks_document_access ON blocks
    FOR ALL USING (
        document_id IN (
            SELECT id FROM documents WHERE owner_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            )
        )
    );
```

### 3.4 Versions Table (Document History)

```sql
CREATE TABLE versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    content TEXT NOT NULL,
    content_hash VARCHAR(64) NOT NULL,
    word_count INTEGER DEFAULT 0,
    author_id UUID REFERENCES users(id),
    change_summary TEXT,
    change_type version_change_type_enum DEFAULT 'edit',
    diff_ops JSONB, -- JSON patch operations
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT versions_version_number_positive CHECK (version_number > 0),
    CONSTRAINT versions_word_count_positive CHECK (word_count >= 0),
    CONSTRAINT versions_content_hash_valid CHECK (content_hash ~ '^[a-f0-9]{64}$'),
    CONSTRAINT versions_diff_ops_valid CHECK (
        diff_ops IS NULL OR jsonb_typeof(diff_ops) = 'array'
    ),
    
    UNIQUE(block_id, version_number)
);

-- Enums
CREATE TYPE version_change_type_enum AS ENUM (
    'create', 'edit', 'ai_suggestion', 'collaboration', 'import', 'restore'
);

-- Indexes
CREATE INDEX idx_versions_block ON versions(block_id, version_number DESC);
CREATE INDEX idx_versions_author ON versions(author_id, created_at DESC);
CREATE INDEX idx_versions_created ON versions(created_at DESC);
CREATE INDEX idx_versions_change_type ON versions(change_type);

-- Partitioning by month for large-scale deployments
-- CREATE TABLE versions_y2024m01 PARTITION OF versions
-- FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Row Level Security
ALTER TABLE versions ENABLE ROW LEVEL SECURITY;

CREATE POLICY versions_block_access ON versions
    FOR ALL USING (
        block_id IN (
            SELECT id FROM blocks WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );
```

### 3.5 Suggestions Table (AI Suggestions)

```sql
CREATE TABLE suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    agent_type agent_type_enum NOT NULL,
    severity suggestion_severity_enum NOT NULL,
    category VARCHAR(50),
    original_text TEXT NOT NULL,
    suggested_text TEXT NOT NULL,
    explanation TEXT,
    confidence DECIMAL(3,2) CHECK (confidence >= 0 AND confidence <= 1),
    position JSONB NOT NULL, -- {start: int, end: int, line: int}
    original_hash VARCHAR(64) NOT NULL, -- Hash of the original block content
    status suggestion_status_enum DEFAULT 'pending',
    user_feedback suggestion_feedback_enum,
    feedback_note TEXT,
    tokens_used INTEGER DEFAULT 0,
    model_used VARCHAR(50),
    processing_time_ms INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    resolved_at TIMESTAMPTZ,
    resolved_by UUID REFERENCES users(id),
    expires_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT suggestions_position_valid CHECK (
        jsonb_typeof(position) = 'object' AND
        position ? 'start' AND position ? 'end'
    ),
    CONSTRAINT suggestions_original_hash_valid CHECK (original_hash ~ '^[a-f0-9]{64}$'),
    CONSTRAINT suggestions_tokens_positive CHECK (tokens_used >= 0),
    CONSTRAINT suggestions_processing_time_positive CHECK (processing_time_ms >= 0),
    CONSTRAINT suggestions_confidence_range CHECK (confidence IS NULL OR (confidence >= 0 AND confidence <= 1))
);

-- Enums
CREATE TYPE agent_type_enum AS ENUM ('grammar', 'style', 'structure', 'content', 'consistency', 'plagiarism');
CREATE TYPE suggestion_severity_enum AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE suggestion_status_enum AS ENUM ('pending', 'accepted', 'rejected', 'stale', 'expired');
CREATE TYPE suggestion_feedback_enum AS ENUM ('helpful', 'not_helpful', 'incorrect');

-- Indexes
CREATE INDEX idx_suggestions_block_status ON suggestions(block_id, status);
CREATE INDEX idx_suggestions_agent_severity ON suggestions(agent_type, severity);
CREATE INDEX idx_suggestions_created ON suggestions(created_at DESC);
CREATE INDEX idx_suggestions_expires ON suggestions(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_suggestions_hash ON suggestions(original_hash);
CREATE INDEX idx_suggestions_position ON suggestions USING gin(position);

-- Partial indexes for performance
CREATE INDEX idx_suggestions_pending ON suggestions(block_id) WHERE status = 'pending';
CREATE INDEX idx_suggestions_active ON suggestions(block_id, created_at DESC) 
    WHERE status IN ('pending', 'accepted');

-- Row Level Security
ALTER TABLE suggestions ENABLE ROW LEVEL SECURITY;

CREATE POLICY suggestions_block_access ON suggestions
    FOR ALL USING (
        block_id IN (
            SELECT id FROM blocks WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );
```

### 3.6 Summaries Table (Cached AI Summaries)

```sql
CREATE TABLE summaries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    level summary_level_enum NOT NULL,
    summary_text TEXT NOT NULL,
    key_points TEXT[],
    embedding vector(1536), -- OpenAI embedding dimension
    token_count INTEGER DEFAULT 0,
    model_used VARCHAR(50),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    content_hash VARCHAR(64) NOT NULL, -- Hash of original content
    
    -- Constraints
    CONSTRAINT summaries_token_count_positive CHECK (token_count >= 0),
    CONSTRAINT summaries_content_hash_valid CHECK (content_hash ~ '^[a-f0-9]{64}$'),
    CONSTRAINT summaries_summary_not_empty CHECK (LENGTH(TRIM(summary_text)) > 0),
    
    UNIQUE(block_id, level, content_hash)
);

-- Enums
CREATE TYPE summary_level_enum AS ENUM ('paragraph', 'section', 'chapter', 'document');

-- Indexes
CREATE INDEX idx_summaries_block_level ON summaries(block_id, level);
CREATE INDEX idx_summaries_expires ON summaries(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_summaries_hash ON summaries(content_hash);

-- Vector similarity search (requires pgvector extension)
CREATE INDEX idx_summaries_embedding ON summaries 
    USING ivfflat (embedding vector_cosine_ops)
    WITH (lists = 100);

-- Row Level Security
ALTER TABLE summaries ENABLE ROW LEVEL SECURITY;

CREATE POLICY summaries_block_access ON summaries
    FOR ALL USING (
        block_id IN (
            SELECT id FROM blocks WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );
```

## 4. Collaboration Tables

### 4.1 Collaborations Table

```sql
CREATE TABLE collaborations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role collaboration_role_enum NOT NULL,
    status collaboration_status_enum DEFAULT 'pending',
    invited_by UUID REFERENCES users(id),
    permissions JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT collaborations_permissions_valid CHECK (jsonb_typeof(permissions) = 'object'),
    
    UNIQUE(document_id, user_id)
);

-- Enums
CREATE TYPE collaboration_role_enum AS ENUM ('viewer', 'commenter', 'editor', 'admin');
CREATE TYPE collaboration_status_enum AS ENUM ('pending', 'active', 'declined', 'revoked');

-- Indexes
CREATE INDEX idx_collaborations_document ON collaborations(document_id, status);
CREATE INDEX idx_collaborations_user ON collaborations(user_id, status);
CREATE INDEX idx_collaborations_expires ON collaborations(expires_at) WHERE expires_at IS NOT NULL;

-- Row Level Security
ALTER TABLE collaborations ENABLE ROW LEVEL SECURITY;

CREATE POLICY collaborations_access ON collaborations
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub') OR
        document_id IN (
            SELECT id FROM documents WHERE owner_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            )
        )
    );
```

### 4.2 Comments Table

```sql
CREATE TABLE comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    author_id UUID REFERENCES users(id),
    parent_id UUID REFERENCES comments(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    position JSONB, -- {start: int, end: int} for inline comments
    type comment_type_enum DEFAULT 'general',
    status comment_status_enum DEFAULT 'open',
    resolved_by UUID REFERENCES users(id),
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT comments_content_not_empty CHECK (LENGTH(TRIM(content)) > 0),
    CONSTRAINT comments_position_valid CHECK (
        position IS NULL OR (
            jsonb_typeof(position) = 'object' AND
            position ? 'start' AND position ? 'end'
        )
    ),
    CONSTRAINT comments_parent_different CHECK (id != parent_id)
);

-- Enums
CREATE TYPE comment_type_enum AS ENUM ('general', 'suggestion', 'question', 'issue');
CREATE TYPE comment_status_enum AS ENUM ('open', 'resolved', 'archived');

-- Indexes
CREATE INDEX idx_comments_block ON comments(block_id, created_at DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_comments_author ON comments(author_id, created_at DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_comments_parent ON comments(parent_id) WHERE parent_id IS NOT NULL;
CREATE INDEX idx_comments_status ON comments(status) WHERE deleted_at IS NULL;

-- Row Level Security
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;

CREATE POLICY comments_collaboration_access ON comments
    FOR ALL USING (
        block_id IN (
            SELECT b.id FROM blocks b
            JOIN documents d ON b.document_id = d.id
            JOIN collaborations c ON d.id = c.document_id
            WHERE c.user_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            ) AND c.status = 'active'
        ) OR
        block_id IN (
            SELECT id FROM blocks WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );
```

## 5. Scoring System Tables

### 5.1 Document Scores Table

```sql
CREATE TABLE document_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    total_score DECIMAL(5,2) NOT NULL,
    grammar_score DECIMAL(5,2) NOT NULL,
    style_score DECIMAL(5,2) NOT NULL,
    structure_score DECIMAL(5,2) NOT NULL,
    content_score DECIMAL(5,2) NOT NULL,
    word_count INTEGER NOT NULL,
    document_type document_type_enum NOT NULL,
    detailed_breakdown JSONB NOT NULL,
    improvement_suggestions TEXT[],
    scoring_version VARCHAR(10) DEFAULT '1.0',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT document_scores_total_range CHECK (total_score >= 0 AND total_score <= 100),
    CONSTRAINT document_scores_component_range CHECK (
        grammar_score >= 0 AND grammar_score <= 25 AND
        style_score >= 0 AND style_score <= 25 AND
        structure_score >= 0 AND structure_score <= 25 AND
        content_score >= 0 AND content_score <= 25
    ),
    CONSTRAINT document_scores_word_count_positive CHECK (word_count > 0)
);

-- Indexes
CREATE INDEX idx_document_scores_document ON document_scores(document_id, created_at DESC);
CREATE INDEX idx_document_scores_user ON document_scores(user_id, created_at DESC);
CREATE INDEX idx_document_scores_type_score ON document_scores(document_type, total_score DESC);
CREATE INDEX idx_document_scores_date ON document_scores(created_at DESC);

-- Row Level Security
ALTER TABLE document_scores ENABLE ROW LEVEL SECURITY;

CREATE POLICY document_scores_own_data ON document_scores
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );
```

### 5.2 User Writing Statistics

```sql
CREATE TABLE user_writing_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    document_type document_type_enum NOT NULL,
    date DATE NOT NULL,
    average_score DECIMAL(5,2) NOT NULL,
    average_grammar DECIMAL(5,2) NOT NULL,
    average_style DECIMAL(5,2) NOT NULL,
    average_structure DECIMAL(5,2) NOT NULL,
    average_content DECIMAL(5,2) NOT NULL,
    documents_scored INTEGER NOT NULL,
    total_words INTEGER NOT NULL,
    improvement_trend DECIMAL(5,2), -- Change from previous period
    streak_days INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT user_writing_stats_scores_range CHECK (
        average_score >= 0 AND average_score <= 100 AND
        average_grammar >= 0 AND average_grammar <= 25 AND
        average_style >= 0 AND average_style <= 25 AND
        average_structure >= 0 AND average_structure <= 25 AND
        average_content >= 0 AND average_content <= 25
    ),
    CONSTRAINT user_writing_stats_counts_positive CHECK (
        documents_scored >= 0 AND total_words >= 0 AND streak_days >= 0
    ),
    
    UNIQUE(user_id, document_type, date)
);

-- Indexes
CREATE INDEX idx_user_writing_stats_user_date ON user_writing_stats(user_id, date DESC);
CREATE INDEX idx_user_writing_stats_type ON user_writing_stats(document_type, date DESC);
CREATE INDEX idx_user_writing_stats_score ON user_writing_stats(average_score DESC);

-- Row Level Security
ALTER TABLE user_writing_stats ENABLE ROW LEVEL SECURITY;

CREATE POLICY user_writing_stats_own_data ON user_writing_stats
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );
```

### 5.3 Writing Achievements

```sql
CREATE TABLE writing_achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    achievement_type achievement_type_enum NOT NULL,
    achievement_name VARCHAR(100) NOT NULL,
    achievement_level achievement_level_enum NOT NULL,
    description TEXT,
    score_requirement DECIMAL(5,2),
    document_count_requirement INTEGER,
    streak_requirement INTEGER,
    earned_at TIMESTAMPTZ DEFAULT NOW(),
    document_id UUID REFERENCES documents(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    
    -- Constraints
    CONSTRAINT writing_achievements_metadata_valid CHECK (jsonb_typeof(metadata) = 'object'),
    
    UNIQUE(user_id, achievement_type, achievement_level)
);

-- Enums
CREATE TYPE achievement_type_enum AS ENUM (
    'grammar_master', 'style_savant', 'structure_specialist', 'content_creator',
    'rising_star', 'consistent_improver', 'perfect_score', 'prolific_writer',
    'academic_scholar', 'creative_genius', 'professional_powerhouse', 'technical_expert'
);

CREATE TYPE achievement_level_enum AS ENUM ('bronze', 'silver', 'gold', 'platinum');

-- Indexes
CREATE INDEX idx_writing_achievements_user ON writing_achievements(user_id, earned_at DESC);
CREATE INDEX idx_writing_achievements_type ON writing_achievements(achievement_type, achievement_level);
CREATE INDEX idx_writing_achievements_earned ON writing_achievements(earned_at DESC);

-- Row Level Security
ALTER TABLE writing_achievements ENABLE ROW LEVEL SECURITY;

CREATE POLICY writing_achievements_own_data ON writing_achievements
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );
```

### 5.4 Daily Writing Challenges

```sql
CREATE TABLE writing_challenges (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    challenge_type challenge_type_enum NOT NULL,
    challenge_title VARCHAR(200) NOT NULL,
    challenge_description TEXT NOT NULL,
    target_metric VARCHAR(50) NOT NULL,
    target_value DECIMAL(10,2) NOT NULL,
    difficulty challenge_difficulty_enum NOT NULL,
    document_type_filter document_type_enum,
    assigned_date DATE NOT NULL,
    completed_at TIMESTAMPTZ,
    completion_value DECIMAL(10,2),
    document_id UUID REFERENCES documents(id) ON DELETE SET NULL,
    reward_points INTEGER DEFAULT 0,
    
    -- Constraints
    CONSTRAINT writing_challenges_target_value_positive CHECK (target_value > 0),
    CONSTRAINT writing_challenges_completion_value_positive CHECK (
        completion_value IS NULL OR completion_value >= 0
    ),
    CONSTRAINT writing_challenges_reward_points_positive CHECK (reward_points >= 0),
    
    UNIQUE(user_id, assigned_date)
);

-- Enums
CREATE TYPE challenge_type_enum AS ENUM (
    'grammar_perfection', 'style_improvement', 'structure_mastery', 'content_quality',
    'word_count', 'vocabulary_diversity', 'readability_target', 'error_reduction'
);

CREATE TYPE challenge_difficulty_enum AS ENUM ('easy', 'medium', 'hard', 'expert');

-- Indexes
CREATE INDEX idx_writing_challenges_user_date ON writing_challenges(user_id, assigned_date DESC);
CREATE INDEX idx_writing_challenges_type ON writing_challenges(challenge_type);
CREATE INDEX idx_writing_challenges_completed ON writing_challenges(completed_at) 
    WHERE completed_at IS NOT NULL;

-- Row Level Security
ALTER TABLE writing_challenges ENABLE ROW LEVEL SECURITY;

CREATE POLICY writing_challenges_own_data ON writing_challenges
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );
```

### 5.5 Scoring Algorithm Metadata

```sql
CREATE TABLE scoring_algorithms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    version VARCHAR(10) NOT NULL UNIQUE,
    algorithm_name VARCHAR(100) NOT NULL,
    description TEXT,
    grammar_weights JSONB NOT NULL,
    style_weights JSONB NOT NULL,
    structure_weights JSONB NOT NULL,
    content_weights JSONB NOT NULL,
    document_type_adjustments JSONB NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT scoring_algorithms_weights_valid CHECK (
        jsonb_typeof(grammar_weights) = 'object' AND
        jsonb_typeof(style_weights) = 'object' AND
        jsonb_typeof(structure_weights) = 'object' AND
        jsonb_typeof(content_weights) = 'object' AND
        jsonb_typeof(document_type_adjustments) = 'object'
    )
);

-- Ensure only one active algorithm
CREATE UNIQUE INDEX idx_scoring_algorithms_active ON scoring_algorithms(is_active) 
    WHERE is_active = TRUE;

-- Insert default algorithm
INSERT INTO scoring_algorithms (
    version, algorithm_name, description, is_active,
    grammar_weights, style_weights, structure_weights, content_weights,
    document_type_adjustments
) VALUES (
    '1.0', 'Base Scoring Algorithm', 'Initial scoring algorithm with balanced weights', TRUE,
    '{"spelling": 0.3, "grammar": 0.4, "punctuation": 0.3}',
    '{"readability": 0.25, "vocabulary": 0.25, "sentence_variety": 0.25, "conciseness": 0.25}',
    '{"organization": 0.4, "transitions": 0.3, "coherence": 0.3}',
    '{"depth": 0.3, "evidence": 0.3, "consistency": 0.2, "originality": 0.2}',
    '{
        "creative": {"grammar": 0.15, "style": 0.35, "structure": 0.25, "content": 0.25},
        "academic": {"grammar": 0.20, "style": 0.20, "structure": 0.30, "content": 0.30},
        "professional": {"grammar": 0.25, "style": 0.30, "structure": 0.25, "content": 0.20},
        "technical": {"grammar": 0.30, "style": 0.25, "structure": 0.25, "content": 0.20},
        "general": {"grammar": 0.25, "style": 0.25, "structure": 0.25, "content": 0.25}
    }'
);
```

## 6. Analytics and Usage Tables

### 5.1 Usage Tracking Table

```sql
CREATE TABLE usage_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    document_id UUID REFERENCES documents(id) ON DELETE SET NULL,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB DEFAULT '{}'::jsonb,
    tokens_used INTEGER DEFAULT 0,
    model_used VARCHAR(50),
    processing_time_ms INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT usage_events_tokens_positive CHECK (tokens_used >= 0),
    CONSTRAINT usage_events_processing_time_positive CHECK (processing_time_ms >= 0),
    CONSTRAINT usage_events_event_data_valid CHECK (jsonb_typeof(event_data) = 'object')
);

-- Indexes
CREATE INDEX idx_usage_events_user_date ON usage_events(user_id, created_at DESC);
CREATE INDEX idx_usage_events_document ON usage_events(document_id, created_at DESC);
CREATE INDEX idx_usage_events_type ON usage_events(event_type, created_at DESC);
CREATE INDEX idx_usage_events_tokens ON usage_events(tokens_used) WHERE tokens_used > 0;

-- Partitioning by month for scalability
CREATE TABLE usage_events_y2024m01 PARTITION OF usage_events
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Row Level Security
ALTER TABLE usage_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY usage_events_own_data ON usage_events
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );
```

### 5.2 Token Usage Aggregations

```sql
CREATE TABLE token_usage_daily (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    tokens_total INTEGER DEFAULT 0,
    tokens_by_model JSONB DEFAULT '{}'::jsonb,
    tokens_by_agent JSONB DEFAULT '{}'::jsonb,
    operations_count INTEGER DEFAULT 0,
    documents_edited INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT token_usage_daily_tokens_positive CHECK (tokens_total >= 0),
    CONSTRAINT token_usage_daily_operations_positive CHECK (operations_count >= 0),
    CONSTRAINT token_usage_daily_documents_positive CHECK (documents_edited >= 0),
    
    UNIQUE(user_id, date)
);

-- Indexes
CREATE INDEX idx_token_usage_daily_user_date ON token_usage_daily(user_id, date DESC);
CREATE INDEX idx_token_usage_daily_date ON token_usage_daily(date DESC);

-- Row Level Security
ALTER TABLE token_usage_daily ENABLE ROW LEVEL SECURITY;

CREATE POLICY token_usage_daily_own_data ON token_usage_daily
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );
```

## 6. Persona System Tables

### 6.1 Personas Table

```sql
CREATE TABLE personas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type persona_type_enum NOT NULL,
    demographics JSONB NOT NULL DEFAULT '{}'::jsonb,
    reading_preferences JSONB NOT NULL DEFAULT '{}'::jsonb,
    personality_traits JSONB NOT NULL DEFAULT '{}'::jsonb,
    feedback_style JSONB NOT NULL DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT TRUE,
    is_template BOOLEAN DEFAULT FALSE,
    usage_count INTEGER DEFAULT 0,
    effectiveness_score DECIMAL(3,2),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT personas_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT personas_demographics_valid CHECK (jsonb_typeof(demographics) = 'object'),
    CONSTRAINT personas_reading_prefs_valid CHECK (jsonb_typeof(reading_preferences) = 'object'),
    CONSTRAINT personas_personality_valid CHECK (jsonb_typeof(personality_traits) = 'object'),
    CONSTRAINT personas_feedback_style_valid CHECK (jsonb_typeof(feedback_style) = 'object'),
    CONSTRAINT personas_usage_count_positive CHECK (usage_count >= 0),
    CONSTRAINT personas_effectiveness_range CHECK (effectiveness_score IS NULL OR (effectiveness_score >= 0 AND effectiveness_score <= 1))
);

-- Enums
CREATE TYPE persona_type_enum AS ENUM (
    'academic', 'professional', 'creative', 'personal', 
    'technical', 'cultural', 'age_specific', 'accessibility'
);

-- Indexes
CREATE INDEX idx_personas_user ON personas(user_id) WHERE is_active = TRUE;
CREATE INDEX idx_personas_type ON personas(type) WHERE is_active = TRUE;
CREATE INDEX idx_personas_template ON personas(is_template) WHERE is_template = TRUE;
CREATE INDEX idx_personas_usage ON personas(usage_count DESC);
CREATE INDEX idx_personas_effectiveness ON personas(effectiveness_score DESC) WHERE effectiveness_score IS NOT NULL;

-- Row Level Security
ALTER TABLE personas ENABLE ROW LEVEL SECURITY;

CREATE POLICY personas_own_access ON personas
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub') OR
        is_template = TRUE
    );
```

### 6.2 Persona Feedback Table

```sql
CREATE TABLE persona_feedback (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    persona_id UUID REFERENCES personas(id) ON DELETE CASCADE,
    session_id UUID NOT NULL,
    comprehension_score DECIMAL(3,2) NOT NULL,
    engagement_score DECIMAL(3,2) NOT NULL,
    emotional_score DECIMAL(3,2) NOT NULL,
    style_score DECIMAL(3,2) NOT NULL,
    overall_score DECIMAL(3,2) NOT NULL,
    feedback_text TEXT NOT NULL,
    specific_issues JSONB DEFAULT '[]'::jsonb,
    suggestions JSONB DEFAULT '[]'::jsonb,
    confidence DECIMAL(3,2) NOT NULL,
    processing_time_ms INTEGER NOT NULL,
    model_used VARCHAR(50),
    tokens_used INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT persona_feedback_scores_range CHECK (
        comprehension_score >= 0 AND comprehension_score <= 1 AND
        engagement_score >= 0 AND engagement_score <= 1 AND
        emotional_score >= 0 AND emotional_score <= 1 AND
        style_score >= 0 AND style_score <= 1 AND
        overall_score >= 0 AND overall_score <= 1 AND
        confidence >= 0 AND confidence <= 1
    ),
    CONSTRAINT persona_feedback_processing_time_positive CHECK (processing_time_ms >= 0),
    CONSTRAINT persona_feedback_tokens_positive CHECK (tokens_used >= 0),
    CONSTRAINT persona_feedback_text_not_empty CHECK (LENGTH(TRIM(feedback_text)) > 0),
    CONSTRAINT persona_feedback_issues_valid CHECK (jsonb_typeof(specific_issues) = 'array'),
    CONSTRAINT persona_feedback_suggestions_valid CHECK (jsonb_typeof(suggestions) = 'array')
);

-- Indexes
CREATE INDEX idx_persona_feedback_block ON persona_feedback(block_id, created_at DESC);
CREATE INDEX idx_persona_feedback_persona ON persona_feedback(persona_id, created_at DESC);
CREATE INDEX idx_persona_feedback_session ON persona_feedback(session_id);
CREATE INDEX idx_persona_feedback_score ON persona_feedback(overall_score DESC);

-- Row Level Security
ALTER TABLE persona_feedback ENABLE ROW LEVEL SECURITY;

CREATE POLICY persona_feedback_block_access ON persona_feedback
    FOR ALL USING (
        block_id IN (
            SELECT id FROM blocks WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );
```

### 6.3 Audience Analysis Table

```sql
CREATE TABLE audience_analysis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    persona_ids UUID[] NOT NULL,
    overall_appeal_score DECIMAL(3,2) NOT NULL,
    cross_audience_conflicts JSONB DEFAULT '[]'::jsonb,
    optimization_suggestions JSONB DEFAULT '[]'::jsonb,
    audience_alignment JSONB NOT NULL,
    analysis_summary TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT audience_analysis_appeal_range CHECK (overall_appeal_score >= 0 AND overall_appeal_score <= 1),
    CONSTRAINT audience_analysis_conflicts_valid CHECK (jsonb_typeof(cross_audience_conflicts) = 'array'),
    CONSTRAINT audience_analysis_suggestions_valid CHECK (jsonb_typeof(optimization_suggestions) = 'array'),
    CONSTRAINT audience_analysis_alignment_valid CHECK (jsonb_typeof(audience_alignment) = 'object'),
    CONSTRAINT audience_analysis_personas_not_empty CHECK (array_length(persona_ids, 1) > 0)
);

-- Indexes
CREATE INDEX idx_audience_analysis_document ON audience_analysis(document_id, created_at DESC);
CREATE INDEX idx_audience_analysis_expires ON audience_analysis(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_audience_analysis_score ON audience_analysis(overall_appeal_score DESC);

-- Row Level Security
ALTER TABLE audience_analysis ENABLE ROW LEVEL SECURITY;

CREATE POLICY audience_analysis_document_access ON audience_analysis
    FOR ALL USING (
        document_id IN (
            SELECT id FROM documents WHERE owner_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            )
        )
    );
```

## 7. Custom Agent System Tables

### 7.1 Custom Agents Table

```sql
CREATE TABLE custom_agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type agent_type_enum NOT NULL,
    capabilities agent_capability_enum[] DEFAULT '{}',
    priority INTEGER DEFAULT 50,
    is_active BOOLEAN DEFAULT TRUE,
    is_template BOOLEAN DEFAULT FALSE,
    model_preference VARCHAR(50),
    max_context_tokens INTEGER DEFAULT 4000,
    temperature DECIMAL(3,2) DEFAULT 0.3,
    execution_timeout INTEGER DEFAULT 30000,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_used_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT custom_agents_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT custom_agents_priority_range CHECK (priority >= 1 AND priority <= 100),
    CONSTRAINT custom_agents_temperature_range CHECK (temperature >= 0 AND temperature <= 2),
    CONSTRAINT custom_agents_timeout_positive CHECK (execution_timeout > 0),
    CONSTRAINT custom_agents_context_tokens_positive CHECK (max_context_tokens > 0)
);

-- Enums
CREATE TYPE agent_type_enum AS ENUM (
    'grammar', 'style', 'structure', 'content', 'research', 'citation', 'technical', 'creative'
);

CREATE TYPE agent_capability_enum AS ENUM (
    'edit', 'add', 'delete', 'restructure', 'expand', 'condense',
    'find_duplicates', 'fact_check', 'consistency_check', 'citation_validation',
    'tone_adjustment', 'voice_consistency', 'readability_optimization'
);

-- Indexes
CREATE INDEX idx_custom_agents_user ON custom_agents(user_id) WHERE is_active = TRUE;
CREATE INDEX idx_custom_agents_type ON custom_agents(type) WHERE is_active = TRUE;
CREATE INDEX idx_custom_agents_priority ON custom_agents(priority DESC) WHERE is_active = TRUE;
CREATE INDEX idx_custom_agents_template ON custom_agents(is_template) WHERE is_template = TRUE;
CREATE INDEX idx_custom_agents_last_used ON custom_agents(last_used_at DESC) WHERE last_used_at IS NOT NULL;

-- Row Level Security
ALTER TABLE custom_agents ENABLE ROW LEVEL SECURITY;

CREATE POLICY custom_agents_own_access ON custom_agents
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub') OR
        is_template = TRUE
    );
```

### 7.2 Agent Style Rules Table

```sql
CREATE TABLE agent_style_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID REFERENCES custom_agents(id) ON DELETE CASCADE,
    category style_rule_category_enum NOT NULL,
    rule_name VARCHAR(255) NOT NULL,
    rule_value JSONB NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 50,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT agent_style_rules_name_not_empty CHECK (LENGTH(TRIM(rule_name)) > 0),
    CONSTRAINT agent_style_rules_value_valid CHECK (jsonb_typeof(rule_value) = 'object'),
    CONSTRAINT agent_style_rules_priority_range CHECK (priority >= 1 AND priority <= 100),
    
    UNIQUE(agent_id, category, rule_name)
);

-- Enums
CREATE TYPE style_rule_category_enum AS ENUM (
    'grammar', 'punctuation', 'capitalization', 'formatting',
    'style_preferences', 'vocabulary', 'sentence_structure', 'tone'
);

-- Indexes
CREATE INDEX idx_agent_style_rules_agent ON agent_style_rules(agent_id, priority DESC) WHERE is_enabled = TRUE;
CREATE INDEX idx_agent_style_rules_category ON agent_style_rules(category) WHERE is_enabled = TRUE;

-- Row Level Security
ALTER TABLE agent_style_rules ENABLE ROW LEVEL SECURITY;

CREATE POLICY agent_style_rules_agent_access ON agent_style_rules
    FOR ALL USING (
        agent_id IN (
            SELECT id FROM custom_agents WHERE user_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            ) OR is_template = TRUE
        )
    );
```

### 7.3 Agent Usage Statistics Table

```sql
CREATE TABLE agent_usage_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID REFERENCES custom_agents(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    invocations INTEGER DEFAULT 0,
    total_processing_time_ms BIGINT DEFAULT 0,
    suggestions_generated INTEGER DEFAULT 0,
    suggestions_accepted INTEGER DEFAULT 0,
    suggestions_rejected INTEGER DEFAULT 0,
    avg_confidence DECIMAL(3,2),
    error_count INTEGER DEFAULT 0,
    tokens_used INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT agent_usage_stats_counts_positive CHECK (
        invocations >= 0 AND total_processing_time_ms >= 0 AND
        suggestions_generated >= 0 AND suggestions_accepted >= 0 AND
        suggestions_rejected >= 0 AND error_count >= 0 AND tokens_used >= 0
    ),
    CONSTRAINT agent_usage_stats_confidence_range CHECK (
        avg_confidence IS NULL OR (avg_confidence >= 0 AND avg_confidence <= 1)
    ),
    
    UNIQUE(agent_id, user_id, date)
);

-- Indexes
CREATE INDEX idx_agent_usage_stats_agent_date ON agent_usage_stats(agent_id, date DESC);
CREATE INDEX idx_agent_usage_stats_user_date ON agent_usage_stats(user_id, date DESC);
CREATE INDEX idx_agent_usage_stats_date ON agent_usage_stats(date DESC);

-- Row Level Security
ALTER TABLE agent_usage_stats ENABLE ROW LEVEL SECURITY;

CREATE POLICY agent_usage_stats_own_data ON agent_usage_stats
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );
```

## 8. Writing Health Analysis Tables

### 8.1 Health Metrics Table

```sql
CREATE TABLE health_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    readability_score DECIMAL(5,2) NOT NULL,
    clarity_score DECIMAL(5,2) NOT NULL,
    voice_consistency_score DECIMAL(5,2) NOT NULL,
    brand_alignment_score DECIMAL(5,2),
    overall_score DECIMAL(5,2) NOT NULL,
    flesch_kincaid_grade DECIMAL(4,2),
    flesch_reading_ease DECIMAL(5,2),
    passive_voice_percentage DECIMAL(4,2),
    avg_sentence_length DECIMAL(5,2),
    complex_words_percentage DECIMAL(4,2),
    processing_time_ms INTEGER NOT NULL,
    analysis_version VARCHAR(10) DEFAULT '1.0',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT health_metrics_scores_range CHECK (
        readability_score >= 0 AND readability_score <= 100 AND
        clarity_score >= 0 AND clarity_score <= 100 AND
        voice_consistency_score >= 0 AND voice_consistency_score <= 100 AND
        overall_score >= 0 AND overall_score <= 100
    ),
    CONSTRAINT health_metrics_brand_range CHECK (
        brand_alignment_score IS NULL OR (brand_alignment_score >= 0 AND brand_alignment_score <= 100)
    ),
    CONSTRAINT health_metrics_processing_time_positive CHECK (processing_time_ms >= 0)
);

-- Indexes
CREATE INDEX idx_health_metrics_block ON health_metrics(block_id, created_at DESC);
CREATE INDEX idx_health_metrics_user ON health_metrics(user_id, created_at DESC);
CREATE INDEX idx_health_metrics_overall_score ON health_metrics(overall_score DESC);
CREATE INDEX idx_health_metrics_created ON health_metrics(created_at DESC);

-- Row Level Security
ALTER TABLE health_metrics ENABLE ROW LEVEL SECURITY;

CREATE POLICY health_metrics_own_data ON health_metrics
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );
```

### 8.2 Health Issues Table

```sql
CREATE TABLE health_issues (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    health_metric_id UUID REFERENCES health_metrics(id) ON DELETE CASCADE,
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    issue_type health_issue_type_enum NOT NULL,
    severity health_issue_severity_enum NOT NULL,
    category VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    position JSONB NOT NULL,
    suggested_fix TEXT,
    confidence DECIMAL(3,2) NOT NULL,
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMPTZ,
    resolved_method VARCHAR(50),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT health_issues_description_not_empty CHECK (LENGTH(TRIM(description)) > 0),
    CONSTRAINT health_issues_position_valid CHECK (
        jsonb_typeof(position) = 'object' AND
        position ? 'start' AND position ? 'end'
    ),
    CONSTRAINT health_issues_confidence_range CHECK (confidence >= 0 AND confidence <= 1)
);

-- Enums
CREATE TYPE health_issue_type_enum AS ENUM (
    'readability', 'clarity', 'voice', 'brand', 'grammar', 'style'
);

CREATE TYPE health_issue_severity_enum AS ENUM ('low', 'medium', 'high', 'critical');

-- Indexes
CREATE INDEX idx_health_issues_block ON health_issues(block_id, severity, created_at DESC);
CREATE INDEX idx_health_issues_type ON health_issues(issue_type, severity);
CREATE INDEX idx_health_issues_unresolved ON health_issues(block_id) WHERE is_resolved = FALSE;
CREATE INDEX idx_health_issues_severity ON health_issues(severity, created_at DESC);

-- Row Level Security
ALTER TABLE health_issues ENABLE ROW LEVEL SECURITY;

CREATE POLICY health_issues_block_access ON health_issues
    FOR ALL USING (
        block_id IN (
            SELECT id FROM blocks WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );
```

## 9. Cross-Reference Intelligence Tables

### 9.1 Entities Table

```sql
CREATE TABLE entities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(500) NOT NULL,
    entity_type entity_type_enum NOT NULL,
    aliases TEXT[] DEFAULT '{}',
    description TEXT,
    attributes JSONB DEFAULT '{}'::jsonb,
    physical_attributes JSONB DEFAULT '{}'::jsonb,
    personality_traits JSONB DEFAULT '{}'::jsonb,
    first_mentioned_block_id UUID REFERENCES blocks(id),
    last_mentioned_block_id UUID REFERENCES blocks(id),
    mention_count INTEGER DEFAULT 1,
    importance_score DECIMAL(3,2) DEFAULT 0.5,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT entities_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT entities_attributes_valid CHECK (jsonb_typeof(attributes) = 'object'),
    CONSTRAINT entities_physical_valid CHECK (jsonb_typeof(physical_attributes) = 'object'),
    CONSTRAINT entities_personality_valid CHECK (jsonb_typeof(personality_traits) = 'object'),
    CONSTRAINT entities_mention_count_positive CHECK (mention_count >= 0),
    CONSTRAINT entities_importance_range CHECK (importance_score >= 0 AND importance_score <= 1)
);

-- Enums
CREATE TYPE entity_type_enum AS ENUM (
    'character', 'location', 'organization', 'concept', 'object', 'event', 'theme'
);

-- Indexes
CREATE INDEX idx_entities_document ON entities(document_id, entity_type);
CREATE INDEX idx_entities_user ON entities(user_id, created_at DESC);
CREATE INDEX idx_entities_name ON entities USING gin(to_tsvector('english', name));
CREATE INDEX idx_entities_aliases ON entities USING gin(aliases);
CREATE INDEX idx_entities_importance ON entities(importance_score DESC);
CREATE INDEX idx_entities_mentions ON entities(mention_count DESC);

-- Row Level Security
ALTER TABLE entities ENABLE ROW LEVEL SECURITY;

CREATE POLICY entities_document_access ON entities
    FOR ALL USING (
        document_id IN (
            SELECT id FROM documents WHERE owner_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            )
        )
    );
```

### 9.2 Entity Relationships Table

```sql
CREATE TABLE entity_relationships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_entity_id UUID REFERENCES entities(id) ON DELETE CASCADE,
    target_entity_id UUID REFERENCES entities(id) ON DELETE CASCADE,
    relationship_type relationship_type_enum NOT NULL,
    relationship_name VARCHAR(255),
    description TEXT,
    strength DECIMAL(3,2) DEFAULT 0.5,
    is_bidirectional BOOLEAN DEFAULT FALSE,
    context_block_id UUID REFERENCES blocks(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT entity_relationships_different CHECK (source_entity_id != target_entity_id),
    CONSTRAINT entity_relationships_strength_range CHECK (strength >= 0 AND strength <= 1)
);

-- Enums
CREATE TYPE relationship_type_enum AS ENUM (
    'family', 'friendship', 'professional', 'romantic', 'antagonistic',
    'hierarchical', 'geographic', 'temporal', 'causal', 'thematic'
);

-- Indexes
CREATE INDEX idx_entity_relationships_source ON entity_relationships(source_entity_id, relationship_type);
CREATE INDEX idx_entity_relationships_target ON entity_relationships(target_entity_id, relationship_type);
CREATE INDEX idx_entity_relationships_strength ON entity_relationships(strength DESC);
CREATE UNIQUE INDEX idx_entity_relationships_unique ON entity_relationships(source_entity_id, target_entity_id, relationship_type);

-- Row Level Security
ALTER TABLE entity_relationships ENABLE ROW LEVEL SECURITY;

CREATE POLICY entity_relationships_entity_access ON entity_relationships
    FOR ALL USING (
        source_entity_id IN (
            SELECT id FROM entities WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );
```

### 9.3 Consistency Violations Table

```sql
CREATE TABLE consistency_violations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    entity_id UUID REFERENCES entities(id) ON DELETE CASCADE,
    violation_type violation_type_enum NOT NULL,
    severity consistency_severity_enum NOT NULL,
    description TEXT NOT NULL,
    conflicting_blocks UUID[] NOT NULL,
    suggested_resolution TEXT,
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMPTZ,
    resolved_by UUID REFERENCES users(id),
    resolution_method VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT consistency_violations_description_not_empty CHECK (LENGTH(TRIM(description)) > 0),
    CONSTRAINT consistency_violations_blocks_not_empty CHECK (array_length(conflicting_blocks, 1) > 0)
);

-- Enums
CREATE TYPE violation_type_enum AS ENUM (
    'attribute_conflict', 'timeline_conflict', 'location_conflict',
    'relationship_conflict', 'personality_conflict', 'physical_impossibility'
);

CREATE TYPE consistency_severity_enum AS ENUM ('minor', 'moderate', 'major', 'critical');

-- Indexes
CREATE INDEX idx_consistency_violations_document ON consistency_violations(document_id, severity, created_at DESC);
CREATE INDEX idx_consistency_violations_entity ON consistency_violations(entity_id, severity);
CREATE INDEX idx_consistency_violations_unresolved ON consistency_violations(document_id) WHERE is_resolved = FALSE;
CREATE INDEX idx_consistency_violations_type ON consistency_violations(violation_type, severity);

-- Row Level Security
ALTER TABLE consistency_violations ENABLE ROW LEVEL SECURITY;

CREATE POLICY consistency_violations_document_access ON consistency_violations
    FOR ALL USING (
        document_id IN (
            SELECT id FROM documents WHERE owner_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            )
        )
    );
```

## 10. Citation Management Tables

### 10.1 Citations Table

```sql
CREATE TABLE citations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    citation_key VARCHAR(255) NOT NULL,
    citation_type citation_type_enum NOT NULL,
    title TEXT NOT NULL,
    authors TEXT[] NOT NULL,
    publication_year INTEGER,
    publisher VARCHAR(500),
    doi VARCHAR(255),
    url TEXT,
    page_numbers VARCHAR(50),
    volume VARCHAR(50),
    issue VARCHAR(50),
    journal_name VARCHAR(500),
    isbn VARCHAR(20),
    raw_citation TEXT,
    formatted_citation TEXT,
    citation_style citation_style_enum DEFAULT 'apa',
    access_date DATE,
    position JSONB,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_date TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT citations_key_not_empty CHECK (LENGTH(TRIM(citation_key)) > 0),
    CONSTRAINT citations_title_not_empty CHECK (LENGTH(TRIM(title)) > 0),
    CONSTRAINT citations_authors_not_empty CHECK (array_length(authors, 1) > 0),
    CONSTRAINT citations_year_reasonable CHECK (publication_year IS NULL OR (publication_year >= 1000 AND publication_year <= EXTRACT(YEAR FROM NOW()) + 5)),
    CONSTRAINT citations_position_valid CHECK (
        position IS NULL OR (
            jsonb_typeof(position) = 'object' AND
            position ? 'start' AND position ? 'end'
        )
    ),
    
    UNIQUE(document_id, citation_key)
);

-- Enums
CREATE TYPE citation_type_enum AS ENUM (
    'book', 'journal_article', 'conference_paper', 'thesis', 'report',
    'website', 'news_article', 'blog_post', 'video', 'podcast', 'other'
);

CREATE TYPE citation_style_enum AS ENUM ('apa', 'mla', 'chicago', 'harvard', 'ieee', 'vancouver');

-- Indexes
CREATE INDEX idx_citations_document ON citations(document_id, created_at DESC);
CREATE INDEX idx_citations_block ON citations(block_id);
CREATE INDEX idx_citations_key ON citations(citation_key);
CREATE INDEX idx_citations_type ON citations(citation_type);
CREATE INDEX idx_citations_authors ON citations USING gin(authors);
CREATE INDEX idx_citations_title_search ON citations USING gin(to_tsvector('english', title));
CREATE INDEX idx_citations_unverified ON citations(document_id) WHERE is_verified = FALSE;

-- Row Level Security
ALTER TABLE citations ENABLE ROW LEVEL SECURITY;

CREATE POLICY citations_document_access ON citations
    FOR ALL USING (
        document_id IN (
            SELECT id FROM documents WHERE owner_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            )
        )
    );
```

### 10.2 Reference Library Table

```sql
CREATE TABLE reference_library (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    authors TEXT[] NOT NULL,
    publication_year INTEGER,
    citation_type citation_type_enum NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    tags TEXT[] DEFAULT '{}',
    notes TEXT,
    file_url TEXT,
    doi VARCHAR(255),
    url TEXT,
    is_favorite BOOLEAN DEFAULT FALSE,
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT reference_library_title_not_empty CHECK (LENGTH(TRIM(title)) > 0),
    CONSTRAINT reference_library_authors_not_empty CHECK (array_length(authors, 1) > 0),
    CONSTRAINT reference_library_metadata_valid CHECK (jsonb_typeof(metadata) = 'object'),
    CONSTRAINT reference_library_usage_positive CHECK (usage_count >= 0)
);

-- Indexes
CREATE INDEX idx_reference_library_user ON reference_library(user_id, created_at DESC);
CREATE INDEX idx_reference_library_type ON reference_library(citation_type);
CREATE INDEX idx_reference_library_authors ON reference_library USING gin(authors);
CREATE INDEX idx_reference_library_tags ON reference_library USING gin(tags);
CREATE INDEX idx_reference_library_title_search ON reference_library USING gin(to_tsvector('english', title));
CREATE INDEX idx_reference_library_favorites ON reference_library(user_id) WHERE is_favorite = TRUE;
CREATE INDEX idx_reference_library_usage ON reference_library(usage_count DESC);

-- Row Level Security
ALTER TABLE reference_library ENABLE ROW LEVEL SECURITY;

CREATE POLICY reference_library_own_data ON reference_library
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );
```

## 11. Export and Templates

### 6.1 Export Jobs Table

```sql
CREATE TABLE export_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    format export_format_enum NOT NULL,
    status export_status_enum DEFAULT 'pending',
    options JSONB DEFAULT '{}'::jsonb,
    file_url TEXT,
    file_size INTEGER,
    error_message TEXT,
    processing_started_at TIMESTAMPTZ,
    processing_completed_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT export_jobs_file_size_positive CHECK (file_size >= 0),
    CONSTRAINT export_jobs_options_valid CHECK (jsonb_typeof(options) = 'object')
);

-- Enums
CREATE TYPE export_format_enum AS ENUM ('pdf', 'docx', 'markdown', 'html', 'epub', 'latex');
CREATE TYPE export_status_enum AS ENUM ('pending', 'processing', 'completed', 'failed', 'expired');

-- Indexes
CREATE INDEX idx_export_jobs_user ON export_jobs(user_id, created_at DESC);
CREATE INDEX idx_export_jobs_document ON export_jobs(document_id, created_at DESC);
CREATE INDEX idx_export_jobs_status ON export_jobs(status);
CREATE INDEX idx_export_jobs_expires ON export_jobs(expires_at) WHERE expires_at IS NOT NULL;

-- Row Level Security
ALTER TABLE export_jobs ENABLE ROW LEVEL SECURITY;

CREATE POLICY export_jobs_own_data ON export_jobs
    FOR ALL USING (
        user_id IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );
```

### 6.2 Templates Table

```sql
CREATE TABLE templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category template_category_enum NOT NULL,
    document_type document_type_enum NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES users(id),
    structure JSONB NOT NULL, -- Template structure definition
    metadata JSONB DEFAULT '{}'::jsonb,
    usage_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2),
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT templates_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT templates_structure_valid CHECK (jsonb_typeof(structure) = 'object'),
    CONSTRAINT templates_metadata_valid CHECK (jsonb_typeof(metadata) = 'object'),
    CONSTRAINT templates_usage_count_positive CHECK (usage_count >= 0),
    CONSTRAINT templates_rating_range CHECK (rating IS NULL OR (rating >= 0 AND rating <= 5))
);

-- Enums
CREATE TYPE template_category_enum AS ENUM (
    'novel', 'short_story', 'screenplay', 'research_paper', 'essay', 
    'business_plan', 'report', 'letter', 'blog_post', 'article'
);

-- Indexes
CREATE INDEX idx_templates_category ON templates(category) WHERE is_public = TRUE;
CREATE INDEX idx_templates_type ON templates(document_type) WHERE is_public = TRUE;
CREATE INDEX idx_templates_created_by ON templates(created_by);
CREATE INDEX idx_templates_usage ON templates(usage_count DESC) WHERE is_public = TRUE;
CREATE INDEX idx_templates_rating ON templates(rating DESC) WHERE is_public = TRUE AND rating IS NOT NULL;
CREATE INDEX idx_templates_tags ON templates USING gin(tags);

-- Row Level Security
ALTER TABLE templates ENABLE ROW LEVEL SECURITY;

CREATE POLICY templates_public_read ON templates
    FOR SELECT USING (is_public = TRUE);

CREATE POLICY templates_own_access ON templates
    FOR ALL USING (
        created_by IN (SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub')
    );
```

## 7. Functions and Triggers

### 7.1 Update Timestamps

```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply to all tables with updated_at column
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blocks_updated_at BEFORE UPDATE ON blocks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_collaborations_updated_at BEFORE UPDATE ON collaborations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_comments_updated_at BEFORE UPDATE ON comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_templates_updated_at BEFORE UPDATE ON templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 7.2 Document Statistics

```sql
CREATE OR REPLACE FUNCTION update_document_stats()
RETURNS TRIGGER AS $$
DECLARE
    doc_id UUID;
    total_words INTEGER;
    total_chars INTEGER;
BEGIN
    -- Get document ID from the block
    IF TG_OP = 'DELETE' THEN
        doc_id := OLD.document_id;
    ELSE
        doc_id := NEW.document_id;
    END IF;
    
    -- Calculate totals
    SELECT 
        COALESCE(SUM(word_count), 0),
        COALESCE(SUM(content_length), 0)
    INTO total_words, total_chars
    FROM blocks 
    WHERE document_id = doc_id 
    AND deleted_at IS NULL 
    AND type = 'paragraph';
    
    -- Update document
    UPDATE documents 
    SET 
        word_count = total_words,
        character_count = total_chars,
        updated_at = NOW()
    WHERE id = doc_id;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_document_stats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON blocks
    FOR EACH ROW EXECUTE FUNCTION update_document_stats();
```

### 7.3 Content Hash Generation

```sql
CREATE OR REPLACE FUNCTION generate_content_hash()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.content IS NOT NULL THEN
        NEW.content_hash = encode(sha256(NEW.content::bytea), 'hex');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER generate_content_hash_trigger
    BEFORE INSERT OR UPDATE OF content ON blocks
    FOR EACH ROW EXECUTE FUNCTION generate_content_hash();

CREATE TRIGGER generate_version_hash_trigger
    BEFORE INSERT ON versions
    FOR EACH ROW EXECUTE FUNCTION generate_content_hash();
```

### 7.4 Expire Stale Suggestions

```sql
CREATE OR REPLACE FUNCTION expire_stale_suggestions()
RETURNS TRIGGER AS $$
BEGIN
    -- Mark suggestions as stale when block content changes
    UPDATE suggestions 
    SET 
        status = 'stale',
        resolved_at = NOW()
    WHERE 
        block_id = NEW.id 
        AND status = 'pending'
        AND original_hash != NEW.content_hash;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER expire_stale_suggestions_trigger
    AFTER UPDATE OF content_hash ON blocks
    FOR EACH ROW EXECUTE FUNCTION expire_stale_suggestions();
```

## 8. Views for Common Queries

### 12.3 Enhanced Views for New Features

#### Document Overview View (Enhanced)

```sql
CREATE VIEW document_overview AS
SELECT 
    d.id,
    d.title,
    d.type,
    d.status,
    d.word_count,
    d.created_at,
    d.updated_at,
    u.display_name as owner_name,
    u.email as owner_email,
    COUNT(DISTINCT c.id) as collaborator_count,
    COUNT(DISTINCT com.id) as comment_count,
    COUNT(DISTINCT s.id) FILTER (WHERE s.status = 'pending') as pending_suggestions,
    COUNT(DISTINCT e.id) as entity_count,
    COUNT(DISTINCT cv.id) FILTER (WHERE cv.is_resolved = FALSE) as consistency_violations,
    COUNT(DISTINCT cit.id) as citation_count,
    AVG(hm.overall_score) as avg_health_score,
    COUNT(DISTINCT pf.persona_id) as active_personas
FROM documents d
JOIN users u ON d.owner_id = u.id
LEFT JOIN collaborations c ON d.id = c.document_id AND c.status = 'active'
LEFT JOIN blocks b ON d.id = b.document_id AND b.deleted_at IS NULL
LEFT JOIN comments com ON b.id = com.block_id AND com.deleted_at IS NULL
LEFT JOIN suggestions s ON b.id = s.block_id
LEFT JOIN entities e ON d.id = e.document_id
LEFT JOIN consistency_violations cv ON d.id = cv.document_id
LEFT JOIN citations cit ON d.id = cit.document_id
LEFT JOIN health_metrics hm ON b.id = hm.block_id
LEFT JOIN persona_feedback pf ON b.id = pf.block_id
WHERE d.deleted_at IS NULL
GROP BY d.id, u.id;
```

#### Persona Effectiveness View

```sql
CREATE VIEW persona_effectiveness AS
SELECT 
    p.id,
    p.name,
    p.type,
    p.usage_count,
    AVG(pf.overall_score) as avg_feedback_score,
    AVG(pf.confidence) as avg_confidence,
    COUNT(DISTINCT pf.block_id) as blocks_analyzed,
    COUNT(DISTINCT pf.session_id) as sessions_used,
    p.effectiveness_score,
    p.created_at
FROM personas p
LEFT JOIN persona_feedback pf ON p.id = pf.persona_id
WHERE p.is_active = TRUE
GROUP BY p.id;
```

#### Agent Performance View

```sql
CREATE VIEW agent_performance AS
SELECT 
    ca.id,
    ca.name,
    ca.type,
    ca.user_id,
    SUM(aus.invocations) as total_invocations,
    AVG(aus.avg_confidence) as avg_confidence,
    SUM(aus.suggestions_accepted)::float / NULLIF(SUM(aus.suggestions_generated), 0) as acceptance_rate,
    AVG(aus.total_processing_time_ms::float / NULLIF(aus.invocations, 0)) as avg_processing_time,
    SUM(aus.tokens_used) as total_tokens_used,
    ca.last_used_at
FROM custom_agents ca
LEFT JOIN agent_usage_stats aus ON ca.id = aus.agent_id
WHERE ca.is_active = TRUE
GROUP BY ca.id;
```

#### Health Trends View

```sql
CREATE VIEW health_trends AS
SELECT 
    hm.user_id,
    DATE(hm.created_at) as analysis_date,
    AVG(hm.overall_score) as avg_score,
    AVG(hm.readability_score) as avg_readability,
    AVG(hm.clarity_score) as avg_clarity,
    AVG(hm.voice_consistency_score) as avg_voice,
    COUNT(*) as analyses_count
FROM health_metrics hm
WHERE hm.created_at >= NOW() - INTERVAL '90 days'
GROUP BY hm.user_id, DATE(hm.created_at)
ORDER BY hm.user_id, analysis_date;
```

#### Enhanced User Analytics View

```sql
CREATE VIEW user_analytics AS
SELECT 
    u.id as user_id,
    u.display_name,
    u.subscription_tier,
    COUNT(DISTINCT d.id) as document_count,
    SUM(d.word_count) as total_words,
    COUNT(DISTINCT ue.id) FILTER (WHERE ue.created_at >= NOW() - INTERVAL '30 days') as events_last_30_days,
    SUM(ue.tokens_used) FILTER (WHERE ue.created_at >= NOW() - INTERVAL '30 days') as tokens_last_30_days,
    COUNT(DISTINCT p.id) as persona_count,
    COUNT(DISTINCT ca.id) as custom_agent_count,
    COUNT(DISTINCT e.id) as entity_count,
    AVG(hm.overall_score) as avg_health_score,
    COUNT(DISTINCT ua.achievement_id) as achievements_unlocked,
    MAX(u.last_active_at) as last_active
FROM users u
LEFT JOIN documents d ON u.id = d.owner_id AND d.deleted_at IS NULL
LEFT JOIN usage_events ue ON u.id = ue.user_id
LEFT JOIN personas p ON u.id = p.user_id AND p.is_active = TRUE
LEFT JOIN custom_agents ca ON u.id = ca.user_id AND ca.is_active = TRUE
LEFT JOIN entities e ON u.id = e.user_id
LEFT JOIN health_metrics hm ON u.id = hm.user_id
LEFT JOIN user_achievements ua ON u.id = ua.user_id
WHERE u.deleted_at IS NULL
GROUP BY u.id;
```

## 12. Maintenance and Optimization

### 12.1 Cleanup Jobs

```sql
-- Clean up expired suggestions
CREATE OR REPLACE FUNCTION cleanup_expired_suggestions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    UPDATE suggestions 
    SET status = 'expired'
    WHERE expires_at < NOW() AND status = 'pending';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Clean up old export jobs
CREATE OR REPLACE FUNCTION cleanup_old_exports()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM export_jobs 
    WHERE expires_at < NOW() OR 
          (status = 'completed' AND created_at < NOW() - INTERVAL '7 days');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Clean up old persona feedback
CREATE OR REPLACE FUNCTION cleanup_old_persona_feedback()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM persona_feedback 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Clean up old health metrics
CREATE OR REPLACE FUNCTION cleanup_old_health_metrics()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Keep only the latest 10 health metrics per block
    DELETE FROM health_metrics 
    WHERE id NOT IN (
        SELECT id FROM (
            SELECT id, ROW_NUMBER() OVER (PARTITION BY block_id ORDER BY created_at DESC) as rn
            FROM health_metrics
        ) ranked WHERE rn <= 10
    );
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Clean up expired audience analysis
CREATE OR REPLACE FUNCTION cleanup_expired_audience_analysis()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM audience_analysis 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Clean up resolved consistency violations
CREATE OR REPLACE FUNCTION cleanup_old_consistency_violations()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM consistency_violations 
    WHERE is_resolved = TRUE AND resolved_at < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;
```

### 12.2 Performance Monitoring

```sql
-- Index usage statistics
CREATE VIEW index_usage_stats AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_tup_read::float / GREATEST(idx_tup_fetch, 1) as selectivity
FROM pg_stat_user_indexes 
ORDER BY idx_tup_read DESC;

-- Table size statistics
CREATE VIEW table_sizes AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY size_bytes DESC;

-- Feature usage analytics
CREATE VIEW feature_usage_summary AS
SELECT 
    'personas' as feature,
    COUNT(DISTINCT p.user_id) as active_users,
    COUNT(*) as total_records,
    MAX(p.created_at) as last_activity
FROM personas p WHERE p.is_active = TRUE
UNION ALL
SELECT 
    'custom_agents' as feature,
    COUNT(DISTINCT ca.user_id) as active_users,
    COUNT(*) as total_records,
    MAX(ca.last_used_at) as last_activity
FROM custom_agents ca WHERE ca.is_active = TRUE
UNION ALL
SELECT 
    'health_analysis' as feature,
    COUNT(DISTINCT hm.user_id) as active_users,
    COUNT(*) as total_records,
    MAX(hm.created_at) as last_activity
FROM health_metrics hm
UNION ALL
SELECT 
    'cross_reference' as feature,
    COUNT(DISTINCT e.user_id) as active_users,
    COUNT(*) as total_records,
    MAX(e.updated_at) as last_activity
FROM entities e
UNION ALL
SELECT 
    'citations' as feature,
    COUNT(DISTINCT c.document_id) as active_documents,
    COUNT(*) as total_records,
    MAX(c.created_at) as last_activity
FROM citations c;
```

## 10. Data Migration Scripts

### 10.1 Initial Setup

```sql
-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "ltree";
CREATE EXTENSION IF NOT EXISTS "vector";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create all enums, tables, indexes, and policies
-- (All the CREATE statements from above sections)

-- Insert default data
INSERT INTO templates (name, description, category, document_type, is_public, structure) VALUES
('Novel Template', 'Standard three-act novel structure', 'novel', 'creative', true, 
 '{"chapters": [{"title": "Chapter 1", "scenes": []}]}');

INSERT INTO templates (name, description, category, document_type, is_public, structure) VALUES
('Research Paper', 'Academic research paper template', 'research_paper', 'academic', true,
 '{"sections": ["Abstract", "Introduction", "Methodology", "Results", "Discussion", "Conclusion"]}');
```

### 10.2 Version Updates

```sql
-- Migration for adding new fields (example)
ALTER TABLE documents ADD COLUMN IF NOT EXISTS language VARCHAR(10) DEFAULT 'en';
ALTER TABLE documents ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}';

-- Update existing data
UPDATE documents SET language = 'en' WHERE language IS NULL;

-- Create new indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_documents_language ON documents(language);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_documents_tags ON documents USING gin(tags);
```

This comprehensive database schema provides a solid foundation for the Revisionary application with proper normalization, performance optimization, security, and scalability considerations.