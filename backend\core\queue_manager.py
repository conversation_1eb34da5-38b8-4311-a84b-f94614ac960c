"""
Queue Manager for Revisionary

Purpose: Centralized job queue management and worker coordination
- Redis Streams for job distribution (budget-friendly)
- Priority-based job routing
- Worker scaling and monitoring
- Job lifecycle management

Features:
- Multi-agent job distribution
- Priority queues by user tier
- Auto-scaling worker management
- Job status tracking
- Dead letter queue handling
"""

import os
import json
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from enum import Enum
import structlog

from core.redis_service import get_redis
from core.settings import settings

logger = structlog.get_logger(__name__)


class JobPriority(Enum):
    """Job priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


class UserTierPriority:
    """Map user tiers to job priorities."""
    TIER_PRIORITY = {
        'free': JobPriority.LOW,
        'professional': JobPriority.NORMAL,
        'studio': JobPriority.HIGH,
        'enterprise': JobPriority.CRITICAL
    }


class QueueManager:
    """Centralized queue management for all worker types."""
    
    def __init__(self):
        """Initialize queue manager."""
        self.agent_types = ['grammar', 'style', 'structure', 'content', 'health']
        self.redis = None
        
        # Worker limits per agent type
        self.worker_limits = {
            'grammar': int(os.getenv('MAX_WORKERS_GRAMMAR', '3')),
            'style': int(os.getenv('MAX_WORKERS_STYLE', '2')),
            'structure': int(os.getenv('MAX_WORKERS_STRUCTURE', '1')),
            'content': int(os.getenv('MAX_WORKERS_CONTENT', '1')),
            'health': int(os.getenv('MAX_WORKERS_HEALTH', '2'))
        }
        
        # Scaling thresholds
        self.scale_up_threshold = int(os.getenv('WORKER_SCALE_UP_THRESHOLD', '50'))
        self.scale_down_threshold = int(os.getenv('WORKER_SCALE_DOWN_THRESHOLD', '10'))
        
        # Job timeout settings
        self.job_timeout = int(os.getenv('WORKER_JOB_TIMEOUT', '30'))  # seconds
        self.max_retries = int(os.getenv('JOB_MAX_RETRIES', '3'))
        
        logger.info("Queue Manager initialized", 
                   worker_limits=self.worker_limits,
                   scale_thresholds={
                       'up': self.scale_up_threshold,
                       'down': self.scale_down_threshold
                   })
    
    async def initialize(self):
        """Initialize async resources."""
        self.redis = await get_redis()
        
        # Create streams and consumer groups for each agent type
        for agent_type in self.agent_types:
            await self._setup_agent_queue(agent_type)
        
        logger.info("Queue Manager async resources initialized")
    
    async def _setup_agent_queue(self, agent_type: str):
        """Set up queue and consumer group for an agent type."""
        queue_name = f"queue:{agent_type}"
        consumer_group = f"{agent_type}_workers"
        
        try:
            # Create stream if it doesn't exist
            await self.redis.redis_client.xgroup_create(
                queue_name,
                consumer_group,
                id='0',
                mkstream=True
            )
            logger.info(f"Set up queue for {agent_type}")
        except Exception as e:
            if "BUSYGROUP" not in str(e):
                logger.error(f"Failed to setup queue for {agent_type}: {e}")
                raise
    
    async def enqueue_job(
        self,
        agent_type: str,
        job_data: Dict[str, Any],
        user_tier: str = 'free',
        user_id: Optional[str] = None,
        priority: Optional[JobPriority] = None
    ) -> str:
        """
        Enqueue a job for processing.
        
        Args:
            agent_type: Type of agent (grammar, style, structure, content)
            job_data: Job data dictionary
            user_tier: User's subscription tier
            user_id: Optional user ID
            priority: Optional explicit priority
            
        Returns:
            Job ID
        """
        if not self.redis:
            await self.initialize()
        
        # Validate agent type
        if agent_type not in self.agent_types:
            raise ValueError(f"Unknown agent type: {agent_type}")
        
        # Determine priority
        if priority is None:
            priority = UserTierPriority.TIER_PRIORITY.get(user_tier, JobPriority.LOW)
        
        # Generate job ID
        job_id = f"{agent_type}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}"
        
        # Prepare job payload
        job_payload = {
            'job_id': job_id,
            'agent_type': agent_type,
            'user_tier': user_tier,
            'user_id': user_id,
            'priority': priority.value,
            'created_at': datetime.utcnow().isoformat(),
            'retry_count': 0,
            'max_retries': self.max_retries,
            **job_data
        }
        
        # Add to appropriate queue based on priority
        queue_name = f"queue:{agent_type}"
        if priority in [JobPriority.HIGH, JobPriority.CRITICAL]:
            queue_name = f"queue:{agent_type}:priority"
        
        # Add job to stream
        message_id = await self.redis.redis_client.xadd(
            queue_name,
            {'data': json.dumps(job_payload)},
            maxlen=10000  # Prevent unbounded growth
        )
        
        # Track job in status table
        await self._track_job_status(job_id, 'queued', job_payload)
        
        logger.info(
            f"Job enqueued",
            job_id=job_id,
            agent_type=agent_type,
            user_tier=user_tier,
            priority=priority.name,
            queue=queue_name
        )
        
        # Check if we need to scale workers
        await self._check_worker_scaling(agent_type)
        
        return job_id
    
    async def enqueue_multi_agent_job(
        self,
        agent_types: List[str],
        job_data: Dict[str, Any],
        user_tier: str = 'free',
        user_id: Optional[str] = None
    ) -> List[str]:
        """
        Enqueue a job for multiple agents.
        
        Args:
            agent_types: List of agent types to process the job
            job_data: Job data dictionary
            user_tier: User's subscription tier
            user_id: Optional user ID
            
        Returns:
            List of job IDs
        """
        job_ids = []
        
        # Create parent job ID for tracking
        parent_job_id = f"multi_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}"
        
        # Enqueue job for each agent
        for agent_type in agent_types:
            agent_job_data = {
                **job_data,
                'parent_job_id': parent_job_id,
                'multi_agent_job': True
            }
            
            job_id = await self.enqueue_job(
                agent_type=agent_type,
                job_data=agent_job_data,
                user_tier=user_tier,
                user_id=user_id
            )
            
            job_ids.append(job_id)
        
        # Track multi-agent job
        await self._track_multi_agent_job(parent_job_id, job_ids, user_id)
        
        logger.info(
            f"Multi-agent job enqueued",
            parent_job_id=parent_job_id,
            agent_types=agent_types,
            job_ids=job_ids
        )
        
        return job_ids
    
    async def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific job."""
        if not self.redis:
            await self.initialize()
        
        status_key = f"job:status:{job_id}"
        status_data = await self.redis.get(status_key)
        
        if status_data:
            return json.loads(status_data)
        
        return None
    
    async def get_queue_stats(self, agent_type: str) -> Dict[str, Any]:
        """Get statistics for a specific agent queue."""
        if not self.redis:
            await self.initialize()
        
        queue_name = f"queue:{agent_type}"
        priority_queue_name = f"queue:{agent_type}:priority"
        
        # Get queue lengths
        queue_length = await self.redis.redis_client.xlen(queue_name)
        priority_queue_length = await self.redis.redis_client.xlen(priority_queue_name)
        
        # Get worker stats
        worker_stats = await self._get_worker_stats(agent_type)
        
        # Get processing stats
        processed_count = await self.redis.get(f"worker:stats:{agent_type}:processed") or 0
        failed_count = await self.redis.get(f"worker:stats:{agent_type}:failed") or 0
        avg_time = await self.redis.get(f"worker:stats:{agent_type}:avg_time") or 0
        
        return {
            'agent_type': agent_type,
            'queue_length': queue_length,
            'priority_queue_length': priority_queue_length,
            'total_pending': queue_length + priority_queue_length,
            'workers': worker_stats,
            'processing_stats': {
                'processed': int(processed_count),
                'failed': int(failed_count),
                'average_time': float(avg_time),
                'success_rate': (int(processed_count) / max(int(processed_count) + int(failed_count), 1)) * 100
            }
        }
    
    async def get_all_queue_stats(self) -> Dict[str, Any]:
        """Get statistics for all agent queues."""
        stats = {}
        
        for agent_type in self.agent_types:
            stats[agent_type] = await self.get_queue_stats(agent_type)
        
        # Add overall summary
        total_pending = sum(stat['total_pending'] for stat in stats.values())
        total_processed = sum(stat['processing_stats']['processed'] for stat in stats.values())
        total_failed = sum(stat['processing_stats']['failed'] for stat in stats.values())
        
        stats['summary'] = {
            'total_pending': total_pending,
            'total_processed': total_processed,
            'total_failed': total_failed,
            'overall_success_rate': (total_processed / max(total_processed + total_failed, 1)) * 100,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        return stats
    
    async def _check_worker_scaling(self, agent_type: str):
        """Check if workers need to be scaled up or down."""
        stats = await self.get_queue_stats(agent_type)
        pending_jobs = stats['total_pending']
        active_workers = stats['workers']['active']
        
        # Scale up if queue is growing
        if pending_jobs > self.scale_up_threshold and active_workers < self.worker_limits[agent_type]:
            await self._scale_workers_up(agent_type)
        
        # Scale down if queue is empty and multiple workers active
        elif pending_jobs < self.scale_down_threshold and active_workers > 1:
            await self._scale_workers_down(agent_type)
    
    async def _scale_workers_up(self, agent_type: str):
        """Signal to scale workers up (implementation depends on deployment)."""
        logger.info(f"Scaling up workers for {agent_type}")
        
        # Set scaling signal in Redis
        await self.redis.set(
            f"scaling:up:{agent_type}",
            datetime.utcnow().isoformat(),
            ttl=300  # 5 minutes
        )
        
        # TODO: Implement actual worker scaling based on deployment
        # For Docker Compose: could use docker-compose scale
        # For Kubernetes: update deployment replicas
        # For systemd: start additional service instances
    
    async def _scale_workers_down(self, agent_type: str):
        """Signal to scale workers down."""
        logger.info(f"Scaling down workers for {agent_type}")
        
        # Set scaling signal in Redis
        await self.redis.set(
            f"scaling:down:{agent_type}",
            datetime.utcnow().isoformat(),
            ttl=300  # 5 minutes
        )
    
    async def _get_worker_stats(self, agent_type: str) -> Dict[str, Any]:
        """Get worker statistics for an agent type."""
        # Get worker heartbeats
        heartbeat_pattern = f"worker:heartbeat:{agent_type}:*"
        heartbeat_keys = await self.redis.redis_client.keys(heartbeat_pattern)
        
        active_workers = 0
        worker_list = []
        
        for key in heartbeat_keys:
            heartbeat = await self.redis.get(key)
            if heartbeat:
                heartbeat_time = datetime.fromisoformat(heartbeat)
                # Consider worker active if heartbeat is within last minute
                if (datetime.utcnow() - heartbeat_time).total_seconds() < 60:
                    active_workers += 1
                    worker_id = key.split(':')[-1]
                    worker_list.append({
                        'worker_id': worker_id,
                        'last_heartbeat': heartbeat,
                        'status': 'active'
                    })
        
        return {
            'active': active_workers,
            'limit': self.worker_limits[agent_type],
            'utilization': (active_workers / self.worker_limits[agent_type]) * 100,
            'workers': worker_list
        }
    
    async def _track_job_status(self, job_id: str, status: str, job_data: Dict[str, Any]):
        """Track job status in Redis."""
        status_key = f"job:status:{job_id}"
        status_record = {
            'job_id': job_id,
            'status': status,
            'updated_at': datetime.utcnow().isoformat(),
            'job_data': job_data
        }
        
        # Store with 24 hour TTL
        await self.redis.set(status_key, json.dumps(status_record), ttl=86400)
    
    async def _track_multi_agent_job(self, parent_job_id: str, job_ids: List[str], user_id: Optional[str]):
        """Track multi-agent job progress."""
        multi_job_key = f"multi_job:{parent_job_id}"
        multi_job_record = {
            'parent_job_id': parent_job_id,
            'job_ids': job_ids,
            'user_id': user_id,
            'created_at': datetime.utcnow().isoformat(),
            'status': 'processing',
            'completed_jobs': 0,
            'total_jobs': len(job_ids)
        }
        
        # Store with 24 hour TTL
        await self.redis.set(multi_job_key, json.dumps(multi_job_record), ttl=86400)
    
    async def cleanup_stale_jobs(self, max_age_hours: int = 24):
        """Clean up stale jobs from queues."""
        logger.info(f"Cleaning up jobs older than {max_age_hours} hours")
        
        cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
        cutoff_timestamp = int(cutoff_time.timestamp() * 1000)
        
        cleaned_count = 0
        
        for agent_type in self.agent_types:
            for queue_suffix in ['', ':priority']:
                queue_name = f"queue:{agent_type}{queue_suffix}"
                
                try:
                    # Use XTRIM to remove old messages
                    await self.redis.redis_client.xtrim(
                        queue_name,
                        maxlen=1000,  # Keep last 1000 messages
                        approximate=True
                    )
                    cleaned_count += 1
                    
                except Exception as e:
                    logger.warning(f"Failed to trim queue {queue_name}: {e}")
        
        logger.info(f"Cleaned up {cleaned_count} queues")
    
    async def get_worker_health(self) -> Dict[str, Any]:
        """Get overall worker health status."""
        health_status = {
            'overall_status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'agents': {}
        }
        
        for agent_type in self.agent_types:
            stats = await self.get_queue_stats(agent_type)
            
            # Determine health based on queue length and worker availability
            if stats['total_pending'] > self.scale_up_threshold * 2:
                agent_health = 'overloaded'
            elif stats['workers']['active'] == 0 and stats['total_pending'] > 0:
                agent_health = 'unhealthy'
            elif stats['processing_stats']['success_rate'] < 80:
                agent_health = 'degraded'
            else:
                agent_health = 'healthy'
            
            health_status['agents'][agent_type] = {
                'status': agent_health,
                'pending_jobs': stats['total_pending'],
                'active_workers': stats['workers']['active'],
                'success_rate': stats['processing_stats']['success_rate']
            }
            
            # Update overall status
            if agent_health in ['unhealthy', 'overloaded']:
                health_status['overall_status'] = 'unhealthy'
            elif agent_health == 'degraded' and health_status['overall_status'] == 'healthy':
                health_status['overall_status'] = 'degraded'
        
        return health_status


# Global instance
_queue_manager: Optional[QueueManager] = None


async def get_queue_manager() -> QueueManager:
    """Get or create queue manager instance."""
    global _queue_manager
    if _queue_manager is None:
        _queue_manager = QueueManager()
        await _queue_manager.initialize()
    return _queue_manager