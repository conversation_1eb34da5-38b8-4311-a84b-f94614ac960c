-- Revisionary Writing Analytics Tables
-- Part 7 of the schema migration
-- These tables support core analytics features for tracking writing sessions and goals

-- Writing Sessions Table
-- Tracks individual writing sessions for analytics
CREATE TABLE writing_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE NOT NULL,
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ,
    words_written INTEGER DEFAULT 0,
    words_at_start INTEGER DEFAULT 0,
    time_spent_minutes INTEGER DEFAULT 0,
    ai_suggestions_accepted INTEGER DEFAULT 0,
    ai_suggestions_dismissed INTEGER DEFAULT 0,
    ai_generations_used INTEGER DEFAULT 0,
    focus_mode_used BOOLEAN DEFAULT FALSE,
    features_used TEXT[] DEFAULT '{}',
    session_goal TEXT,
    target_word_count INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT writing_sessions_words_positive CHECK (
        words_written >= 0 AND 
        words_at_start >= 0
    ),
    CONSTRAINT writing_sessions_counts_positive CHECK (
        time_spent_minutes >= 0 AND
        ai_suggestions_accepted >= 0 AND
        ai_suggestions_dismissed >= 0 AND
        ai_generations_used >= 0
    ),
    CONSTRAINT writing_sessions_target_positive CHECK (
        target_word_count IS NULL OR target_word_count > 0
    ),
    CONSTRAINT writing_sessions_end_after_start CHECK (
        end_time IS NULL OR end_time >= start_time
    )
);

-- Writing Goals Table
-- User-defined writing goals (different from challenges which are system-assigned)
CREATE TABLE writing_goals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('daily_words', 'weekly_words', 'daily_time', 'weekly_time')),
    target INTEGER NOT NULL CHECK (target > 0),
    current INTEGER DEFAULT 0 CHECK (current >= 0),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused')),
    description TEXT,
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ,
    achieved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT writing_goals_end_after_start CHECK (
        end_date IS NULL OR end_date >= start_date
    ),
    CONSTRAINT writing_goals_achieved_valid CHECK (
        achieved_at IS NULL OR (status = 'completed' AND achieved_at >= start_date)
    )
);

-- Achievements Table
-- Template definitions for achievements (not user-specific records)
CREATE TABLE IF NOT EXISTS achievements (
    id VARCHAR(100) PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    icon VARCHAR(50) NOT NULL,
    category VARCHAR(50) NOT NULL,
    rarity VARCHAR(20) NOT NULL CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
    unlock_criteria JSONB NOT NULL,
    is_template BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT achievements_unlock_criteria_valid CHECK (jsonb_typeof(unlock_criteria) = 'object')
);

-- User Achievements Table
-- Records which achievements each user has unlocked
CREATE TABLE IF NOT EXISTS user_achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    achievement_id VARCHAR(100) REFERENCES achievements(id) ON DELETE CASCADE NOT NULL,
    unlocked_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    progress_data JSONB,
    
    -- Constraints
    CONSTRAINT user_achievements_progress_valid CHECK (
        progress_data IS NULL OR jsonb_typeof(progress_data) = 'object'
    ),
    UNIQUE(user_id, achievement_id)
);

-- Create indexes for performance
CREATE INDEX idx_writing_sessions_user_id ON writing_sessions(user_id);
CREATE INDEX idx_writing_sessions_document_id ON writing_sessions(document_id);
CREATE INDEX idx_writing_sessions_start_time ON writing_sessions(start_time);
CREATE INDEX idx_writing_sessions_user_date ON writing_sessions(user_id, start_time);

CREATE INDEX idx_writing_goals_user_id ON writing_goals(user_id);
CREATE INDEX idx_writing_goals_status ON writing_goals(status);
CREATE INDEX idx_writing_goals_type ON writing_goals(type);
CREATE INDEX idx_writing_goals_user_status ON writing_goals(user_id, status);

CREATE INDEX idx_achievements_category ON achievements(category);
CREATE INDEX idx_achievements_rarity ON achievements(rarity);

CREATE INDEX idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX idx_user_achievements_achievement_id ON user_achievements(achievement_id);
CREATE INDEX idx_user_achievements_unlocked_at ON user_achievements(unlocked_at);

-- Insert default achievement definitions
INSERT INTO achievements (id, title, description, icon, category, rarity, unlock_criteria) VALUES
    ('first_document', 'First Steps', 'Create your first document', '📄', 'milestone', 'common', '{"documents_created": 1}'),
    ('ai_collaborator', 'AI Collaborator', 'Accept 50 AI suggestions', '🤖', 'collaboration', 'rare', '{"suggestions_accepted": 50}'),
    ('marathon_writer', 'Marathon Writer', 'Write 10,000 words in a single day', '🏃', 'productivity', 'epic', '{"daily_words": 10000}'),
    ('week_streak', 'Consistent Writer', 'Write every day for a week', '🔥', 'consistency', 'rare', '{"streak_days": 7}'),
    ('quality_master', 'Quality Master', 'Achieve a 90+ quality score', '⭐', 'quality', 'epic', '{"quality_score": 90}'),
    ('early_bird', 'Early Bird', 'Start writing before 6 AM', '🌅', 'habits', 'common', '{"writing_time": "early_morning"}'),
    ('night_owl', 'Night Owl', 'Write after midnight', '🦉', 'habits', 'common', '{"writing_time": "late_night"}'),
    ('speed_demon', 'Speed Demon', 'Write 1000 words in 30 minutes', '⚡', 'productivity', 'rare', '{"words_per_minute": 33}'),
    ('perfectionist', 'Perfectionist', 'Achieve 100% grammar score', '✨', 'quality', 'rare', '{"grammar_score": 100}'),
    ('prolific_author', 'Prolific Author', 'Complete 10 documents', '📚', 'milestone', 'rare', '{"documents_completed": 10}')
ON CONFLICT (id) DO NOTHING;

-- Add helpful comments
COMMENT ON TABLE writing_sessions IS 'Tracks individual writing sessions for analytics and progress tracking';
COMMENT ON TABLE writing_goals IS 'User-defined writing goals (different from system-assigned challenges)';
COMMENT ON TABLE achievements IS 'Achievement template definitions (not user-specific)';
COMMENT ON TABLE user_achievements IS 'Tracks which achievements each user has unlocked';
COMMENT ON TABLE writing_achievements IS 'Legacy table for achievement records - kept for compatibility';
COMMENT ON TABLE writing_challenges IS 'System-assigned daily challenges for users';