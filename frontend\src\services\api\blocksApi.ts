/**
 * Blocks API Service
 * 
 * Handles all block-related API calls including:
 * - Block CRUD operations
 * - Block content management
 * - Block hierarchy operations
 * - Auto-save functionality
 */

import { baseApi, ApiResponse } from './baseApi';

export interface Block {
  id: string;
  document_id: string;
  parent_id?: string;
  type: 'paragraph' | 'heading' | 'list' | 'quote' | 'code' | 'image' | 'table' | 'section';
  position: number;
  content: string;
  metadata: {
    level?: number; // For headings
    list_type?: 'ordered' | 'unordered'; // For lists
    language?: string; // For code blocks
    alt_text?: string; // For images
    url?: string; // For images/links
    styling?: Record<string, any>;
  };
  word_count: number;
  character_count: number;
  created_at: string;
  updated_at: string;
  version: number;
}

export interface BlockCreate {
  document_id: string;
  parent_id?: string;
  type: Block['type'];
  position: number;
  content: string;
  metadata?: Block['metadata'];
}

export interface BlockUpdate {
  content?: string;
  type?: Block['type'];
  position?: number;
  metadata?: Block['metadata'];
}

export interface BlockMove {
  parent_id?: string;
  position: number;
}

export interface BlockStructure {
  block: Block;
  children: BlockStructure[];
}

export interface BlockStatistics {
  total_blocks: number;
  blocks_by_type: Record<Block['type'], number>;
  total_words: number;
  total_characters: number;
  reading_time: number;
}

export interface AutoSaveData {
  block_id: string;
  content: string;
  cursor_position?: number;
  timestamp: number;
}

class BlocksApiService {
  private autoSaveTimeouts = new Map<string, number>();
  private readonly AUTO_SAVE_DELAY = 3000; // 3 seconds

  /**
   * Get all blocks for a document
   */
  async getDocumentBlocks(documentId: string): Promise<Block[]> {
    const response = await baseApi.get<Block[]>(`/blocks/document/${documentId}`);
    return response.data;
  }

  /**
   * Get document blocks as hierarchical structure
   */
  async getDocumentStructure(documentId: string): Promise<BlockStructure[]> {
    const response = await baseApi.get<BlockStructure[]>(`/blocks/document/${documentId}/structure`);
    return response.data;
  }

  /**
   * Get a specific block by ID
   */
  async getBlock(id: string): Promise<Block> {
    const response = await baseApi.get<Block>(`/blocks/${id}`);
    return response.data;
  }

  /**
   * Create a new block
   */
  async createBlock(block: BlockCreate): Promise<Block> {
    const response = await baseApi.post<Block>('/blocks', block);
    return response.data;
  }

  /**
   * Update an existing block
   */
  async updateBlock(id: string, updates: BlockUpdate): Promise<Block> {
    const response = await baseApi.put<Block>(`/blocks/${id}`, updates);
    return response.data;
  }

  /**
   * Delete a block
   */
  async deleteBlock(id: string): Promise<{ success: boolean }> {
    const response = await baseApi.delete(`/blocks/${id}`);
    return response.data;
  }

  /**
   * Move a block to new position/parent
   */
  async moveBlock(id: string, move: BlockMove): Promise<Block> {
    const response = await baseApi.post<Block>(`/blocks/${id}/move`, move);
    return response.data;
  }

  /**
   * Duplicate a block
   */
  async duplicateBlock(id: string, position?: number): Promise<Block> {
    const response = await baseApi.post<Block>(`/blocks/${id}/duplicate`, { position });
    return response.data;
  }

  /**
   * Get block statistics for a document
   */
  async getBlockStatistics(documentId: string): Promise<BlockStatistics> {
    const response = await baseApi.get<BlockStatistics>(`/blocks/document/${documentId}/statistics`);
    return response.data;
  }

  /**
   * Batch update multiple blocks
   */
  async batchUpdateBlocks(updates: Array<{ id: string; updates: BlockUpdate }>): Promise<Block[]> {
    const response = await baseApi.post<Block[]>('/blocks/batch-update', { updates });
    return response.data;
  }

  /**
   * Batch create multiple blocks
   */
  async batchCreateBlocks(blocks: BlockCreate[]): Promise<Block[]> {
    const response = await baseApi.post<Block[]>('/blocks/batch-create', { blocks });
    return response.data;
  }

  /**
   * Reorder blocks within a parent
   */
  async reorderBlocks(parentId: string | null, blockIds: string[]): Promise<Block[]> {
    const response = await baseApi.post<Block[]>('/blocks/reorder', {
      parent_id: parentId,
      block_ids: blockIds,
    });
    return response.data;
  }

  /**
   * Convert block type
   */
  async convertBlockType(id: string, newType: Block['type'], metadata?: Block['metadata']): Promise<Block> {
    const response = await baseApi.post<Block>(`/blocks/${id}/convert`, {
      type: newType,
      metadata,
    });
    return response.data;
  }

  /**
   * Merge blocks together
   */
  async mergeBlocks(sourceId: string, targetId: string): Promise<Block> {
    const response = await baseApi.post<Block>(`/blocks/${targetId}/merge`, {
      source_id: sourceId,
    });
    return response.data;
  }

  /**
   * Split a block at cursor position
   */
  async splitBlock(id: string, position: number): Promise<Block[]> {
    const response = await baseApi.post<Block[]>(`/blocks/${id}/split`, { position });
    return response.data;
  }

  /**
   * Get block history/versions
   */
  async getBlockHistory(id: string): Promise<Array<{
    version: number;
    content: string;
    updated_at: string;
    updated_by: string;
  }>> {
    const response = await baseApi.get(`/blocks/${id}/history`);
    return response.data;
  }

  /**
   * Restore block to specific version
   */
  async restoreBlockVersion(id: string, version: number): Promise<Block> {
    const response = await baseApi.post<Block>(`/blocks/${id}/restore/${version}`);
    return response.data;
  }

  // Auto-save functionality
  /**
   * Auto-save block content with debouncing
   */
  autoSaveBlock(blockId: string, content: string, cursorPosition?: number): void {
    // Clear existing timeout for this block
    const existingTimeout = this.autoSaveTimeouts.get(blockId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Set new timeout
    const timeout: number = setTimeout(() => {
      this.saveBlockContent(blockId, content, cursorPosition);
      this.autoSaveTimeouts.delete(blockId);
    }, this.AUTO_SAVE_DELAY);

    this.autoSaveTimeouts.set(blockId, timeout);
  }

  /**
   * Immediately save block content
   */
  async saveBlockContent(blockId: string, content: string, cursorPosition?: number): Promise<Block> {
    try {
      const response = await baseApi.post<Block>(`/blocks/${blockId}/auto-save`, {
        content,
        cursor_position: cursorPosition,
        timestamp: Date.now(),
      });
      
      console.log(`[Auto-save] Block ${blockId} saved successfully`);
      return response.data;
    } catch (error) {
      console.error(`[Auto-save] Failed to save block ${blockId}:`, error);
      throw error;
    }
  }

  /**
   * Cancel pending auto-save for a block
   */
  cancelAutoSave(blockId: string): void {
    const timeout = this.autoSaveTimeouts.get(blockId);
    if (timeout) {
      clearTimeout(timeout);
      this.autoSaveTimeouts.delete(blockId);
      console.log(`[Auto-save] Cancelled auto-save for block ${blockId}`);
    }
  }

  /**
   * Force save all pending auto-saves
   */
  async forceSaveAll(): Promise<void> {
    const promises: Promise<Block>[] = [];

    for (const [blockId, timeout] of this.autoSaveTimeouts.entries()) {
      clearTimeout(timeout);
      // Note: We'd need to store the content somewhere to implement this
      // For now, just clear the timeouts
    }

    this.autoSaveTimeouts.clear();
    await Promise.all(promises);
  }

  /**
   * Get auto-save status for blocks
   */
  getAutoSaveStatus(): Array<{ blockId: string; pending: boolean }> {
    return Array.from(this.autoSaveTimeouts.keys()).map(blockId => ({
      blockId,
      pending: true,
    }));
  }

  // Search within blocks
  /**
   * Search within document blocks
   */
  async searchBlocks(documentId: string, query: string): Promise<Array<{
    block: Block;
    matches: Array<{
      text: string;
      start: number;
      end: number;
    }>;
    score: number;
  }>> {
    const response = await baseApi.post(`/blocks/document/${documentId}/search`, { query });
    return response.data;
  }

  /**
   * Find and replace within blocks
   */
  async findAndReplace(documentId: string, find: string, replace: string, options?: {
    case_sensitive?: boolean;
    whole_words?: boolean;
    block_ids?: string[];
  }): Promise<{
    blocks_modified: number;
    total_replacements: number;
    modified_blocks: Block[];
  }> {
    const response = await baseApi.post(`/blocks/document/${documentId}/find-replace`, {
      find,
      replace,
      options,
    });
    return response.data;
  }
}

// Export singleton instance
export const blocksApi = new BlocksApiService();
export default blocksApi;