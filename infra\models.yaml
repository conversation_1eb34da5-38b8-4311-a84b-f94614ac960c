# Revisionary - LLM Model Configuration
# Single source of truth for all model routing decisions

version: "1.0"
last_updated: "2024-01-15"

models:
  # OpenAI GPT-4.1 Models
  gpt-4-nano:
    provider: "openai"
    model_id: "gpt-4-1106-preview" 
    alias: "gpt-4.1-nano"
    max_tokens: 4096
    context_window: 128000
    cost_per_1k_tokens:
      input: 0.01
      output: 0.03
    use_cases: ["grammar", "quick_fixes"]
    rate_limits:
      requests_per_minute: 500
      tokens_per_minute: 80000

  gpt-4-mini:
    provider: "openai"
    model_id: "gpt-4-1106-preview"
    alias: "gpt-4.1-mini"
    max_tokens: 8192
    context_window: 128000
    cost_per_1k_tokens:
      input: 0.01
      output: 0.03
    use_cases: ["style", "rewriting"]
    rate_limits:
      requests_per_minute: 500
      tokens_per_minute: 80000

  gpt-4-full:
    provider: "openai"
    model_id: "gpt-4-1106-preview"
    alias: "gpt-4.1"
    max_tokens: 16384
    context_window: 128000
    cost_per_1k_tokens:
      input: 0.01
      output: 0.03
    use_cases: ["structure", "content", "generation"]
    rate_limits:
      requests_per_minute: 500
      tokens_per_minute: 80000

  # Google Gemini 2.5 Flash for long context
  gemini-flash:
    provider: "google"
    model_id: "gemini-2.5-flash"
    alias: "gemini-2.5-flash"
    max_tokens: 32768
    context_window: 2000000  # 2M tokens
    cost_per_1k_tokens:
      input: 0.000075
      output: 0.0003
      cached_input: 0.********  # 75% discount for cached context
    use_cases: ["content", "long_context", "document_analysis"]
    features:
      context_caching: true
      cache_ttl_hours: 1
    rate_limits:
      requests_per_minute: 1000
      tokens_per_minute: 4000000

# Agent to Model Routing Rules
agent_routing:
  grammar:
    primary: "gpt-4-nano"
    fallback: "gpt-4-mini"
    context_threshold: 400  # tokens
    
  style:
    primary: "gpt-4-mini"
    fallback: "gpt-4-full"
    context_threshold: 1000  # tokens
    
  structure:
    primary: "gpt-4-full"
    fallback: "gemini-flash"
    context_threshold: 4000  # tokens
    
  content:
    primary: "gpt-4-full"
    fallback: "gemini-flash"
    context_threshold: 6000  # tokens
    long_context: "gemini-flash"  # Use for >6k tokens

# Cost optimization rules
cost_optimization:
  # Use nano for simple tasks
  simple_tasks:
    - "spelling_check"
    - "punctuation_fix"
    - "basic_grammar"
  
  # Use caching aggressively
  cache_strategies:
    - provider: "google"
      min_context_size: 10000  # Only cache large contexts
      cache_duration_hours: 1
    - provider: "openai"
      response_cache_ttl: 3600  # Cache responses for 1 hour
  
  # Rate limiting and quotas
  daily_limits:
    total_cost_usd: 50
    tokens_per_user:
      free: 10000
      professional: 225000
      studio: 1000000
      enterprise: -1  # unlimited

# Environment-specific overrides
environments:
  development:
    models:
      gpt-4-nano:
        rate_limits:
          requests_per_minute: 100
      gemini-flash:
        rate_limits:
          requests_per_minute: 200
    cost_optimization:
      daily_limits:
        total_cost_usd: 10
  
  staging:
    models:
      gpt-4-nano:
        rate_limits:
          requests_per_minute: 200
      gemini-flash:
        rate_limits:
          requests_per_minute: 500
    cost_optimization:
      daily_limits:
        total_cost_usd: 25
  
  production:
    # Use full rate limits from base configuration
    alerting:
      cost_threshold_5min: 1.0  # Alert if >$1 in 5 minutes
      error_rate_threshold: 0.05  # Alert if >5% errors