/**
 * Device Detection Utilities for Mobile-Optimized Editor
 */

export const isMobileDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  // Only detect actual mobile phones, not tablets
  return /Android.*Mobile|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
};

export const isTouchDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

export const getViewportWidth = (): number => {
  if (typeof window === 'undefined') return 0;
  
  return window.innerWidth;
};

export const isMobileViewport = (): boolean => {
  return getViewportWidth() < 640; // sm breakpoint - more conservative
};

export const shouldUseMobileEditor = (): boolean => {
  // Only use mobile editor for actual mobile phones with small screens
  // Tablets and desktop should ALWAYS use full editor
  return isMobileDevice() && isMobileViewport();
};

export const getDeviceType = (): 'mobile' | 'tablet' | 'desktop' => {
  const width = getViewportWidth();
  
  if (width < 768) return 'mobile';
  if (width < 1024) return 'tablet'; 
  return 'desktop';
};

export const supportsHover = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  return window.matchMedia('(hover: hover)').matches;
};

export const getKeyboardHeight = (): number => {
  if (typeof window === 'undefined') return 0;
  
  // Estimate keyboard height on mobile devices
  const visualViewport = window.visualViewport;
  if (visualViewport) {
    return window.innerHeight - visualViewport.height;
  }
  
  return 0;
};