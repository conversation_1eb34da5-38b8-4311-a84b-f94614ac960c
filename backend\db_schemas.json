{"export_metadata": {"timestamp": "2025-07-02T02:40:41.248896Z", "database_info": {"version": "PostgreSQL 15.8 on aarch64-unknown-linux-gnu", "current_database": "postgres"}, "export_summary": {"total_tables": 35, "total_rows": 325, "total_columns": 466, "empty_tables": ["consistency_violations", "entities", "entity_relationships", "reference_library", "schema_migrations", "user_achievements", "writing_goals", "writing_sessions"]}}, "tables": {"achievements": {"table_name": "achievements", "row_count": 10, "column_count": 9, "columns": [{"name": "id", "position": 1, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 100}, {"name": "title", "position": 2, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 200}, {"name": "description", "position": 3, "data_type": "text", "udt_name": "text", "nullable": false, "default": null}, {"name": "icon", "position": 4, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 50}, {"name": "category", "position": 5, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 50}, {"name": "rarity", "position": 6, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 20}, {"name": "unlock_criteria", "position": 7, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": null}, {"name": "is_template", "position": 8, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "true"}, {"name": "created_at", "position": 9, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"achievements_rarity_check": "(((rarity)::text = ANY ((ARRAY['common'::character varying, 'rare'::character varying, 'epic'::character varying, 'legendary'::character varying])::text[])))", "achievements_unlock_criteria_valid": "((jsonb_typeof(unlock_criteria) = 'object'::text))", "2200_33162_1_not_null": "id IS NOT NULL", "2200_33162_2_not_null": "title IS NOT NULL", "2200_33162_3_not_null": "description IS NOT NULL", "2200_33162_4_not_null": "icon IS NOT NULL", "2200_33162_5_not_null": "category IS NOT NULL", "2200_33162_6_not_null": "rarity IS NOT NULL", "2200_33162_7_not_null": "unlock_criteria IS NOT NULL"}, "indexes": [{"name": "achievements_pkey", "definition": "CREATE UNIQUE INDEX achievements_pkey ON public.achievements USING btree (id)", "tablespace": null}, {"name": "idx_achievements_category", "definition": "CREATE INDEX idx_achievements_category ON public.achievements USING btree (category)", "tablespace": null}, {"name": "idx_achievements_rarity", "definition": "CREATE INDEX idx_achievements_rarity ON public.achievements USING btree (rarity)", "tablespace": null}], "foreign_keys": []}, "agent_style_rules": {"table_name": "agent_style_rules", "row_count": 19, "column_count": 9, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "agent_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "category", "position": 3, "data_type": "USER-DEFINED", "udt_name": "style_rule_category_enum", "nullable": false, "default": null}, {"name": "rule_name", "position": 4, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 255}, {"name": "rule_value", "position": 5, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": null}, {"name": "is_enabled", "position": 6, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "true"}, {"name": "priority", "position": 7, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "50", "precision": 32}, {"name": "created_at", "position": 8, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 9, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {"agent_style_rules_agent_id_category_rule_name_key": ["agent_id", "category", "rule_name"]}, "check_constraints": {"agent_style_rules_name_not_empty": "((length(TRIM(BOTH FROM rule_name)) > 0))", "agent_style_rules_priority_range": "(((priority >= 1) AND (priority <= 100)))", "agent_style_rules_value_valid": "((jsonb_typeof(rule_value) = 'object'::text))", "2200_18804_1_not_null": "id IS NOT NULL", "2200_18804_3_not_null": "category IS NOT NULL", "2200_18804_4_not_null": "rule_name IS NOT NULL", "2200_18804_5_not_null": "rule_value IS NOT NULL"}, "indexes": [{"name": "agent_style_rules_agent_id_category_rule_name_key", "definition": "CREATE UNIQUE INDEX agent_style_rules_agent_id_category_rule_name_key ON public.agent_style_rules USING btree (agent_id, category, rule_name)", "tablespace": null}, {"name": "agent_style_rules_pkey", "definition": "CREATE UNIQUE INDEX agent_style_rules_pkey ON public.agent_style_rules USING btree (id)", "tablespace": null}, {"name": "idx_agent_style_rules_agent", "definition": "CREATE INDEX idx_agent_style_rules_agent ON public.agent_style_rules USING btree (agent_id, priority DESC) WHERE (is_enabled = true)", "tablespace": null}, {"name": "idx_agent_style_rules_category", "definition": "CREATE INDEX idx_agent_style_rules_category ON public.agent_style_rules USING btree (category) WHERE (is_enabled = true)", "tablespace": null}], "foreign_keys": [{"source_column": "agent_id", "target_table": "custom_agents", "target_column": "id"}]}, "agent_usage_stats": {"table_name": "agent_usage_stats", "row_count": 8, "column_count": 14, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "agent_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "user_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "date", "position": 4, "data_type": "date", "udt_name": "date", "nullable": false, "default": null}, {"name": "invocations", "position": 5, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "total_processing_time_ms", "position": 6, "data_type": "bigint", "udt_name": "int8", "nullable": true, "default": "0", "precision": 64}, {"name": "suggestions_generated", "position": 7, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "suggestions_accepted", "position": 8, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "suggestions_rejected", "position": 9, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "avg_confidence", "position": 10, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": null, "precision": 3, "scale": 2}, {"name": "error_count", "position": 11, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "tokens_used", "position": 12, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "created_at", "position": 13, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 14, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {"agent_usage_stats_agent_id_user_id_date_key": ["agent_id", "user_id", "date"]}, "check_constraints": {"agent_usage_stats_confidence_range": "(((avg_confidence IS NULL) OR ((avg_confidence >= (0)::numeric) AND (avg_confidence <= (1)::numeric))))", "agent_usage_stats_counts_positive": "(((invocations >= 0) AND (total_processing_time_ms >= 0) AND (suggestions_generated >= 0) AND (suggestions_accepted >= 0) AND (suggestions_rejected >= 0) AND (error_count >= 0) AND (tokens_used >= 0)))", "2200_18826_1_not_null": "id IS NOT NULL", "2200_18826_4_not_null": "date IS NOT NULL"}, "indexes": [{"name": "agent_usage_stats_agent_id_user_id_date_key", "definition": "CREATE UNIQUE INDEX agent_usage_stats_agent_id_user_id_date_key ON public.agent_usage_stats USING btree (agent_id, user_id, date)", "tablespace": null}, {"name": "agent_usage_stats_pkey", "definition": "CREATE UNIQUE INDEX agent_usage_stats_pkey ON public.agent_usage_stats USING btree (id)", "tablespace": null}, {"name": "idx_agent_usage_stats_agent_date", "definition": "CREATE INDEX idx_agent_usage_stats_agent_date ON public.agent_usage_stats USING btree (agent_id, date DESC)", "tablespace": null}, {"name": "idx_agent_usage_stats_date", "definition": "CREATE INDEX idx_agent_usage_stats_date ON public.agent_usage_stats USING btree (date DESC)", "tablespace": null}, {"name": "idx_agent_usage_stats_user_date", "definition": "CREATE INDEX idx_agent_usage_stats_user_date ON public.agent_usage_stats USING btree (user_id, date DESC)", "tablespace": null}], "foreign_keys": [{"source_column": "agent_id", "target_table": "custom_agents", "target_column": "id"}, {"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "audience_analysis": {"table_name": "audience_analysis", "row_count": 3, "column_count": 10, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "document_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "persona_ids", "position": 3, "data_type": "ARRAY", "udt_name": "_uuid", "nullable": false, "default": null}, {"name": "overall_appeal_score", "position": 4, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 3, "scale": 2}, {"name": "cross_audience_conflicts", "position": 5, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'[]'::jsonb"}, {"name": "optimization_suggestions", "position": 6, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'[]'::jsonb"}, {"name": "audience_alignment", "position": 7, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": null}, {"name": "analysis_summary", "position": 8, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "created_at", "position": 9, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "expires_at", "position": 10, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"audience_analysis_alignment_valid": "((jsonb_typeof(audience_alignment) = 'object'::text))", "audience_analysis_appeal_range": "(((overall_appeal_score >= (0)::numeric) AND (overall_appeal_score <= (1)::numeric)))", "audience_analysis_conflicts_valid": "((jsonb_typeof(cross_audience_conflicts) = 'array'::text))", "audience_analysis_personas_not_empty": "((array_length(persona_ids, 1) > 0))", "audience_analysis_suggestions_valid": "((jsonb_typeof(optimization_suggestions) = 'array'::text))", "2200_18756_1_not_null": "id IS NOT NULL", "2200_18756_3_not_null": "persona_ids IS NOT NULL", "2200_18756_4_not_null": "overall_appeal_score IS NOT NULL", "2200_18756_7_not_null": "audience_alignment IS NOT NULL"}, "indexes": [{"name": "audience_analysis_pkey", "definition": "CREATE UNIQUE INDEX audience_analysis_pkey ON public.audience_analysis USING btree (id)", "tablespace": null}, {"name": "idx_audience_analysis_document", "definition": "CREATE INDEX idx_audience_analysis_document ON public.audience_analysis USING btree (document_id, created_at DESC)", "tablespace": null}, {"name": "idx_audience_analysis_expires", "definition": "CREATE INDEX idx_audience_analysis_expires ON public.audience_analysis USING btree (expires_at) WHERE (expires_at IS NOT NULL)", "tablespace": null}, {"name": "idx_audience_analysis_score", "definition": "CREATE INDEX idx_audience_analysis_score ON public.audience_analysis USING btree (overall_appeal_score DESC)", "tablespace": null}], "foreign_keys": [{"source_column": "document_id", "target_table": "documents", "target_column": "id"}]}, "blocks": {"table_name": "blocks", "row_count": 21, "column_count": 15, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "document_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "parent_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "type", "position": 4, "data_type": "USER-DEFINED", "udt_name": "block_type_enum", "nullable": false, "default": null}, {"name": "position", "position": 5, "data_type": "integer", "udt_name": "int4", "nullable": false, "default": null, "precision": 32}, {"name": "depth", "position": 6, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "path", "position": 7, "data_type": "USER-DEFINED", "udt_name": "ltree", "nullable": true, "default": null}, {"name": "content", "position": 8, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "content_hash", "position": 9, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 64}, {"name": "content_length", "position": 10, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "precision": 32}, {"name": "word_count", "position": 11, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "metadata", "position": 12, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}, {"name": "created_at", "position": 13, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 14, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "deleted_at", "position": 15, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"blocks_content_hash_valid": "(((content_hash)::text ~ '^[a-f0-9]{64}$'::text))", "blocks_depth_positive": "((depth >= 0))", "blocks_metadata_valid": "((jsonb_typeof(metadata) = 'object'::text))", "blocks_parent_different": "((id <> parent_id))", "blocks_position_positive": "((\"position\" >= 0))", "blocks_root_no_parent": "((((type = 'document'::block_type_enum) AND (parent_id IS NULL)) OR ((type <> 'document'::block_type_enum) AND (parent_id IS NOT NULL))))", "blocks_word_count_positive": "((word_count >= 0))", "2200_18380_1_not_null": "id IS NOT NULL", "2200_18380_4_not_null": "type IS NOT NULL", "2200_18380_5_not_null": "position IS NOT NULL"}, "indexes": [{"name": "blocks_pkey", "definition": "CREATE UNIQUE INDEX blocks_pkey ON public.blocks USING btree (id)", "tablespace": null}, {"name": "idx_blocks_content_search", "definition": "CREATE INDEX idx_blocks_content_search ON public.blocks USING gin (to_tsvector('english'::regconfig, content)) WHERE ((content IS NOT NULL) AND (deleted_at IS NULL))", "tablespace": null}, {"name": "idx_blocks_document_position", "definition": "CREATE UNIQUE INDEX idx_blocks_document_position ON public.blocks USING btree (document_id, parent_id, \"position\") WHERE (deleted_at IS NULL)", "tablespace": null}, {"name": "idx_blocks_hash", "definition": "CREATE INDEX idx_blocks_hash ON public.blocks USING btree (content_hash)", "tablespace": null}, {"name": "idx_blocks_parent", "definition": "CREATE INDEX idx_blocks_parent ON public.blocks USING btree (parent_id) WHERE (deleted_at IS NULL)", "tablespace": null}, {"name": "idx_blocks_path", "definition": "CREATE INDEX idx_blocks_path ON public.blocks USING gist (path) WHERE (deleted_at IS NULL)", "tablespace": null}, {"name": "idx_blocks_type", "definition": "CREATE INDEX idx_blocks_type ON public.blocks USING btree (type) WHERE (deleted_at IS NULL)", "tablespace": null}, {"name": "idx_blocks_updated", "definition": "CREATE INDEX idx_blocks_updated ON public.blocks USING btree (updated_at DESC)", "tablespace": null}], "foreign_keys": [{"source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_column": "parent_id", "target_table": "blocks", "target_column": "id"}]}, "citations": {"table_name": "citations", "row_count": 6, "column_count": 25, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "document_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "block_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "citation_key", "position": 4, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 255}, {"name": "citation_type", "position": 5, "data_type": "USER-DEFINED", "udt_name": "citation_type_enum", "nullable": false, "default": null}, {"name": "title", "position": 6, "data_type": "text", "udt_name": "text", "nullable": false, "default": null}, {"name": "authors", "position": 7, "data_type": "ARRAY", "udt_name": "_text", "nullable": false, "default": null}, {"name": "publication_year", "position": 8, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "precision": 32}, {"name": "publisher", "position": 9, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 500}, {"name": "doi", "position": 10, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 255}, {"name": "url", "position": 11, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "page_numbers", "position": 12, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 50}, {"name": "volume", "position": 13, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 50}, {"name": "issue", "position": 14, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 50}, {"name": "journal_name", "position": 15, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 500}, {"name": "isbn", "position": 16, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 20}, {"name": "raw_citation", "position": 17, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "formatted_citation", "position": 18, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "citation_style", "position": 19, "data_type": "USER-DEFINED", "udt_name": "citation_style_enum", "nullable": true, "default": "'apa'::citation_style_enum"}, {"name": "access_date", "position": 20, "data_type": "date", "udt_name": "date", "nullable": true, "default": null}, {"name": "position", "position": 21, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": null}, {"name": "is_verified", "position": 22, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "false"}, {"name": "verification_date", "position": 23, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "created_at", "position": 24, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 25, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {"citations_document_id_citation_key_key": ["document_id", "citation_key"]}, "check_constraints": {"citations_authors_not_empty": "((array_length(authors, 1) > 0))", "citations_key_not_empty": "((length(TRIM(BOTH FROM citation_key)) > 0))", "citations_position_valid": "(((\"position\" IS NULL) OR ((jsonb_typeof(\"position\") = 'object'::text) AND (\"position\" ? 'start'::text) AND (\"position\" ? 'end'::text))))", "citations_title_not_empty": "((length(TRIM(BOTH FROM title)) > 0))", "citations_year_reasonable": "(((publication_year IS NULL) OR ((publication_year >= 1000) AND ((publication_year)::numeric <= (EXTRACT(year FROM now()) + (5)::numeric)))))", "2200_18997_1_not_null": "id IS NOT NULL", "2200_18997_4_not_null": "citation_key IS NOT NULL", "2200_18997_5_not_null": "citation_type IS NOT NULL", "2200_18997_6_not_null": "title IS NOT NULL", "2200_18997_7_not_null": "authors IS NOT NULL"}, "indexes": [{"name": "citations_document_id_citation_key_key", "definition": "CREATE UNIQUE INDEX citations_document_id_citation_key_key ON public.citations USING btree (document_id, citation_key)", "tablespace": null}, {"name": "citations_pkey", "definition": "CREATE UNIQUE INDEX citations_pkey ON public.citations USING btree (id)", "tablespace": null}, {"name": "idx_citations_authors", "definition": "CREATE INDEX idx_citations_authors ON public.citations USING gin (authors)", "tablespace": null}, {"name": "idx_citations_block", "definition": "CREATE INDEX idx_citations_block ON public.citations USING btree (block_id)", "tablespace": null}, {"name": "idx_citations_document", "definition": "CREATE INDEX idx_citations_document ON public.citations USING btree (document_id, created_at DESC)", "tablespace": null}, {"name": "idx_citations_key", "definition": "CREATE INDEX idx_citations_key ON public.citations USING btree (citation_key)", "tablespace": null}, {"name": "idx_citations_title_search", "definition": "CREATE INDEX idx_citations_title_search ON public.citations USING gin (to_tsvector('english'::regconfig, title))", "tablespace": null}, {"name": "idx_citations_type", "definition": "CREATE INDEX idx_citations_type ON public.citations USING btree (citation_type)", "tablespace": null}, {"name": "idx_citations_unverified", "definition": "CREATE INDEX idx_citations_unverified ON public.citations USING btree (document_id) WHERE (is_verified = false)", "tablespace": null}], "foreign_keys": [{"source_column": "block_id", "target_table": "blocks", "target_column": "id"}, {"source_column": "document_id", "target_table": "documents", "target_column": "id"}]}, "collaborations": {"table_name": "collaborations", "row_count": 8, "column_count": 10, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "document_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "user_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "role", "position": 4, "data_type": "USER-DEFINED", "udt_name": "collaboration_role_enum", "nullable": false, "default": null}, {"name": "status", "position": 5, "data_type": "USER-DEFINED", "udt_name": "collaboration_status_enum", "nullable": true, "default": "'pending'::collaboration_status_enum"}, {"name": "invited_by", "position": 6, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "permissions", "position": 7, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}, {"name": "created_at", "position": 8, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 9, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "expires_at", "position": 10, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}], "primary_keys": ["id"], "unique_constraints": {"collaborations_document_id_user_id_key": ["document_id", "user_id"]}, "check_constraints": {"collaborations_permissions_valid": "((jsonb_typeof(permissions) = 'object'::text))", "2200_18485_1_not_null": "id IS NOT NULL", "2200_18485_4_not_null": "role IS NOT NULL"}, "indexes": [{"name": "collaborations_document_id_user_id_key", "definition": "CREATE UNIQUE INDEX collaborations_document_id_user_id_key ON public.collaborations USING btree (document_id, user_id)", "tablespace": null}, {"name": "collaborations_pkey", "definition": "CREATE UNIQUE INDEX collaborations_pkey ON public.collaborations USING btree (id)", "tablespace": null}, {"name": "idx_collaborations_document", "definition": "CREATE INDEX idx_collaborations_document ON public.collaborations USING btree (document_id, status)", "tablespace": null}, {"name": "idx_collaborations_expires", "definition": "CREATE INDEX idx_collaborations_expires ON public.collaborations USING btree (expires_at) WHERE (expires_at IS NOT NULL)", "tablespace": null}, {"name": "idx_collaborations_user", "definition": "CREATE INDEX idx_collaborations_user ON public.collaborations USING btree (user_id, status)", "tablespace": null}], "foreign_keys": [{"source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_column": "invited_by", "target_table": "users", "target_column": "id"}, {"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "comments": {"table_name": "comments", "row_count": 17, "column_count": 13, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "block_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "author_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "parent_id", "position": 4, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "content", "position": 5, "data_type": "text", "udt_name": "text", "nullable": false, "default": null}, {"name": "position", "position": 6, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": null}, {"name": "type", "position": 7, "data_type": "USER-DEFINED", "udt_name": "comment_type_enum", "nullable": true, "default": "'general'::comment_type_enum"}, {"name": "status", "position": 8, "data_type": "USER-DEFINED", "udt_name": "comment_status_enum", "nullable": true, "default": "'open'::comment_status_enum"}, {"name": "resolved_by", "position": 9, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "resolved_at", "position": 10, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "created_at", "position": 11, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 12, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "deleted_at", "position": 13, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"comments_content_not_empty": "((length(TRIM(BOTH FROM content)) > 0))", "comments_parent_different": "((id <> parent_id))", "comments_position_valid": "(((\"position\" IS NULL) OR ((jsonb_typeof(\"position\") = 'object'::text) AND (\"position\" ? 'start'::text) AND (\"position\" ? 'end'::text))))", "2200_18515_1_not_null": "id IS NOT NULL", "2200_18515_5_not_null": "content IS NOT NULL"}, "indexes": [{"name": "comments_pkey", "definition": "CREATE UNIQUE INDEX comments_pkey ON public.comments USING btree (id)", "tablespace": null}, {"name": "idx_comments_author", "definition": "CREATE INDEX idx_comments_author ON public.comments USING btree (author_id, created_at DESC) WHERE (deleted_at IS NULL)", "tablespace": null}, {"name": "idx_comments_block", "definition": "CREATE INDEX idx_comments_block ON public.comments USING btree (block_id, created_at DESC) WHERE (deleted_at IS NULL)", "tablespace": null}, {"name": "idx_comments_parent", "definition": "CREATE INDEX idx_comments_parent ON public.comments USING btree (parent_id) WHERE (parent_id IS NOT NULL)", "tablespace": null}, {"name": "idx_comments_status", "definition": "CREATE INDEX idx_comments_status ON public.comments USING btree (status) WHERE (deleted_at IS NULL)", "tablespace": null}], "foreign_keys": [{"source_column": "author_id", "target_table": "users", "target_column": "id"}, {"source_column": "block_id", "target_table": "blocks", "target_column": "id"}, {"source_column": "parent_id", "target_table": "comments", "target_column": "id"}, {"source_column": "resolved_by", "target_table": "users", "target_column": "id"}]}, "consistency_violations": {"table_name": "consistency_violations", "row_count": 0, "column_count": 13, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "document_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "entity_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "violation_type", "position": 4, "data_type": "USER-DEFINED", "udt_name": "violation_type_enum", "nullable": false, "default": null}, {"name": "severity", "position": 5, "data_type": "USER-DEFINED", "udt_name": "consistency_severity_enum", "nullable": false, "default": null}, {"name": "description", "position": 6, "data_type": "text", "udt_name": "text", "nullable": false, "default": null}, {"name": "conflicting_blocks", "position": 7, "data_type": "ARRAY", "udt_name": "_uuid", "nullable": false, "default": null}, {"name": "suggested_resolution", "position": 8, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "is_resolved", "position": 9, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "false"}, {"name": "resolved_at", "position": 10, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "resolved_by", "position": 11, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "resolution_method", "position": 12, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 100}, {"name": "created_at", "position": 13, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"consistency_violations_blocks_not_empty": "((array_length(conflicting_blocks, 1) > 0))", "consistency_violations_description_not_empty": "((length(TRIM(BOTH FROM description)) > 0))", "2200_18970_1_not_null": "id IS NOT NULL", "2200_18970_4_not_null": "violation_type IS NOT NULL", "2200_18970_5_not_null": "severity IS NOT NULL", "2200_18970_6_not_null": "description IS NOT NULL", "2200_18970_7_not_null": "conflicting_blocks IS NOT NULL"}, "indexes": [{"name": "consistency_violations_pkey", "definition": "CREATE UNIQUE INDEX consistency_violations_pkey ON public.consistency_violations USING btree (id)", "tablespace": null}, {"name": "idx_consistency_violations_document", "definition": "CREATE INDEX idx_consistency_violations_document ON public.consistency_violations USING btree (document_id, severity, created_at DESC)", "tablespace": null}, {"name": "idx_consistency_violations_entity", "definition": "CREATE INDEX idx_consistency_violations_entity ON public.consistency_violations USING btree (entity_id, severity)", "tablespace": null}, {"name": "idx_consistency_violations_type", "definition": "CREATE INDEX idx_consistency_violations_type ON public.consistency_violations USING btree (violation_type, severity)", "tablespace": null}, {"name": "idx_consistency_violations_unresolved", "definition": "CREATE INDEX idx_consistency_violations_unresolved ON public.consistency_violations USING btree (document_id) WHERE (is_resolved = false)", "tablespace": null}], "foreign_keys": [{"source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_column": "entity_id", "target_table": "entities", "target_column": "id"}, {"source_column": "resolved_by", "target_table": "users", "target_column": "id"}]}, "custom_agents": {"table_name": "custom_agents", "row_count": 8, "column_count": 16, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "user_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "name", "position": 3, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 255}, {"name": "description", "position": 4, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "type", "position": 5, "data_type": "USER-DEFINED", "udt_name": "agent_type_enum", "nullable": false, "default": null}, {"name": "capabilities", "position": 6, "data_type": "ARRAY", "udt_name": "_agent_capability_enum", "nullable": true, "default": "'{}'::agent_capability_enum[]"}, {"name": "priority", "position": 7, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "50", "precision": 32}, {"name": "is_active", "position": 8, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "true"}, {"name": "is_template", "position": 9, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "false"}, {"name": "model_preference", "position": 10, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 50}, {"name": "max_context_tokens", "position": 11, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "4000", "precision": 32}, {"name": "temperature", "position": 12, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": "0.3", "precision": 3, "scale": 2}, {"name": "execution_timeout", "position": 13, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "30000", "precision": 32}, {"name": "created_at", "position": 14, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 15, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "last_used_at", "position": 16, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"custom_agents_context_tokens_positive": "((max_context_tokens > 0))", "custom_agents_name_not_empty": "((length(TRIM(BOTH FROM name)) > 0))", "custom_agents_priority_range": "(((priority >= 1) AND (priority <= 100)))", "custom_agents_temperature_range": "(((temperature >= (0)::numeric) AND (temperature <= (2)::numeric)))", "custom_agents_timeout_positive": "((execution_timeout > 0))", "2200_18777_1_not_null": "id IS NOT NULL", "2200_18777_3_not_null": "name IS NOT NULL", "2200_18777_5_not_null": "type IS NOT NULL"}, "indexes": [{"name": "custom_agents_pkey", "definition": "CREATE UNIQUE INDEX custom_agents_pkey ON public.custom_agents USING btree (id)", "tablespace": null}, {"name": "idx_custom_agents_last_used", "definition": "CREATE INDEX idx_custom_agents_last_used ON public.custom_agents USING btree (last_used_at DESC) WHERE (last_used_at IS NOT NULL)", "tablespace": null}, {"name": "idx_custom_agents_priority", "definition": "CREATE INDEX idx_custom_agents_priority ON public.custom_agents USING btree (priority DESC) WHERE (is_active = true)", "tablespace": null}, {"name": "idx_custom_agents_template", "definition": "CREATE INDEX idx_custom_agents_template ON public.custom_agents USING btree (is_template) WHERE (is_template = true)", "tablespace": null}, {"name": "idx_custom_agents_type", "definition": "CREATE INDEX idx_custom_agents_type ON public.custom_agents USING btree (type) WHERE (is_active = true)", "tablespace": null}, {"name": "idx_custom_agents_user", "definition": "CREATE INDEX idx_custom_agents_user ON public.custom_agents USING btree (user_id) WHERE (is_active = true)", "tablespace": null}], "foreign_keys": [{"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "document_scores": {"table_name": "document_scores", "row_count": 10, "column_count": 14, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "document_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "user_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "total_score", "position": 4, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "grammar_score", "position": 5, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "style_score", "position": 6, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "structure_score", "position": 7, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "content_score", "position": 8, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "word_count", "position": 9, "data_type": "integer", "udt_name": "int4", "nullable": false, "default": null, "precision": 32}, {"name": "document_type", "position": 10, "data_type": "USER-DEFINED", "udt_name": "document_type_enum", "nullable": false, "default": null}, {"name": "detailed_breakdown", "position": 11, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": null}, {"name": "improvement_suggestions", "position": 12, "data_type": "ARRAY", "udt_name": "_text", "nullable": true, "default": null}, {"name": "scoring_version", "position": 13, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": "'1.0'::character varying", "max_length": 10}, {"name": "created_at", "position": 14, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"document_scores_component_range": "(((grammar_score >= (0)::numeric) AND (grammar_score <= (25)::numeric) AND (style_score >= (0)::numeric) AND (style_score <= (25)::numeric) AND (structure_score >= (0)::numeric) AND (structure_score <= (25)::numeric) AND (content_score >= (0)::numeric) AND (content_score <= (25)::numeric)))", "document_scores_total_range": "(((total_score >= (0)::numeric) AND (total_score <= (100)::numeric)))", "document_scores_word_count_positive": "((word_count > 0))", "2200_18550_1_not_null": "id IS NOT NULL", "2200_18550_4_not_null": "total_score IS NOT NULL", "2200_18550_5_not_null": "grammar_score IS NOT NULL", "2200_18550_6_not_null": "style_score IS NOT NULL", "2200_18550_7_not_null": "structure_score IS NOT NULL", "2200_18550_8_not_null": "content_score IS NOT NULL", "2200_18550_9_not_null": "word_count IS NOT NULL", "2200_18550_10_not_null": "document_type IS NOT NULL", "2200_18550_11_not_null": "detailed_breakdown IS NOT NULL"}, "indexes": [{"name": "document_scores_pkey", "definition": "CREATE UNIQUE INDEX document_scores_pkey ON public.document_scores USING btree (id)", "tablespace": null}, {"name": "idx_document_scores_date", "definition": "CREATE INDEX idx_document_scores_date ON public.document_scores USING btree (created_at DESC)", "tablespace": null}, {"name": "idx_document_scores_document", "definition": "CREATE INDEX idx_document_scores_document ON public.document_scores USING btree (document_id, created_at DESC)", "tablespace": null}, {"name": "idx_document_scores_type_score", "definition": "CREATE INDEX idx_document_scores_type_score ON public.document_scores USING btree (document_type, total_score DESC)", "tablespace": null}, {"name": "idx_document_scores_user", "definition": "CREATE INDEX idx_document_scores_user ON public.document_scores USING btree (user_id, created_at DESC)", "tablespace": null}], "foreign_keys": [{"source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "documents": {"table_name": "documents", "row_count": 49, "column_count": 20, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "owner_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "title", "position": 3, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 500}, {"name": "type", "position": 4, "data_type": "USER-DEFINED", "udt_name": "document_type_enum", "nullable": false, "default": null}, {"name": "status", "position": 5, "data_type": "USER-DEFINED", "udt_name": "document_status_enum", "nullable": true, "default": "'draft'::document_status_enum"}, {"name": "description", "position": 6, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "word_count", "position": 7, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "character_count", "position": 8, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "version", "position": 9, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "1", "precision": 32}, {"name": "is_template", "position": 10, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "false"}, {"name": "is_public", "position": 11, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "false"}, {"name": "language", "position": 12, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": "'en'::character varying", "max_length": 10}, {"name": "metadata", "position": 13, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}, {"name": "settings", "position": 14, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}, {"name": "tags", "position": 15, "data_type": "ARRAY", "udt_name": "_text", "nullable": true, "default": "'{}'::text[]"}, {"name": "created_at", "position": 16, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 17, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "published_at", "position": 18, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "deleted_at", "position": 19, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "slug", "position": 20, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 255}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"documents_character_count_positive": "((character_count >= 0))", "documents_metadata_valid": "((jsonb_typeof(metadata) = 'object'::text))", "documents_settings_valid": "((jsonb_typeof(settings) = 'object'::text))", "documents_title_not_empty": "((length(TRIM(BOTH FROM title)) > 0))", "documents_word_count_positive": "((word_count >= 0))", "2200_18349_1_not_null": "id IS NOT NULL", "2200_18349_3_not_null": "title IS NOT NULL", "2200_18349_4_not_null": "type IS NOT NULL"}, "indexes": [{"name": "documents_pkey", "definition": "CREATE UNIQUE INDEX documents_pkey ON public.documents USING btree (id)", "tablespace": null}, {"name": "idx_documents_metadata", "definition": "CREATE INDEX idx_documents_metadata ON public.documents USING gin (metadata)", "tablespace": null}, {"name": "idx_documents_owner", "definition": "CREATE INDEX idx_documents_owner ON public.documents USING btree (owner_id) WHERE (deleted_at IS NULL)", "tablespace": null}, {"name": "idx_documents_search", "definition": "CREATE INDEX idx_documents_search ON public.documents USING gin (to_tsvector('english'::regconfig, (((title)::text || ' '::text) || COALESCE(description, ''::text))))", "tablespace": null}, {"name": "idx_documents_status", "definition": "CREATE INDEX idx_documents_status ON public.documents USING btree (status) WHERE (deleted_at IS NULL)", "tablespace": null}, {"name": "idx_documents_tags", "definition": "CREATE INDEX idx_documents_tags ON public.documents USING gin (tags)", "tablespace": null}, {"name": "idx_documents_type", "definition": "CREATE INDEX idx_documents_type ON public.documents USING btree (type) WHERE (deleted_at IS NULL)", "tablespace": null}, {"name": "idx_documents_updated", "definition": "CREATE INDEX idx_documents_updated ON public.documents USING btree (updated_at DESC) WHERE (deleted_at IS NULL)", "tablespace": null}], "foreign_keys": [{"source_column": "owner_id", "target_table": "users", "target_column": "id"}]}, "entities": {"table_name": "entities", "row_count": 0, "column_count": 16, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "document_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "user_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "name", "position": 4, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 500}, {"name": "entity_type", "position": 5, "data_type": "USER-DEFINED", "udt_name": "entity_type_enum", "nullable": false, "default": null}, {"name": "aliases", "position": 6, "data_type": "ARRAY", "udt_name": "_text", "nullable": true, "default": "'{}'::text[]"}, {"name": "description", "position": 7, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "attributes", "position": 8, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}, {"name": "physical_attributes", "position": 9, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}, {"name": "personality_traits", "position": 10, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}, {"name": "first_mentioned_block_id", "position": 11, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "last_mentioned_block_id", "position": 12, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "mention_count", "position": 13, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "1", "precision": 32}, {"name": "importance_score", "position": 14, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": "0.5", "precision": 3, "scale": 2}, {"name": "created_at", "position": 15, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 16, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"entities_attributes_valid": "((jsonb_typeof(attributes) = 'object'::text))", "entities_importance_range": "(((importance_score >= (0)::numeric) AND (importance_score <= (1)::numeric)))", "entities_mention_count_positive": "((mention_count >= 0))", "entities_name_not_empty": "((length(TRIM(BOTH FROM name)) > 0))", "entities_personality_valid": "((jsonb_typeof(personality_traits) = 'object'::text))", "entities_physical_valid": "((jsonb_typeof(physical_attributes) = 'object'::text))", "2200_18899_1_not_null": "id IS NOT NULL", "2200_18899_4_not_null": "name IS NOT NULL", "2200_18899_5_not_null": "entity_type IS NOT NULL"}, "indexes": [{"name": "entities_pkey", "definition": "CREATE UNIQUE INDEX entities_pkey ON public.entities USING btree (id)", "tablespace": null}, {"name": "idx_entities_aliases", "definition": "CREATE INDEX idx_entities_aliases ON public.entities USING gin (aliases)", "tablespace": null}, {"name": "idx_entities_document", "definition": "CREATE INDEX idx_entities_document ON public.entities USING btree (document_id, entity_type)", "tablespace": null}, {"name": "idx_entities_importance", "definition": "CREATE INDEX idx_entities_importance ON public.entities USING btree (importance_score DESC)", "tablespace": null}, {"name": "idx_entities_mentions", "definition": "CREATE INDEX idx_entities_mentions ON public.entities USING btree (mention_count DESC)", "tablespace": null}, {"name": "idx_entities_name", "definition": "CREATE INDEX idx_entities_name ON public.entities USING gin (to_tsvector('english'::regconfig, (name)::text))", "tablespace": null}, {"name": "idx_entities_user", "definition": "CREATE INDEX idx_entities_user ON public.entities USING btree (user_id, created_at DESC)", "tablespace": null}], "foreign_keys": [{"source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_column": "first_mentioned_block_id", "target_table": "blocks", "target_column": "id"}, {"source_column": "last_mentioned_block_id", "target_table": "blocks", "target_column": "id"}, {"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "entity_relationships": {"table_name": "entity_relationships", "row_count": 0, "column_count": 11, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "source_entity_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "target_entity_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "relationship_type", "position": 4, "data_type": "USER-DEFINED", "udt_name": "relationship_type_enum", "nullable": false, "default": null}, {"name": "relationship_name", "position": 5, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 255}, {"name": "description", "position": 6, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "strength", "position": 7, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": "0.5", "precision": 3, "scale": 2}, {"name": "is_bidirectional", "position": 8, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "false"}, {"name": "context_block_id", "position": 9, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "created_at", "position": 10, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 11, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"entity_relationships_different": "((source_entity_id <> target_entity_id))", "entity_relationships_strength_range": "(((strength >= (0)::numeric) AND (strength <= (1)::numeric)))", "2200_18941_1_not_null": "id IS NOT NULL", "2200_18941_4_not_null": "relationship_type IS NOT NULL"}, "indexes": [{"name": "entity_relationships_pkey", "definition": "CREATE UNIQUE INDEX entity_relationships_pkey ON public.entity_relationships USING btree (id)", "tablespace": null}, {"name": "idx_entity_relationships_source", "definition": "CREATE INDEX idx_entity_relationships_source ON public.entity_relationships USING btree (source_entity_id, relationship_type)", "tablespace": null}, {"name": "idx_entity_relationships_strength", "definition": "CREATE INDEX idx_entity_relationships_strength ON public.entity_relationships USING btree (strength DESC)", "tablespace": null}, {"name": "idx_entity_relationships_target", "definition": "CREATE INDEX idx_entity_relationships_target ON public.entity_relationships USING btree (target_entity_id, relationship_type)", "tablespace": null}, {"name": "idx_entity_relationships_unique", "definition": "CREATE UNIQUE INDEX idx_entity_relationships_unique ON public.entity_relationships USING btree (source_entity_id, target_entity_id, relationship_type)", "tablespace": null}], "foreign_keys": [{"source_column": "context_block_id", "target_table": "blocks", "target_column": "id"}, {"source_column": "source_entity_id", "target_table": "entities", "target_column": "id"}, {"source_column": "target_entity_id", "target_table": "entities", "target_column": "id"}]}, "export_jobs": {"table_name": "export_jobs", "row_count": 6, "column_count": 13, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "user_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "document_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "format", "position": 4, "data_type": "USER-DEFINED", "udt_name": "export_format_enum", "nullable": false, "default": null}, {"name": "status", "position": 5, "data_type": "USER-DEFINED", "udt_name": "export_status_enum", "nullable": true, "default": "'pending'::export_status_enum"}, {"name": "options", "position": 6, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}, {"name": "file_url", "position": 7, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "file_size", "position": 8, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "precision": 32}, {"name": "error_message", "position": 9, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "processing_started_at", "position": 10, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "processing_completed_at", "position": 11, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "expires_at", "position": 12, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "created_at", "position": 13, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"export_jobs_file_size_positive": "((file_size >= 0))", "export_jobs_options_valid": "((jsonb_typeof(options) = 'object'::text))", "2200_19049_1_not_null": "id IS NOT NULL", "2200_19049_4_not_null": "format IS NOT NULL"}, "indexes": [{"name": "export_jobs_pkey", "definition": "CREATE UNIQUE INDEX export_jobs_pkey ON public.export_jobs USING btree (id)", "tablespace": null}, {"name": "idx_export_jobs_document", "definition": "CREATE INDEX idx_export_jobs_document ON public.export_jobs USING btree (document_id, created_at DESC)", "tablespace": null}, {"name": "idx_export_jobs_expires", "definition": "CREATE INDEX idx_export_jobs_expires ON public.export_jobs USING btree (expires_at) WHERE (expires_at IS NOT NULL)", "tablespace": null}, {"name": "idx_export_jobs_status", "definition": "CREATE INDEX idx_export_jobs_status ON public.export_jobs USING btree (status)", "tablespace": null}, {"name": "idx_export_jobs_user", "definition": "CREATE INDEX idx_export_jobs_user ON public.export_jobs USING btree (user_id, created_at DESC)", "tablespace": null}], "foreign_keys": [{"source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "health_issues": {"table_name": "health_issues", "row_count": 6, "column_count": 14, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "health_metric_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "block_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "issue_type", "position": 4, "data_type": "USER-DEFINED", "udt_name": "health_issue_type_enum", "nullable": false, "default": null}, {"name": "severity", "position": 5, "data_type": "USER-DEFINED", "udt_name": "health_issue_severity_enum", "nullable": false, "default": null}, {"name": "category", "position": 6, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 50}, {"name": "description", "position": 7, "data_type": "text", "udt_name": "text", "nullable": false, "default": null}, {"name": "position", "position": 8, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": null}, {"name": "suggested_fix", "position": 9, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "confidence", "position": 10, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 3, "scale": 2}, {"name": "is_resolved", "position": 11, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "false"}, {"name": "resolved_at", "position": 12, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "resolved_method", "position": 13, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 50}, {"name": "created_at", "position": 14, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"health_issues_confidence_range": "(((confidence >= (0)::numeric) AND (confidence <= (1)::numeric)))", "health_issues_description_not_empty": "((length(TRIM(BOTH FROM description)) > 0))", "health_issues_position_valid": "(((jsonb_typeof(\"position\") = 'object'::text) AND (\"position\" ? 'start'::text) AND (\"position\" ? 'end'::text)))", "2200_18876_1_not_null": "id IS NOT NULL", "2200_18876_4_not_null": "issue_type IS NOT NULL", "2200_18876_5_not_null": "severity IS NOT NULL", "2200_18876_6_not_null": "category IS NOT NULL", "2200_18876_7_not_null": "description IS NOT NULL", "2200_18876_8_not_null": "position IS NOT NULL", "2200_18876_10_not_null": "confidence IS NOT NULL"}, "indexes": [{"name": "health_issues_pkey", "definition": "CREATE UNIQUE INDEX health_issues_pkey ON public.health_issues USING btree (id)", "tablespace": null}, {"name": "idx_health_issues_block", "definition": "CREATE INDEX idx_health_issues_block ON public.health_issues USING btree (block_id, severity, created_at DESC)", "tablespace": null}, {"name": "idx_health_issues_severity", "definition": "CREATE INDEX idx_health_issues_severity ON public.health_issues USING btree (severity, created_at DESC)", "tablespace": null}, {"name": "idx_health_issues_type", "definition": "CREATE INDEX idx_health_issues_type ON public.health_issues USING btree (issue_type, severity)", "tablespace": null}, {"name": "idx_health_issues_unresolved", "definition": "CREATE INDEX idx_health_issues_unresolved ON public.health_issues USING btree (block_id) WHERE (is_resolved = false)", "tablespace": null}], "foreign_keys": [{"source_column": "block_id", "target_table": "blocks", "target_column": "id"}, {"source_column": "health_metric_id", "target_table": "health_metrics", "target_column": "id"}]}, "health_metrics": {"table_name": "health_metrics", "row_count": 5, "column_count": 18, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "block_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "user_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "readability_score", "position": 4, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "clarity_score", "position": 5, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "voice_consistency_score", "position": 6, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "inclusivity_score", "position": 7, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "brand_alignment_score", "position": 8, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": null, "precision": 5, "scale": 2}, {"name": "overall_score", "position": 9, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "flesch_kincaid_grade", "position": 10, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": null, "precision": 4, "scale": 2}, {"name": "flesch_reading_ease", "position": 11, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": null, "precision": 5, "scale": 2}, {"name": "passive_voice_percentage", "position": 12, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": null, "precision": 4, "scale": 2}, {"name": "avg_sentence_length", "position": 13, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": null, "precision": 5, "scale": 2}, {"name": "complex_words_percentage", "position": 14, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": null, "precision": 4, "scale": 2}, {"name": "processing_time_ms", "position": 15, "data_type": "integer", "udt_name": "int4", "nullable": false, "default": null, "precision": 32}, {"name": "analysis_version", "position": 16, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": "'1.0'::character varying", "max_length": 10}, {"name": "created_at", "position": 17, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "text_hash", "position": 18, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 32}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"health_metrics_brand_range": "(((brand_alignment_score IS NULL) OR ((brand_alignment_score >= (0)::numeric) AND (brand_alignment_score <= (100)::numeric))))", "health_metrics_processing_time_positive": "((processing_time_ms >= 0))", "health_metrics_scores_range": "(((readability_score >= (0)::numeric) AND (readability_score <= (100)::numeric) AND (clarity_score >= (0)::numeric) AND (clarity_score <= (100)::numeric) AND (voice_consistency_score >= (0)::numeric) AND (voice_consistency_score <= (100)::numeric) AND (inclusivity_score >= (0)::numeric) AND (inclusivity_score <= (100)::numeric) AND (overall_score >= (0)::numeric) AND (overall_score <= (100)::numeric)))", "2200_18855_1_not_null": "id IS NOT NULL", "2200_18855_4_not_null": "readability_score IS NOT NULL", "2200_18855_5_not_null": "clarity_score IS NOT NULL", "2200_18855_6_not_null": "voice_consistency_score IS NOT NULL", "2200_18855_7_not_null": "inclusivity_score IS NOT NULL", "2200_18855_9_not_null": "overall_score IS NOT NULL", "2200_18855_15_not_null": "processing_time_ms IS NOT NULL"}, "indexes": [{"name": "health_metrics_pkey", "definition": "CREATE UNIQUE INDEX health_metrics_pkey ON public.health_metrics USING btree (id)", "tablespace": null}, {"name": "idx_health_metrics_block", "definition": "CREATE INDEX idx_health_metrics_block ON public.health_metrics USING btree (block_id, created_at DESC)", "tablespace": null}, {"name": "idx_health_metrics_created", "definition": "CREATE INDEX idx_health_metrics_created ON public.health_metrics USING btree (created_at DESC)", "tablespace": null}, {"name": "idx_health_metrics_overall_score", "definition": "CREATE INDEX idx_health_metrics_overall_score ON public.health_metrics USING btree (overall_score DESC)", "tablespace": null}, {"name": "idx_health_metrics_user", "definition": "CREATE INDEX idx_health_metrics_user ON public.health_metrics USING btree (user_id, created_at DESC)", "tablespace": null}], "foreign_keys": [{"source_column": "block_id", "target_table": "blocks", "target_column": "id"}, {"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "persona_feedback": {"table_name": "persona_feedback", "row_count": 5, "column_count": 17, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "block_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "persona_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "session_id", "position": 4, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": null}, {"name": "comprehension_score", "position": 5, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 3, "scale": 2}, {"name": "engagement_score", "position": 6, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 3, "scale": 2}, {"name": "emotional_score", "position": 7, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 3, "scale": 2}, {"name": "style_score", "position": 8, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 3, "scale": 2}, {"name": "overall_score", "position": 9, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 3, "scale": 2}, {"name": "feedback_text", "position": 10, "data_type": "text", "udt_name": "text", "nullable": false, "default": null}, {"name": "specific_issues", "position": 11, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'[]'::jsonb"}, {"name": "suggestions", "position": 12, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'[]'::jsonb"}, {"name": "confidence", "position": 13, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 3, "scale": 2}, {"name": "processing_time_ms", "position": 14, "data_type": "integer", "udt_name": "int4", "nullable": false, "default": null, "precision": 32}, {"name": "model_used", "position": 15, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 50}, {"name": "tokens_used", "position": 16, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "created_at", "position": 17, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"persona_feedback_issues_valid": "((jsonb_typeof(specific_issues) = 'array'::text))", "persona_feedback_processing_time_positive": "((processing_time_ms >= 0))", "persona_feedback_scores_range": "(((comprehension_score >= (0)::numeric) AND (comprehension_score <= (1)::numeric) AND (engagement_score >= (0)::numeric) AND (engagement_score <= (1)::numeric) AND (emotional_score >= (0)::numeric) AND (emotional_score <= (1)::numeric) AND (style_score >= (0)::numeric) AND (style_score <= (1)::numeric) AND (overall_score >= (0)::numeric) AND (overall_score <= (1)::numeric) AND (confidence >= (0)::numeric) AND (confidence <= (1)::numeric)))", "persona_feedback_suggestions_valid": "((jsonb_typeof(suggestions) = 'array'::text))", "persona_feedback_text_not_empty": "((length(TRIM(BOTH FROM feedback_text)) > 0))", "persona_feedback_tokens_positive": "((tokens_used >= 0))", "2200_18728_1_not_null": "id IS NOT NULL", "2200_18728_4_not_null": "session_id IS NOT NULL", "2200_18728_5_not_null": "comprehension_score IS NOT NULL", "2200_18728_6_not_null": "engagement_score IS NOT NULL", "2200_18728_7_not_null": "emotional_score IS NOT NULL", "2200_18728_8_not_null": "style_score IS NOT NULL", "2200_18728_9_not_null": "overall_score IS NOT NULL", "2200_18728_10_not_null": "feedback_text IS NOT NULL", "2200_18728_13_not_null": "confidence IS NOT NULL", "2200_18728_14_not_null": "processing_time_ms IS NOT NULL"}, "indexes": [{"name": "idx_persona_feedback_block", "definition": "CREATE INDEX idx_persona_feedback_block ON public.persona_feedback USING btree (block_id, created_at DESC)", "tablespace": null}, {"name": "idx_persona_feedback_persona", "definition": "CREATE INDEX idx_persona_feedback_persona ON public.persona_feedback USING btree (persona_id, created_at DESC)", "tablespace": null}, {"name": "idx_persona_feedback_score", "definition": "CREATE INDEX idx_persona_feedback_score ON public.persona_feedback USING btree (overall_score DESC)", "tablespace": null}, {"name": "idx_persona_feedback_session", "definition": "CREATE INDEX idx_persona_feedback_session ON public.persona_feedback USING btree (session_id)", "tablespace": null}, {"name": "persona_feedback_pkey", "definition": "CREATE UNIQUE INDEX persona_feedback_pkey ON public.persona_feedback USING btree (id)", "tablespace": null}], "foreign_keys": [{"source_column": "block_id", "target_table": "blocks", "target_column": "id"}, {"source_column": "persona_id", "target_table": "personas", "target_column": "id"}]}, "personas": {"table_name": "personas", "row_count": 8, "column_count": 15, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "user_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "name", "position": 3, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 255}, {"name": "description", "position": 4, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "type", "position": 5, "data_type": "USER-DEFINED", "udt_name": "persona_type_enum", "nullable": false, "default": null}, {"name": "demographics", "position": 6, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": "'{}'::jsonb"}, {"name": "reading_preferences", "position": 7, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": "'{}'::jsonb"}, {"name": "personality_traits", "position": 8, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": "'{}'::jsonb"}, {"name": "feedback_style", "position": 9, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": "'{}'::jsonb"}, {"name": "is_active", "position": 10, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "true"}, {"name": "is_template", "position": 11, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "false"}, {"name": "usage_count", "position": 12, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "effectiveness_score", "position": 13, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": null, "precision": 3, "scale": 2}, {"name": "created_at", "position": 14, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 15, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"personas_demographics_valid": "((jsonb_typeof(demographics) = 'object'::text))", "personas_effectiveness_range": "(((effectiveness_score IS NULL) OR ((effectiveness_score >= (0)::numeric) AND (effectiveness_score <= (1)::numeric))))", "personas_feedback_style_valid": "((jsonb_typeof(feedback_style) = 'object'::text))", "personas_name_not_empty": "((length(TRIM(BOTH FROM name)) > 0))", "personas_personality_valid": "((jsonb_typeof(personality_traits) = 'object'::text))", "personas_reading_prefs_valid": "((jsonb_typeof(reading_preferences) = 'object'::text))", "personas_usage_count_positive": "((usage_count >= 0))", "2200_18699_1_not_null": "id IS NOT NULL", "2200_18699_3_not_null": "name IS NOT NULL", "2200_18699_5_not_null": "type IS NOT NULL", "2200_18699_6_not_null": "demographics IS NOT NULL", "2200_18699_7_not_null": "reading_preferences IS NOT NULL", "2200_18699_8_not_null": "personality_traits IS NOT NULL", "2200_18699_9_not_null": "feedback_style IS NOT NULL"}, "indexes": [{"name": "idx_personas_effectiveness", "definition": "CREATE INDEX idx_personas_effectiveness ON public.personas USING btree (effectiveness_score DESC) WHERE (effectiveness_score IS NOT NULL)", "tablespace": null}, {"name": "idx_personas_template", "definition": "CREATE INDEX idx_personas_template ON public.personas USING btree (is_template) WHERE (is_template = true)", "tablespace": null}, {"name": "idx_personas_type", "definition": "CREATE INDEX idx_personas_type ON public.personas USING btree (type) WHERE (is_active = true)", "tablespace": null}, {"name": "idx_personas_usage", "definition": "CREATE INDEX idx_personas_usage ON public.personas USING btree (usage_count DESC)", "tablespace": null}, {"name": "idx_personas_user", "definition": "CREATE INDEX idx_personas_user ON public.personas USING btree (user_id) WHERE (is_active = true)", "tablespace": null}, {"name": "personas_pkey", "definition": "CREATE UNIQUE INDEX personas_pkey ON public.personas USING btree (id)", "tablespace": null}], "foreign_keys": [{"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "reference_library": {"table_name": "reference_library", "row_count": 0, "column_count": 16, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "user_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "title", "position": 3, "data_type": "text", "udt_name": "text", "nullable": false, "default": null}, {"name": "authors", "position": 4, "data_type": "ARRAY", "udt_name": "_text", "nullable": false, "default": null}, {"name": "publication_year", "position": 5, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "precision": 32}, {"name": "citation_type", "position": 6, "data_type": "USER-DEFINED", "udt_name": "citation_type_enum", "nullable": false, "default": null}, {"name": "metadata", "position": 7, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}, {"name": "tags", "position": 8, "data_type": "ARRAY", "udt_name": "_text", "nullable": true, "default": "'{}'::text[]"}, {"name": "notes", "position": 9, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "file_url", "position": 10, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "doi", "position": 11, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 255}, {"name": "url", "position": 12, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "is_favorite", "position": 13, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "false"}, {"name": "usage_count", "position": 14, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "created_at", "position": 15, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 16, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"reference_library_authors_not_empty": "((array_length(authors, 1) > 0))", "reference_library_metadata_valid": "((jsonb_typeof(metadata) = 'object'::text))", "reference_library_title_not_empty": "((length(TRIM(BOTH FROM title)) > 0))", "reference_library_usage_positive": "((usage_count >= 0))", "2200_19026_1_not_null": "id IS NOT NULL", "2200_19026_3_not_null": "title IS NOT NULL", "2200_19026_4_not_null": "authors IS NOT NULL", "2200_19026_6_not_null": "citation_type IS NOT NULL"}, "indexes": [{"name": "idx_reference_library_authors", "definition": "CREATE INDEX idx_reference_library_authors ON public.reference_library USING gin (authors)", "tablespace": null}, {"name": "idx_reference_library_favorites", "definition": "CREATE INDEX idx_reference_library_favorites ON public.reference_library USING btree (user_id) WHERE (is_favorite = true)", "tablespace": null}, {"name": "idx_reference_library_tags", "definition": "CREATE INDEX idx_reference_library_tags ON public.reference_library USING gin (tags)", "tablespace": null}, {"name": "idx_reference_library_title_search", "definition": "CREATE INDEX idx_reference_library_title_search ON public.reference_library USING gin (to_tsvector('english'::regconfig, title))", "tablespace": null}, {"name": "idx_reference_library_type", "definition": "CREATE INDEX idx_reference_library_type ON public.reference_library USING btree (citation_type)", "tablespace": null}, {"name": "idx_reference_library_usage", "definition": "CREATE INDEX idx_reference_library_usage ON public.reference_library USING btree (usage_count DESC)", "tablespace": null}, {"name": "idx_reference_library_user", "definition": "CREATE INDEX idx_reference_library_user ON public.reference_library USING btree (user_id, created_at DESC)", "tablespace": null}, {"name": "reference_library_pkey", "definition": "CREATE UNIQUE INDEX reference_library_pkey ON public.reference_library USING btree (id)", "tablespace": null}], "foreign_keys": [{"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "schema_migrations": {"table_name": "schema_migrations", "row_count": 0, "column_count": 2, "columns": [{"name": "version", "position": 1, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 255}, {"name": "executed_at", "position": 2, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["version"], "unique_constraints": {}, "check_constraints": {"2200_17252_1_not_null": "version IS NOT NULL"}, "indexes": [{"name": "schema_migrations_pkey", "definition": "CREATE UNIQUE INDEX schema_migrations_pkey ON public.schema_migrations USING btree (version)", "tablespace": null}], "foreign_keys": []}, "scoring_algorithms": {"table_name": "scoring_algorithms", "row_count": 4, "column_count": 11, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "version", "position": 2, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 10}, {"name": "algorithm_name", "position": 3, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 100}, {"name": "description", "position": 4, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "grammar_weights", "position": 5, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": null}, {"name": "style_weights", "position": 6, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": null}, {"name": "structure_weights", "position": 7, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": null}, {"name": "content_weights", "position": 8, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": null}, {"name": "document_type_adjustments", "position": 9, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": null}, {"name": "is_active", "position": 10, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "false"}, {"name": "created_at", "position": 11, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {"scoring_algorithms_version_key": ["version"]}, "check_constraints": {"scoring_algorithms_weights_valid": "(((jsonb_typeof(grammar_weights) = 'object'::text) AND (jsonb_typeof(style_weights) = 'object'::text) AND (jsonb_typeof(structure_weights) = 'object'::text) AND (jsonb_typeof(content_weights) = 'object'::text) AND (jsonb_typeof(document_type_adjustments) = 'object'::text)))", "2200_18638_1_not_null": "id IS NOT NULL", "2200_18638_2_not_null": "version IS NOT NULL", "2200_18638_3_not_null": "algorithm_name IS NOT NULL", "2200_18638_5_not_null": "grammar_weights IS NOT NULL", "2200_18638_6_not_null": "style_weights IS NOT NULL", "2200_18638_7_not_null": "structure_weights IS NOT NULL", "2200_18638_8_not_null": "content_weights IS NOT NULL", "2200_18638_9_not_null": "document_type_adjustments IS NOT NULL"}, "indexes": [{"name": "idx_scoring_algorithms_active", "definition": "CREATE UNIQUE INDEX idx_scoring_algorithms_active ON public.scoring_algorithms USING btree (is_active) WHERE (is_active = true)", "tablespace": null}, {"name": "scoring_algorithms_pkey", "definition": "CREATE UNIQUE INDEX scoring_algorithms_pkey ON public.scoring_algorithms USING btree (id)", "tablespace": null}, {"name": "scoring_algorithms_version_key", "definition": "CREATE UNIQUE INDEX scoring_algorithms_version_key ON public.scoring_algorithms USING btree (version)", "tablespace": null}], "foreign_keys": []}, "suggestions": {"table_name": "suggestions", "row_count": 13, "column_count": 21, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "block_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "agent_type", "position": 3, "data_type": "USER-DEFINED", "udt_name": "agent_type_enum", "nullable": false, "default": null}, {"name": "severity", "position": 4, "data_type": "USER-DEFINED", "udt_name": "suggestion_severity_enum", "nullable": false, "default": null}, {"name": "category", "position": 5, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 50}, {"name": "original_text", "position": 6, "data_type": "text", "udt_name": "text", "nullable": false, "default": null}, {"name": "suggested_text", "position": 7, "data_type": "text", "udt_name": "text", "nullable": false, "default": null}, {"name": "explanation", "position": 8, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "confidence", "position": 9, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": null, "precision": 3, "scale": 2}, {"name": "position", "position": 10, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": null}, {"name": "original_hash", "position": 11, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 64}, {"name": "status", "position": 12, "data_type": "USER-DEFINED", "udt_name": "suggestion_status_enum", "nullable": true, "default": "'pending'::suggestion_status_enum"}, {"name": "user_feedback", "position": 13, "data_type": "USER-DEFINED", "udt_name": "suggestion_feedback_enum", "nullable": true, "default": null}, {"name": "feedback_note", "position": 14, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "tokens_used", "position": 15, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "model_used", "position": 16, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 50}, {"name": "processing_time_ms", "position": 17, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "precision": 32}, {"name": "created_at", "position": 18, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "resolved_at", "position": 19, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "resolved_by", "position": 20, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "expires_at", "position": 21, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"suggestions_confidence_check": "(((confidence >= (0)::numeric) AND (confidence <= (1)::numeric)))", "suggestions_confidence_range": "(((confidence IS NULL) OR ((confidence >= (0)::numeric) AND (confidence <= (1)::numeric))))", "suggestions_original_hash_valid": "(((original_hash)::text ~ '^[a-f0-9]{64}$'::text))", "suggestions_position_valid": "(((jsonb_typeof(\"position\") = 'object'::text) AND (\"position\" ? 'start'::text) AND (\"position\" ? 'end'::text)))", "suggestions_processing_time_positive": "((processing_time_ms >= 0))", "suggestions_tokens_positive": "((tokens_used >= 0))", "2200_18438_1_not_null": "id IS NOT NULL", "2200_18438_3_not_null": "agent_type IS NOT NULL", "2200_18438_4_not_null": "severity IS NOT NULL", "2200_18438_6_not_null": "original_text IS NOT NULL", "2200_18438_7_not_null": "suggested_text IS NOT NULL", "2200_18438_10_not_null": "position IS NOT NULL", "2200_18438_11_not_null": "original_hash IS NOT NULL"}, "indexes": [{"name": "idx_suggestions_active", "definition": "CREATE INDEX idx_suggestions_active ON public.suggestions USING btree (block_id, created_at DESC) WHERE (status = ANY (ARRAY['pending'::suggestion_status_enum, 'accepted'::suggestion_status_enum]))", "tablespace": null}, {"name": "idx_suggestions_agent_severity", "definition": "CREATE INDEX idx_suggestions_agent_severity ON public.suggestions USING btree (agent_type, severity)", "tablespace": null}, {"name": "idx_suggestions_block_status", "definition": "CREATE INDEX idx_suggestions_block_status ON public.suggestions USING btree (block_id, status)", "tablespace": null}, {"name": "idx_suggestions_created", "definition": "CREATE INDEX idx_suggestions_created ON public.suggestions USING btree (created_at DESC)", "tablespace": null}, {"name": "idx_suggestions_expires", "definition": "CREATE INDEX idx_suggestions_expires ON public.suggestions USING btree (expires_at) WHERE (expires_at IS NOT NULL)", "tablespace": null}, {"name": "idx_suggestions_hash", "definition": "CREATE INDEX idx_suggestions_hash ON public.suggestions USING btree (original_hash)", "tablespace": null}, {"name": "idx_suggestions_pending", "definition": "CREATE INDEX idx_suggestions_pending ON public.suggestions USING btree (block_id) WHERE (status = 'pending'::suggestion_status_enum)", "tablespace": null}, {"name": "idx_suggestions_position", "definition": "CREATE INDEX idx_suggestions_position ON public.suggestions USING gin (\"position\")", "tablespace": null}, {"name": "suggestions_pkey", "definition": "CREATE UNIQUE INDEX suggestions_pkey ON public.suggestions USING btree (id)", "tablespace": null}], "foreign_keys": [{"source_column": "block_id", "target_table": "blocks", "target_column": "id"}, {"source_column": "resolved_by", "target_table": "users", "target_column": "id"}]}, "summaries": {"table_name": "summaries", "row_count": 9, "column_count": 11, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "block_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "level", "position": 3, "data_type": "USER-DEFINED", "udt_name": "summary_level_enum", "nullable": false, "default": null}, {"name": "summary_text", "position": 4, "data_type": "text", "udt_name": "text", "nullable": false, "default": null}, {"name": "key_points", "position": 5, "data_type": "ARRAY", "udt_name": "_text", "nullable": true, "default": null}, {"name": "embedding", "position": 6, "data_type": "USER-DEFINED", "udt_name": "vector", "nullable": true, "default": null}, {"name": "token_count", "position": 7, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "model_used", "position": 8, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 50}, {"name": "created_at", "position": 9, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "expires_at", "position": 10, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "content_hash", "position": 11, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 64}], "primary_keys": ["id"], "unique_constraints": {"summaries_block_id_level_content_hash_key": ["block_id", "level", "content_hash"]}, "check_constraints": {"summaries_content_hash_valid": "(((content_hash)::text ~ '^[a-f0-9]{64}$'::text))", "summaries_summary_not_empty": "((length(TRIM(BOTH FROM summary_text)) > 0))", "summaries_token_count_positive": "((token_count >= 0))", "2200_18465_1_not_null": "id IS NOT NULL", "2200_18465_3_not_null": "level IS NOT NULL", "2200_18465_4_not_null": "summary_text IS NOT NULL", "2200_18465_11_not_null": "content_hash IS NOT NULL"}, "indexes": [{"name": "idx_summaries_block_level", "definition": "CREATE INDEX idx_summaries_block_level ON public.summaries USING btree (block_id, level)", "tablespace": null}, {"name": "idx_summaries_embedding", "definition": "CREATE INDEX idx_summaries_embedding ON public.summaries USING ivfflat (embedding vector_cosine_ops) WITH (lists='100')", "tablespace": null}, {"name": "idx_summaries_expires", "definition": "CREATE INDEX idx_summaries_expires ON public.summaries USING btree (expires_at) WHERE (expires_at IS NOT NULL)", "tablespace": null}, {"name": "idx_summaries_hash", "definition": "CREATE INDEX idx_summaries_hash ON public.summaries USING btree (content_hash)", "tablespace": null}, {"name": "summaries_block_id_level_content_hash_key", "definition": "CREATE UNIQUE INDEX summaries_block_id_level_content_hash_key ON public.summaries USING btree (block_id, level, content_hash)", "tablespace": null}, {"name": "summaries_pkey", "definition": "CREATE UNIQUE INDEX summaries_pkey ON public.summaries USING btree (id)", "tablespace": null}], "foreign_keys": [{"source_column": "block_id", "target_table": "blocks", "target_column": "id"}]}, "templates": {"table_name": "templates", "row_count": 6, "column_count": 14, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "name", "position": 2, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 255}, {"name": "description", "position": 3, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "category", "position": 4, "data_type": "USER-DEFINED", "udt_name": "template_category_enum", "nullable": false, "default": null}, {"name": "document_type", "position": 5, "data_type": "USER-DEFINED", "udt_name": "document_type_enum", "nullable": false, "default": null}, {"name": "is_public", "position": 6, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "false"}, {"name": "created_by", "position": 7, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "structure", "position": 8, "data_type": "jsonb", "udt_name": "jsonb", "nullable": false, "default": null}, {"name": "metadata", "position": 9, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}, {"name": "usage_count", "position": 10, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "rating", "position": 11, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": null, "precision": 3, "scale": 2}, {"name": "tags", "position": 12, "data_type": "ARRAY", "udt_name": "_text", "nullable": true, "default": "'{}'::text[]"}, {"name": "created_at", "position": 13, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 14, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"templates_metadata_valid": "((jsonb_typeof(metadata) = 'object'::text))", "templates_name_not_empty": "((length(TRIM(BOTH FROM name)) > 0))", "templates_rating_range": "(((rating IS NULL) OR ((rating >= (0)::numeric) AND (rating <= (5)::numeric))))", "templates_structure_valid": "((jsonb_typeof(structure) = 'object'::text))", "templates_usage_count_positive": "((usage_count >= 0))", "2200_19072_1_not_null": "id IS NOT NULL", "2200_19072_2_not_null": "name IS NOT NULL", "2200_19072_4_not_null": "category IS NOT NULL", "2200_19072_5_not_null": "document_type IS NOT NULL", "2200_19072_8_not_null": "structure IS NOT NULL"}, "indexes": [{"name": "idx_templates_category", "definition": "CREATE INDEX idx_templates_category ON public.templates USING btree (category) WHERE (is_public = true)", "tablespace": null}, {"name": "idx_templates_created_by", "definition": "CREATE INDEX idx_templates_created_by ON public.templates USING btree (created_by)", "tablespace": null}, {"name": "idx_templates_rating", "definition": "CREATE INDEX idx_templates_rating ON public.templates USING btree (rating DESC) WHERE ((is_public = true) AND (rating IS NOT NULL))", "tablespace": null}, {"name": "idx_templates_tags", "definition": "CREATE INDEX idx_templates_tags ON public.templates USING gin (tags)", "tablespace": null}, {"name": "idx_templates_type", "definition": "CREATE INDEX idx_templates_type ON public.templates USING btree (document_type) WHERE (is_public = true)", "tablespace": null}, {"name": "idx_templates_usage", "definition": "CREATE INDEX idx_templates_usage ON public.templates USING btree (usage_count DESC) WHERE (is_public = true)", "tablespace": null}, {"name": "templates_pkey", "definition": "CREATE UNIQUE INDEX templates_pkey ON public.templates USING btree (id)", "tablespace": null}], "foreign_keys": [{"source_column": "created_by", "target_table": "users", "target_column": "id"}]}, "token_usage_daily": {"table_name": "token_usage_daily", "row_count": 15, "column_count": 9, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "user_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "date", "position": 3, "data_type": "date", "udt_name": "date", "nullable": false, "default": null}, {"name": "tokens_total", "position": 4, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "tokens_by_model", "position": 5, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}, {"name": "tokens_by_agent", "position": 6, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}, {"name": "operations_count", "position": 7, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "documents_edited", "position": 8, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "created_at", "position": 9, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {"token_usage_daily_user_id_date_key": ["user_id", "date"]}, "check_constraints": {"token_usage_daily_documents_positive": "((documents_edited >= 0))", "token_usage_daily_operations_positive": "((operations_count >= 0))", "token_usage_daily_tokens_positive": "((tokens_total >= 0))", "2200_18675_1_not_null": "id IS NOT NULL", "2200_18675_3_not_null": "date IS NOT NULL"}, "indexes": [{"name": "idx_token_usage_daily_date", "definition": "CREATE INDEX idx_token_usage_daily_date ON public.token_usage_daily USING btree (date DESC)", "tablespace": null}, {"name": "idx_token_usage_daily_user_date", "definition": "CREATE INDEX idx_token_usage_daily_user_date ON public.token_usage_daily USING btree (user_id, date DESC)", "tablespace": null}, {"name": "token_usage_daily_pkey", "definition": "CREATE UNIQUE INDEX token_usage_daily_pkey ON public.token_usage_daily USING btree (id)", "tablespace": null}, {"name": "token_usage_daily_user_id_date_key", "definition": "CREATE UNIQUE INDEX token_usage_daily_user_id_date_key ON public.token_usage_daily USING btree (user_id, date)", "tablespace": null}], "foreign_keys": [{"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "usage_events": {"table_name": "usage_events", "row_count": 20, "column_count": 9, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "user_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "document_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "event_type", "position": 4, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 50}, {"name": "event_data", "position": 5, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}, {"name": "tokens_used", "position": 6, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "model_used", "position": 7, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 50}, {"name": "processing_time_ms", "position": 8, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "precision": 32}, {"name": "created_at", "position": 9, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"usage_events_event_data_valid": "((jsonb_typeof(event_data) = 'object'::text))", "usage_events_processing_time_positive": "((processing_time_ms >= 0))", "usage_events_tokens_positive": "((tokens_used >= 0))", "2200_18651_1_not_null": "id IS NOT NULL", "2200_18651_4_not_null": "event_type IS NOT NULL"}, "indexes": [{"name": "idx_usage_events_document", "definition": "CREATE INDEX idx_usage_events_document ON public.usage_events USING btree (document_id, created_at DESC)", "tablespace": null}, {"name": "idx_usage_events_tokens", "definition": "CREATE INDEX idx_usage_events_tokens ON public.usage_events USING btree (tokens_used) WHERE (tokens_used > 0)", "tablespace": null}, {"name": "idx_usage_events_type", "definition": "CREATE INDEX idx_usage_events_type ON public.usage_events USING btree (event_type, created_at DESC)", "tablespace": null}, {"name": "idx_usage_events_user_date", "definition": "CREATE INDEX idx_usage_events_user_date ON public.usage_events USING btree (user_id, created_at DESC)", "tablespace": null}, {"name": "usage_events_pkey", "definition": "CREATE UNIQUE INDEX usage_events_pkey ON public.usage_events USING btree (id)", "tablespace": null}], "foreign_keys": [{"source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "user_achievements": {"table_name": "user_achievements", "row_count": 0, "column_count": 5, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "user_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": null}, {"name": "achievement_id", "position": 3, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 100}, {"name": "unlocked_at", "position": 4, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": false, "default": "now()"}, {"name": "progress_data", "position": 5, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": null}], "primary_keys": ["id"], "unique_constraints": {"user_achievements_user_id_achievement_id_key": ["user_id", "achievement_id"]}, "check_constraints": {"user_achievements_progress_valid": "(((progress_data IS NULL) OR (jsonb_typeof(progress_data) = 'object'::text)))", "2200_33173_1_not_null": "id IS NOT NULL", "2200_33173_2_not_null": "user_id IS NOT NULL", "2200_33173_3_not_null": "achievement_id IS NOT NULL", "2200_33173_4_not_null": "unlocked_at IS NOT NULL"}, "indexes": [{"name": "idx_user_achievements_achievement_id", "definition": "CREATE INDEX idx_user_achievements_achievement_id ON public.user_achievements USING btree (achievement_id)", "tablespace": null}, {"name": "idx_user_achievements_unlocked_at", "definition": "CREATE INDEX idx_user_achievements_unlocked_at ON public.user_achievements USING btree (unlocked_at)", "tablespace": null}, {"name": "idx_user_achievements_user_id", "definition": "CREATE INDEX idx_user_achievements_user_id ON public.user_achievements USING btree (user_id)", "tablespace": null}, {"name": "user_achievements_pkey", "definition": "CREATE UNIQUE INDEX user_achievements_pkey ON public.user_achievements USING btree (id)", "tablespace": null}, {"name": "user_achievements_user_id_achievement_id_key", "definition": "CREATE UNIQUE INDEX user_achievements_user_id_achievement_id_key ON public.user_achievements USING btree (user_id, achievement_id)", "tablespace": null}], "foreign_keys": [{"source_column": "achievement_id", "target_table": "achievements", "target_column": "id"}, {"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "user_writing_stats": {"table_name": "user_writing_stats", "row_count": 22, "column_count": 15, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "user_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "document_type", "position": 3, "data_type": "USER-DEFINED", "udt_name": "document_type_enum", "nullable": false, "default": null}, {"name": "date", "position": 4, "data_type": "date", "udt_name": "date", "nullable": false, "default": null}, {"name": "average_score", "position": 5, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "average_grammar", "position": 6, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "average_style", "position": 7, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "average_structure", "position": 8, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "average_content", "position": 9, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 5, "scale": 2}, {"name": "documents_scored", "position": 10, "data_type": "integer", "udt_name": "int4", "nullable": false, "default": null, "precision": 32}, {"name": "total_words", "position": 11, "data_type": "integer", "udt_name": "int4", "nullable": false, "default": null, "precision": 32}, {"name": "improvement_trend", "position": 12, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": null, "precision": 5, "scale": 2}, {"name": "streak_days", "position": 13, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "created_at", "position": 14, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 15, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {"user_writing_stats_user_id_document_type_date_key": ["user_id", "document_type", "date"]}, "check_constraints": {"user_writing_stats_counts_positive": "(((documents_scored >= 0) AND (total_words >= 0) AND (streak_days >= 0)))", "user_writing_stats_scores_range": "(((average_score >= (0)::numeric) AND (average_score <= (100)::numeric) AND (average_grammar >= (0)::numeric) AND (average_grammar <= (25)::numeric) AND (average_style >= (0)::numeric) AND (average_style <= (25)::numeric) AND (average_structure >= (0)::numeric) AND (average_structure <= (25)::numeric) AND (average_content >= (0)::numeric) AND (average_content <= (25)::numeric)))", "2200_18573_1_not_null": "id IS NOT NULL", "2200_18573_3_not_null": "document_type IS NOT NULL", "2200_18573_4_not_null": "date IS NOT NULL", "2200_18573_5_not_null": "average_score IS NOT NULL", "2200_18573_6_not_null": "average_grammar IS NOT NULL", "2200_18573_7_not_null": "average_style IS NOT NULL", "2200_18573_8_not_null": "average_structure IS NOT NULL", "2200_18573_9_not_null": "average_content IS NOT NULL", "2200_18573_10_not_null": "documents_scored IS NOT NULL", "2200_18573_11_not_null": "total_words IS NOT NULL"}, "indexes": [{"name": "idx_user_writing_stats_score", "definition": "CREATE INDEX idx_user_writing_stats_score ON public.user_writing_stats USING btree (average_score DESC)", "tablespace": null}, {"name": "idx_user_writing_stats_type", "definition": "CREATE INDEX idx_user_writing_stats_type ON public.user_writing_stats USING btree (document_type, date DESC)", "tablespace": null}, {"name": "idx_user_writing_stats_user_date", "definition": "CREATE INDEX idx_user_writing_stats_user_date ON public.user_writing_stats USING btree (user_id, date DESC)", "tablespace": null}, {"name": "user_writing_stats_pkey", "definition": "CREATE UNIQUE INDEX user_writing_stats_pkey ON public.user_writing_stats USING btree (id)", "tablespace": null}, {"name": "user_writing_stats_user_id_document_type_date_key", "definition": "CREATE UNIQUE INDEX user_writing_stats_user_id_document_type_date_key ON public.user_writing_stats USING btree (user_id, document_type, date)", "tablespace": null}], "foreign_keys": [{"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "users": {"table_name": "users", "row_count": 5, "column_count": 15, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "firebase_uid", "position": 2, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 128}, {"name": "email", "position": 3, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 255}, {"name": "display_name", "position": 4, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": null, "max_length": 255}, {"name": "avatar_url", "position": 5, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "subscription_tier", "position": 6, "data_type": "USER-DEFINED", "udt_name": "subscription_tier_enum", "nullable": false, "default": "'free'::subscription_tier_enum"}, {"name": "subscription_status", "position": 7, "data_type": "USER-DEFINED", "udt_name": "subscription_status_enum", "nullable": false, "default": "'active'::subscription_status_enum"}, {"name": "subscription_period_start", "position": 8, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "subscription_period_end", "position": 9, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "onboarding_completed", "position": 10, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "false"}, {"name": "preferences", "position": 11, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}, {"name": "created_at", "position": 12, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 13, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "last_active_at", "position": 14, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "deleted_at", "position": 15, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}], "primary_keys": ["id"], "unique_constraints": {"users_firebase_uid_key": ["firebase_uid"]}, "check_constraints": {"users_email_valid": "(((email)::text ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'::text))", "users_preferences_valid": "((jsonb_typeof(preferences) = 'object'::text))", "2200_18331_1_not_null": "id IS NOT NULL", "2200_18331_2_not_null": "firebase_uid IS NOT NULL", "2200_18331_3_not_null": "email IS NOT NULL", "2200_18331_6_not_null": "subscription_tier IS NOT NULL", "2200_18331_7_not_null": "subscription_status IS NOT NULL"}, "indexes": [{"name": "idx_users_email", "definition": "CREATE INDEX idx_users_email ON public.users USING btree (email)", "tablespace": null}, {"name": "idx_users_firebase_uid", "definition": "CREATE INDEX idx_users_firebase_uid ON public.users USING btree (firebase_uid)", "tablespace": null}, {"name": "idx_users_last_active", "definition": "CREATE INDEX idx_users_last_active ON public.users USING btree (last_active_at) WHERE (deleted_at IS NULL)", "tablespace": null}, {"name": "idx_users_subscription", "definition": "CREATE INDEX idx_users_subscription ON public.users USING btree (subscription_tier, subscription_status)", "tablespace": null}, {"name": "users_firebase_uid_key", "definition": "CREATE UNIQUE INDEX users_firebase_uid_key ON public.users USING btree (firebase_uid)", "tablespace": null}, {"name": "users_pkey", "definition": "CREATE UNIQUE INDEX users_pkey ON public.users USING btree (id)", "tablespace": null}], "foreign_keys": []}, "versions": {"table_name": "versions", "row_count": 14, "column_count": 11, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "block_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "version_number", "position": 3, "data_type": "integer", "udt_name": "int4", "nullable": false, "default": null, "precision": 32}, {"name": "content", "position": 4, "data_type": "text", "udt_name": "text", "nullable": false, "default": null}, {"name": "content_hash", "position": 5, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 64}, {"name": "word_count", "position": 6, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "author_id", "position": 7, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "change_summary", "position": 8, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "change_type", "position": 9, "data_type": "USER-DEFINED", "udt_name": "version_change_type_enum", "nullable": true, "default": "'edit'::version_change_type_enum"}, {"name": "diff_ops", "position": 10, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": null}, {"name": "created_at", "position": 11, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {"versions_block_id_version_number_key": ["block_id", "version_number"]}, "check_constraints": {"versions_content_hash_valid": "(((content_hash)::text ~ '^[a-f0-9]{64}$'::text))", "versions_diff_ops_valid": "(((diff_ops IS NULL) OR (jsonb_typeof(diff_ops) = 'array'::text)))", "versions_version_number_positive": "((version_number > 0))", "versions_word_count_positive": "((word_count >= 0))", "2200_18411_1_not_null": "id IS NOT NULL", "2200_18411_3_not_null": "version_number IS NOT NULL", "2200_18411_4_not_null": "content IS NOT NULL", "2200_18411_5_not_null": "content_hash IS NOT NULL"}, "indexes": [{"name": "idx_versions_author", "definition": "CREATE INDEX idx_versions_author ON public.versions USING btree (author_id, created_at DESC)", "tablespace": null}, {"name": "idx_versions_block", "definition": "CREATE INDEX idx_versions_block ON public.versions USING btree (block_id, version_number DESC)", "tablespace": null}, {"name": "idx_versions_change_type", "definition": "CREATE INDEX idx_versions_change_type ON public.versions USING btree (change_type)", "tablespace": null}, {"name": "idx_versions_created", "definition": "CREATE INDEX idx_versions_created ON public.versions USING btree (created_at DESC)", "tablespace": null}, {"name": "versions_block_id_version_number_key", "definition": "CREATE UNIQUE INDEX versions_block_id_version_number_key ON public.versions USING btree (block_id, version_number)", "tablespace": null}, {"name": "versions_pkey", "definition": "CREATE UNIQUE INDEX versions_pkey ON public.versions USING btree (id)", "tablespace": null}], "foreign_keys": [{"source_column": "author_id", "target_table": "users", "target_column": "id"}, {"source_column": "block_id", "target_table": "blocks", "target_column": "id"}]}, "writing_achievements": {"table_name": "writing_achievements", "row_count": 16, "column_count": 12, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "user_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "achievement_type", "position": 3, "data_type": "USER-DEFINED", "udt_name": "achievement_type_enum", "nullable": false, "default": null}, {"name": "achievement_name", "position": 4, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 100}, {"name": "achievement_level", "position": 5, "data_type": "USER-DEFINED", "udt_name": "achievement_level_enum", "nullable": false, "default": null}, {"name": "description", "position": 6, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "score_requirement", "position": 7, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": null, "precision": 5, "scale": 2}, {"name": "document_count_requirement", "position": 8, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "precision": 32}, {"name": "streak_requirement", "position": 9, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "precision": 32}, {"name": "earned_at", "position": 10, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "document_id", "position": 11, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "metadata", "position": 12, "data_type": "jsonb", "udt_name": "jsonb", "nullable": true, "default": "'{}'::jsonb"}], "primary_keys": ["id"], "unique_constraints": {"writing_achievements_user_id_achievement_type_achievement_l_key": ["user_id", "achievement_type", "achievement_level"]}, "check_constraints": {"writing_achievements_metadata_valid": "((jsonb_typeof(metadata) = 'object'::text))", "2200_18591_1_not_null": "id IS NOT NULL", "2200_18591_3_not_null": "achievement_type IS NOT NULL", "2200_18591_4_not_null": "achievement_name IS NOT NULL", "2200_18591_5_not_null": "achievement_level IS NOT NULL"}, "indexes": [{"name": "idx_writing_achievements_earned", "definition": "CREATE INDEX idx_writing_achievements_earned ON public.writing_achievements USING btree (earned_at DESC)", "tablespace": null}, {"name": "idx_writing_achievements_type", "definition": "CREATE INDEX idx_writing_achievements_type ON public.writing_achievements USING btree (achievement_type, achievement_level)", "tablespace": null}, {"name": "idx_writing_achievements_user", "definition": "CREATE INDEX idx_writing_achievements_user ON public.writing_achievements USING btree (user_id, earned_at DESC)", "tablespace": null}, {"name": "writing_achievements_pkey", "definition": "CREATE UNIQUE INDEX writing_achievements_pkey ON public.writing_achievements USING btree (id)", "tablespace": null}, {"name": "writing_achievements_user_id_achievement_type_achievement_l_key", "definition": "CREATE UNIQUE INDEX writing_achievements_user_id_achievement_type_achievement_l_key ON public.writing_achievements USING btree (user_id, achievement_type, achievement_level)", "tablespace": null}], "foreign_keys": [{"source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "writing_challenges": {"table_name": "writing_challenges", "row_count": 12, "column_count": 14, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "user_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "challenge_type", "position": 3, "data_type": "USER-DEFINED", "udt_name": "challenge_type_enum", "nullable": false, "default": null}, {"name": "challenge_title", "position": 4, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 200}, {"name": "challenge_description", "position": 5, "data_type": "text", "udt_name": "text", "nullable": false, "default": null}, {"name": "target_metric", "position": 6, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 50}, {"name": "target_value", "position": 7, "data_type": "numeric", "udt_name": "numeric", "nullable": false, "default": null, "precision": 10, "scale": 2}, {"name": "difficulty", "position": 8, "data_type": "USER-DEFINED", "udt_name": "challenge_difficulty_enum", "nullable": false, "default": null}, {"name": "document_type_filter", "position": 9, "data_type": "USER-DEFINED", "udt_name": "document_type_enum", "nullable": true, "default": null}, {"name": "assigned_date", "position": 10, "data_type": "date", "udt_name": "date", "nullable": false, "default": null}, {"name": "completed_at", "position": 11, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "completion_value", "position": 12, "data_type": "numeric", "udt_name": "numeric", "nullable": true, "default": null, "precision": 10, "scale": 2}, {"name": "document_id", "position": 13, "data_type": "uuid", "udt_name": "uuid", "nullable": true, "default": null}, {"name": "reward_points", "position": 14, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}], "primary_keys": ["id"], "unique_constraints": {"writing_challenges_user_id_assigned_date_key": ["user_id", "assigned_date"]}, "check_constraints": {"writing_challenges_completion_value_positive": "(((completion_value IS NULL) OR (completion_value >= (0)::numeric)))", "writing_challenges_reward_points_positive": "((reward_points >= 0))", "writing_challenges_target_value_positive": "((target_value > (0)::numeric))", "2200_18614_1_not_null": "id IS NOT NULL", "2200_18614_3_not_null": "challenge_type IS NOT NULL", "2200_18614_4_not_null": "challenge_title IS NOT NULL", "2200_18614_5_not_null": "challenge_description IS NOT NULL", "2200_18614_6_not_null": "target_metric IS NOT NULL", "2200_18614_7_not_null": "target_value IS NOT NULL", "2200_18614_8_not_null": "difficulty IS NOT NULL", "2200_18614_10_not_null": "assigned_date IS NOT NULL"}, "indexes": [{"name": "idx_writing_challenges_completed", "definition": "CREATE INDEX idx_writing_challenges_completed ON public.writing_challenges USING btree (completed_at) WHERE (completed_at IS NOT NULL)", "tablespace": null}, {"name": "idx_writing_challenges_type", "definition": "CREATE INDEX idx_writing_challenges_type ON public.writing_challenges USING btree (challenge_type)", "tablespace": null}, {"name": "idx_writing_challenges_user_date", "definition": "CREATE INDEX idx_writing_challenges_user_date ON public.writing_challenges USING btree (user_id, assigned_date DESC)", "tablespace": null}, {"name": "writing_challenges_pkey", "definition": "CREATE UNIQUE INDEX writing_challenges_pkey ON public.writing_challenges USING btree (id)", "tablespace": null}, {"name": "writing_challenges_user_id_assigned_date_key", "definition": "CREATE UNIQUE INDEX writing_challenges_user_id_assigned_date_key ON public.writing_challenges USING btree (user_id, assigned_date)", "tablespace": null}], "foreign_keys": [{"source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "writing_goals": {"table_name": "writing_goals", "row_count": 0, "column_count": 12, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "user_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": null}, {"name": "type", "position": 3, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": false, "default": null, "max_length": 50}, {"name": "target", "position": 4, "data_type": "integer", "udt_name": "int4", "nullable": false, "default": null, "precision": 32}, {"name": "current", "position": 5, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "status", "position": 6, "data_type": "character varying", "udt_name": "<PERSON><PERSON><PERSON>", "nullable": true, "default": "'active'::character varying", "max_length": 20}, {"name": "description", "position": 7, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "start_date", "position": 8, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": false, "default": null}, {"name": "end_date", "position": 9, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "achieved_at", "position": 10, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "created_at", "position": 11, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 12, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"writing_goals_achieved_valid": "(((achieved_at IS NULL) OR (((status)::text = 'completed'::text) AND (achieved_at >= start_date))))", "writing_goals_current_check": "((current >= 0))", "writing_goals_end_after_start": "(((end_date IS NULL) OR (end_date >= start_date)))", "writing_goals_status_check": "(((status)::text = ANY ((ARRAY['active'::character varying, 'completed'::character varying, 'paused'::character varying])::text[])))", "writing_goals_target_check": "((target > 0))", "writing_goals_type_check": "(((type)::text = ANY ((ARRAY['daily_words'::character varying, 'weekly_words'::character varying, 'daily_time'::character varying, 'weekly_time'::character varying])::text[])))", "2200_33118_1_not_null": "id IS NOT NULL", "2200_33118_2_not_null": "user_id IS NOT NULL", "2200_33118_3_not_null": "type IS NOT NULL", "2200_33118_4_not_null": "target IS NOT NULL", "2200_33118_8_not_null": "start_date IS NOT NULL"}, "indexes": [{"name": "idx_writing_goals_status", "definition": "CREATE INDEX idx_writing_goals_status ON public.writing_goals USING btree (status)", "tablespace": null}, {"name": "idx_writing_goals_type", "definition": "CREATE INDEX idx_writing_goals_type ON public.writing_goals USING btree (type)", "tablespace": null}, {"name": "idx_writing_goals_user_id", "definition": "CREATE INDEX idx_writing_goals_user_id ON public.writing_goals USING btree (user_id)", "tablespace": null}, {"name": "idx_writing_goals_user_status", "definition": "CREATE INDEX idx_writing_goals_user_status ON public.writing_goals USING btree (user_id, status)", "tablespace": null}, {"name": "writing_goals_pkey", "definition": "CREATE UNIQUE INDEX writing_goals_pkey ON public.writing_goals USING btree (id)", "tablespace": null}], "foreign_keys": [{"source_column": "user_id", "target_table": "users", "target_column": "id"}]}, "writing_sessions": {"table_name": "writing_sessions", "row_count": 0, "column_count": 17, "columns": [{"name": "id", "position": 1, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": "gen_random_uuid()"}, {"name": "user_id", "position": 2, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": null}, {"name": "document_id", "position": 3, "data_type": "uuid", "udt_name": "uuid", "nullable": false, "default": null}, {"name": "start_time", "position": 4, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": false, "default": null}, {"name": "end_time", "position": 5, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": null}, {"name": "words_written", "position": 6, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "words_at_start", "position": 7, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "time_spent_minutes", "position": 8, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "ai_suggestions_accepted", "position": 9, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "ai_suggestions_dismissed", "position": 10, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "ai_generations_used", "position": 11, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": "0", "precision": 32}, {"name": "focus_mode_used", "position": 12, "data_type": "boolean", "udt_name": "bool", "nullable": true, "default": "false"}, {"name": "features_used", "position": 13, "data_type": "ARRAY", "udt_name": "_text", "nullable": true, "default": "'{}'::text[]"}, {"name": "session_goal", "position": 14, "data_type": "text", "udt_name": "text", "nullable": true, "default": null}, {"name": "target_word_count", "position": 15, "data_type": "integer", "udt_name": "int4", "nullable": true, "default": null, "precision": 32}, {"name": "created_at", "position": 16, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}, {"name": "updated_at", "position": 17, "data_type": "timestamp with time zone", "udt_name": "timestamptz", "nullable": true, "default": "now()"}], "primary_keys": ["id"], "unique_constraints": {}, "check_constraints": {"writing_sessions_counts_positive": "(((time_spent_minutes >= 0) AND (ai_suggestions_accepted >= 0) AND (ai_suggestions_dismissed >= 0) AND (ai_generations_used >= 0)))", "writing_sessions_end_after_start": "(((end_time IS NULL) OR (end_time >= start_time)))", "writing_sessions_target_positive": "(((target_word_count IS NULL) OR (target_word_count > 0)))", "writing_sessions_words_positive": "(((words_written >= 0) AND (words_at_start >= 0)))", "2200_33063_1_not_null": "id IS NOT NULL", "2200_33063_2_not_null": "user_id IS NOT NULL", "2200_33063_3_not_null": "document_id IS NOT NULL", "2200_33063_4_not_null": "start_time IS NOT NULL"}, "indexes": [{"name": "idx_writing_sessions_document_id", "definition": "CREATE INDEX idx_writing_sessions_document_id ON public.writing_sessions USING btree (document_id)", "tablespace": null}, {"name": "idx_writing_sessions_start_time", "definition": "CREATE INDEX idx_writing_sessions_start_time ON public.writing_sessions USING btree (start_time)", "tablespace": null}, {"name": "idx_writing_sessions_user_date", "definition": "CREATE INDEX idx_writing_sessions_user_date ON public.writing_sessions USING btree (user_id, start_time)", "tablespace": null}, {"name": "idx_writing_sessions_user_id", "definition": "CREATE INDEX idx_writing_sessions_user_id ON public.writing_sessions USING btree (user_id)", "tablespace": null}, {"name": "writing_sessions_pkey", "definition": "CREATE UNIQUE INDEX writing_sessions_pkey ON public.writing_sessions USING btree (id)", "tablespace": null}], "foreign_keys": [{"source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_column": "user_id", "target_table": "users", "target_column": "id"}]}}, "extensions": [{"name": "ltree", "version": "1.2"}, {"name": "pg_graphql", "version": "1.5.11"}, {"name": "pg_stat_statements", "version": "1.10"}, {"name": "pg_trgm", "version": "1.6"}, {"name": "pgcrypto", "version": "1.3"}, {"name": "plpgsql", "version": "1.0"}, {"name": "supabase_vault", "version": "0.3.1"}, {"name": "uuid-ossp", "version": "1.1"}, {"name": "vector", "version": "0.8.0"}], "foreign_key_relationships": [{"source_table": "agent_style_rules", "source_column": "agent_id", "target_table": "custom_agents", "target_column": "id"}, {"source_table": "agent_usage_stats", "source_column": "agent_id", "target_table": "custom_agents", "target_column": "id"}, {"source_table": "agent_usage_stats", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "audience_analysis", "source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_table": "blocks", "source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_table": "blocks", "source_column": "parent_id", "target_table": "blocks", "target_column": "id"}, {"source_table": "citations", "source_column": "block_id", "target_table": "blocks", "target_column": "id"}, {"source_table": "citations", "source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_table": "collaborations", "source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_table": "collaborations", "source_column": "invited_by", "target_table": "users", "target_column": "id"}, {"source_table": "collaborations", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "comments", "source_column": "author_id", "target_table": "users", "target_column": "id"}, {"source_table": "comments", "source_column": "block_id", "target_table": "blocks", "target_column": "id"}, {"source_table": "comments", "source_column": "parent_id", "target_table": "comments", "target_column": "id"}, {"source_table": "comments", "source_column": "resolved_by", "target_table": "users", "target_column": "id"}, {"source_table": "consistency_violations", "source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_table": "consistency_violations", "source_column": "entity_id", "target_table": "entities", "target_column": "id"}, {"source_table": "consistency_violations", "source_column": "resolved_by", "target_table": "users", "target_column": "id"}, {"source_table": "custom_agents", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "document_scores", "source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_table": "document_scores", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "documents", "source_column": "owner_id", "target_table": "users", "target_column": "id"}, {"source_table": "entities", "source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_table": "entities", "source_column": "first_mentioned_block_id", "target_table": "blocks", "target_column": "id"}, {"source_table": "entities", "source_column": "last_mentioned_block_id", "target_table": "blocks", "target_column": "id"}, {"source_table": "entities", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "entity_relationships", "source_column": "context_block_id", "target_table": "blocks", "target_column": "id"}, {"source_table": "entity_relationships", "source_column": "source_entity_id", "target_table": "entities", "target_column": "id"}, {"source_table": "entity_relationships", "source_column": "target_entity_id", "target_table": "entities", "target_column": "id"}, {"source_table": "export_jobs", "source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_table": "export_jobs", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "health_issues", "source_column": "block_id", "target_table": "blocks", "target_column": "id"}, {"source_table": "health_issues", "source_column": "health_metric_id", "target_table": "health_metrics", "target_column": "id"}, {"source_table": "health_metrics", "source_column": "block_id", "target_table": "blocks", "target_column": "id"}, {"source_table": "health_metrics", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "persona_feedback", "source_column": "block_id", "target_table": "blocks", "target_column": "id"}, {"source_table": "persona_feedback", "source_column": "persona_id", "target_table": "personas", "target_column": "id"}, {"source_table": "personas", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "reference_library", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "suggestions", "source_column": "block_id", "target_table": "blocks", "target_column": "id"}, {"source_table": "suggestions", "source_column": "resolved_by", "target_table": "users", "target_column": "id"}, {"source_table": "summaries", "source_column": "block_id", "target_table": "blocks", "target_column": "id"}, {"source_table": "templates", "source_column": "created_by", "target_table": "users", "target_column": "id"}, {"source_table": "token_usage_daily", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "usage_events", "source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_table": "usage_events", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "user_achievements", "source_column": "achievement_id", "target_table": "achievements", "target_column": "id"}, {"source_table": "user_achievements", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "user_writing_stats", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "versions", "source_column": "author_id", "target_table": "users", "target_column": "id"}, {"source_table": "versions", "source_column": "block_id", "target_table": "blocks", "target_column": "id"}, {"source_table": "writing_achievements", "source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_table": "writing_achievements", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "writing_challenges", "source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_table": "writing_challenges", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "writing_goals", "source_column": "user_id", "target_table": "users", "target_column": "id"}, {"source_table": "writing_sessions", "source_column": "document_id", "target_table": "documents", "target_column": "id"}, {"source_table": "writing_sessions", "source_column": "user_id", "target_table": "users", "target_column": "id"}]}