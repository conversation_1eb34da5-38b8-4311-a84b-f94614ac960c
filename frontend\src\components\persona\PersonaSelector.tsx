import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  UserGroupIcon,
  AcademicCapIcon,
  BriefcaseIcon,
  PencilIcon,
  UserIcon,
  CheckIcon,
  PlusIcon,
  XMarkIcon,
  StarIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';
import { usePersonaStore } from '../../stores/personaStore';
import { ReaderPersona, PersonaCategory } from '../../types/persona';
import PersonaCreationWizard from './PersonaCreationWizard';

interface PersonaSelectorProps {
  onClose?: () => void;
}

const PersonaSelector: React.FC<PersonaSelectorProps> = ({ onClose }) => {
  const {
    personas,
    selectedPersonas,
    templates,
    createPersona,
    toggleSelectedPersona,
    getPersona,
  } = usePersonaStore();

  const [selectedCategory, setSelectedCategory] = useState<PersonaCategory | 'all'>('all');
  const [showCreateWizard, setShowCreateWizard] = useState(false);

  const categoryIcons: Record<PersonaCategory, React.ComponentType<any>> = {
    academic: AcademicCapIcon,
    professional: BriefcaseIcon,
    creative: PencilIcon,
    personal: UserIcon,
    technical: AcademicCapIcon,
    cultural: UserIcon,
    'age-specific': UserIcon,
    custom: UserIcon,
  };

  const categoryColors: Record<PersonaCategory, string> = {
    academic: 'blue',
    professional: 'purple',
    creative: 'pink',
    personal: 'green',
    technical: 'indigo',
    cultural: 'yellow',
    'age-specific': 'orange',
    custom: 'gray',
  };

  const filteredTemplates = selectedCategory === 'all' 
    ? templates 
    : templates.filter(t => t.category === selectedCategory);

  const handleSelectTemplate = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      // Check if persona already exists
      const existingPersona = personas.find(p => p.name === template.name);
      
      if (existingPersona) {
        // Toggle existing persona
        toggleSelectedPersona(existingPersona.id);
      } else {
        // Create a new persona from template
        const newPersonaData = {
          name: template.name,
          description: template.description,
          category: template.category,
          demographics: template.template.demographics || {},
          readingPreferences: template.template.readingPreferences || {},
          personality: template.template.personality || {},
          context: template.template.context || {},
          feedbackStyle: template.template.feedbackStyle || {},
          isActive: true,
          tags: [template.category, 'template'],
          color: template.template.color || 'gray',
        };
        
        createPersona(newPersonaData);
        
        // Find the newly created persona and select it
        setTimeout(() => {
          const newPersona = personas.find(p => p.name === template.name && !p.isBuiltIn);
          if (newPersona) {
            toggleSelectedPersona(newPersona.id);
          }
        }, 100);
      }
    }
  };

  const isPersonaSelected = (templateId: string) => {
    // Check if any selected persona was created from this template
    const template = templates.find(t => t.id === templateId);
    if (!template) return false;
    
    return selectedPersonas.some(personaId => {
      const persona = getPersona(personaId);
      return persona?.name === template.name;
    });
  };

  return (
    <div className="bg-white rounded-2xl shadow-xl w-full max-w-4xl h-full max-h-[80vh] flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-slate-200 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-slate-900 flex items-center">
              <UserGroupIcon className="w-7 h-7 mr-3 text-purple-600" />
              Select Reader Personas
            </h2>
            <p className="text-slate-600 mt-1">
              Choose personas to get feedback from different audience perspectives
            </p>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="p-2 hover:bg-slate-100 rounded-lg transition-colors"
            >
              <XMarkIcon className="w-6 h-6 text-slate-500" />
            </button>
          )}
        </div>
      </div>

      {/* Category Filters */}
      <div className="p-6 border-b border-slate-200 flex-shrink-0">
        <div className="flex space-x-2">
          <button
            onClick={() => setSelectedCategory('all')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedCategory === 'all'
                ? 'bg-purple-100 text-purple-700'
                : 'text-slate-600 hover:bg-slate-100'
            }`}
          >
            All Categories
          </button>
          {['academic', 'professional', 'creative', 'personal'].map((category) => {
            const Icon = categoryIcons[category as PersonaCategory];
            return (
              <button
                key={category}
                onClick={() => setSelectedCategory(category as PersonaCategory)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center ${
                  selectedCategory === category
                    ? `bg-${categoryColors[category as PersonaCategory]}-100 text-${categoryColors[category as PersonaCategory]}-700`
                    : 'text-slate-600 hover:bg-slate-100'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </button>
            );
          })}
        </div>
      </div>

      {/* Persona Templates Grid */}
      <div className="p-6 flex-1 overflow-y-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pb-6">
          {filteredTemplates.map((template) => {
            const Icon = categoryIcons[template.category] || UserIcon;
            const color = categoryColors[template.category] || 'gray';
            const isSelected = isPersonaSelected(template.id);

            return (
              <motion.button
                key={template.id}
                onClick={() => handleSelectTemplate(template.id)}
                className={`p-4 rounded-xl border-2 transition-all text-left ${
                  isSelected
                    ? `border-${color}-500 bg-${color}-50`
                    : 'border-slate-200 hover:border-slate-300 bg-white'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className={`p-2 rounded-lg bg-${color}-100`}>
                    <Icon className={`w-5 h-5 text-${color}-600`} />
                  </div>
                  <div className="flex items-center space-x-2">
                    {template.isPopular && (
                      <div className="flex items-center text-yellow-600 text-xs">
                        <StarIcon className="w-4 h-4 mr-1" />
                        Popular
                      </div>
                    )}
                    {isSelected && (
                      <div className={`w-6 h-6 rounded-full bg-${color}-500 flex items-center justify-center`}>
                        <CheckIcon className="w-4 h-4 text-white" />
                      </div>
                    )}
                  </div>
                </div>

                <h3 className="font-semibold text-slate-900 mb-1">{template.name}</h3>
                <p className="text-sm text-slate-600 mb-3">{template.description}</p>

                <div className="flex flex-wrap gap-2">
                  {template.template.feedbackStyle?.priorities?.slice(0, 3).map((priority) => (
                    <span
                      key={priority}
                      className={`text-xs px-2 py-1 rounded-full bg-${color}-100 text-${color}-700`}
                    >
                      {priority}
                    </span>
                  )) || []}
                </div>
              </motion.button>
            );
          })}

          {/* Create Custom Persona Card */}
          <motion.button
            onClick={() => setShowCreateWizard(true)}
            className="p-6 rounded-xl border-2 border-dashed border-slate-300 hover:border-purple-400 bg-slate-50 hover:bg-purple-50 transition-all flex flex-col items-center justify-center min-h-[160px] relative"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <PlusIcon className="w-10 h-10 text-slate-400 mb-3" />
            <span className="font-medium text-slate-700 text-lg">Create Custom Persona</span>
            <span className="text-sm text-slate-500 mt-2 text-center">Define your own audience with specific traits and preferences</span>
          </motion.button>
        </div>
      </div>

      {/* Selected Count Footer */}
      {selectedPersonas.length > 0 && (
        <div className="p-6 border-t border-slate-200 bg-slate-50 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="text-sm text-slate-600">
              <span className="font-medium text-slate-900">{selectedPersonas.length}</span> persona{selectedPersonas.length > 1 ? 's' : ''} selected
            </div>
            <div className="flex items-center text-purple-600 text-sm font-medium">
              Generate feedback
              <ChevronRightIcon className="w-4 h-4 ml-1" />
            </div>
          </div>
        </div>
      )}
      
      {/* Persona Creation Wizard */}
      <PersonaCreationWizard
        isOpen={showCreateWizard}
        onClose={() => setShowCreateWizard(false)}
        onPersonaCreated={(persona) => {
          toggleSelectedPersona(persona.id);
          setShowCreateWizard(false);
        }}
      />
    </div>
  );
};

export default PersonaSelector;