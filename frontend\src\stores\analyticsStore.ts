import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  AnalyticsStore,
  WritingSession,
  DailyStats,
  WritingGoal,
  Achievement,
  DocumentAnalytics,
  AIUsageStats,
  ProductivityInsights,
  WritingStreak,
  WritingQualityScore,
} from '../types/analytics';

// Default achievements
const defaultAchievements: Achievement[] = [
  {
    id: 'first_document',
    title: 'First Steps',
    description: 'Created your first document',
    icon: '📝',
    category: 'writing',
    rarity: 'common',
  },
  {
    id: 'week_streak',
    title: 'Week Warrior',
    description: 'Wrote for 7 consecutive days',
    icon: '🔥',
    category: 'consistency',
    rarity: 'rare',
  },
  {
    id: 'ai_collaborator',
    title: 'AI Collaborator',
    description: 'Accepted 50 AI suggestions',
    icon: '🤖',
    category: 'ai_usage',
    rarity: 'common',
  },
  {
    id: 'quality_master',
    title: 'Quality Master',
    description: 'Achieved 90+ quality score',
    icon: '⭐',
    category: 'quality',
    rarity: 'epic',
  },
  {
    id: 'marathon_writer',
    title: 'Marathon Writer',
    description: 'Wrote 10,000 words in a single day',
    icon: '🏃‍♂️',
    category: 'writing',
    rarity: 'legendary',
  },
];

// Helper function to safely handle dates that might be strings from persistence
const ensureDate = (date: Date | string | undefined | null): Date => {
  if (!date) return new Date();
  return date instanceof Date ? date : new Date(date);
};

const getDateString = (date: Date | string = new Date()): string => {
  const dateObj = ensureDate(date);
  return dateObj.toISOString().split('T')[0];
};

const calculateStreak = (dailyStats: DailyStats[], goalType: 'words' | 'time', threshold: number): number => {
  const sortedStats = [...dailyStats].sort((a, b) => ensureDate(b.date).getTime() - ensureDate(a.date).getTime());
  let streak = 0;
  
  for (const day of sortedStats) {
    const value = goalType === 'words' ? day.wordsWritten : day.timeSpent;
    if (value >= threshold) {
      streak++;
    } else {
      break;
    }
  }
  
  return streak;
};

export const useAnalyticsStore = create<AnalyticsStore>()(
  persist(
    (set, get) => ({
      // State
      sessions: [],
      dailyStats: [],
      goals: [],
      achievements: defaultAchievements,
      documentAnalytics: [],
      aiUsageStats: {
        totalSuggestions: 0,
        acceptedSuggestions: 0,
        dismissedSuggestions: 0,
        acceptanceRate: 0,
        favoriteAgent: '',
        mostUsedFeature: '',
        timesSaved: 0,
        qualityImprovement: 0,
      },
      productivityInsights: {
        peakWritingHours: [],
        averageSessionLength: 0,
        mostProductiveDays: [],
        distractionScore: 0,
        focusModeEffectiveness: 0,
        optimalBreakInterval: 30,
      },
      writingStreak: {
        current: 0,
        longest: 0,
        startDate: new Date(),
        lastWritingDate: new Date(),
        goalType: 'words',
        threshold: 100,
      },
      lastUpdated: new Date(),

      // Actions
      startSession: (documentId: string) => {
        const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const session: WritingSession = {
          id: sessionId,
          documentId,
          startTime: new Date(),
          wordsWritten: 0,
          wordsAtStart: 0,
          timeSpent: 0,
          aiSuggestionsAccepted: 0,
          aiSuggestionsDismissed: 0,
          aiGenerationsUsed: 0,
          focusModeUsed: false,
          featuresUsed: [],
        };

        set((state) => ({
          sessions: [...state.sessions, session],
          lastUpdated: new Date(),
        }));

        return sessionId;
      },

      endSession: (sessionId: string) => {
        set((state) => {
          const sessionIndex = state.sessions.findIndex(s => s.id === sessionId);
          if (sessionIndex === -1) return state;

          const session = state.sessions[sessionIndex];
          const endTime = new Date();
          const startTime = ensureDate(session.startTime);
          const timeSpent = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));
          
          const updatedSession = {
            ...session,
            endTime,
            timeSpent,
          };

          const updatedSessions = [...state.sessions];
          updatedSessions[sessionIndex] = updatedSession;

          // Update daily stats
          const today = getDateString();
          const todayStatsIndex = state.dailyStats.findIndex(d => d.date === today);
          
          let updatedDailyStats = [...state.dailyStats];
          if (todayStatsIndex >= 0) {
            updatedDailyStats[todayStatsIndex] = {
              ...updatedDailyStats[todayStatsIndex],
              wordsWritten: updatedDailyStats[todayStatsIndex].wordsWritten + session.wordsWritten,
              timeSpent: updatedDailyStats[todayStatsIndex].timeSpent + timeSpent,
              sessionsCount: updatedDailyStats[todayStatsIndex].sessionsCount + 1,
              aiInteractions: updatedDailyStats[todayStatsIndex].aiInteractions + session.aiSuggestionsAccepted + session.aiSuggestionsDismissed,
            };
          } else {
            updatedDailyStats.push({
              date: today,
              wordsWritten: session.wordsWritten,
              timeSpent,
              documentsWorkedOn: 1,
              sessionsCount: 1,
              aiInteractions: session.aiSuggestionsAccepted + session.aiSuggestionsDismissed,
              qualityScoreAverage: 0,
              streak: 0,
            });
          }

          return {
            ...state,
            sessions: updatedSessions,
            dailyStats: updatedDailyStats,
            lastUpdated: new Date(),
          };
        });

        // Trigger calculations
        get().updateStreak();
        get().checkAchievements();
      },

      updateSession: (sessionId: string, updates: Partial<WritingSession>) => {
        set((state) => ({
          sessions: state.sessions.map(session =>
            session.id === sessionId ? { ...session, ...updates } : session
          ),
          lastUpdated: new Date(),
        }));
      },

      createGoal: (goal) => {
        const newGoal: WritingGoal = {
          ...goal,
          id: `goal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        };

        set((state) => ({
          goals: [...state.goals, newGoal],
          lastUpdated: new Date(),
        }));
      },

      updateGoal: (goalId: string, updates: Partial<WritingGoal>) => {
        set((state) => ({
          goals: state.goals.map(goal =>
            goal.id === goalId ? { ...goal, ...updates } : goal
          ),
          lastUpdated: new Date(),
        }));
      },

      deleteGoal: (goalId: string) => {
        set((state) => ({
          goals: state.goals.filter(goal => goal.id !== goalId),
          lastUpdated: new Date(),
        }));
      },

      updateDocumentAnalytics: (documentId: string, analytics: Partial<DocumentAnalytics>) => {
        set((state) => {
          const existingIndex = state.documentAnalytics.findIndex(d => d.documentId === documentId);
          let updatedAnalytics = [...state.documentAnalytics];

          if (existingIndex >= 0) {
            updatedAnalytics[existingIndex] = { ...updatedAnalytics[existingIndex], ...analytics };
          } else {
            const newAnalytics: DocumentAnalytics = {
              documentId,
              title: 'Untitled Document',
              wordCount: 0,
              readingTime: 0,
              qualityScore: {
                overall: 0,
                grammar: 0,
                style: 0,
                structure: 0,
                content: 0,
                readability: 0,
                languageClarity: 0,
              },
              createdAt: new Date(),
              lastModified: new Date(),
              timeSpent: 0,
              revisionsCount: 0,
              aiSuggestionsAccepted: 0,
              collaborators: 0,
              viewCount: 0,
              category: 'general',
              ...analytics,
            };
            updatedAnalytics.push(newAnalytics);
          }

          return {
            ...state,
            documentAnalytics: updatedAnalytics,
            lastUpdated: new Date(),
          };
        });
      },

      recordAIInteraction: (type: 'accepted' | 'dismissed' | 'generated', agent?: string) => {
        set((state) => {
          const updatedStats = { ...state.aiUsageStats };
          
          if (type === 'accepted') {
            updatedStats.acceptedSuggestions++;
            updatedStats.totalSuggestions++;
          } else if (type === 'dismissed') {
            updatedStats.dismissedSuggestions++;
            updatedStats.totalSuggestions++;
          }
          
          updatedStats.acceptanceRate = updatedStats.totalSuggestions > 0 
            ? (updatedStats.acceptedSuggestions / updatedStats.totalSuggestions) * 100 
            : 0;

          return {
            ...state,
            aiUsageStats: updatedStats,
            lastUpdated: new Date(),
          };
        });
      },

      updateQualityScore: (documentId: string, score: WritingQualityScore) => {
        get().updateDocumentAnalytics(documentId, { qualityScore: score });
        
        // Update daily stats with quality average
        const today = getDateString();
        set((state) => {
          const todayStatsIndex = state.dailyStats.findIndex(d => d.date === today);
          if (todayStatsIndex >= 0) {
            const updatedDailyStats = [...state.dailyStats];
            updatedDailyStats[todayStatsIndex] = {
              ...updatedDailyStats[todayStatsIndex],
              qualityScoreAverage: score.overall,
            };
            return { ...state, dailyStats: updatedDailyStats };
          }
          return state;
        });
      },

      calculateInsights: () => {
        const state = get();
        const completedSessions = state.sessions.filter(s => s.endTime);
        
        if (completedSessions.length === 0) return;

        // Calculate peak writing hours
        const hourCounts: { [hour: number]: number } = {};
        completedSessions.forEach(session => {
          // Ensure startTime is a Date object
          const startTime = session.startTime instanceof Date ? session.startTime : new Date(session.startTime);
          const hour = startTime.getHours();
          hourCounts[hour] = (hourCounts[hour] || 0) + session.wordsWritten;
        });

        const peakWritingHours = Object.entries(hourCounts)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 3)
          .map(([hour]) => parseInt(hour));

        // Calculate average session length
        const averageSessionLength = completedSessions.reduce(
          (acc, session) => acc + session.timeSpent, 0
        ) / completedSessions.length;

        // Calculate most productive days
        const dayStats: { [day: string]: number } = {};
        state.dailyStats.forEach(day => {
          const dayName = ensureDate(day.date).toLocaleDateString('en-US', { weekday: 'long' });
          dayStats[dayName] = (dayStats[dayName] || 0) + day.wordsWritten;
        });

        const mostProductiveDays = Object.entries(dayStats)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 3)
          .map(([day]) => day);

        const insights: ProductivityInsights = {
          peakWritingHours,
          averageSessionLength,
          mostProductiveDays,
          distractionScore: 0, // Could be calculated based on session interruptions
          focusModeEffectiveness: 0, // Could be calculated based on focus mode usage
          optimalBreakInterval: 30,
        };

        set((state) => ({
          productivityInsights: insights,
          lastUpdated: new Date(),
        }));
      },

      updateStreak: () => {
        const state = get();
        const current = calculateStreak(state.dailyStats, state.writingStreak.goalType, state.writingStreak.threshold);
        const longest = Math.max(current, state.writingStreak.longest);

        set((prevState) => ({
          writingStreak: {
            ...prevState.writingStreak,
            current,
            longest,
            lastWritingDate: new Date(),
          },
          lastUpdated: new Date(),
        }));
      },

      checkAchievements: () => {
        const state = get();
        const updatedAchievements = [...state.achievements];
        let hasNewAchievements = false;

        // Check for new achievements
        const totalWords = state.dailyStats.reduce((acc, day) => acc + day.wordsWritten, 0);
        const acceptedSuggestions = state.aiUsageStats.acceptedSuggestions;
        const maxQualityScore = Math.max(...state.documentAnalytics.map(d => d.qualityScore.overall));
        const todayWords = state.dailyStats.find(d => d.date === getDateString())?.wordsWritten || 0;

        // Unlock achievements
        updatedAchievements.forEach(achievement => {
          if (achievement.unlockedAt) return; // Already unlocked

          let shouldUnlock = false;

          switch (achievement.id) {
            case 'first_document':
              shouldUnlock = state.documentAnalytics.length > 0;
              break;
            case 'week_streak':
              shouldUnlock = state.writingStreak.current >= 7;
              break;
            case 'ai_collaborator':
              shouldUnlock = acceptedSuggestions >= 50;
              break;
            case 'quality_master':
              shouldUnlock = maxQualityScore >= 90;
              break;
            case 'marathon_writer':
              shouldUnlock = todayWords >= 10000;
              break;
          }

          if (shouldUnlock) {
            achievement.unlockedAt = new Date();
            hasNewAchievements = true;
          }
        });

        if (hasNewAchievements) {
          set((state) => ({
            achievements: updatedAchievements,
            lastUpdated: new Date(),
          }));
        }
      },

      exportData: () => {
        const state = get();
        return JSON.stringify({
          sessions: state.sessions,
          dailyStats: state.dailyStats,
          goals: state.goals,
          achievements: state.achievements,
          documentAnalytics: state.documentAnalytics,
          aiUsageStats: state.aiUsageStats,
          writingStreak: state.writingStreak,
          exportedAt: new Date(),
        }, null, 2);
      },

      importData: (data: string) => {
        try {
          const parsed = JSON.parse(data);
          set((state) => ({
            ...state,
            ...parsed,
            lastUpdated: new Date(),
          }));
        } catch (error) {
          console.error('Failed to import analytics data:', error);
        }
      },

      clearData: () => {
        set({
          sessions: [],
          dailyStats: [],
          goals: [],
          achievements: defaultAchievements,
          documentAnalytics: [],
          aiUsageStats: {
            totalSuggestions: 0,
            acceptedSuggestions: 0,
            dismissedSuggestions: 0,
            acceptanceRate: 0,
            favoriteAgent: '',
            mostUsedFeature: '',
            timesSaved: 0,
            qualityImprovement: 0,
          },
          productivityInsights: {
            peakWritingHours: [],
            averageSessionLength: 0,
            mostProductiveDays: [],
            distractionScore: 0,
            focusModeEffectiveness: 0,
            optimalBreakInterval: 30,
          },
          writingStreak: {
            current: 0,
            longest: 0,
            startDate: new Date(),
            lastWritingDate: new Date(),
            goalType: 'words',
            threshold: 100,
          },
          lastUpdated: new Date(),
        });
      },

      resetAllData: () => {
        // Same as clearData - reset everything to default state
        get().clearData();
      },
    }),
    {
      name: 'revisionary-analytics',
      version: 1,
    }
  )
);

// Computed selectors
export const useAnalyticsSelectors = () => {
  const store = useAnalyticsStore();
  
  return {
    // Get current session
    getCurrentSession: (documentId: string) => {
      return store.sessions.find(s => s.documentId === documentId && !s.endTime);
    },
    
    // Get today's stats
    getTodayStats: () => {
      const today = getDateString();
      return store.dailyStats.find(d => d.date === today) || {
        date: today,
        wordsWritten: 0,
        timeSpent: 0,
        documentsWorkedOn: 0,
        sessionsCount: 0,
        aiInteractions: 0,
        qualityScoreAverage: 0,
        streak: 0,
      };
    },
    
    // Get week stats
    getWeekStats: () => {
      const now = new Date();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      return store.dailyStats.filter(d => ensureDate(d.date) >= weekAgo);
    },

    // Get month stats
    getMonthStats: () => {
      const now = new Date();
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      return store.dailyStats.filter(d => ensureDate(d.date) >= monthAgo);
    },

    // Get day stats for specific date
    getDayStats: (date: Date) => {
      const dateString = getDateString(date);
      return store.dailyStats.find(d => d.date === dateString) || {
        date: dateString,
        wordsWritten: 0,
        timeSpent: 0,
        documentsWorkedOn: 0,
        sessionsCount: 0,
        aiInteractions: 0,
        qualityScoreAverage: 0,
        streak: 0,
      };
    },
    
    // Get active goals
    getActiveGoals: () => {
      return store.goals.filter(g => g.status === 'active');
    },

    // Get completed goals
    getCompletedGoals: () => {
      return store.goals.filter(g => g.status === 'completed');
    },
    
    // Get unlocked achievements
    getUnlockedAchievements: () => {
      return store.achievements.filter(a => a.unlockedAt);
    },
    
    // Get recent achievements (last 7 days)
    getRecentAchievements: () => {
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      return store.achievements.filter(a => a.unlockedAt && ensureDate(a.unlockedAt) >= weekAgo);
    },
  };
};