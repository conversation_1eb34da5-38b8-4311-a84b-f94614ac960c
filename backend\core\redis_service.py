"""
Revisionary Redis Service

Purpose: Redis connection and cache management
- Redis client initialization and connection management
- Caching operations with TTL and key management
- Pub/Sub for real-time features
- Job queue management for background tasks

Features:
- Async Redis operations
- Connection retry logic
- Structured cache key patterns
- Pub/Sub event handling
- Performance monitoring
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Union
from contextlib import asynccontextmanager
import structlog
from datetime import datetime, timedelta

from .settings import settings

logger = structlog.get_logger(__name__)

# Conditional import based on mock mode
if not settings.mock.use_mock_redis:
    try:
        # Try modern redis package first (redis-py 4.2+)
        import redis.asyncio as aioredis
        from redis.asyncio import Redis
    except ImportError:
        try:
            # Fall back to old aioredis if needed
            import aioredis
            from aioredis import Redis
        except ImportError:
            logger.error("No Redis client available. Please install 'redis>=4.5.0' or 'aioredis'")
            raise
else:
    # Mock imports when in mock mode
    aioredis = None
    Redis = None


class RedisService:
    """Redis service for caching and pub/sub operations."""
    
    def __init__(self):
        self.redis_client: Optional[Redis] = None
        self.pubsub_client: Optional[Redis] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize Redis connections."""
        if self._initialized:
            return
        
        try:
            logger.info("Initializing Redis service")
            
            # Initialize main Redis client
            await self._init_redis_client()
            
            # Initialize pub/sub client
            await self._init_pubsub_client()
            
            self._initialized = True
            logger.info("Redis service initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize Redis service", error=str(e), exc_info=True)
            raise
    
    async def _init_redis_client(self) -> None:
        """Initialize main Redis client."""
        if settings.mock.use_mock_redis:
            logger.info("Redis client in mock mode - skipping initialization")
            return
            
        try:
            self.redis_client = await aioredis.from_url(
                settings.redis.redis_url,
                password=settings.redis.redis_password,
                db=settings.redis.redis_db,
                encoding="utf-8",
                decode_responses=True,
                max_connections=settings.redis.connection_pool_size,
                socket_connect_timeout=settings.redis.connection_timeout,
                socket_timeout=settings.redis.connection_timeout,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.info("Redis client initialized", url=settings.redis.redis_url)
            
        except Exception as e:
            logger.error("Failed to initialize Redis client", error=str(e))
            raise
    
    async def _init_pubsub_client(self) -> None:
        """Initialize pub/sub Redis client."""
        if settings.mock.use_mock_redis:
            logger.info("Redis pub/sub client in mock mode - skipping initialization")
            return
            
        try:
            self.pubsub_client = await aioredis.from_url(
                settings.redis.redis_url,
                password=settings.redis.redis_password,
                db=settings.redis.redis_db,
                encoding="utf-8",
                decode_responses=True
            )
            
            logger.info("Redis pub/sub client initialized")
            
        except Exception as e:
            logger.error("Failed to initialize Redis pub/sub client", error=str(e))
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Check Redis health."""
        health_status = {
            "redis": False,
            "pubsub": False,
            "details": {}
        }
        
        # Check main Redis client
        try:
            if self.redis_client:
                latency = await self.redis_client.ping()
                health_status["redis"] = True
                health_status["details"]["redis"] = f"Connected (latency: {latency}ms)"
        except Exception as e:
            health_status["details"]["redis"] = f"Error: {str(e)}"
        
        # Check pub/sub client
        try:
            if self.pubsub_client:
                await self.pubsub_client.ping()
                health_status["pubsub"] = True
                health_status["details"]["pubsub"] = "Connected"
        except Exception as e:
            health_status["details"]["pubsub"] = f"Error: {str(e)}"
        
        return health_status
    
    # Cache operations
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if settings.mock.use_mock_redis:
            return None  # Return None in mock mode
            
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")
        
        try:
            value = await self.redis_client.get(key)
            if value is None:
                return None
            
            # Try to parse as JSON, fallback to string
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
        
        except Exception as e:
            logger.error("Redis get failed", key=key, error=str(e))
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        nx: bool = False
    ) -> bool:
        """Set value in cache."""
        if settings.mock.use_mock_redis:
            return True  # Return success in mock mode
            
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")
        
        try:
            # Serialize value
            if isinstance(value, (dict, list)):
                serialized_value = json.dumps(value)
            else:
                serialized_value = str(value)
            
            # Set with optional TTL and NX flag
            result = await self.redis_client.set(
                key,
                serialized_value,
                ex=ttl,
                nx=nx
            )
            
            return bool(result)
        
        except Exception as e:
            logger.error("Redis set failed", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")
        
        try:
            result = await self.redis_client.delete(key)
            return result > 0
        
        except Exception as e:
            logger.error("Redis delete failed", key=key, error=str(e))
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")
        
        try:
            result = await self.redis_client.exists(key)
            return result > 0
        
        except Exception as e:
            logger.error("Redis exists failed", key=key, error=str(e))
            return False
    
    async def expire(self, key: str, ttl: int) -> bool:
        """Set TTL for existing key."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")
        
        try:
            result = await self.redis_client.expire(key, ttl)
            return bool(result)
        
        except Exception as e:
            logger.error("Redis expire failed", key=key, ttl=ttl, error=str(e))
            return False
    
    # Pub/Sub operations
    async def publish(self, channel: str, message: Any) -> int:
        """Publish message to channel."""
        if settings.mock.use_mock_redis:
            logger.debug("Mock Redis: Message published", channel=channel)
            return 0  # Return 0 subscribers in mock mode
            
        if not self.pubsub_client:
            raise RuntimeError("Redis pub/sub client not initialized")
        
        try:
            # Serialize message
            if isinstance(message, (dict, list)):
                serialized_message = json.dumps(message)
            else:
                serialized_message = str(message)
            
            result = await self.pubsub_client.publish(channel, serialized_message)
            logger.debug("Message published", channel=channel, subscribers=result)
            return result
        
        except Exception as e:
            logger.error("Redis publish failed", channel=channel, error=str(e))
            raise
    
    async def subscribe(self, *channels: str):
        """Subscribe to channels."""
        if not self.pubsub_client:
            raise RuntimeError("Redis pub/sub client not initialized")
        
        try:
            pubsub = self.pubsub_client.pubsub()
            await pubsub.subscribe(*channels)
            logger.info("Subscribed to channels", channels=list(channels))
            return pubsub
        
        except Exception as e:
            logger.error("Redis subscribe failed", channels=list(channels), error=str(e))
            raise
    
    # Cache key patterns for different data types
    def make_key(self, prefix: str, *parts: str) -> str:
        """Create a standardized cache key."""
        return f"revisionary:{prefix}:" + ":".join(str(part) for part in parts)
    
    def document_key(self, document_id: str) -> str:
        """Create document cache key."""
        return self.make_key("document", document_id)
    
    def block_key(self, block_id: str) -> str:
        """Create block cache key."""
        return self.make_key("block", block_id)
    
    def user_session_key(self, user_id: str) -> str:
        """Create user session cache key."""
        return self.make_key("session", user_id)
    
    def ai_cache_key(self, model: str, content_hash: str) -> str:
        """Create AI response cache key."""
        return self.make_key("ai", model, content_hash)
    
    def health_cache_key(self, block_id: str, content_hash: str) -> str:
        """Create health analysis cache key."""
        return self.make_key("health", block_id, content_hash)
    
    # High-level cache operations
    async def cache_document(self, document_id: str, document_data: Dict[str, Any], ttl: int = 3600) -> bool:
        """Cache document data."""
        key = self.document_key(document_id)
        return await self.set(key, document_data, ttl=ttl)
    
    async def get_cached_document(self, document_id: str) -> Optional[Dict[str, Any]]:
        """Get cached document data."""
        key = self.document_key(document_id)
        return await self.get(key)
    
    async def cache_ai_response(
        self,
        model: str,
        content_hash: str,
        response: Dict[str, Any],
        ttl: int = 86400  # 24 hours
    ) -> bool:
        """Cache AI response."""
        key = self.ai_cache_key(model, content_hash)
        return await self.set(key, response, ttl=ttl)
    
    async def get_cached_ai_response(self, model: str, content_hash: str) -> Optional[Dict[str, Any]]:
        """Get cached AI response."""
        key = self.ai_cache_key(model, content_hash)
        return await self.get(key)
    
    async def invalidate_document_cache(self, document_id: str) -> bool:
        """Invalidate all cache entries for a document."""
        pattern = f"{self.document_key(document_id)}*"
        if not self.redis_client:
            return False
        
        try:
            keys = await self.redis_client.keys(pattern)
            if keys:
                await self.redis_client.delete(*keys)
                logger.info("Document cache invalidated", document_id=document_id, keys_deleted=len(keys))
            return True
        
        except Exception as e:
            logger.error("Cache invalidation failed", document_id=document_id, error=str(e))
            return False
    
    # Real-time pub/sub methods
    async def publish_ai_result(self, session_id: str, result: Dict[str, Any]) -> int:
        """Publish AI analysis result to specific session."""
        channel = f"ai:suggestions:{session_id}"
        return await self.publish(channel, result)
    
    async def publish_document_update(self, doc_id: str, update_data: Dict[str, Any]) -> int:
        """Publish document update to all subscribers."""
        channel = f"document:updates:{doc_id}"
        return await self.publish(channel, update_data)
    
    async def publish_user_notification(self, user_id: str, notification: Dict[str, Any]) -> int:
        """Publish notification to specific user."""
        channel = f"user:{user_id}:notifications"
        return await self.publish(channel, notification)
    
    async def publish_cost_alert(self, alert_data: Dict[str, Any]) -> int:
        """Publish cost alert to all admin subscribers."""
        channel = "system:cost:alerts"
        return await self.publish(channel, alert_data)
    
    async def publish_worker_status(self, worker_type: str, status_data: Dict[str, Any]) -> int:
        """Publish worker status update."""
        channel = f"worker:status:{worker_type}"
        return await self.publish(channel, status_data)
    
    async def subscribe_to_ai_results(self):
        """Subscribe to all AI result channels."""
        return await self.subscribe("ai:suggestions:*")
    
    async def subscribe_to_document_updates(self, doc_id: str):
        """Subscribe to specific document updates."""
        channel = f"document:updates:{doc_id}"
        return await self.subscribe(channel)
    
    async def subscribe_to_user_notifications(self, user_id: str):
        """Subscribe to specific user notifications."""
        channel = f"user:{user_id}:notifications"
        return await self.subscribe(channel)
    
    async def subscribe_to_system_alerts(self):
        """Subscribe to system-wide alerts."""
        return await self.subscribe("system:*")
    
    # Counter operations for analytics
    async def incr(self, key: str, amount: int = 1) -> int:
        """Increment counter."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")
        
        try:
            result = await self.redis_client.incr(key, amount)
            return result
        except Exception as e:
            logger.error("Redis incr failed", key=key, amount=amount, error=str(e))
            raise
    
    async def incrbyfloat(self, key: str, amount: float) -> float:
        """Increment float counter."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")
        
        try:
            result = await self.redis_client.incrbyfloat(key, amount)
            return result
        except Exception as e:
            logger.error("Redis incrbyfloat failed", key=key, amount=amount, error=str(e))
            raise
    
    async def lpush(self, key: str, *values: str) -> int:
        """Push values to left of list."""
        if not self.redis_client:
            raise RuntimeError("Redis client not initialized")
        
        try:
            result = await self.redis_client.lpush(key, *values)
            return result
        except Exception as e:
            logger.error("Redis lpush failed", key=key, error=str(e))
            raise
    
    async def close(self) -> None:
        """Close Redis connections."""
        if self.redis_client:
            await self.redis_client.close()
            logger.info("Redis client closed")
        
        if self.pubsub_client:
            await self.pubsub_client.close()
            logger.info("Redis pub/sub client closed")
        
        self._initialized = False
        logger.info("Redis service closed")


# Global Redis service instance
redis_service = RedisService()


async def get_redis() -> RedisService:
    """Get Redis service instance."""
    if not redis_service._initialized:
        await redis_service.initialize()
    return redis_service