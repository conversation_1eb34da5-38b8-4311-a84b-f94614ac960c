import React, { useState } from 'react';
import {
  SparklesIcon,
  PencilSquareIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  ArrowPathIcon,
  BoltIcon,
  ChatBubbleBottomCenterTextIcon,
  DocumentTextIcon,
  HashtagIcon,
  ChatBubbleLeftIcon,
  ChevronRightIcon,
  WrenchScrewdriverIcon,
  BeakerIcon,
  CubeIcon,
  PresentationChartBarIcon,
  UserGroupIcon,
  MagnifyingGlassIcon,
  CogIcon,
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';
import DocumentOutline from './DocumentOutline';
import CommentsPanel from './CommentsPanel';
import { useAgentStore } from '../../stores/agentStore';

interface AISuggestion {
  id: string;
  type: 'grammar' | 'style' | 'content' | 'structure';
  severity: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  originalText: string;
  suggestedText: string;
  line: number;
  isStale?: boolean;
}

interface Comment {
  id: string;
  text: string;
  author: {
    name: string;
    avatar?: string;
    email: string;
  };
  createdAt: Date;
  line?: number;
  resolved: boolean;
  replies: Comment[];
}

type AIMode = 'fix-its' | 'rephrase' | 'build-from' | 'analyze';
type TabType = 'ai' | 'outline' | 'comments';

interface EnhancedAIPanelProps {
  suggestions: AISuggestion[];
  onAcceptSuggestion: (id: string) => void;
  onDismissSuggestion: (id: string) => void;
  onRerunSuggestion: (id: string) => void;
  onGenerateContent: (mode: AIMode, options?: any) => void;
  isGenerating: boolean;
  documentContent: string;
  selectedText?: string;
  onNavigateToLine: (line: number) => void;
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
  // Comment-related props
  comments: Comment[];
  onAddComment: (text: string, line?: number) => void;
  onResolveComment: (commentId: string) => void;
  onReplyToComment: (commentId: string, text: string) => void;
  // Document structure props
  documentStructure: any[]; // BlockStructure[] from API
  onNavigateToBlock: (blockId: string) => void;
  isStructureLoading?: boolean;
  structureError?: string | null;
}

const aiModes = [
  {
    id: 'fix-its' as const,
    label: 'Fix-Its',
    description: 'Quick grammar and style fixes',
    icon: WrenchScrewdriverIcon,
    color: 'blue',
  },
  {
    id: 'rephrase' as const,
    label: 'Rephrase',
    description: 'Rewrite with adjustable creativity',
    icon: BeakerIcon,
    color: 'purple',
  },
  {
    id: 'build-from' as const,
    label: 'Build-From',
    description: 'Generate structures and content',
    icon: CubeIcon,
    color: 'green',
  },
  {
    id: 'analyze' as const,
    label: 'Analyze',
    description: 'Multiple agents review content',
    icon: MagnifyingGlassIcon,
    color: 'orange',
  },
];

const buildFromTemplates = [
  {
    id: 'outline',
    label: 'Outline',
    description: 'Structured document outline',
    icon: HashtagIcon,
  },
  {
    id: 'summary',
    label: 'Summary',
    description: 'Concise content summary',
    icon: DocumentTextIcon,
  },
  {
    id: 'slides',
    label: 'Slides',
    description: 'Presentation structure',
    icon: PresentationChartBarIcon,
  },
  {
    id: 'character',
    label: 'Character',
    description: 'Character profile',
    icon: UserGroupIcon,
  },
];

// Available agents now come from the agent store

const EnhancedAIPanel: React.FC<EnhancedAIPanelProps> = ({
  suggestions,
  onAcceptSuggestion,
  onDismissSuggestion,
  onRerunSuggestion,
  onGenerateContent,
  isGenerating,
  documentContent,
  selectedText,
  onNavigateToLine,
  activeTab,
  onTabChange,
  onToggleCollapse,
  comments,
  onAddComment,
  onResolveComment,
  onReplyToComment,
  documentStructure,
  onNavigateToBlock,
  isStructureLoading = false,
  structureError = null,
}) => {
  const [activeAIMode, setActiveAIMode] = useState<AIMode>('fix-its');
  const [rephraseBoldness, setRephraseBoldness] = useState(50);
  const [selectedTemplate, setSelectedTemplate] = useState('outline');
  
  // Use agent store
  const { 
    selectedAgents, 
    selectAgent, 
    deselectAgent, 
    getActiveAgents 
  } = useAgentStore();

  // Comments are now passed as props from the parent component

  const tabs = [
    { 
      id: 'ai' as const, 
      label: 'AI', 
      icon: SparklesIcon,
      count: suggestions.length,
      color: 'text-purple-600' 
    },
    { 
      id: 'outline' as const, 
      label: 'Outline', 
      icon: HashtagIcon,
      count: null,
      color: 'text-blue-600' 
    },
    { 
      id: 'comments' as const, 
      label: 'Comments', 
      icon: ChatBubbleLeftIcon,
      count: comments.filter(c => !c.resolved).length,
      color: 'text-green-600' 
    },
  ];

  // Comment handlers are now passed as props from the parent component

  return (
    <div className="w-96 bg-gradient-to-b from-slate-50/80 to-white/80 backdrop-blur-xl border-l border-slate-200/50 flex flex-col relative z-50">
      {/* Toggle button */}
      <motion.button
        onClick={onToggleCollapse || (() => {})}
        className="absolute bottom-4 left-4 w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg shadow-xl flex items-center justify-center z-[60] group"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        <ChevronRightIcon className="w-5 h-5 text-white" />
        <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg" />
      </motion.button>

      {/* Header */}
      <div className="px-4 py-4 border-b border-slate-200/50">
        {/* Main Tabs */}
        <div className="flex bg-slate-100/80 rounded-xl p-1 mb-4">
          {tabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                className={`flex-1 flex items-center justify-center px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-white text-slate-900 shadow-sm'
                    : 'text-slate-600 hover:text-slate-900'
                }`}
              >
                <IconComponent className={`w-4 h-4 mr-1 ${activeTab === tab.id ? tab.color : 'text-slate-500'}`} />
                {tab.label}
                {tab.count !== null && tab.count > 0 && (
                  <span className={`ml-2 px-1.5 py-0.5 text-xs font-bold rounded-full ${
                    activeTab === tab.id
                      ? 'bg-slate-100 text-slate-700'
                      : 'bg-slate-200 text-slate-600'
                  }`}>
                    {tab.count}
                  </span>
                )}
              </button>
            );
          })}
        </div>

        {/* AI Mode Selector - Only show when AI tab is active */}
        {activeTab === 'ai' && (
          <div className="space-y-3">
            <div className="flex bg-white/60 rounded-xl p-1">
              {aiModes.map((mode) => {
                const IconComponent = mode.icon;
                return (
                  <button
                    key={mode.id}
                    onClick={() => setActiveAIMode(mode.id)}
                    className={`flex-1 flex flex-col items-center px-2 py-2 rounded-lg text-xs font-semibold transition-all duration-200 ${
                      activeAIMode === mode.id
                        ? `bg-${mode.color}-100 text-${mode.color}-700 shadow-sm`
                        : 'text-slate-600 hover:text-slate-900'
                    }`}
                  >
                    <IconComponent className={`w-4 h-4 mb-1 ${
                      activeAIMode === mode.id ? `text-${mode.color}-600` : 'text-slate-500'
                    }`} />
                    {mode.label}
                  </button>
                );
              })}
            </div>

            {/* Mode-specific controls */}
            <AnimatePresence mode="wait">
              {activeAIMode === 'rephrase' && (
                <motion.div
                  key="rephrase-controls"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="bg-white/60 rounded-xl p-3"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-slate-700">Boldness</span>
                    <span className="text-sm text-purple-600 font-bold">{rephraseBoldness}%</span>
                  </div>
                  <div className="relative">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={rephraseBoldness}
                      onChange={(e) => setRephraseBoldness(Number(e.target.value))}
                      className="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer"
                      style={{
                        background: `linear-gradient(to right, #a855f7 0%, #a855f7 ${rephraseBoldness}%, #e2e8f0 ${rephraseBoldness}%, #e2e8f0 100%)`
                      }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-slate-500 mt-1">
                    <span>Conservative</span>
                    <span>Creative</span>
                  </div>
                </motion.div>
              )}

              {activeAIMode === 'build-from' && (
                <motion.div
                  key="build-from-controls"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="bg-white/60 rounded-xl p-3"
                >
                  <div className="grid grid-cols-2 gap-2">
                    {buildFromTemplates.map((template) => {
                      const IconComponent = template.icon;
                      return (
                        <button
                          key={template.id}
                          onClick={() => setSelectedTemplate(template.id)}
                          className={`flex flex-col items-center p-2 rounded-lg text-xs transition-all ${
                            selectedTemplate === template.id
                              ? 'bg-green-100 text-green-700 shadow-sm'
                              : 'bg-white/60 text-slate-600 hover:bg-white'
                          }`}
                        >
                          <IconComponent className={`w-4 h-4 mb-1 ${
                            selectedTemplate === template.id ? 'text-green-600' : 'text-slate-500'
                          }`} />
                          <span className="font-medium">{template.label}</span>
                        </button>
                      );
                    })}
                  </div>
                </motion.div>
              )}

              {activeAIMode === 'analyze' && (
                <motion.div
                  key="analyze-controls"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="bg-white/60 rounded-xl p-3"
                >
                  <div className="mb-3">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-slate-700">Select Review Agents</h4>
                      <button
                        onClick={() => window.open('/settings?tab=agents', '_blank')}
                        className="flex items-center text-xs text-blue-600 hover:text-blue-700 transition-colors"
                        title="Manage Agents"
                      >
                        <CogIcon className="w-3 h-3 mr-1" />
                        Manage
                      </button>
                    </div>
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {getActiveAgents().map((agent) => {
                        const isSelected = selectedAgents.includes(agent.id);
                        return (
                          <button
                            key={agent.id}
                            onClick={() => {
                              if (isSelected) {
                                deselectAgent(agent.id);
                              } else {
                                selectAgent(agent.id);
                              }
                            }}
                            className={`w-full flex items-center p-2 rounded-lg text-xs transition-all ${
                              isSelected
                                ? `bg-${agent.color}-100 text-${agent.color}-700 border border-${agent.color}-200`
                                : 'bg-white/60 text-slate-600 hover:bg-white border border-transparent'
                            }`}
                          >
                            <div className={`w-2 h-2 rounded-full mr-2 ${
                              isSelected ? `bg-${agent.color}-500` : 'bg-slate-300'
                            }`} />
                            <div className="flex-1 text-left">
                              <div className="font-medium">{agent.name}</div>
                              <div className="text-xs opacity-75">{agent.specialty}</div>
                            </div>
                            {!agent.isBuiltIn && (
                              <div className="ml-1 px-1 py-0.5 bg-purple-100 text-purple-600 text-xs rounded">
                                Custom
                              </div>
                            )}
                          </button>
                        );
                      })}
                      {getActiveAgents().length === 0 && (
                        <div className="text-center py-4 text-slate-500">
                          <p className="text-xs">No active agents</p>
                          <button
                            onClick={() => window.open('/settings?tab=agents', '_blank')}
                            className="text-blue-600 hover:text-blue-700 text-xs mt-1"
                          >
                            Activate agents in Settings
                          </button>
                        </div>
                      )}
                    </div>
                    {selectedAgents.length > 0 && (
                      <div className="mt-2 pt-2 border-t border-slate-200">
                        <div className="text-xs text-slate-600">
                          {selectedAgents.length} agent{selectedAgents.length > 1 ? 's' : ''} selected
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <AnimatePresence mode="wait">
          {activeTab === 'ai' ? (
            <motion.div
              key="ai"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="h-full overflow-y-auto"
            >
              {activeAIMode === 'fix-its' && suggestions.length > 0 ? (
                <div className="p-4 space-y-4">
                  {suggestions.map((suggestion, index) => (
                    <SuggestionCard
                      key={suggestion.id}
                      suggestion={suggestion}
                      index={index}
                      onAccept={() => onAcceptSuggestion(suggestion.id)}
                      onDismiss={() => onDismissSuggestion(suggestion.id)}
                      onRerun={() => onRerunSuggestion(suggestion.id)}
                    />
                  ))}
                </div>
              ) : (
                <div className="p-6">
                  <AIGenerationPanel 
                    mode={activeAIMode}
                    selectedText={selectedText}
                    rephraseBoldness={rephraseBoldness}
                    selectedTemplate={selectedTemplate}
                    selectedAgents={selectedAgents}
                    onGenerate={onGenerateContent}
                    isGenerating={isGenerating}
                  />
                </div>
              )}
            </motion.div>
          ) : activeTab === 'outline' ? (
            <motion.div
              key="outline"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="h-full overflow-y-auto p-4"
            >
              <DocumentOutline 
                blockStructure={documentStructure}
                onNavigate={onNavigateToBlock}
                isLoading={isStructureLoading}
                error={structureError}
              />
            </motion.div>
          ) : (
            <motion.div
              key="comments"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="h-full overflow-y-auto p-4"
            >
              <CommentsPanel
                comments={comments}
                onAddComment={onAddComment}
                onResolveComment={onResolveComment}
                onReplyToComment={onReplyToComment}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

// Enhanced AI Generation Panel
const AIGenerationPanel: React.FC<{
  mode: AIMode;
  selectedText?: string;
  rephraseBoldness: number;
  selectedTemplate: string;
  selectedAgents: string[];
  onGenerate: (mode: AIMode, options?: any) => void;
  isGenerating: boolean;
}> = ({ mode, selectedText, rephraseBoldness, selectedTemplate, selectedAgents, onGenerate, isGenerating }) => {
  const { getAgent } = useAgentStore();
  const handleGenerate = () => {
    const options = mode === 'rephrase' 
      ? { boldness: rephraseBoldness, text: selectedText }
      : mode === 'build-from'
      ? { template: selectedTemplate }
      : mode === 'analyze'
      ? { 
          agents: selectedAgents.map(agentId => getAgent(agentId)).filter(Boolean),
          agentIds: selectedAgents 
        }
      : {};
    
    onGenerate(mode, options);
  };

  const getModeConfig = (mode: AIMode) => {
    switch (mode) {
      case 'fix-its':
        return {
          title: 'Quick Fix-Its',
          description: 'Get instant grammar, style, and clarity suggestions',
          buttonText: 'Analyze Text',
          color: 'blue',
        };
      case 'rephrase':
        return {
          title: 'Smart Rephrase',
          description: selectedText 
            ? `Rephrase selected text with ${rephraseBoldness}% creativity`
            : 'Select text to rephrase with adjustable creativity',
          buttonText: 'Rephrase Selection',
          color: 'purple',
        };
      case 'build-from':
        return {
          title: 'Build Structure',
          description: `Generate a ${selectedTemplate} based on your content`,
          buttonText: `Create ${selectedTemplate}`,
          color: 'green',
        };
      case 'analyze':
        return {
          title: 'Agent Analysis',
          description: selectedAgents.length > 0 
            ? `${selectedAgents.length} agent${selectedAgents.length > 1 ? 's' : ''} will review your content`
            : 'Select agents to review and provide feedback',
          buttonText: 'Start Analysis',
          color: 'orange',
        };
    }
  };

  const config = getModeConfig(mode);

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-slate-900 mb-2">{config.title}</h3>
        <p className="text-sm text-slate-600 mb-6">
          {config.description}
        </p>
      </div>

      {selectedText && mode === 'rephrase' && (
        <div className="bg-slate-50 rounded-xl p-4 mb-4">
          <p className="text-xs font-semibold text-slate-700 mb-2">Selected Text:</p>
          <p className="text-sm text-slate-900 italic">"{selectedText}"</p>
        </div>
      )}

      {mode === 'analyze' && selectedAgents.length > 0 && (
        <div className="bg-slate-50 rounded-xl p-4 mb-4">
          <p className="text-xs font-semibold text-slate-700 mb-2">Selected Agents:</p>
          <div className="space-y-2">
            {selectedAgents.map(agentId => {
              const agent = getAgent(agentId);
              return agent ? (
                <div key={agentId} className="flex items-center text-sm">
                  <div className={`w-2 h-2 rounded-full bg-${agent.color}-500 mr-2`} />
                  <span className="font-medium">{agent.name}</span>
                  <span className="text-slate-500 ml-1">• {agent.specialty}</span>
                </div>
              ) : null;
            })}
          </div>
        </div>
      )}

      <motion.button
        onClick={handleGenerate}
        disabled={isGenerating || (mode === 'rephrase' && !selectedText) || (mode === 'analyze' && selectedAgents.length === 0)}
        className={`w-full flex items-center justify-center px-6 py-4 rounded-2xl text-white font-semibold transition-all duration-200 ${
          isGenerating || (mode === 'rephrase' && !selectedText) || (mode === 'analyze' && selectedAgents.length === 0)
            ? 'bg-slate-400 cursor-not-allowed'
            : `bg-gradient-to-r from-${config.color}-600 to-${config.color}-700 hover:shadow-lg hover:shadow-${config.color}-500/25`
        }`}
        whileHover={!isGenerating ? { scale: 1.02 } : {}}
        whileTap={!isGenerating ? { scale: 0.98 } : {}}
      >
        {isGenerating ? (
          <>
            <ArrowPathIcon className="w-5 h-5 mr-3 animate-spin" />
            Generating...
          </>
        ) : (
          <>
            <BoltIcon className="w-5 h-5 mr-3" />
            {config.buttonText}
          </>
        )}
      </motion.button>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 gap-3">
        {mode === 'fix-its' && (
          <>
            <ActionButton
              icon={CheckCircleIcon}
              label="Grammar Check"
              description="Fix grammar and spelling"
              color="green"
              onClick={() => onGenerate('fix-its', { type: 'grammar' })}
            />
            <ActionButton
              icon={PencilSquareIcon}
              label="Style Polish"
              description="Improve clarity and flow"
              color="blue"
              onClick={() => onGenerate('fix-its', { type: 'style' })}
            />
            <ActionButton
              icon={DocumentTextIcon}
              label="Structure Review"
              description="Enhance organization"
              color="purple"
              onClick={() => onGenerate('fix-its', { type: 'structure' })}
            />
          </>
        )}
      </div>
    </div>
  );
};

const ActionButton: React.FC<{
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  description: string;
  color: string;
  onClick: () => void;
}> = ({ icon: IconComponent, label, description, color, onClick }) => (
  <button
    onClick={onClick}
    className="flex items-center p-4 text-left bg-white border border-slate-200 rounded-2xl hover:border-purple-300 hover:shadow-sm transition-all duration-200"
  >
    <IconComponent className={`w-5 h-5 text-${color}-600 mr-3`} />
    <div>
      <p className="text-sm font-semibold text-slate-900">{label}</p>
      <p className="text-xs text-slate-500">{description}</p>
    </div>
  </button>
);

// Reuse the existing SuggestionCard component
const SuggestionCard: React.FC<{
  suggestion: AISuggestion;
  index: number;
  onAccept: () => void;
  onDismiss: () => void;
  onRerun: () => void;
}> = ({ suggestion, index, onAccept, onDismiss, onRerun }) => {
  const suggestionTypeConfig = {
    grammar: { color: 'blue', icon: CheckCircleIcon, label: 'Grammar' },
    style: { color: 'green', icon: PencilSquareIcon, label: 'Style' },
    content: { color: 'purple', icon: DocumentTextIcon, label: 'Content' },
    structure: { color: 'orange', icon: ChatBubbleBottomCenterTextIcon, label: 'Structure' },
  };

  const severityConfig = {
    low: { priority: 'Low', bgColor: 'bg-slate-50', textColor: 'text-slate-600' },
    medium: { priority: 'Medium', bgColor: 'bg-yellow-50', textColor: 'text-yellow-700' },
    high: { priority: 'High', bgColor: 'bg-red-50', textColor: 'text-red-700' },
  };

  const config = suggestionTypeConfig[suggestion.type];
  const severity = severityConfig[suggestion.severity];
  const IconComponent = config.icon;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className={`relative overflow-hidden rounded-2xl border shadow-sm transition-all duration-200 hover:shadow-md ${
        suggestion.isStale
          ? 'bg-yellow-50/50 border-yellow-200'
          : 'bg-white border-slate-200'
      }`}
    >
      {suggestion.isStale && (
        <div className="absolute top-0 left-0 right-0 bg-yellow-400 text-yellow-900 text-xs font-semibold px-4 py-2 flex items-center">
          <ExclamationTriangleIcon className="w-4 h-4 mr-2" />
          Text changed - suggestion may be outdated
          <button
            onClick={onRerun}
            className="ml-auto flex items-center text-yellow-800 hover:text-yellow-900"
          >
            <ArrowPathIcon className="w-4 h-4 mr-1" />
            Re-run
          </button>
        </div>
      )}

      <div className={`p-4 ${suggestion.isStale ? 'pt-16' : ''}`}>
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className={`w-8 h-8 bg-${config.color}-100 rounded-xl flex items-center justify-center`}>
              <IconComponent className={`w-4 h-4 text-${config.color}-600`} />
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <span className={`text-sm font-semibold text-${config.color}-700`}>
                  {config.label}
                </span>
                <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${severity.bgColor} ${severity.textColor}`}>
                  {severity.priority}
                </span>
              </div>
              <span className="text-xs text-slate-500">Line {suggestion.line}</span>
            </div>
          </div>
          <button
            onClick={onDismiss}
            className="p-1 text-slate-400 hover:text-slate-600 transition-colors"
          >
            <XMarkIcon className="w-4 h-4" />
          </button>
        </div>

        <div className="space-y-3">
          <p className="text-sm text-slate-900 font-medium">{suggestion.title}</p>
          <p className="text-sm text-slate-600">{suggestion.description}</p>

          <div className="space-y-2">
            <div className="p-3 bg-red-50 border border-red-200 rounded-xl">
              <p className="text-xs font-semibold text-red-700 mb-1">Original</p>
              <p className="text-sm text-red-900">{suggestion.originalText}</p>
            </div>
            <div className="p-3 bg-green-50 border border-green-200 rounded-xl">
              <p className="text-xs font-semibold text-green-700 mb-1">Suggested</p>
              <p className="text-sm text-green-900">{suggestion.suggestedText}</p>
            </div>
          </div>

          <div className="flex space-x-2 pt-2">
            <motion.button
              onClick={onAccept}
              disabled={suggestion.isStale}
              className={`flex-1 px-4 py-2 text-sm font-semibold rounded-xl transition-all duration-200 ${
                suggestion.isStale
                  ? 'bg-slate-100 text-slate-400 cursor-not-allowed'
                  : `bg-${config.color}-600 text-white hover:bg-${config.color}-700 shadow-sm hover:shadow-md`
              }`}
              whileHover={!suggestion.isStale ? { scale: 1.02 } : {}}
              whileTap={!suggestion.isStale ? { scale: 0.98 } : {}}
            >
              Accept
            </motion.button>
            <motion.button
              onClick={onDismiss}
              className="px-4 py-2 text-sm font-semibold text-slate-600 bg-slate-100 hover:bg-slate-200 rounded-xl transition-colors"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Dismiss
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default EnhancedAIPanel;