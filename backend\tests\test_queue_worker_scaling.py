#!/usr/bin/env python3
"""
Queue Management and Worker Auto-Scaling Test
Tests the queue management system and worker auto-scaling capabilities.
"""

import sys
import os
import ast
import json
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

def test_queue_manager_structure():
    """Test queue manager structure and capabilities."""
    print("Testing queue manager structure...")
    
    try:
        queue_file = backend_path / "core" / "queue_manager.py"
        
        if not queue_file.exists():
            print("❌ queue_manager.py not found")
            return False
        
        with open(queue_file, 'r') as f:
            content = f.read()
        
        # Check for core queue management patterns
        required_patterns = [
            'class QueueManager',
            'enqueue_job',
            'agent_types',
            'worker_limits',
            'scale_up_threshold',
            'scale_down_threshold'
        ]
        
        for pattern in required_patterns:
            if pattern not in content:
                print(f"❌ Missing queue manager pattern: {pattern}")
                return False
        
        print("✓ Queue manager structure found")
        
        # Check for scaling and priority features
        scaling_patterns = [
            'JobPriority',
            'UserTierPriority',
            '_check_worker_scaling',
            'priority',
            'user_tier'
        ]
        
        found_scaling = 0
        for pattern in scaling_patterns:
            if pattern in content:
                found_scaling += 1
        
        if found_scaling >= 4:
            print("✓ Queue scaling features found")
        else:
            print(f"⚠️  Limited scaling features ({found_scaling} patterns)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing queue manager: {e}")
        return False

def test_worker_scaling_logic():
    """Test worker auto-scaling logic."""
    print("Testing worker auto-scaling logic...")
    
    try:
        queue_file = backend_path / "core" / "queue_manager.py"
        
        with open(queue_file, 'r') as f:
            content = f.read()
        
        # Check for scaling implementation
        scaling_implementation_patterns = [
            '_check_worker_scaling',
            '_get_worker_stats',
            'scale_up_threshold',
            'scale_down_threshold',
            'worker_limits'
        ]
        
        found_implementation = 0
        for pattern in scaling_implementation_patterns:
            if pattern in content:
                found_implementation += 1
        
        if found_implementation >= 4:
            print("✓ Worker scaling logic implemented")
        else:
            print(f"⚠️  Incomplete scaling logic ({found_implementation} patterns)")
        
        # Check for Redis integration for scaling
        redis_scaling_patterns = [
            'redis',
            'worker:stats',
            'queue_length',
            'xlen'
        ]
        
        found_redis = 0
        for pattern in redis_scaling_patterns:
            if pattern in content:
                found_redis += 1
        
        if found_redis >= 3:
            print("✓ Redis integration for scaling found")
            return True
        else:
            print(f"⚠️  Limited Redis scaling integration ({found_redis} patterns)")
            return False
        
    except Exception as e:
        print(f"❌ Error testing scaling logic: {e}")
        return False

def test_job_priority_system():
    """Test job priority and user tier system."""
    print("Testing job priority system...")
    
    try:
        queue_file = backend_path / "core" / "queue_manager.py"
        
        with open(queue_file, 'r') as f:
            content = f.read()
        
        # Check for priority system components
        priority_patterns = [
            'JobPriority',
            'UserTierPriority',
            'free',
            'professional',
            'studio',
            'enterprise'
        ]
        
        found_priority = 0
        for pattern in priority_patterns:
            if pattern in content:
                found_priority += 1
        
        if found_priority >= 5:
            print("✓ Comprehensive priority system found")
        else:
            print(f"⚠️  Limited priority system ({found_priority} patterns)")
        
        # Check for priority queue handling
        queue_patterns = [
            'priority_queue',
            'queue:',
            ':priority',
            'HIGH',
            'CRITICAL'
        ]
        
        found_queues = 0
        for pattern in queue_patterns:
            if pattern in content:
                found_queues += 1
        
        if found_queues >= 4:
            print("✓ Priority queue handling found")
            return True
        else:
            print(f"⚠️  Limited priority queue handling ({found_queues} patterns)")
            return False
        
    except Exception as e:
        print(f"❌ Error testing priority system: {e}")
        return False

def test_multi_agent_job_support():
    """Test multi-agent job distribution."""
    print("Testing multi-agent job support...")
    
    try:
        queue_file = backend_path / "core" / "queue_manager.py"
        
        with open(queue_file, 'r') as f:
            content = f.read()
        
        # Check for multi-agent job patterns
        multi_agent_patterns = [
            'enqueue_multi_agent_job',
            'parent_job_id',
            'multi_agent_job',
            'agent_types',
            '_track_multi_agent_job'
        ]
        
        found_multi_agent = 0
        for pattern in multi_agent_patterns:
            if pattern in content:
                found_multi_agent += 1
        
        if found_multi_agent >= 4:
            print("✓ Multi-agent job support found")
        else:
            print(f"⚠️  Limited multi-agent support ({found_multi_agent} patterns)")
        
        # Check for job tracking
        tracking_patterns = [
            'job_status',
            'get_job_status',
            '_track_job_status',
            'job:status:'
        ]
        
        found_tracking = 0
        for pattern in tracking_patterns:
            if pattern in content:
                found_tracking += 1
        
        if found_tracking >= 3:
            print("✓ Job tracking system found")
            return True
        else:
            print(f"⚠️  Limited job tracking ({found_tracking} patterns)")
            return False
        
    except Exception as e:
        print(f"❌ Error testing multi-agent support: {e}")
        return False

def test_queue_statistics():
    """Test queue statistics and monitoring."""
    print("Testing queue statistics...")
    
    try:
        queue_file = backend_path / "core" / "queue_manager.py"
        
        with open(queue_file, 'r') as f:
            content = f.read()
        
        # Check for statistics methods
        stats_patterns = [
            'get_queue_stats',
            'get_all_queue_stats',
            'queue_length',
            'processing_stats',
            'success_rate'
        ]
        
        found_stats = 0
        for pattern in stats_patterns:
            if pattern in content:
                found_stats += 1
        
        if found_stats >= 4:
            print("✓ Queue statistics system found")
        else:
            print(f"⚠️  Limited statistics system ({found_stats} patterns)")
        
        # Check for worker statistics
        worker_stats_patterns = [
            '_get_worker_stats',
            'worker:stats:',
            'processed',
            'failed',
            'avg_time'
        ]
        
        found_worker_stats = 0
        for pattern in worker_stats_patterns:
            if pattern in content:
                found_worker_stats += 1
        
        if found_worker_stats >= 4:
            print("✓ Worker statistics tracking found")
            return True
        else:
            print(f"⚠️  Limited worker statistics ({found_worker_stats} patterns)")
            return False
        
    except Exception as e:
        print(f"❌ Error testing statistics: {e}")
        return False

def test_redis_streams_integration():
    """Test Redis Streams integration for job queues."""
    print("Testing Redis Streams integration...")
    
    try:
        queue_file = backend_path / "core" / "queue_manager.py"
        
        with open(queue_file, 'r') as f:
            content = f.read()
        
        # Check for Redis Streams patterns
        streams_patterns = [
            'xadd',
            'xlen',
            'xgroup_create',
            'consumer_group',
            'stream'
        ]
        
        found_streams = 0
        for pattern in streams_patterns:
            if pattern in content:
                found_streams += 1
        
        if found_streams >= 4:
            print("✓ Redis Streams integration found")
        else:
            print(f"⚠️  Limited Redis Streams integration ({found_streams} patterns)")
        
        # Check for queue setup
        setup_patterns = [
            '_setup_agent_queue',
            'initialize',
            'queue:',
            'mkstream'
        ]
        
        found_setup = 0
        for pattern in setup_patterns:
            if pattern in content:
                found_setup += 1
        
        if found_setup >= 3:
            print("✓ Queue setup and initialization found")
            return True
        else:
            print(f"⚠️  Limited queue setup ({found_setup} patterns)")
            return False
        
    except Exception as e:
        print(f"❌ Error testing Redis integration: {e}")
        return False

def test_worker_base_class():
    """Test base worker class for auto-scaling support."""
    print("Testing base worker class...")
    
    try:
        base_worker_file = backend_path / "workers" / "base_worker.py"
        
        if not base_worker_file.exists():
            print("❌ base_worker.py not found")
            return False
        
        with open(base_worker_file, 'r') as f:
            content = f.read()
        
        # Check for worker base patterns
        base_patterns = [
            'class BaseWorker',
            'worker_id',
            'worker_type',
            'process_job',
            'initialize'
        ]
        
        found_base = 0
        for pattern in base_patterns:
            if pattern in content:
                found_base += 1
        
        if found_base >= 4:
            print("✓ Base worker class properly structured")
        else:
            print(f"⚠️  Limited base worker structure ({found_base} patterns)")
        
        # Check for scaling support patterns
        scaling_support_patterns = [
            'redis',
            'queue',
            'job_data',
            'stats',
            'performance'
        ]
        
        found_scaling_support = 0
        for pattern in scaling_support_patterns:
            if pattern in content:
                found_scaling_support += 1
        
        if found_scaling_support >= 3:
            print("✓ Worker scaling support found")
            return True
        else:
            print(f"⚠️  Limited scaling support ({found_scaling_support} patterns)")
            return False
        
    except Exception as e:
        print(f"❌ Error testing base worker: {e}")
        return False

def run_queue_scaling_tests():
    """Run all queue management and worker scaling tests."""
    print("=" * 70)
    print("QUEUE MANAGEMENT & WORKER AUTO-SCALING TESTS")
    print("=" * 70)
    
    tests = [
        test_queue_manager_structure,
        test_worker_scaling_logic,
        test_job_priority_system,
        test_multi_agent_job_support,
        test_queue_statistics,
        test_redis_streams_integration,
        test_worker_base_class
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n{test.__name__.replace('_', ' ').title()}:")
        try:
            if test():
                passed += 1
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test error: {e}")
    
    print("\n" + "=" * 70)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed >= total * 0.85:  # 85% pass rate acceptable for scaling features
        print("🎉 QUEUE MANAGEMENT & SCALING TESTS PASSED!")
        print("✅ Queue management and worker auto-scaling systems are properly implemented")
        return True
    else:
        print("❌ Queue and scaling tests failed")
        return False

if __name__ == "__main__":
    success = run_queue_scaling_tests()
    sys.exit(0 if success else 1)