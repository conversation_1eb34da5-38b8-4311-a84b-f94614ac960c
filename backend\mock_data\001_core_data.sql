-- Revisionary Mock Data - Core Users and Documents
-- Part 1: Essential data for users, documents, blocks, and versions
-- Uses database triggers for auto-generation of IDs, paths, hashes, and computed fields

DO $$
DECLARE
    -- User IDs
    sarah_id UUID;
    alex_id UUID;
    kim_id UUID;
    maya_id UUID;
    james_id UUID;
    
    -- Document IDs
    sarah_climate_doc_id UUID;
    sarah_blog_doc_id UUID;
    alex_cafe_doc_id UUID;
    alex_chars_doc_id UUID;
    kim_neural_doc_id UUID;
    kim_conf_doc_id UUID;
    maya_chap1_doc_id UUID;
    maya_world_doc_id UUID;
    maya_blog_doc_id UUID;
    james_q4_doc_id UUID;
    james_launch_doc_id UUID;
    james_comm_doc_id UUID;
    kim_collab_doc_id UUID;
    kim_template_doc_id UUID;
    maya_template_doc_id UUID;
    
    -- Block IDs (for versions)
    sarah_climate_root_id UUID;
    sarah_climate_abstract_id UUID;
    sarah_climate_para1_id UUID;
    sarah_climate_intro_id UUID;
    sarah_climate_para2_id UUID;
    
    alex_cafe_root_id UUID;
    alex_cafe_chapter_id UUID;
    alex_cafe_para1_id UUID;
    alex_cafe_para2_id UUID;
    
    kim_neural_root_id UUID;
    kim_neural_title_id UUID;
    kim_neural_abstract_id UUID;
    kim_neural_para_id UUID;
    
    maya_chap1_root_id UUID;
    maya_chap1_title_id UUID;
    maya_chap1_para1_id UUID;
    maya_chap1_para2_id UUID;
    
    james_q4_root_id UUID;
    james_q4_title_id UUID;
    james_q4_summary_id UUID;
    james_q4_para_id UUID;
    
BEGIN
    -- =================================================================
    -- USERS - Realistic user profiles across different subscription tiers
    -- =================================================================
    
    -- Free tier users
    INSERT INTO users (firebase_uid, email, display_name, avatar_url, subscription_tier, subscription_status, onboarding_completed, preferences, last_active_at) 
    VALUES 
    ('firebase_user_001', '<EMAIL>', 'Sarah Chen', 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150', 'free', 'active', true, 
     '{"language": "en", "theme": "light", "notifications": {"email": true, "push": false}, "writing_goals": {"daily_words": 500}, "ai_assistance": {"grammar": true, "style": false}}'::jsonb, 
     NOW() - INTERVAL '2 hours')
    RETURNING id INTO sarah_id;
    
    INSERT INTO users (firebase_uid, email, display_name, avatar_url, subscription_tier, subscription_status, onboarding_completed, preferences, last_active_at)
    VALUES 
    ('firebase_user_002', '<EMAIL>', 'Alex Rivera', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150', 'free', 'active', true,
     '{"language": "en", "theme": "dark", "notifications": {"email": false, "push": true}, "writing_goals": {"daily_words": 300}, "ai_assistance": {"grammar": true, "style": true}}'::jsonb,
     NOW() - INTERVAL '1 day')
    RETURNING id INTO alex_id;
    
    -- Professional tier users  
    INSERT INTO users (firebase_uid, email, display_name, avatar_url, subscription_tier, subscription_status, onboarding_completed, preferences, last_active_at)
    VALUES 
    ('firebase_user_003', '<EMAIL>', 'Dr. Kim Patel', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150', 'professional', 'active', true,
     '{"language": "en", "theme": "light", "notifications": {"email": true, "push": true}, "writing_goals": {"daily_words": 1000}, "ai_assistance": {"grammar": true, "style": true, "citations": true}, "academic_style": "apa"}'::jsonb,
     NOW() - INTERVAL '30 minutes')
    RETURNING id INTO kim_id;
    
    INSERT INTO users (firebase_uid, email, display_name, avatar_url, subscription_tier, subscription_status, onboarding_completed, preferences, last_active_at)
    VALUES 
    ('firebase_user_004', '<EMAIL>', 'Maya Thompson', 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150', 'professional', 'active', true,
     '{"language": "en", "theme": "sepia", "notifications": {"email": true, "push": false}, "writing_goals": {"daily_words": 2000}, "ai_assistance": {"grammar": true, "style": true, "creativity": true}, "genre_preferences": ["fantasy", "sci-fi"]}'::jsonb,
     NOW() - INTERVAL '4 hours')
    RETURNING id INTO maya_id;
    
    -- Studio tier user
    INSERT INTO users (firebase_uid, email, display_name, avatar_url, subscription_tier, subscription_status, onboarding_completed, preferences, last_active_at)
    VALUES 
    ('firebase_user_005', '<EMAIL>', 'James Wilson', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150', 'studio', 'active', true,
     '{"language": "en", "theme": "light", "notifications": {"email": true, "push": true}, "writing_goals": {"daily_words": 1500}, "ai_assistance": {"grammar": true, "style": true, "tone": "professional", "collaboration": true}, "team_settings": {"auto_share": true}}'::jsonb,
     NOW() - INTERVAL '15 minutes')
    RETURNING id INTO james_id;

    -- =================================================================
    -- DOCUMENTS - Various document types across different users
    -- =================================================================
    
    -- Sarah's documents (free user - academic focus)
    INSERT INTO documents (owner_id, title, type, status, description, language, metadata, tags) 
    VALUES 
    (sarah_id, 'Climate Change Research Paper', 'academic', 'draft', 'Analyzing the impact of rising sea levels on coastal communities', 'en',
     '{"citation_style": "apa", "target_audience": "academic", "submission_deadline": "2024-02-15"}'::jsonb,
     '{"climate", "research", "environmental science", "academia"}')
    RETURNING id INTO sarah_climate_doc_id;
    
    INSERT INTO documents (owner_id, title, type, status, description, language, metadata, tags)
    VALUES 
    (sarah_id, 'Personal Blog Draft', 'general', 'draft', 'Thoughts on sustainable living practices', 'en',
     '{"target_audience": "general", "tone": "casual", "publication_platform": "personal blog"}'::jsonb,
     '{"sustainability", "lifestyle", "blog", "personal"}')
    RETURNING id INTO sarah_blog_doc_id;
    
    -- Alex's documents (free user - creative writing)
    INSERT INTO documents (owner_id, title, type, status, description, version, language, metadata, tags)
    VALUES 
    (alex_id, 'The Midnight Café', 'creative', 'editing', 'A short story about a mysterious coffee shop that only appears at night', 3, 'en',
     '{"genre": "urban fantasy", "target_audience": "young adult", "setting": "modern city"}'::jsonb,
     '{"short story", "urban fantasy", "mystery", "coffee shop"}')
    RETURNING id INTO alex_cafe_doc_id;
    
    INSERT INTO documents (owner_id, title, type, status, description, language, metadata, tags)
    VALUES 
    (alex_id, 'Character Development Notes', 'creative', 'draft', 'Detailed character profiles for my upcoming novel series', 'en',
     '{"project": "fantasy novel series", "world_building": true}'::jsonb,
     '{"characters", "fantasy", "world building", "novel"}')
    RETURNING id INTO alex_chars_doc_id;
    
    -- Dr. Kim's documents (professional user - academic)
    INSERT INTO documents (owner_id, title, type, status, description, version, language, metadata, tags)
    VALUES 
    (kim_id, 'Neural Networks in Medical Diagnosis', 'academic', 'review', 'Comprehensive study on AI applications in healthcare diagnostics', 5, 'en',
     '{"citation_style": "ieee", "journal": "Medical AI Quarterly", "peer_review": true, "funding_source": "NSF Grant #12345"}'::jsonb,
     '{"artificial intelligence", "medical diagnosis", "neural networks", "healthcare"}')
    RETURNING id INTO kim_neural_doc_id;
    
    INSERT INTO documents (owner_id, title, type, status, description, version, language, metadata, tags)
    VALUES 
    (kim_id, 'Conference Presentation Outline', 'professional', 'draft', 'Presentation for the International AI in Medicine Conference', 2, 'en',
     '{"conference": "AI in Medicine 2024", "presentation_length": "20 minutes", "audience": "medical professionals"}'::jsonb,
     '{"presentation", "conference", "AI", "medicine"}')
    RETURNING id INTO kim_conf_doc_id;
    
    -- Maya's documents (professional user - creative writing)
    INSERT INTO documents (owner_id, title, type, status, description, version, language, metadata, tags)
    VALUES 
    (maya_id, 'Chronicles of Aetheria: Chapter 1', 'creative', 'editing', 'Opening chapter of epic fantasy novel about magical realms', 4, 'en',
     '{"genre": "epic fantasy", "series": "Chronicles of Aetheria", "target_word_count": 80000, "publisher_interest": true}'::jsonb,
     '{"epic fantasy", "novel", "magic", "adventure", "series"}')
    RETURNING id INTO maya_chap1_doc_id;
    
    INSERT INTO documents (owner_id, title, type, status, description, language, metadata, tags)
    VALUES 
    (maya_id, 'World Building Bible', 'creative', 'draft', 'Comprehensive guide to the world of Aetheria including maps, cultures, and magic systems', 'en',
     '{"project": "Chronicles of Aetheria", "world_building": true, "reference_document": true}'::jsonb,
     '{"world building", "fantasy", "reference", "magic system", "cultures"}')
    RETURNING id INTO maya_world_doc_id;
    
    INSERT INTO documents (owner_id, title, type, status, description, language, metadata, tags)
    VALUES 
    (maya_id, 'Writing Process Blog Series', 'general', 'published', 'Blog posts about the creative writing process for aspiring authors', 'en',
     '{"publication_platform": "Medium", "target_audience": "aspiring writers", "series": "Behind the Scenes"}'::jsonb,
     '{"writing advice", "blog", "creative process", "authors"}')
    RETURNING id INTO maya_blog_doc_id;
    
    -- James's documents (studio user - business)
    INSERT INTO documents (owner_id, title, type, status, description, version, language, metadata, tags)
    VALUES 
    (james_id, 'Q4 Marketing Strategy Report', 'professional', 'review', 'Comprehensive analysis of marketing performance and strategic recommendations', 3, 'en',
     '{"department": "Marketing", "quarter": "Q4 2024", "confidentiality": "internal", "stakeholders": ["CMO", "VP Sales", "Board"]}'::jsonb,
     '{"marketing", "strategy", "Q4", "business", "report"}')
    RETURNING id INTO james_q4_doc_id;
    
    INSERT INTO documents (owner_id, title, type, status, description, version, language, metadata, tags)
    VALUES 
    (james_id, 'Product Launch Proposal', 'professional', 'draft', 'Detailed proposal for launching our new AI-powered analytics platform', 2, 'en',
     '{"product": "AI Analytics Platform", "launch_date": "2024-03-01", "target_market": "enterprise", "budget_required": true}'::jsonb,
     '{"product launch", "AI", "analytics", "proposal", "enterprise"}')
    RETURNING id INTO james_launch_doc_id;
    
    INSERT INTO documents (owner_id, title, type, status, description, language, metadata, tags)
    VALUES 
    (james_id, 'Team Communication Guidelines', 'professional', 'published', 'Best practices for effective team communication in remote work environments', 'en',
     '{"audience": "internal team", "policy_type": "guidelines", "effective_date": "2024-01-01"}'::jsonb,
     '{"communication", "remote work", "guidelines", "team", "policy"}')
    RETURNING id INTO james_comm_doc_id;
    
    -- Collaborative document
    INSERT INTO documents (owner_id, title, type, status, description, version, language, metadata, tags)
    VALUES 
    (kim_id, 'Industry Research Collaboration', 'academic', 'editing', 'Multi-author research paper on emerging technologies in healthcare', 7, 'en',
     '{"collaboration": true, "lead_author": "Dr. Kim Patel", "co_authors": ["Dr. Sarah Martinez", "Dr. Alex Johnson"], "journal_target": "Nature Medicine"}'::jsonb,
     '{"collaboration", "healthcare", "technology", "research", "multi-author"}')
    RETURNING id INTO kim_collab_doc_id;
    
    -- Template documents
    INSERT INTO documents (owner_id, title, type, status, description, is_template, language, metadata, tags)
    VALUES 
    (kim_id, 'Academic Paper Template', 'academic', 'published', 'Standard template for academic papers following APA guidelines', true, 'en',
     '{"template": true, "citation_style": "apa", "sections": ["abstract", "introduction", "methodology", "results", "discussion", "conclusion"]}'::jsonb,
     '{"template", "academic", "apa", "research paper"}')
    RETURNING id INTO kim_template_doc_id;
    
    INSERT INTO documents (owner_id, title, type, status, description, is_template, language, metadata, tags)
    VALUES 
    (maya_id, 'Novel Chapter Template', 'creative', 'published', 'Template for structuring novel chapters with scene breakdowns', true, 'en',
     '{"template": true, "genre": "general fiction", "structure": "three-act", "target_length": "3000-5000 words"}'::jsonb,
     '{"template", "novel", "chapter", "fiction", "structure"}')
    RETURNING id INTO maya_template_doc_id;

    -- =================================================================
    -- BLOCKS - Document content structure with proper hierarchy
    -- =================================================================
    
    -- Sarah's Climate Change Research Paper blocks
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata) 
    VALUES
    (sarah_climate_doc_id, NULL, 'document', 0, NULL, 0, '{}'::jsonb)
    RETURNING id INTO sarah_climate_root_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (sarah_climate_doc_id, sarah_climate_root_id, 'heading', 0, 'Abstract', 1, '{"level": 1}'::jsonb)
    RETURNING id INTO sarah_climate_abstract_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (sarah_climate_doc_id, sarah_climate_root_id, 'paragraph', 1, 'Climate change represents one of the most pressing challenges of our time, with rising sea levels posing immediate threats to coastal communities worldwide. This study examines the socioeconomic impacts of sea level rise on vulnerable populations, analyzing data from fifteen coastal regions across three continents.', 42, '{}'::jsonb)
    RETURNING id INTO sarah_climate_para1_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (sarah_climate_doc_id, sarah_climate_root_id, 'heading', 2, 'Introduction', 1, '{"level": 1}'::jsonb)
    RETURNING id INTO sarah_climate_intro_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (sarah_climate_doc_id, sarah_climate_root_id, 'paragraph', 3, 'The Intergovernmental Panel on Climate Change (IPCC) has consistently warned that global sea levels are rising at an accelerating rate. Current projections suggest a rise of 0.3 to 2.5 meters by 2100, depending on greenhouse gas emission scenarios and ice sheet dynamics.', 40, '{}'::jsonb)
    RETURNING id INTO sarah_climate_para2_id;
    
    -- Alex's Midnight Café story blocks
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (alex_cafe_doc_id, NULL, 'document', 0, NULL, 0, '{}'::jsonb)
    RETURNING id INTO alex_cafe_root_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (alex_cafe_doc_id, alex_cafe_root_id, 'heading', 0, 'Chapter 1: The First Night', 4, '{"level": 1, "chapter": 1}'::jsonb)
    RETURNING id INTO alex_cafe_chapter_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (alex_cafe_doc_id, alex_cafe_root_id, 'paragraph', 1, 'Elena had walked past the corner of Fifth and Main thousands of times, but she had never seen the small café that now glowed softly in the midnight darkness. The hand-painted sign read "Temporal Grounds" in elegant script, and warm light spilled from windows that seemed to shimmer with an otherworldly quality.', 48, '{"scene": "opening", "character": "Elena", "location": "Fifth and Main"}'::jsonb)
    RETURNING id INTO alex_cafe_para1_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (alex_cafe_doc_id, alex_cafe_root_id, 'paragraph', 2, 'She hesitated at the threshold, her hand resting on the brass door handle that felt surprisingly warm despite the cold October night. Through the glass, she could see patrons seated at mismatched tables, their faces illuminated by candlelight, each person absorbed in writing, reading, or quiet conversation.', 45, '{"mood": "mysterious", "atmosphere": "warm", "tension": "hesitation"}'::jsonb)
    RETURNING id INTO alex_cafe_para2_id;
    
    -- Dr. Kim's research paper blocks
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (kim_neural_doc_id, NULL, 'document', 0, NULL, 0, '{}'::jsonb)
    RETURNING id INTO kim_neural_root_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (kim_neural_doc_id, kim_neural_root_id, 'heading', 0, 'Neural Networks in Medical Diagnosis: A Comprehensive Analysis', 9, '{"level": 1, "title": true}'::jsonb)
    RETURNING id INTO kim_neural_title_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (kim_neural_doc_id, kim_neural_root_id, 'heading', 1, 'Abstract', 1, '{"level": 2}'::jsonb)
    RETURNING id INTO kim_neural_abstract_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (kim_neural_doc_id, kim_neural_root_id, 'paragraph', 2, 'The integration of artificial neural networks (ANNs) in medical diagnostic procedures has demonstrated significant potential for improving accuracy, efficiency, and accessibility of healthcare services. This comprehensive study analyzes the implementation of various neural network architectures across multiple medical specialties, examining their effectiveness in pattern recognition, image analysis, and decision support systems.', 52, '{"section": "abstract"}'::jsonb)
    RETURNING id INTO kim_neural_para_id;
    
    -- Maya's fantasy novel blocks
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (maya_chap1_doc_id, NULL, 'document', 0, NULL, 0, '{}'::jsonb)
    RETURNING id INTO maya_chap1_root_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (maya_chap1_doc_id, maya_chap1_root_id, 'heading', 0, 'Chapter 1: The Shattered Crown', 4, '{"level": 1, "chapter": 1}'::jsonb)
    RETURNING id INTO maya_chap1_title_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (maya_chap1_doc_id, maya_chap1_root_id, 'paragraph', 1, 'The ancient crown of Aetheria lay in fragments across the marble floor of the throne room, each piece pulsing with residual magic that made the air itself shimmer with otherworldly energy. Princess Lyralei stood among the ruins of her inheritance, her silver hair catching the ethereal light that emanated from the scattered gems.', 50, '{"scene": "opening", "character": "Princess Lyralei", "location": "throne room", "magic_level": "high"}'::jsonb)
    RETURNING id INTO maya_chap1_para1_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (maya_chap1_doc_id, maya_chap1_root_id, 'paragraph', 2, '"The prophecy spoke true," whispered Master Eldaron, his weathered hands trembling as he knelt beside the largest fragment. "The crown that once united the seven realms now marks the beginning of their division. The age of the High Kings has ended."', 37, '{"character": "Master Eldaron", "dialogue": true, "prophecy_reference": true}'::jsonb)
    RETURNING id INTO maya_chap1_para2_id;
    
    -- James's business report blocks
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (james_q4_doc_id, NULL, 'document', 0, NULL, 0, '{}'::jsonb)
    RETURNING id INTO james_q4_root_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (james_q4_doc_id, james_q4_root_id, 'heading', 0, 'Q4 2024 Marketing Strategy Report', 5, '{"level": 1, "document_type": "report"}'::jsonb)
    RETURNING id INTO james_q4_title_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (james_q4_doc_id, james_q4_root_id, 'heading', 1, 'Executive Summary', 2, '{"level": 2}'::jsonb)
    RETURNING id INTO james_q4_summary_id;
    
    INSERT INTO blocks (document_id, parent_id, type, position, content, word_count, metadata)
    VALUES
    (james_q4_doc_id, james_q4_root_id, 'paragraph', 2, 'Q4 2024 demonstrated exceptional growth across all marketing channels, with digital engagement increasing by 147% year-over-year and customer acquisition costs decreasing by 23%. Our omnichannel approach successfully integrated social media, content marketing, and traditional advertising to create a cohesive brand experience that resonated with our target demographics.', 48, '{"section": "executive_summary", "metrics": ["engagement", "acquisition_cost"]}'::jsonb)
    RETURNING id INTO james_q4_para_id;

    -- =================================================================
    -- VERSIONS - Document revision history
    -- =================================================================
    
    -- Sarah's research paper versions
    INSERT INTO versions (block_id, version_number, content, word_count, author_id, change_summary, change_type) 
    VALUES
    (sarah_climate_para1_id, 1, 'Climate change represents one of the most pressing challenges of our time, with rising sea levels posing immediate threats to coastal communities worldwide. This study examines the socioeconomic impacts of sea level rise on vulnerable populations, analyzing data from fifteen coastal regions across three continents.', 42, sarah_id, 'Initial draft of abstract', 'create');
    
    INSERT INTO versions (block_id, version_number, content, word_count, author_id, change_summary, change_type)
    VALUES
    (sarah_climate_para2_id, 1, 'The Intergovernmental Panel on Climate Change (IPCC) has consistently warned that global sea levels are rising at an accelerating rate. Current projections suggest a rise of 0.3 to 2.5 meters by 2100, depending on greenhouse gas emission scenarios and ice sheet dynamics.', 40, sarah_id, 'Added introduction paragraph', 'create');
    
    -- Alex's story versions  
    INSERT INTO versions (block_id, version_number, content, word_count, author_id, change_summary, change_type)
    VALUES
    (alex_cafe_para1_id, 1, 'Elena walked past the corner of Fifth and Main every day, but tonight she noticed a small café that glowed softly in the darkness.', 21, alex_id, 'Initial story opening', 'create');
    
    INSERT INTO versions (block_id, version_number, content, word_count, author_id, change_summary, change_type)
    VALUES
    (alex_cafe_para1_id, 2, 'Elena had walked past the corner of Fifth and Main hundreds of times, but she had never seen the small café that now glowed softly in the midnight darkness.', 26, alex_id, 'Enhanced opening with better flow', 'edit');
    
    INSERT INTO versions (block_id, version_number, content, word_count, author_id, change_summary, change_type)
    VALUES
    (alex_cafe_para1_id, 3, 'Elena had walked past the corner of Fifth and Main thousands of times, but she had never seen the small café that now glowed softly in the midnight darkness. The hand-painted sign read "Temporal Grounds" in elegant script, and warm light spilled from windows that seemed to shimmer with an otherworldly quality.', 48, alex_id, 'Added café name and atmospheric details', 'edit');
    
    -- Dr. Kim's research versions
    INSERT INTO versions (block_id, version_number, content, word_count, author_id, change_summary, change_type)
    VALUES
    (kim_neural_para_id, 1, 'The integration of artificial neural networks (ANNs) in medical diagnostic procedures has shown potential for improving healthcare services.', 18, kim_id, 'Initial abstract draft', 'create');
    
    INSERT INTO versions (block_id, version_number, content, word_count, author_id, change_summary, change_type)
    VALUES
    (kim_neural_para_id, 2, 'The integration of artificial neural networks (ANNs) in medical diagnostic procedures has demonstrated significant potential for improving accuracy and efficiency of healthcare services. This study analyzes various neural network architectures across medical specialties.', 30, kim_id, 'Expanded abstract with study scope', 'edit');
    
    INSERT INTO versions (block_id, version_number, content, word_count, author_id, change_summary, change_type)
    VALUES
    (kim_neural_para_id, 3, 'The integration of artificial neural networks (ANNs) in medical diagnostic procedures has demonstrated significant potential for improving accuracy, efficiency, and accessibility of healthcare services. This comprehensive study analyzes the implementation of various neural network architectures across multiple medical specialties, examining their effectiveness in pattern recognition, image analysis, and decision support systems.', 52, kim_id, 'Final abstract with comprehensive scope', 'edit');
    
    -- Maya's fantasy versions
    INSERT INTO versions (block_id, version_number, content, word_count, author_id, change_summary, change_type)
    VALUES
    (maya_chap1_para1_id, 1, 'The crown lay broken on the floor. Princess Lyralei looked at the pieces sadly.', 13, maya_id, 'Initial scene setup', 'create');
    
    INSERT INTO versions (block_id, version_number, content, word_count, author_id, change_summary, change_type)
    VALUES
    (maya_chap1_para1_id, 2, 'The ancient crown of Aetheria lay in fragments across the marble floor, each piece glowing with magic. Princess Lyralei stood among the ruins of her inheritance.', 25, maya_id, 'Added atmospheric details and emotion', 'edit');
    
    INSERT INTO versions (block_id, version_number, content, word_count, author_id, change_summary, change_type)
    VALUES
    (maya_chap1_para1_id, 3, 'The ancient crown of Aetheria lay in fragments across the marble floor of the throne room, each piece pulsing with residual magic that made the air itself shimmer with otherworldly energy. Princess Lyralei stood among the ruins of her inheritance, her silver hair catching the ethereal light that emanated from the scattered gems.', 50, maya_id, 'Enhanced with vivid imagery and character details', 'edit');
    
    -- James's business report versions
    INSERT INTO versions (block_id, version_number, content, word_count, author_id, change_summary, change_type)
    VALUES
    (james_q4_para_id, 1, 'Q4 showed good growth in marketing with increased digital engagement and lower costs.', 13, james_id, 'Initial summary draft', 'create');
    
    INSERT INTO versions (block_id, version_number, content, word_count, author_id, change_summary, change_type)
    VALUES
    (james_q4_para_id, 2, 'Q4 2024 demonstrated strong growth across marketing channels, with digital engagement up 147% and customer acquisition costs down 23%.', 20, james_id, 'Added specific metrics', 'edit');
    
    INSERT INTO versions (block_id, version_number, content, word_count, author_id, change_summary, change_type)
    VALUES
    (james_q4_para_id, 3, 'Q4 2024 demonstrated exceptional growth across all marketing channels, with digital engagement increasing by 147% year-over-year and customer acquisition costs decreasing by 23%. Our omnichannel approach successfully integrated social media, content marketing, and traditional advertising to create a cohesive brand experience that resonated with our target demographics.', 48, james_id, 'Comprehensive executive summary with strategy details', 'edit');

END $$;