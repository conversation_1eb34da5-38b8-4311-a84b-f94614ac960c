import React, { useState } from 'react';
import {
  SparklesIcon,
  HashtagIcon,
  ChatBubbleLeftIcon,
  Cog6ToothIcon,
  BoltIcon,
  DocumentTextIcon,
  BookmarkIcon,
  ShareIcon,
  MagnifyingGlassIcon,
  CommandLineIcon,
  FolderIcon,
  ClockIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

interface LeftToolbarProps {
  activeTab: 'ai' | 'outline' | 'comments';
  onTabChange: (tab: 'ai' | 'outline' | 'comments') => void;
  onGenerateContent: () => void;
  onToggleFocusMode: () => void;
  onOpenCommandPalette: () => void;
  isGenerating: boolean;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  recentDocs?: Array<{
    id: string;
    name: string;
    progress: number;
    color: string;
  }>;
}

const LeftToolbar: React.FC<LeftToolbarProps> = ({
  activeTab,
  onTabChange,
  onGenerateContent,
  onToggleFocusMode,
  onOpenCommandPalette,
  isGenerating,
  isCollapsed,
  onToggleCollapse,
  recentDocs = [
    { id: '1', name: 'AI Research Paper', progress: 75, color: 'bg-blue-500' },
    { id: '2', name: 'Business Proposal', progress: 92, color: 'bg-green-500' },
    { id: '3', name: 'Creative Chapter', progress: 60, color: 'bg-purple-500' },
  ],
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const tools = [
    {
      id: 'ai',
      icon: SparklesIcon,
      label: 'AI Assistant',
      shortcut: '⌘⇧A',
      action: () => onTabChange('ai'),
    },
    {
      id: 'outline',
      icon: HashtagIcon,
      label: 'Document Outline',
      shortcut: '⌘⇧O',
      action: () => onTabChange('outline'),
    },
    {
      id: 'comments',
      icon: ChatBubbleLeftIcon,
      label: 'Comments',
      shortcut: '⌘⇧C',
      action: () => onTabChange('comments'),
    },
  ];

  const bottomTools = [
    {
      id: 'focus',
      icon: BoltIcon,
      label: 'Focus Mode',
      shortcut: '⌘⇧F',
      action: onToggleFocusMode,
    },
    {
      id: 'settings',
      icon: Cog6ToothIcon,
      label: 'Settings',
      action: () => console.log('Settings'),
    },
  ];

  return (
    <motion.div
      initial={false}
      animate={{ width: isCollapsed ? 64 : 280 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className="flex flex-col border-r border-slate-200/50 bg-white/70 backdrop-blur-xl h-screen overflow-hidden relative"
    >
      {/* Toggle button - SAME position for expand/collapse - LARGER */}
      <motion.button
        onClick={onToggleCollapse}
        className="absolute bottom-4 right-4 w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg shadow-xl flex items-center justify-center z-[60] group"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        {isCollapsed ? (
          <ChevronRightIcon className="w-5 h-5 text-white" />
        ) : (
          <ChevronLeftIcon className="w-5 h-5 text-white" />
        )}
        <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg" />
      </motion.button>

      {/* Header with Logo */}
      <div className="p-4 border-b border-slate-200/50">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl flex items-center justify-center shadow-lg flex-shrink-0">
            <img 
              src="/src/assets/logo.png" 
              alt="Revisionary" 
              className="w-6 h-6 object-cover rounded"
            />
          </div>
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
              >
                <h1 className="text-lg font-bold text-slate-900">Revisionary</h1>
                <p className="text-xs text-slate-500">AI Writing Studio</p>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Search */}
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="mt-4"
            >
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-4 w-4 text-slate-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search documents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onClick={onOpenCommandPalette}
                  className="block w-full pl-9 pr-8 py-2 bg-slate-50/50 border border-slate-200/50 rounded-xl text-sm placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500/30 transition-all duration-200 cursor-pointer"
                  readOnly
                />
                <div className="absolute inset-y-0 right-0 pr-2 flex items-center">
                  <div className="flex items-center space-x-1 bg-slate-200/50 px-1 py-0.5 rounded text-xs">
                    <CommandLineIcon className="h-3 w-3 text-slate-400" />
                    <span className="text-slate-400">K</span>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Navigation */}
      <div className="flex-1 p-2">
        {/* Main tools */}
        <div className="space-y-1">
          {tools.map((tool) => {
            const IconComponent = tool.icon;
            const isActive = activeTab === tool.id;
            
            return (
              <motion.button
                key={tool.id}
                onClick={tool.action}
                className={`group w-full flex items-center rounded-xl transition-all duration-200 ${
                  isCollapsed ? 'p-3 justify-center' : 'px-3 py-2'
                } ${
                  isActive
                    ? 'bg-purple-100 text-purple-700 shadow-sm'
                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                title={isCollapsed ? tool.label : undefined}
              >
                <IconComponent className="w-5 h-5 flex-shrink-0" />
                <AnimatePresence>
                  {!isCollapsed && (
                    <motion.span
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.2 }}
                      className="ml-3 text-sm font-medium"
                    >
                      {tool.label}
                    </motion.span>
                  )}
                </AnimatePresence>
                
                {/* Tooltip for collapsed state */}
                {isCollapsed && (
                  <div className="absolute left-16 bg-slate-900 text-white text-xs px-2 py-1 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                    {tool.label}
                    {tool.shortcut && (
                      <span className="ml-2 text-slate-400">{tool.shortcut}</span>
                    )}
                    <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-0 h-0 border-t-2 border-b-2 border-r-2 border-t-transparent border-b-transparent border-r-slate-900"></div>
                  </div>
                )}
              </motion.button>
            );
          })}
        </div>

        {/* Quick actions separator */}
        <div className="my-4 border-t border-slate-200/50"></div>
        
        {/* Generate button */}
        <motion.button
          onClick={onGenerateContent}
          disabled={isGenerating}
          className={`group w-full flex items-center rounded-xl transition-all duration-200 ${
            isCollapsed ? 'p-3 justify-center' : 'px-3 py-2'
          } ${
            isGenerating
              ? 'bg-purple-100 text-purple-700 cursor-not-allowed'
              : 'text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:shadow-lg hover:shadow-purple-500/25'
          }`}
          whileHover={!isGenerating ? { scale: 1.02 } : {}}
          whileTap={!isGenerating ? { scale: 0.98 } : {}}
          title={isCollapsed ? (isGenerating ? 'Generating...' : 'Generate Content') : undefined}
        >
          {isGenerating ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="w-5 h-5"
            >
              <SparklesIcon className="w-5 h-5" />
            </motion.div>
          ) : (
            <BoltIcon className="w-5 h-5 flex-shrink-0" />
          )}
          <AnimatePresence>
            {!isCollapsed && (
              <motion.span
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                transition={{ duration: 0.2 }}
                className="ml-3 text-sm font-medium"
              >
                {isGenerating ? 'Generating...' : 'Generate Content'}
              </motion.span>
            )}
          </AnimatePresence>
        </motion.button>

        {/* Recent Documents */}
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mt-6"
            >
              <h3 className="text-xs font-bold text-slate-500 uppercase tracking-wider mb-3 px-3">
                Recent Work
              </h3>
              <div className="space-y-2">
                {recentDocs.map((doc) => (
                  <motion.div
                    key={doc.id}
                    className="group relative overflow-hidden bg-white/60 rounded-xl p-3 border border-slate-200/50 hover:bg-white/80 hover:shadow-md cursor-pointer transition-all duration-200"
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className={`w-2 h-2 ${doc.color} rounded-full mr-2 shadow-sm`}></div>
                        <span className="text-sm font-medium text-slate-700 truncate group-hover:text-purple-700 transition-colors">
                          {doc.name}
                        </span>
                      </div>
                      <span className="text-xs text-slate-500 font-medium">{doc.progress}%</span>
                    </div>
                    
                    <div className="w-full bg-slate-200 rounded-full h-1 overflow-hidden">
                      <div 
                        className={`h-full bg-gradient-to-r ${doc.color.replace('bg-', 'from-')} to-transparent transition-all duration-500`}
                        style={{ width: `${doc.progress}%` }}
                      ></div>
                    </div>
                    
                    {/* Hover effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Bottom tools */}
      <div className="p-2 border-t border-slate-200/50">
        <div className="space-y-1">
          {bottomTools.map((tool) => {
            const IconComponent = tool.icon;
            
            return (
              <motion.button
                key={tool.id}
                onClick={tool.action}
                className={`group w-full flex items-center rounded-xl text-slate-600 hover:text-slate-900 hover:bg-slate-100 transition-all duration-200 ${
                  isCollapsed ? 'p-3 justify-center' : 'px-3 py-2'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                title={isCollapsed ? tool.label : undefined}
              >
                <IconComponent className="w-5 h-5 flex-shrink-0" />
                <AnimatePresence>
                  {!isCollapsed && (
                    <motion.span
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.2 }}
                      className="ml-3 text-sm font-medium"
                    >
                      {tool.label}
                    </motion.span>
                  )}
                </AnimatePresence>
                
                {/* Tooltip for collapsed state */}
                {isCollapsed && (
                  <div className="absolute left-16 bg-slate-900 text-white text-xs px-2 py-1 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                    {tool.label}
                    {tool.shortcut && (
                      <span className="ml-2 text-slate-400">{tool.shortcut}</span>
                    )}
                    <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-0 h-0 border-t-2 border-b-2 border-r-2 border-t-transparent border-b-transparent border-r-slate-900"></div>
                  </div>
                )}
              </motion.button>
            );
          })}
        </div>
      </div>
    </motion.div>
  );
};

export default LeftToolbar;