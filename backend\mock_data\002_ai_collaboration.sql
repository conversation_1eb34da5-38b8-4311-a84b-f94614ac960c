-- Revisionary Mock Data - AI Collaboration Features
-- Part 2: Suggestions, summaries, collaborations, and comments
-- Depends on 001_core_data.sql for user and document references

DO $$
DECLARE
    -- Get user IDs from firebase_uid (from 001_core_data.sql)
    sarah_id UUID;
    alex_id UUID;
    kim_id UUID;
    maya_id UUID;
    james_id UUID;
    
    -- Get document IDs by title (from 001_core_data.sql)
    sarah_climate_doc_id UUID;
    alex_cafe_doc_id UUID;
    kim_neural_doc_id UUID;
    maya_chap1_doc_id UUID;
    maya_world_doc_id UUID;
    james_q4_doc_id UUID;
    kim_collab_doc_id UUID;
    
    -- Get block IDs from specific content patterns
    sarah_climate_para1_id UUID;
    sarah_climate_para2_id UUID;
    alex_cafe_para1_id UUID;
    alex_cafe_para2_id UUID;
    kim_neural_para_id UUID;
    maya_chap1_title_id UUID;
    maya_chap1_para1_id UUID;
    maya_chap1_para2_id UUID;
    james_q4_title_id UUID;
    james_q4_para_id UUID;
    
    -- For capturing new IDs
    first_comment_id UUID;
    threaded_comment_id UUID;
    
BEGIN
    -- Get user IDs from existing users
    SELECT id INTO sarah_id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1;
    SELECT id INTO alex_id FROM users WHERE firebase_uid = 'firebase_user_002' LIMIT 1;
    SELECT id INTO kim_id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1;
    SELECT id INTO maya_id FROM users WHERE firebase_uid = 'firebase_user_004' LIMIT 1;
    SELECT id INTO james_id FROM users WHERE firebase_uid = 'firebase_user_005' LIMIT 1;
    
    -- Get document IDs from existing documents
    SELECT id INTO sarah_climate_doc_id FROM documents WHERE title = 'Climate Change Research Paper' LIMIT 1;
    SELECT id INTO alex_cafe_doc_id FROM documents WHERE title = 'The Midnight Café' LIMIT 1;
    SELECT id INTO kim_neural_doc_id FROM documents WHERE title = 'Neural Networks in Medical Diagnosis' LIMIT 1;
    SELECT id INTO maya_chap1_doc_id FROM documents WHERE title = 'Chronicles of Aetheria: Chapter 1' LIMIT 1;
    SELECT id INTO maya_world_doc_id FROM documents WHERE title = 'World Building Bible' LIMIT 1;
    SELECT id INTO james_q4_doc_id FROM documents WHERE title = 'Q4 Marketing Strategy Report' LIMIT 1;
    SELECT id INTO kim_collab_doc_id FROM documents WHERE title = 'Industry Research Collaboration' LIMIT 1;
    
    -- Get block IDs from existing blocks by content patterns
    SELECT id INTO sarah_climate_para1_id FROM blocks WHERE content LIKE 'Climate change represents one of the most pressing%' AND document_id = sarah_climate_doc_id LIMIT 1;
    SELECT id INTO sarah_climate_para2_id FROM blocks WHERE content LIKE 'The Intergovernmental Panel on Climate Change%' AND document_id = sarah_climate_doc_id LIMIT 1;
    SELECT id INTO alex_cafe_para1_id FROM blocks WHERE content LIKE 'Elena had walked past the corner%' AND document_id = alex_cafe_doc_id LIMIT 1;
    SELECT id INTO alex_cafe_para2_id FROM blocks WHERE content LIKE 'She hesitated at the threshold%' AND document_id = alex_cafe_doc_id LIMIT 1;
    SELECT id INTO kim_neural_para_id FROM blocks WHERE content LIKE 'The integration of artificial neural networks%' AND document_id = kim_neural_doc_id LIMIT 1;
    SELECT id INTO maya_chap1_title_id FROM blocks WHERE content = 'Chapter 1: The Shattered Crown' AND document_id = maya_chap1_doc_id LIMIT 1;
    SELECT id INTO maya_chap1_para1_id FROM blocks WHERE content LIKE 'The ancient crown of Aetheria lay in fragments%' AND document_id = maya_chap1_doc_id LIMIT 1;
    SELECT id INTO maya_chap1_para2_id FROM blocks WHERE content LIKE '"The prophecy spoke true,"%' AND document_id = maya_chap1_doc_id LIMIT 1;
    SELECT id INTO james_q4_title_id FROM blocks WHERE content = 'Q4 2024 Marketing Strategy Report' AND document_id = james_q4_doc_id LIMIT 1;
    SELECT id INTO james_q4_para_id FROM blocks WHERE content LIKE 'Q4 2024 demonstrated exceptional growth%' AND document_id = james_q4_doc_id LIMIT 1;

    -- =================================================================
    -- SUGGESTIONS - AI-generated writing suggestions across all agent types
    -- =================================================================
    
    -- Grammar suggestions for Sarah's research paper
    INSERT INTO suggestions (block_id, agent_type, severity, category, original_text, suggested_text, explanation, confidence, position, original_hash, status, tokens_used, model_used, processing_time_ms, expires_at) 
    VALUES
    (sarah_climate_para1_id, 'grammar', 'medium', 'subject_verb_agreement', 'posing immediate threats', 'pose immediate threats', 'The subject "sea levels" requires the plural verb form "pose" rather than the singular "posing".', 0.92, '{"start": 85, "end": 105, "line": 1}'::jsonb, '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090', 'pending', 45, 'gpt-4', 120, NOW() + INTERVAL '7 days');
    
    INSERT INTO suggestions (block_id, agent_type, severity, category, original_text, suggested_text, explanation, confidence, position, original_hash, status, tokens_used, model_used, processing_time_ms, expires_at)
    VALUES
    (sarah_climate_para2_id, 'grammar', 'low', 'article_usage', 'The Intergovernmental Panel', 'The Intergovernmental Panel', 'Consider whether the definite article "The" is necessary here for clarity.', 0.65, '{"start": 0, "end": 3, "line": 1}'::jsonb, '8f61ad5cfa0c471c8cbf810ea285cb1e5f9c2c5e5e5e4f58a3229667703e1587', 'rejected', 30, 'gpt-4', 95, NOW() + INTERVAL '6 days');
    
    -- Style suggestions for Alex's creative writing
    INSERT INTO suggestions (block_id, agent_type, severity, category, original_text, suggested_text, explanation, confidence, position, original_hash, status, tokens_used, model_used, processing_time_ms, expires_at)
    VALUES
    (alex_cafe_para1_id, 'style', 'high', 'word_choice', 'walked past', 'strolled past', 'Consider using "strolled" to create a more leisurely, contemplative mood that matches the mysterious atmosphere.', 0.78, '{"start": 10, "end": 21, "line": 1}'::jsonb, '5eb9480d37ff7490f465d5fad3b425fe4a4c65b46fd4eef160cb1b644df7f1e8', 'pending', 52, 'gpt-4', 140, NOW() + INTERVAL '7 days');
    
    INSERT INTO suggestions (block_id, agent_type, severity, category, original_text, suggested_text, explanation, confidence, position, original_hash, status, user_feedback, tokens_used, model_used, processing_time_ms, expires_at)
    VALUES
    (alex_cafe_para2_id, 'style', 'medium', 'sentence_structure', 'She hesitated at the threshold, her hand resting on the brass door handle that felt surprisingly warm despite the cold October night.', 'At the threshold she hesitated, her hand finding the brass door handle—surprisingly warm despite the cold October night.', 'This restructuring creates better flow and emphasizes the hesitation while making the warmth discovery more immediate.', 0.71, '{"start": 0, "end": 125, "line": 1}'::jsonb, '5caed690742086c2bd935f39182ffdd56b15e66a58c7f24f8ab7fb67b1197ba3', 'accepted', 'helpful', 75, 'gpt-4', 185, NOW() + INTERVAL '7 days');
    
    -- Content suggestions for Dr. Kim's research
    INSERT INTO suggestions (block_id, agent_type, severity, category, original_text, suggested_text, explanation, confidence, position, original_hash, status, tokens_used, model_used, processing_time_ms, expires_at)
    VALUES
    (kim_neural_para_id, 'content', 'high', 'clarity', 'various neural network architectures', 'convolutional neural networks (CNNs), recurrent neural networks (RNNs), and transformer architectures', 'Being more specific about the types of neural networks studied will help readers understand the scope of your research.', 0.89, '{"start": 180, "end": 215, "line": 1}'::jsonb, 'd6fd5f16ff1be3b5dcb994f4d39b35e7c352566883f950b8de41d053744c43d6', 'pending', 68, 'gpt-4', 160, NOW() + INTERVAL '7 days');
    
    INSERT INTO suggestions (block_id, agent_type, severity, category, original_text, suggested_text, explanation, confidence, position, original_hash, status, tokens_used, model_used, processing_time_ms, expires_at)
    VALUES
    (kim_neural_para_id, 'content', 'medium', 'evidence', 'demonstrated significant potential', 'demonstrated significant potential, with diagnostic accuracy improvements ranging from 15-40% across specialties', 'Adding specific performance metrics will strengthen your claims and provide concrete evidence of effectiveness.', 0.83, '{"start": 95, "end": 125, "line": 1}'::jsonb, '9ca44935243c46a7d03d62e9501fb10b881153134867ab213c582e18d14175a9', 'pending', 55, 'gpt-4', 145, NOW() + INTERVAL '7 days');
    
    -- Structure suggestions for Maya's novel
    INSERT INTO suggestions (block_id, agent_type, severity, category, original_text, suggested_text, explanation, confidence, position, original_hash, status, tokens_used, model_used, processing_time_ms, expires_at)
    VALUES
    (maya_chap1_para1_id, 'structure', 'medium', 'pacing', 'The ancient crown of Aetheria lay in fragments across the marble floor of the throne room, each piece pulsing with residual magic that made the air itself shimmer with otherworldly energy.', 'The ancient crown of Aetheria lay in fragments. Across the marble floor of the throne room, each piece pulsed with residual magic, making the air shimmer with otherworldly energy.', 'Breaking this into shorter sentences will improve pacing and create more dramatic impact for your opening.', 0.74, '{"start": 0, "end": 160, "line": 1}'::jsonb, 'c67be8743bb9b8feb7cfe80790b98859f7e8895a16ef8b573842c1751f5bbb6b', 'pending', 85, 'gpt-4', 200, NOW() + INTERVAL '7 days');
    
    INSERT INTO suggestions (block_id, agent_type, severity, category, original_text, suggested_text, explanation, confidence, position, original_hash, status, user_feedback, tokens_used, model_used, processing_time_ms, expires_at)
    VALUES
    (maya_chap1_para2_id, 'style', 'high', 'dialogue', '"The prophecy spoke true," whispered Master Eldaron', '"The prophecy spoke true," Master Eldaron whispered, his voice barely audible in the vast chamber', 'Adding context about the setting and emphasizing the whisper will enhance the dramatic tension.', 0.76, '{"start": 0, "end": 45, "line": 1}'::jsonb, 'e494853669b1a5aceb48b6c678d324c61e8d10f49b9be21c633b54b24c6cdb7d', 'accepted', 'helpful', 48, 'gpt-4', 125, NOW() + INTERVAL '7 days');
    
    -- Technical suggestions for James's business report
    INSERT INTO suggestions (block_id, agent_type, severity, category, original_text, suggested_text, explanation, confidence, position, original_hash, status, user_feedback, tokens_used, model_used, processing_time_ms, expires_at)
    VALUES
    (james_q4_para_id, 'style', 'low', 'tone', 'exceptional growth', 'strong growth', 'Consider using "strong" instead of "exceptional" for a more measured, professional tone in business reporting.', 0.68, '{"start": 25, "end": 43, "line": 1}'::jsonb, '1716f15cd18fffd129e5557a25fb68946d454de53ef808e69f677f915aaf34d5', 'rejected', 'not_helpful', 35, 'gpt-4', 90, NOW() + INTERVAL '7 days');
    
    INSERT INTO suggestions (block_id, agent_type, severity, category, original_text, suggested_text, explanation, confidence, position, original_hash, status, user_feedback, tokens_used, model_used, processing_time_ms, expires_at)
    VALUES
    (james_q4_para_id, 'grammar', 'critical', 'number_format', '147%', '147%', 'Ensure consistent formatting of percentages throughout the document.', 0.95, '{"start": 110, "end": 114, "line": 1}'::jsonb, '57ce52cfd20c484c8b41569b8c43274f86c45f77d05e83c300854dbdef7c9d7a', 'accepted', 'helpful', 25, 'gpt-4', 80, NOW() + INTERVAL '7 days');
    
    -- Additional AI suggestions across different categories
    INSERT INTO suggestions (block_id, agent_type, severity, category, original_text, suggested_text, explanation, confidence, position, original_hash, status, tokens_used, model_used, processing_time_ms, expires_at)
    VALUES
    (sarah_climate_para2_id, 'research', 'medium', 'citation_needed', 'Current projections suggest', 'Current projections (IPCC, 2023) suggest', 'This claim would benefit from a specific citation to support the projection data.', 0.87, '{"start": 85, "end": 110, "line": 1}'::jsonb, '66b14d400482f6907425fb542d5aba94ab13f7787604be86669453c58be825dc', 'pending', 40, 'gpt-4', 110, NOW() + INTERVAL '7 days');
    
    INSERT INTO suggestions (block_id, agent_type, severity, category, original_text, suggested_text, explanation, confidence, position, original_hash, status, tokens_used, model_used, processing_time_ms, expires_at)
    VALUES
    (alex_cafe_para1_id, 'creative', 'low', 'atmosphere', 'glowed softly', 'glowed with an amber warmth', 'Adding color and warmth description enhances the inviting, mysterious atmosphere.', 0.69, '{"start": 95, "end": 107, "line": 1}'::jsonb, 'd2aea6862dbeb2c96b2764ead332077a6486110074eb3d26c548c7b38478e21d', 'pending', 42, 'gpt-4', 115, NOW() + INTERVAL '7 days');
    
    INSERT INTO suggestions (block_id, agent_type, severity, category, original_text, suggested_text, explanation, confidence, position, original_hash, status, tokens_used, model_used, processing_time_ms, expires_at)
    VALUES
    (maya_chap1_title_id, 'style', 'medium', 'consistency', 'Chapter 1: The Shattered Crown', 'Chapter One: The Shattered Crown', 'Consider using written-out numbers for chapter titles to match publishing conventions.', 0.72, '{"start": 0, "end": 28, "line": 1}'::jsonb, '845d1e3599ab297e9aa42e2c63d1421f4732375633a2efe00fe03793e7acd59e', 'pending', 38, 'gpt-4', 100, NOW() + INTERVAL '7 days');

    -- =================================================================
    -- SUMMARIES - AI-generated content summaries with embeddings
    -- =================================================================
    
    -- Document-level summaries
    INSERT INTO summaries (block_id, level, summary_text, key_points, token_count, model_used, expires_at, content_hash) 
    VALUES
    ((SELECT id FROM blocks WHERE document_id = sarah_climate_doc_id AND type = 'document'), 'document', 'This academic research paper examines the socioeconomic impacts of rising sea levels on coastal communities. The study analyzes data from fifteen coastal regions across three continents, focusing on vulnerable populations and their adaptation strategies. The research contributes to climate change policy discussions by providing empirical evidence of immediate threats facing coastal areas.', 
     '{"Climate change impact on coastal areas", "Socioeconomic vulnerability analysis", "Multi-continental comparative study", "Policy implications for adaptation"}', 
     185, 'gpt-4', NOW() + INTERVAL '30 days', 'c3a4eede5ccf282d14d28c7dc015087ea7e65f5052fde75b531569181b67da83');
    
    INSERT INTO summaries (block_id, level, summary_text, key_points, token_count, model_used, expires_at, content_hash)
    VALUES
    ((SELECT id FROM blocks WHERE document_id = alex_cafe_doc_id AND type = 'document'), 'document', 'A urban fantasy short story about Elena discovering a mysterious café called "Temporal Grounds" that only appears at midnight. The story explores themes of mystery, magic in everyday settings, and the liminal space between the ordinary and extraordinary. The narrative focuses on atmospheric world-building and character discovery.', 
     '{"Urban fantasy genre", "Mysterious café setting", "Temporal/time themes", "Atmospheric storytelling", "Character-driven narrative"}', 
     165, 'gpt-4', NOW() + INTERVAL '30 days', 'f2c32c27fe44dddb535dac554657fe914b0b55a2f1001979cb4cbc2f7bc108c5');
    
    INSERT INTO summaries (block_id, level, summary_text, key_points, token_count, model_used, expires_at, content_hash)
    VALUES
    ((SELECT id FROM blocks WHERE document_id = kim_neural_doc_id AND type = 'document'), 'document', 'A comprehensive academic study analyzing the implementation and effectiveness of neural networks in medical diagnosis. The research examines various AI architectures across multiple medical specialties, evaluating their impact on diagnostic accuracy, efficiency, and healthcare accessibility. The study targets publication in peer-reviewed medical journals.', 
     '{"AI in healthcare", "Neural network applications", "Medical diagnostic accuracy", "Healthcare efficiency", "Multi-specialty analysis"}', 
     198, 'gpt-4', NOW() + INTERVAL '30 days', 'f11d9d5d54fa805be3357035ae45977c692f2eb51c1fa0e8ef48dd8775f68e99');
    
    INSERT INTO summaries (block_id, level, summary_text, key_points, token_count, model_used, expires_at, content_hash)
    VALUES
    ((SELECT id FROM blocks WHERE document_id = maya_chap1_doc_id AND type = 'document'), 'document', 'Opening chapter of an epic fantasy novel set in the world of Aetheria. The story begins with the destruction of an ancient crown that united seven realms, marking the end of the High Kings era. Features Princess Lyralei as the protagonist dealing with the political and magical consequences of this pivotal event.', 
     '{"Epic fantasy genre", "Political upheaval", "Magic system", "Princess protagonist", "World-building", "Series opener"}', 
     175, 'gpt-4', NOW() + INTERVAL '30 days', '66ee4bdbde5f212b300cdbf220d17b02700a4c3634cc77e37148742ce5da11d7');
    
    INSERT INTO summaries (block_id, level, summary_text, key_points, token_count, model_used, expires_at, content_hash)
    VALUES
    ((SELECT id FROM blocks WHERE document_id = james_q4_doc_id AND type = 'document'), 'document', 'Q4 2024 marketing strategy report analyzing performance metrics and strategic recommendations. The document covers digital engagement growth, customer acquisition cost optimization, and omnichannel marketing approach. Intended for executive leadership and strategic planning purposes.', 
     '{"Q4 performance analysis", "Digital marketing growth", "Customer acquisition optimization", "Omnichannel strategy", "Executive reporting"}', 
     155, 'gpt-4', NOW() + INTERVAL '30 days', 'f831b4ef9171285ac6c6da16b52aa096302d488d532b87dc18b0d0d78e22371e');
    
    -- Chapter-level summaries
    INSERT INTO summaries (block_id, level, summary_text, key_points, token_count, model_used, expires_at, content_hash)
    VALUES
    (alex_cafe_para1_id, 'chapter', 'Elena discovers a mysterious café that appears only at midnight on a corner she passes daily. The chapter establishes the otherworldly atmosphere of "Temporal Grounds" and introduces the concept of a place that exists between normal time. Sets up the central mystery and magical realism elements.', 
     '{"Mystery establishment", "Temporal themes", "Atmospheric description", "Character introduction"}', 
     145, 'gpt-4', NOW() + INTERVAL '15 days', '77d7a31c113180876ac623e88d13eaf5b3d7d91088db189ba942fa77dbdc8f9c');
    
    INSERT INTO summaries (block_id, level, summary_text, key_points, token_count, model_used, expires_at, content_hash)
    VALUES
    (maya_chap1_title_id, 'chapter', 'The destruction of Aetheria''s unifying crown marks the end of the High Kings era. Princess Lyralei confronts the political and magical implications while Master Eldaron reveals prophetic significance. Establishes the central conflict and world-changing event that drives the narrative.', 
     '{"Crown destruction", "Political upheaval", "Prophecy fulfillment", "Character introduction", "Magical consequences"}', 
     160, 'gpt-4', NOW() + INTERVAL '15 days', 'f0e11a95a5a73be0f4d9138a6668c6c43a6f9e102fcefeae2a99ede70d99b4f2');
    
    -- Section-level summaries
    INSERT INTO summaries (block_id, level, summary_text, key_points, token_count, model_used, expires_at, content_hash)
    VALUES
    ((SELECT id FROM blocks WHERE content LIKE 'Neural Networks in Medical Diagnosis%' AND document_id = kim_neural_doc_id), 'section', 'Title section introducing a comprehensive analysis of neural networks in medical diagnosis, positioning the study as an examination of AI effectiveness across multiple healthcare specialties with focus on accuracy and accessibility improvements.', 
     '{"Study introduction", "Medical AI focus", "Multi-specialty scope"}', 
     95, 'gpt-4', NOW() + INTERVAL '7 days', '1c4939ccaf1f981d26b06d7a27dac4f5080bce69f029ae3e1c0c42566ace6aa3');
    
    INSERT INTO summaries (block_id, level, summary_text, key_points, token_count, model_used, expires_at, content_hash)
    VALUES
    (james_q4_title_id, 'section', 'Q4 2024 Marketing Strategy Report title section introducing a comprehensive analysis of marketing performance and strategic recommendations for executive leadership review.', 
     '{"Q4 analysis", "Marketing strategy", "Executive reporting"}', 
     85, 'gpt-4', NOW() + INTERVAL '7 days', '7ef0846f7644f886f881833338c86ab9f7517c23c896110437b76b9d866f93e7');

    -- =================================================================
    -- COLLABORATIONS - Document sharing and team work
    -- =================================================================
    
    -- Dr. Kim's research paper collaborations
    INSERT INTO collaborations (document_id, user_id, role, status, invited_by, permissions, expires_at) 
    VALUES
    (kim_neural_doc_id, james_id, 'commenter', 'active', kim_id, 
     '{"can_comment": true, "can_suggest": true, "can_view_analytics": false, "sections": ["methodology", "results"]}'::jsonb, 
     NOW() + INTERVAL '30 days');
    
    INSERT INTO collaborations (document_id, user_id, role, status, invited_by, permissions)
    VALUES
    (kim_neural_doc_id, sarah_id, 'editor', 'active', kim_id, 
     '{"can_comment": true, "can_suggest": true, "can_edit": true, "can_view_analytics": true, "sections": ["all"]}'::jsonb);
    
    -- Maya's novel collaboration with beta readers
    INSERT INTO collaborations (document_id, user_id, role, status, invited_by, permissions, expires_at)
    VALUES
    (maya_chap1_doc_id, alex_id, 'commenter', 'active', maya_id, 
     '{"can_comment": true, "can_suggest": false, "feedback_type": "beta_reader", "focus_areas": ["plot", "character_development", "pacing"]}'::jsonb, 
     NOW() + INTERVAL '21 days');
    
    INSERT INTO collaborations (document_id, user_id, role, status, invited_by, permissions, expires_at)
    VALUES
    (maya_world_doc_id, kim_id, 'viewer', 'active', maya_id, 
     '{"can_comment": false, "can_view": true, "purpose": "research_consultation", "expertise": ["world_building", "technical_accuracy"]}'::jsonb, 
     NOW() + INTERVAL '14 days');
    
    -- James's business document team collaboration
    INSERT INTO collaborations (document_id, user_id, role, status, invited_by, permissions)
    VALUES
    (james_q4_doc_id, kim_id, 'editor', 'active', james_id, 
     '{"can_comment": true, "can_suggest": true, "can_edit": true, "department": "analytics", "approval_required": false}'::jsonb);
    
    INSERT INTO collaborations (document_id, user_id, role, status, invited_by, permissions, expires_at)
    VALUES
    ((SELECT id FROM documents WHERE title = 'Product Launch Proposal'), maya_id, 'commenter', 'pending', james_id, 
     '{"can_comment": true, "can_suggest": true, "role": "creative_consultant", "focus": ["messaging", "brand_voice"]}'::jsonb, 
     NOW() + INTERVAL '7 days');
    
    -- Cross-document collaboration
    INSERT INTO collaborations (document_id, user_id, role, status, invited_by, permissions)
    VALUES
    (kim_collab_doc_id, sarah_id, 'editor', 'active', kim_id, 
     '{"can_comment": true, "can_suggest": true, "can_edit": true, "co_author": true, "sections": ["data_analysis", "environmental_impact"]}'::jsonb);
    
    INSERT INTO collaborations (document_id, user_id, role, status, invited_by, permissions, expires_at)
    VALUES
    (kim_collab_doc_id, james_id, 'commenter', 'active', kim_id, 
     '{"can_comment": true, "can_suggest": true, "expertise": ["business_applications", "policy_implications"]}'::jsonb, 
     NOW() + INTERVAL '45 days');

    -- =================================================================
    -- COMMENTS - User feedback and discussions
    -- =================================================================
    
    -- Comments on Sarah's research paper
    INSERT INTO comments (block_id, author_id, parent_id, content, position, type, status) 
    VALUES
    (sarah_climate_para1_id, kim_id, NULL, 'This is a strong opening that clearly states the research focus. Consider adding a brief mention of your methodology here to give readers a complete picture of the study scope.', 
     '{"start": 0, "end": 200}'::jsonb, 'suggestion', 'open')
    RETURNING id INTO first_comment_id;
    
    INSERT INTO comments (block_id, author_id, parent_id, content, type, status)
    VALUES
    (sarah_climate_para1_id, sarah_id, first_comment_id, 'Thank you for the feedback! I''ll add a sentence about our mixed-methods approach combining quantitative data analysis with qualitative community interviews.', 
     'general', 'open');
    
    INSERT INTO comments (block_id, author_id, parent_id, content, position, type, status)
    VALUES
    (sarah_climate_para2_id, james_id, NULL, 'The IPCC reference is solid, but you might also want to cite some regional studies that show localized variations in sea level rise rates. This would strengthen your argument about the need for region-specific analysis.', 
     '{"start": 45, "end": 120}'::jsonb, 'suggestion', 'open');
    
    -- Comments on Alex's creative writing
    INSERT INTO comments (block_id, author_id, parent_id, content, position, type, status)
    VALUES
    (alex_cafe_para1_id, maya_id, NULL, 'Beautiful atmospheric opening! The mystery is immediately compelling. I love how you''ve made the café feel both inviting and otherworldly.', 
     '{"start": 0, "end": 150}'::jsonb, 'general', 'open');
    
    INSERT INTO comments (block_id, author_id, parent_id, content, position, type, status)
    VALUES
    (alex_cafe_para2_id, alex_id, NULL, 'The sensory details here are wonderful - the warm brass handle contrasting with the cold night really adds to the mysterious atmosphere. Maybe add what Elena can smell or hear from inside?', 
     '{"start": 25, "end": 80}'::jsonb, 'suggestion', 'open');
    
    INSERT INTO comments (block_id, author_id, parent_id, content, type, status)
    VALUES
    (alex_cafe_para1_id, kim_id, NULL, 'From a narrative structure perspective, this opening does an excellent job of establishing the ordinary world before introducing the supernatural element. Well done!', 
     'general', 'resolved');
    
    -- Comments on Dr. Kim's research
    INSERT INTO comments (block_id, author_id, parent_id, content, position, type, status)
    VALUES
    (kim_neural_para_id, sarah_id, NULL, 'This abstract effectively summarizes the research scope. For the final version, consider quantifying "multiple medical specialties" - are we talking about 5, 10, 15 specialties?', 
     '{"start": 150, "end": 200}'::jsonb, 'suggestion', 'open');
    
    INSERT INTO comments (block_id, author_id, parent_id, content, type, status)
    VALUES
    (kim_neural_para_id, james_id, NULL, 'The business applications of this research could be significant. Have you considered including a section on implementation costs and ROI for healthcare systems?', 
     'question', 'open');
    
    -- Comments on Maya's fantasy novel
    INSERT INTO comments (block_id, author_id, parent_id, content, position, type, status)
    VALUES
    (maya_chap1_para1_id, alex_id, NULL, 'Wow! This opening immediately drops us into a pivotal moment. The imagery of the shattered crown is powerful and sets up the political conflict beautifully.', 
     '{"start": 0, "end": 100}'::jsonb, 'general', 'open');
    
    INSERT INTO comments (block_id, author_id, parent_id, content, position, type, status)
    VALUES
    (maya_chap1_para2_id, alex_id, NULL, 'Master Eldaron''s dialogue feels authentic and adds gravitas to the scene. The prophecy reference creates intrigue without being heavy-handed.', 
     '{"start": 0, "end": 50}'::jsonb, 'general', 'open');
    
    INSERT INTO comments (block_id, author_id, parent_id, content, position, type, status)
    VALUES
    (maya_chap1_para1_id, kim_id, NULL, 'The magical descriptions are vivid without being overwhelming. Consider adding how this magic affects Princess Lyralei physically - does she feel the power, is it overwhelming, comforting?', 
     '{"start": 80, "end": 160}'::jsonb, 'suggestion', 'open');
    
    -- Comments on James's business report
    INSERT INTO comments (block_id, author_id, parent_id, content, position, type, status)
    VALUES
    (james_q4_para_id, kim_id, NULL, 'Strong executive summary with specific metrics. The 147% engagement increase is impressive - what attribution model are you using to measure this?', 
     '{"start": 60, "end": 120}'::jsonb, 'question', 'open');
    
    INSERT INTO comments (block_id, author_id, parent_id, content, position, type, status)
    VALUES
    (james_q4_para_id, maya_id, NULL, 'The messaging about "cohesive brand experience" resonates well. Consider adding a brief mention of how this translates to customer lifetime value or retention rates.', 
     '{"start": 200, "end": 280}'::jsonb, 'suggestion', 'open');
    
    INSERT INTO comments (block_id, author_id, parent_id, content, type, status)
    VALUES
    (james_q4_title_id, sarah_id, NULL, 'Minor formatting note: ensure all metric presentations follow the same format (e.g., "147%" vs "twenty-three percent").', 
     'issue', 'open');
    
    -- Threaded conversation example
    INSERT INTO comments (block_id, author_id, parent_id, content, position, type, status)
    VALUES
    (maya_chap1_para1_id, maya_id, NULL, 'Should we add more sensory details about how the magic feels in the air? Temperature, sound, smell?', 
     '{"start": 100, "end": 160}'::jsonb, 'question', 'resolved')
    RETURNING id INTO threaded_comment_id;
    
    INSERT INTO comments (block_id, author_id, parent_id, content, type, status)
    VALUES
    (maya_chap1_para1_id, alex_id, threaded_comment_id, 'Great idea! Maybe a subtle humming sound from the gems, or the air feeling electrically charged?', 
     'suggestion', 'resolved');
    
    INSERT INTO comments (block_id, author_id, parent_id, content, type, status)
    VALUES
    (maya_chap1_para1_id, maya_id, threaded_comment_id, 'Perfect! I''ll work that into the next revision. The electrical charge idea fits perfectly with the "shimmer" description.', 
     'general', 'resolved');

END $$;