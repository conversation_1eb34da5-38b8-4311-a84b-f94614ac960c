"""
Revisionary Database Service

Purpose: Database connection and query management using Supabase
- Supabase client initialization and connection management
- Query execution with error handling and logging
- Connection pooling and performance optimization
- Database health monitoring

Features:
- Async Supabase client operations
- Connection retry logic
- Query performance monitoring
- Structured error handling
"""

import asyncio
from typing import Dict, List, Optional, Any, Union
from contextlib import asynccontextmanager
import structlog
from supabase import create_client, Client
from supabase.lib.client_options import ClientOptions
import asyncpg
from asyncpg import Pool, Connection

from .settings import settings

logger = structlog.get_logger(__name__)


class DatabaseService:
    """Database service for Supabase operations."""
    
    def __init__(self):
        self.supabase_client: Optional[Client] = None
        self.pg_pool: Optional[Pool] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize database connections."""
        if self._initialized:
            return
        
        try:
            logger.info("Initializing database service")
            
            # Initialize Supabase client
            await self._init_supabase_client()
            
            # Initialize PostgreSQL connection pool for direct queries
            await self._init_pg_pool()
            
            self._initialized = True
            logger.info("Database service initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize database service", error=str(e), exc_info=True)
            raise
    
    async def _init_supabase_client(self) -> None:
        """Initialize Supabase client."""
        try:
            # Configure client options
            options = ClientOptions(
                auto_refresh_token=True,
                persist_session=True,
                headers={
                    "User-Agent": "Revisionary/1.0.0",
                }
            )
            
            # Create Supabase client
            self.supabase_client = create_client(
                settings.database.supabase_url,
                settings.database.supabase_key,
                options=options
            )
            
            logger.info("Supabase client initialized", url=settings.database.supabase_url)
            
        except Exception as e:
            logger.error("Failed to initialize Supabase client", error=str(e))
            raise
    
    async def _init_pg_pool(self) -> None:
        """Initialize PostgreSQL connection pool for direct queries."""
        try:
            self.pg_pool = await asyncpg.create_pool(
                settings.database.database_url,
                min_size=1,
                max_size=settings.database.connection_pool_size,
                command_timeout=settings.database.command_timeout,
                statement_cache_size=0,
                server_settings={
                    'application_name': 'revisionary_backend',
                    'jit': 'off'  # Disable JIT for faster query startup
                }
            )
            
            logger.info("PostgreSQL connection pool initialized")
            
        except Exception as e:
            logger.error("Failed to initialize PostgreSQL pool", error=str(e))
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Check database health."""
        health_status = {
            "supabase": False,
            "postgresql": False,
            "details": {}
        }
        
        # Check Supabase connection
        try:
            if self.supabase_client:
                # Simple query to test connection
                result = self.supabase_client.table('users').select('id').limit(1).execute()
                health_status["supabase"] = True
                health_status["details"]["supabase"] = "Connected"
        except Exception as e:
            health_status["details"]["supabase"] = f"Error: {str(e)}"
        
        # Check PostgreSQL pool
        try:
            if self.pg_pool:
                async with self.pg_pool.acquire() as conn:
                    result = await conn.fetchval('SELECT 1')
                    health_status["postgresql"] = result == 1
                    health_status["details"]["postgresql"] = "Connected"
        except Exception as e:
            health_status["details"]["postgresql"] = f"Error: {str(e)}"
        
        return health_status
    
    @asynccontextmanager
    async def get_connection(self):
        """Get a database connection from the pool."""
        if not self.pg_pool:
            raise RuntimeError("Database pool not initialized")
        
        async with self.pg_pool.acquire() as connection:
            yield connection
    
    async def execute_query(
        self,
        query: str,
        params: Optional[List[Any]] = None,
        fetch: str = "all"
    ) -> Union[List[Dict], Dict, Any, None]:
        """
        Execute a SQL query with parameters.
        
        Args:
            query: SQL query string
            params: Query parameters
            fetch: 'all', 'one', 'val', or 'none'
        
        Returns:
            Query result based on fetch type
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            async with self.get_connection() as conn:
                if fetch == "all":
                    result = await conn.fetch(query, *(params or []))
                    return [dict(row) for row in result]
                elif fetch == "one":
                    result = await conn.fetchrow(query, *(params or []))
                    return dict(result) if result else None
                elif fetch == "val":
                    return await conn.fetchval(query, *(params or []))
                elif fetch == "none":
                    await conn.execute(query, *(params or []))
                    return None
                else:
                    raise ValueError(f"Invalid fetch type: {fetch}")
        
        except Exception as e:
            duration = asyncio.get_event_loop().time() - start_time
            logger.error(
                "Database query failed",
                query=query[:100] + "..." if len(query) > 100 else query,
                duration=duration,
                error=str(e),
                exc_info=True
            )
            raise
        
        finally:
            duration = asyncio.get_event_loop().time() - start_time
            if duration > 1.0:  # Log slow queries
                logger.warning(
                    "Slow database query",
                    query=query[:100] + "..." if len(query) > 100 else query,
                    duration=duration
                )
    
    # Supabase table operations
    def table(self, table_name: str):
        """Get Supabase table client."""
        if not self.supabase_client:
            raise RuntimeError("Supabase client not initialized")
        return self.supabase_client.table(table_name)
    
    def auth(self):
        """Get Supabase auth client."""
        if not self.supabase_client:
            raise RuntimeError("Supabase client not initialized")
        return self.supabase_client.auth
    
    def storage(self):
        """Get Supabase storage client."""
        if not self.supabase_client:
            raise RuntimeError("Supabase client not initialized")
        return self.supabase_client.storage
    
    async def close(self) -> None:
        """Close database connections."""
        if self.pg_pool:
            await self.pg_pool.close()
            logger.info("PostgreSQL connection pool closed")
        
        # Supabase client doesn't need explicit closing
        self._initialized = False
        logger.info("Database service closed")


# Global database service instance
db_service = DatabaseService()


async def get_database() -> DatabaseService:
    """Get database service instance."""
    if not db_service._initialized:
        await db_service.initialize()
    return db_service