#!/usr/bin/env python3
"""
Server startup integration test.
Tests that the FastAPI app can initialize with all components.
"""

import sys
import os
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

def test_server_startup():
    """Test that the server can start with all routes registered."""
    print("Testing server startup integration...")
    
    try:
        # Mock the dependencies that would normally be loaded from environment
        os.environ.setdefault('SUPABASE_URL', 'http://mock-supabase')
        os.environ.setdefault('SUPABASE_KEY', 'mock-key')
        os.environ.setdefault('DATABASE_URL', 'postgresql://mock:mock@localhost/mock')
        os.environ.setdefault('REDIS_URL', 'redis://localhost:6379')
        os.environ.setdefault('USE_MOCK_DATA', 'true')
        os.environ.setdefault('USE_MOCK_AUTH', 'true')
        os.environ.setdefault('USE_MOCK_REDIS', 'true')
        
        # Try to import and create the FastAPI app
        from api.main import create_app
        
        app = create_app()
        
        # Check that the app has the expected routes
        routes = [route.path for route in app.routes]
        
        expected_persona_routes = [
            '/api/v1/personas/',
            '/api/v1/personas/{persona_id}',
            '/api/v1/personas/multi-feedback',
            '/api/v1/personas/templates'
        ]
        
        persona_routes_found = 0
        for expected_route in expected_persona_routes:
            # Check for route patterns (FastAPI uses path parameters)
            for actual_route in routes:
                if 'personas' in actual_route:
                    persona_routes_found += 1
                    break
        
        if persona_routes_found == 0:
            print("❌ No persona routes found in FastAPI app")
            return False
        
        print(f"✓ Found {persona_routes_found} persona-related routes")
        
        # Check that all expected routers are included
        expected_tags = ['authentication', 'documents', 'blocks', 'agents', 'personas', 'health']
        
        # Get unique tags from routes
        found_tags = set()
        for route in app.routes:
            if hasattr(route, 'tags') and route.tags:
                found_tags.update(route.tags)
        
        missing_tags = set(expected_tags) - found_tags
        if missing_tags:
            print(f"❌ Missing router tags: {missing_tags}")
            return False
        
        print("✓ All expected router tags found")
        
        # Test that the app metadata is correct
        if app.title != "Revisionary API":
            print("❌ Incorrect app title")
            return False
        
        print("✓ FastAPI app created successfully with all components")
        return True
        
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_websocket_server_creation():
    """Test that WebSocket server can be created."""
    print("Testing WebSocket server creation...")
    
    try:
        # Mock Redis dependency
        os.environ.setdefault('USE_MOCK_REDIS', 'true')
        
        from websocket.server import RealtimeServer
        
        # Create server instance (without initializing async resources)
        server = RealtimeServer()
        
        # Check that server has required attributes
        required_attrs = [
            'sio',
            'connected_users', 
            'user_sessions',
            'document_rooms',
            'cursor_positions'
        ]
        
        for attr in required_attrs:
            if not hasattr(server, attr):
                print(f"❌ WebSocket server missing attribute: {attr}")
                return False
        
        print("✓ WebSocket server created successfully")
        return True
        
    except Exception as e:
        print(f"❌ WebSocket server creation failed: {e}")
        return False

def run_startup_tests():
    """Run all startup integration tests."""
    print("=" * 60)
    print("SERVER STARTUP INTEGRATION TESTS") 
    print("=" * 60)
    
    tests = [
        test_server_startup,
        test_websocket_server_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n{test.__name__.replace('_', ' ').title()}:")
        try:
            if test():
                passed += 1
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test error: {e}")
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} startup tests passed")
    
    if passed == total:
        print("🎉 ALL STARTUP INTEGRATION TESTS PASSED!")
        print("✅ Server can start with all persona components integrated")
        return True
    else:
        print("❌ Some startup tests failed")
        return False

if __name__ == "__main__":
    success = run_startup_tests()
    sys.exit(0 if success else 1)