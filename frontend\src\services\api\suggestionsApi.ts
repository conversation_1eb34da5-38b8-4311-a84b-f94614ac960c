/**
 * Suggestions API Service
 * 
 * Handles all AI suggestion-related API calls including:
 * - Getting suggestions for blocks
 * - Creating suggestion requests
 * - Managing suggestion lifecycle (accept/reject)
 * - Real-time suggestion streaming
 */

import { baseApi, ApiResponse } from './baseApi';

export interface AISuggestion {
  id: string;
  block_id: string;
  agent_type: 'grammar' | 'style' | 'structure' | 'content' | 'consistency' | 'plagiarism';
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  original_text: string;
  suggested_text: string;
  explanation: string;
  confidence: number;
  position: {
    start: number;
    end: number;
    line?: number;
  };
  original_hash: string;
  status: 'pending' | 'accepted' | 'rejected' | 'stale' | 'expired';
  user_feedback?: 'helpful' | 'not_helpful' | 'incorrect';
  feedback_note?: string;
  tokens_used: number;
  model_used?: string;
  processing_time_ms?: number;
  created_at: string;
  resolved_at?: string;
  resolved_by?: string;
  expires_at?: string;
}

export interface SuggestionRequest {
  agents?: string[];
  intent?: 'improve' | 'fix' | 'enhance' | 'analyze';
  context_level?: 'word' | 'sentence' | 'paragraph' | 'document';
  options?: {
    style_preferences?: string[];
    formality?: 'casual' | 'formal' | 'academic';
    max_suggestions?: number;
    min_confidence?: number;
  };
}

export interface SuggestionResponse {
  job_id: string;
  status: 'processing' | 'completed' | 'failed';
  estimated_time?: number;
  stream_url?: string;
  suggestions?: AISuggestion[];
}

export interface SuggestionJob {
  id: string;
  block_id: string;
  status: 'processing' | 'completed' | 'failed';
  progress: number;
  suggestions_count: number;
  processing_time_ms?: number;
  error_message?: string;
  created_at: string;
  completed_at?: string;
}

class SuggestionsApiService {
  /**
   * Get suggestions for a specific block
   */
  async getBlockSuggestions(blockId: string, params?: {
    status?: AISuggestion['status'];
    agent_type?: AISuggestion['agent_type'];
    include_expired?: boolean;
  }): Promise<AISuggestion[]> {
    const response = await baseApi.get<AISuggestion[]>(`/blocks/${blockId}/suggestions`, { params });
    return response.data;
  }

  /**
   * Request AI suggestions for a block
   */
  async requestSuggestions(blockId: string, request: SuggestionRequest): Promise<SuggestionResponse> {
    const response = await baseApi.post<SuggestionResponse>(`/blocks/${blockId}/suggestions`, request);
    return response.data;
  }

  /**
   * Accept a suggestion
   */
  async acceptSuggestion(suggestionId: string): Promise<{ success: boolean }> {
    const response = await baseApi.post(`/suggestions/${suggestionId}/accept`);
    return response.data;
  }

  /**
   * Reject a suggestion
   */
  async rejectSuggestion(suggestionId: string, feedback?: {
    reason?: 'not_helpful' | 'incorrect' | 'other';
    note?: string;
  }): Promise<{ success: boolean }> {
    const response = await baseApi.post(`/suggestions/${suggestionId}/reject`, feedback);
    return response.data;
  }

  /**
   * Provide feedback on a suggestion
   */
  async provideFeedback(suggestionId: string, feedback: {
    feedback: 'helpful' | 'not_helpful' | 'incorrect';
    note?: string;
  }): Promise<{ success: boolean }> {
    const response = await baseApi.post(`/suggestions/${suggestionId}/feedback`, feedback);
    return response.data;
  }

  /**
   * Get suggestion job status
   */
  async getSuggestionJob(jobId: string): Promise<SuggestionJob> {
    const response = await baseApi.get<SuggestionJob>(`/suggestions/jobs/${jobId}`);
    return response.data;
  }

  /**
   * Cancel a suggestion job
   */
  async cancelSuggestionJob(jobId: string): Promise<{ success: boolean }> {
    const response = await baseApi.delete(`/suggestions/jobs/${jobId}`);
    return response.data;
  }

  /**
   * Get suggestions by document
   */
  async getDocumentSuggestions(documentId: string, params?: {
    status?: AISuggestion['status'];
    agent_type?: AISuggestion['agent_type'];
    limit?: number;
  }): Promise<AISuggestion[]> {
    const response = await baseApi.get<AISuggestion[]>(`/documents/${documentId}/suggestions`, { params });
    return response.data;
  }

  /**
   * Batch accept suggestions
   */
  async batchAcceptSuggestions(suggestionIds: string[]): Promise<{
    accepted: number;
    failed: number;
    errors?: string[];
  }> {
    const response = await baseApi.post('/suggestions/batch/accept', { suggestion_ids: suggestionIds });
    return response.data;
  }

  /**
   * Batch reject suggestions
   */
  async batchRejectSuggestions(suggestionIds: string[], reason?: string): Promise<{
    rejected: number;
    failed: number;
    errors?: string[];
  }> {
    const response = await baseApi.post('/suggestions/batch/reject', { 
      suggestion_ids: suggestionIds,
      reason 
    });
    return response.data;
  }
}

// Export singleton instance
export const suggestionsApi = new SuggestionsApiService();
export default suggestionsApi;