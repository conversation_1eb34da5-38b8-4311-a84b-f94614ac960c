-- Revisionary Analytics and Usage Tables
-- Part 2 of the schema migration

-- 10. User Writing Statistics
CREATE TABLE user_writing_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    document_type document_type_enum NOT NULL,
    date DATE NOT NULL,
    average_score DECIMAL(5,2) NOT NULL,
    average_grammar DECIMAL(5,2) NOT NULL,
    average_style DECIMAL(5,2) NOT NULL,
    average_structure DECIMAL(5,2) NOT NULL,
    average_content DECIMAL(5,2) NOT NULL,
    documents_scored INTEGER NOT NULL,
    total_words INTEGER NOT NULL,
    improvement_trend DECIMAL(5,2), -- Change from previous period
    streak_days INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT user_writing_stats_scores_range CHECK (
        average_score >= 0 AND average_score <= 100 AND
        average_grammar >= 0 AND average_grammar <= 25 AND
        average_style >= 0 AND average_style <= 25 AND
        average_structure >= 0 AND average_structure <= 25 AND
        average_content >= 0 AND average_content <= 25
    ),
    CONSTRAINT user_writing_stats_counts_positive CHECK (
        documents_scored >= 0 AND total_words >= 0 AND streak_days >= 0
    ),
    
    UNIQUE(user_id, document_type, date)
);

-- 11. Writing Achievements
CREATE TABLE writing_achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    achievement_type achievement_type_enum NOT NULL,
    achievement_name VARCHAR(100) NOT NULL,
    achievement_level achievement_level_enum NOT NULL,
    description TEXT,
    score_requirement DECIMAL(5,2),
    document_count_requirement INTEGER,
    streak_requirement INTEGER,
    earned_at TIMESTAMPTZ DEFAULT NOW(),
    document_id UUID REFERENCES documents(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    
    -- Constraints
    CONSTRAINT writing_achievements_metadata_valid CHECK (jsonb_typeof(metadata) = 'object'),
    
    UNIQUE(user_id, achievement_type, achievement_level)
);

-- 12. Daily Writing Challenges
CREATE TABLE writing_challenges (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    challenge_type challenge_type_enum NOT NULL,
    challenge_title VARCHAR(200) NOT NULL,
    challenge_description TEXT NOT NULL,
    target_metric VARCHAR(50) NOT NULL,
    target_value DECIMAL(10,2) NOT NULL,
    difficulty challenge_difficulty_enum NOT NULL,
    document_type_filter document_type_enum,
    assigned_date DATE NOT NULL,
    completed_at TIMESTAMPTZ,
    completion_value DECIMAL(10,2),
    document_id UUID REFERENCES documents(id) ON DELETE SET NULL,
    reward_points INTEGER DEFAULT 0,
    
    -- Constraints
    CONSTRAINT writing_challenges_target_value_positive CHECK (target_value > 0),
    CONSTRAINT writing_challenges_completion_value_positive CHECK (
        completion_value IS NULL OR completion_value >= 0
    ),
    CONSTRAINT writing_challenges_reward_points_positive CHECK (reward_points >= 0),
    
    UNIQUE(user_id, assigned_date)
);

-- 13. Scoring Algorithm Metadata
CREATE TABLE scoring_algorithms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    version VARCHAR(10) NOT NULL UNIQUE,
    algorithm_name VARCHAR(100) NOT NULL,
    description TEXT,
    grammar_weights JSONB NOT NULL,
    style_weights JSONB NOT NULL,
    structure_weights JSONB NOT NULL,
    content_weights JSONB NOT NULL,
    document_type_adjustments JSONB NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT scoring_algorithms_weights_valid CHECK (
        jsonb_typeof(grammar_weights) = 'object' AND
        jsonb_typeof(style_weights) = 'object' AND
        jsonb_typeof(structure_weights) = 'object' AND
        jsonb_typeof(content_weights) = 'object' AND
        jsonb_typeof(document_type_adjustments) = 'object'
    )
);

-- 14. Usage Tracking Table
CREATE TABLE usage_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    document_id UUID REFERENCES documents(id) ON DELETE SET NULL,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB DEFAULT '{}'::jsonb,
    tokens_used INTEGER DEFAULT 0,
    model_used VARCHAR(50),
    processing_time_ms INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT usage_events_tokens_positive CHECK (tokens_used >= 0),
    CONSTRAINT usage_events_processing_time_positive CHECK (processing_time_ms >= 0),
    CONSTRAINT usage_events_event_data_valid CHECK (jsonb_typeof(event_data) = 'object')
);

-- 15. Token Usage Aggregations
CREATE TABLE token_usage_daily (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    tokens_total INTEGER DEFAULT 0,
    tokens_by_model JSONB DEFAULT '{}'::jsonb,
    tokens_by_agent JSONB DEFAULT '{}'::jsonb,
    operations_count INTEGER DEFAULT 0,
    documents_edited INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT token_usage_daily_tokens_positive CHECK (tokens_total >= 0),
    CONSTRAINT token_usage_daily_operations_positive CHECK (operations_count >= 0),
    CONSTRAINT token_usage_daily_documents_positive CHECK (documents_edited >= 0),
    
    UNIQUE(user_id, date)
);

-- Insert default scoring algorithm
INSERT INTO scoring_algorithms (
    version, algorithm_name, description, is_active,
    grammar_weights, style_weights, structure_weights, content_weights,
    document_type_adjustments
) VALUES (
    '1.0', 'Base Scoring Algorithm', 'Initial scoring algorithm with balanced weights', TRUE,
    '{"spelling": 0.3, "grammar": 0.4, "punctuation": 0.3}',
    '{"readability": 0.25, "vocabulary": 0.25, "sentence_variety": 0.25, "conciseness": 0.25}',
    '{"organization": 0.4, "transitions": 0.3, "coherence": 0.3}',
    '{"depth": 0.3, "evidence": 0.3, "consistency": 0.2, "originality": 0.2}',
    '{
        "creative": {"grammar": 0.15, "style": 0.35, "structure": 0.25, "content": 0.25},
        "academic": {"grammar": 0.20, "style": 0.20, "structure": 0.30, "content": 0.30},
        "professional": {"grammar": 0.25, "style": 0.30, "structure": 0.25, "content": 0.20},
        "technical": {"grammar": 0.30, "style": 0.25, "structure": 0.25, "content": 0.20},
        "general": {"grammar": 0.25, "style": 0.25, "structure": 0.25, "content": 0.25}
    }'
);