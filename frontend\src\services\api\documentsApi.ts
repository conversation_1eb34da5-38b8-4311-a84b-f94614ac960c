/**
 * Documents API Service
 * 
 * Handles all document-related API calls including:
 * - Document CRUD operations
 * - Document sharing and collaboration
 * - Document templates and imports
 * - Version control operations
 */

import { baseApi, ApiResponse } from './baseApi';

export interface Document {
  id: string;
  owner_id: string;
  title: string;
  type: 'creative' | 'academic' | 'business' | 'technical' | 'blog' | 'email';
  status: 'draft' | 'in_progress' | 'review' | 'completed' | 'archived';
  word_count: number;
  character_count: number;
  reading_time: number;
  version: number;
  is_public: boolean;
  tags: string[];
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
  last_edited_at: string;
  deleted_at?: string;
}

export interface DocumentCreate {
  title: string;
  type: Document['type'];
  is_public?: boolean;
  tags?: string[];
  template_id?: string;
  initial_content?: string;
}

export interface DocumentUpdate {
  title?: string;
  type?: Document['type'];
  status?: Document['status'];
  is_public?: boolean;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface DocumentListResponse {
  success: boolean;
  data: Document[];
  meta: {
    total: number;
    limit: number;
    offset: number;
    has_more: boolean;
  };
}

export interface DocumentSearchResult {
  document: Document;
  matches: Array<{
    block_id: string;
    content: string;
    score: number;
  }>;
}

export interface DocumentTemplate {
  id: string;
  name: string;
  description: string;
  type: Document['type'];
  preview_image?: string;
  is_premium: boolean;
  structure: Array<{
    type: 'heading' | 'paragraph' | 'list' | 'section';
    content: string;
    placeholder?: string;
  }>;
}

export interface DocumentStats {
  total_documents: number;
  total_words: number;
  avg_score: number;
  documents_by_type: Record<Document['type'], number>;
  documents_by_status: Record<Document['status'], number>;
  writing_streak: number;
}

export interface DocumentVersion {
  id: string;
  document_id: string;
  version_number: number;
  title: string;
  created_at: string;
  created_by: string;
  change_summary: string;
  word_count: number;
}

export interface DocumentCollaboration {
  id: string;
  document_id: string;
  user_id: string;
  user_email: string;
  user_name: string;
  role: 'viewer' | 'commenter' | 'editor' | 'owner';
  status: 'pending' | 'active' | 'declined';
  invited_at: string;
  joined_at?: string;
}

class DocumentsApiService {
  /**
   * Get list of documents with pagination and filtering
   */
  async getDocuments(params?: {
    page?: number;
    limit?: number;
    type?: Document['type'];
    status?: Document['status'];
    tags?: string[];
    search?: string;
    sort?: 'title' | 'created_at' | 'updated_at' | 'word_count';
    order?: 'asc' | 'desc';
  }): Promise<DocumentListResponse> {
    const response = await baseApi.get<DocumentListResponse>('/documents', { params });
    return response.data;
  }

  /**
   * Get a specific document by ID
   */
  async getDocument(id: string): Promise<Document> {
    const response = await baseApi.get<Document>(`/documents/${id}`);
    return response.data;
  }

  /**
   * Create a new document
   */
  async createDocument(document: DocumentCreate): Promise<Document> {
    const response = await baseApi.post<Document>('/documents', document);
    return response.data;
  }

  /**
   * Update an existing document
   */
  async updateDocument(id: string, updates: DocumentUpdate): Promise<Document> {
    const response = await baseApi.put<Document>(`/documents/${id}`, updates);
    return response.data;
  }

  /**
   * Delete a document (soft delete)
   */
  async deleteDocument(id: string): Promise<{ success: boolean }> {
    const response = await baseApi.delete(`/documents/${id}`);
    return response.data;
  }

  /**
   * Permanently delete a document
   */
  async permanentlyDeleteDocument(id: string): Promise<{ success: boolean }> {
    const response = await baseApi.delete(`/documents/${id}/permanent`);
    return response.data;
  }

  /**
   * Restore a deleted document
   */
  async restoreDocument(id: string): Promise<Document> {
    const response = await baseApi.post<Document>(`/documents/${id}/restore`);
    return response.data;
  }

  /**
   * Duplicate a document
   */
  async duplicateDocument(id: string, title?: string): Promise<Document> {
    const response = await baseApi.post<Document>(`/documents/${id}/duplicate`, { title });
    return response.data;
  }

  /**
   * Search documents
   */
  async searchDocuments(query: string, filters?: {
    type?: Document['type'];
    tags?: string[];
    date_range?: { start: string; end: string };
  }): Promise<DocumentSearchResult[]> {
    const response = await baseApi.post<DocumentSearchResult[]>('/documents/search', {
      query,
      filters,
    });
    return response.data;
  }

  /**
   * Get document templates
   */
  async getTemplates(type?: Document['type']): Promise<DocumentTemplate[]> {
    const response = await baseApi.get<DocumentTemplate[]>('/documents/templates', {
      params: { type },
    });
    return response.data;
  }

  /**
   * Create document from template
   */
  async createFromTemplate(templateId: string, title: string): Promise<Document> {
    const response = await baseApi.post<Document>('/documents/from-template', {
      template_id: templateId,
      title,
    });
    return response.data;
  }

  /**
   * Get document statistics
   */
  async getDocumentStats(): Promise<DocumentStats> {
    const response = await baseApi.get<DocumentStats>('/documents/stats');
    return response.data;
  }

  /**
   * Export document
   */
  async exportDocument(id: string, format: 'pdf' | 'docx' | 'html' | 'markdown' | 'txt'): Promise<{
    download_url: string;
    expires_at: string;
  }> {
    const response = await baseApi.post(`/documents/${id}/export`, { format });
    return response.data;
  }

  /**
   * Import document
   */
  async importDocument(file: File, options?: {
    title?: string;
    type?: Document['type'];
    preserve_formatting?: boolean;
  }): Promise<Document> {
    const formData = new FormData();
    formData.append('file', file);
    if (options) {
      Object.entries(options).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
    }

    // Use baseApi upload method for consistent response handling
    const response = await baseApi.upload<Document>('/documents/import', file);
    return response.data;
  }

  // Version Control Operations
  /**
   * Get document versions
   */
  async getVersions(documentId: string): Promise<DocumentVersion[]> {
    const response = await baseApi.get<DocumentVersion[]>(`/documents/${documentId}/versions`);
    return response.data;
  }

  /**
   * Create new version
   */
  async createVersion(documentId: string, changeSummary: string): Promise<DocumentVersion> {
    const response = await baseApi.post<DocumentVersion>(`/documents/${documentId}/versions`, {
      change_summary: changeSummary,
    });
    return response.data;
  }

  /**
   * Restore to specific version
   */
  async restoreVersion(documentId: string, versionId: string): Promise<Document> {
    const response = await baseApi.post<Document>(`/documents/${documentId}/versions/${versionId}/restore`);
    return response.data;
  }

  // Collaboration Operations
  /**
   * Get document collaborators
   */
  async getCollaborators(documentId: string): Promise<DocumentCollaboration[]> {
    const response = await baseApi.get<DocumentCollaboration[]>(`/documents/${documentId}/collaborators`);
    return response.data;
  }

  /**
   * Invite collaborator
   */
  async inviteCollaborator(documentId: string, email: string, role: DocumentCollaboration['role']): Promise<DocumentCollaboration> {
    const response = await baseApi.post<DocumentCollaboration>(`/documents/${documentId}/collaborators`, {
      email,
      role,
    });
    return response.data;
  }

  /**
   * Update collaborator role
   */
  async updateCollaboratorRole(documentId: string, collaboratorId: string, role: DocumentCollaboration['role']): Promise<DocumentCollaboration> {
    const response = await baseApi.put<DocumentCollaboration>(`/documents/${documentId}/collaborators/${collaboratorId}`, {
      role,
    });
    return response.data;
  }

  /**
   * Remove collaborator
   */
  async removeCollaborator(documentId: string, collaboratorId: string): Promise<{ success: boolean }> {
    const response = await baseApi.delete(`/documents/${documentId}/collaborators/${collaboratorId}`);
    return response.data;
  }

  /**
   * Accept collaboration invitation
   */
  async acceptCollaboration(collaborationId: string): Promise<DocumentCollaboration> {
    const response = await baseApi.post<DocumentCollaboration>(`/collaborations/${collaborationId}/accept`);
    return response.data;
  }

  /**
   * Decline collaboration invitation
   */
  async declineCollaboration(collaborationId: string): Promise<{ success: boolean }> {
    const response = await baseApi.post(`/collaborations/${collaborationId}/decline`);
    return response.data;
  }
}

// Export singleton instance
export const documentsApi = new DocumentsApiService();
export default documentsApi;