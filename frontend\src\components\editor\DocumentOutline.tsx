import React, { useState, useMemo } from 'react';
import {
  DocumentTextIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  HashtagIcon,
  ListBulletIcon,
  ChatBubbleLeftIcon,
  CodeBracketIcon,
  PhotoIcon,
  TableCellsIcon,
  RectangleGroupIcon,
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

// Import block types from API
interface Block {
  id: string;
  document_id: string;
  parent_id?: string;
  type: 'paragraph' | 'heading' | 'list' | 'quote' | 'code' | 'image' | 'table' | 'section';
  position: number;
  content: string;
  metadata: {
    level?: number;
    list_type?: 'ordered' | 'unordered';
    language?: string;
    alt_text?: string;
    url?: string;
    styling?: Record<string, any>;
  };
  word_count: number;
  character_count: number;
  created_at: string;
  updated_at: string;
  version: number;
}

interface BlockStructure {
  block: Block;
  children: BlockStructure[];
}

interface OutlineItem {
  id: string;
  level: number;
  text: string;
  position: number;
  type: Block['type'];
  blockId: string;
  wordCount: number;
}

interface DocumentOutlineProps {
  blockStructure: BlockStructure[];
  onNavigate: (blockId: string) => void;
  isLoading?: boolean;
  error?: string | null;
}

const DocumentOutline: React.FC<DocumentOutlineProps> = ({ 
  blockStructure, 
  onNavigate, 
  isLoading = false, 
  error = null 
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['headings']));

  const outline = useMemo(() => {
    if (!blockStructure || blockStructure.length === 0) {
      return [];
    }

    const items: OutlineItem[] = [];

    const processBlockStructure = (structures: BlockStructure[], parentLevel = 0) => {
      structures.forEach(({ block, children }) => {
        // Determine the display text based on block type
        let displayText = '';
        let level = parentLevel;

        switch (block.type) {
          case 'heading':
            level = block.metadata.level || 1;
            displayText = block.content.replace(/^#+\s*/, '').trim();
            break;
          case 'list':
            displayText = block.content.replace(/^[-*+]\s*/, '').trim();
            level = parentLevel + 1;
            break;
          case 'quote':
            displayText = block.content.replace(/^>\s*/, '').trim();
            break;
          case 'code':
            displayText = `Code block (${block.metadata.language || 'text'})`;
            break;
          case 'image':
            displayText = block.metadata.alt_text || 'Image';
            break;
          case 'table':
            displayText = 'Table';
            break;
          case 'section':
            displayText = `Section: ${block.content.substring(0, 50)}...`;
            break;
          case 'paragraph':
            // Only show significant paragraphs
            if (block.content.length > 20) {
              displayText = block.content.length > 60 
                ? `${block.content.substring(0, 60)}...` 
                : block.content;
            }
            break;
          default:
            displayText = block.content.substring(0, 60) + (block.content.length > 60 ? '...' : '');
        }

        if (displayText) {
          items.push({
            id: `block-${block.id}`,
            level,
            text: displayText,
            position: block.position,
            type: block.type,
            blockId: block.id,
            wordCount: block.word_count,
          });
        }

        // Process children recursively
        if (children && children.length > 0) {
          processBlockStructure(children, level + 1);
        }
      });
    };

    processBlockStructure(blockStructure);
    return items.sort((a, b) => a.position - b.position);
  }, [blockStructure]);

  const groupedOutline = useMemo(() => {
    const headings = outline.filter(item => item.type === 'heading');
    const lists = outline.filter(item => item.type === 'list');
    const quotes = outline.filter(item => item.type === 'quote');
    const paragraphs = outline.filter(item => item.type === 'paragraph');
    const code = outline.filter(item => item.type === 'code');
    const images = outline.filter(item => item.type === 'image');
    const tables = outline.filter(item => item.type === 'table');
    const sections = outline.filter(item => item.type === 'section');

    return {
      headings,
      lists,
      quotes,
      paragraphs,
      code,
      images,
      tables,
      sections,
    };
  }, [outline]);

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const getIcon = (type: string) => {
    switch (type) {
      case 'heading':
        return HashtagIcon;
      case 'list':
        return ListBulletIcon;
      case 'quote':
        return ChatBubbleLeftIcon;
      case 'code':
        return CodeBracketIcon;
      case 'image':
        return PhotoIcon;
      case 'table':
        return TableCellsIcon;
      case 'section':
        return RectangleGroupIcon;
      case 'paragraph':
      default:
        return DocumentTextIcon;
    }
  };

  const sections = [
    { key: 'headings', label: 'Headings', items: groupedOutline.headings, color: 'text-purple-600' },
    { key: 'sections', label: 'Sections', items: groupedOutline.sections, color: 'text-orange-600' },
    { key: 'lists', label: 'Lists', items: groupedOutline.lists, color: 'text-blue-600' },
    { key: 'quotes', label: 'Quotes', items: groupedOutline.quotes, color: 'text-green-600' },
    { key: 'code', label: 'Code Blocks', items: groupedOutline.code, color: 'text-cyan-600' },
    { key: 'tables', label: 'Tables', items: groupedOutline.tables, color: 'text-indigo-600' },
    { key: 'images', label: 'Images', items: groupedOutline.images, color: 'text-pink-600' },
    { key: 'paragraphs', label: 'Key Paragraphs', items: groupedOutline.paragraphs, color: 'text-slate-600' },
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2 mb-4">
        <DocumentTextIcon className="w-5 h-5 text-purple-600" />
        <h3 className="text-sm font-semibold text-slate-900">Document Outline</h3>
        <span className="text-xs text-slate-500 bg-slate-100 px-2 py-0.5 rounded-full">
          {outline.length} items
        </span>
      </div>

      {isLoading ? (
        <div className="text-center py-8">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-8 h-8 border-2 border-purple-600 border-t-transparent rounded-full mx-auto mb-3"
          />
          <p className="text-sm text-slate-500">Loading document structure...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <DocumentTextIcon className="w-12 h-12 text-red-300 mx-auto mb-3" />
          <p className="text-sm text-red-600 mb-2">Failed to load outline</p>
          <p className="text-xs text-slate-400">{error}</p>
        </div>
      ) : outline.length === 0 ? (
        <div className="text-center py-8">
          <DocumentTextIcon className="w-12 h-12 text-slate-300 mx-auto mb-3" />
          <p className="text-sm text-slate-500 mb-2">No outline yet</p>
          <p className="text-xs text-slate-400">
            Start writing headings, lists, or paragraphs to see the document structure.
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {sections.map((section) => {
            if (section.items.length === 0) return null;
            
            const isExpanded = expandedSections.has(section.key);
            const IconComponent = getIcon(section.key.slice(0, -1));

            return (
              <div key={section.key} className="border border-slate-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleSection(section.key)}
                  className="w-full flex items-center justify-between p-3 hover:bg-slate-50 transition-colors"
                >
                  <div className="flex items-center space-x-2">
                    <IconComponent className={`w-4 h-4 ${section.color}`} />
                    <span className="text-sm font-medium text-slate-700">{section.label}</span>
                    <span className="text-xs text-slate-500 bg-slate-100 px-1.5 py-0.5 rounded">
                      {section.items.length}
                    </span>
                  </div>
                  <motion.div
                    animate={{ rotate: isExpanded ? 90 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronRightIcon className="w-4 h-4 text-slate-400" />
                  </motion.div>
                </button>

                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="border-t border-slate-200"
                    >
                      <div className="p-2 space-y-1">
                        {section.items.map((item, index) => (
                          <motion.button
                            key={item.id}
                            onClick={() => onNavigate(item.blockId)}
                            className="w-full text-left p-2 hover:bg-slate-50 rounded-lg transition-colors group"
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.05 }}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1 min-w-0">
                                <p 
                                  className={`text-sm text-slate-700 group-hover:text-purple-700 transition-colors truncate ${
                                    item.type === 'heading' ? `ml-${Math.min(item.level * 2, 8)}` : ''
                                  }`}
                                  style={{
                                    marginLeft: item.type === 'heading' ? `${Math.min(item.level * 8, 32)}px` : '0',
                                    fontSize: item.type === 'heading' ? `${Math.max(14 - item.level, 12)}px` : '14px',
                                    fontWeight: item.type === 'heading' ? '600' : '400',
                                  }}
                                >
                                  {item.text}
                                </p>
                              </div>
                              <div className="text-xs text-slate-400 ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <div>Pos {item.position}</div>
                                {item.wordCount > 0 && (
                                  <div>{item.wordCount} words</div>
                                )}
                              </div>
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default DocumentOutline;