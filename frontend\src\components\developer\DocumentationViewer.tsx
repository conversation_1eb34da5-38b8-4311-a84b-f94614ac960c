import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import { 
  DocumentTextIcon, 
  MagnifyingGlassIcon,
  BookOpenIcon,
  CodeBracketIcon,
  CommandLineIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import { developerApi, DocumentationFile } from '@/services/api/developerApi';

interface DocWithUI extends DocumentationFile {
  icon: React.ComponentType<any>;
  color: string;
}

const getIconAndColor = (category: string): { icon: React.ComponentType<any>, color: string } => {
  switch (category) {
    case 'API':
      return { icon: CodeBracketIcon, color: 'from-blue-500 to-indigo-500' };
    case 'Backend':
      return { icon: CommandLineIcon, color: 'from-green-500 to-emerald-500' };
    case 'Frontend':
      return { icon: DocumentTextIcon, color: 'from-orange-500 to-red-500' };
    case 'Architecture':
      return { icon: CogIcon, color: 'from-purple-500 to-violet-500' };
    case 'Deployment':
      return { icon: CogIcon, color: 'from-red-500 to-pink-500' };
    default:
      return { icon: BookOpenIcon, color: 'from-slate-500 to-gray-500' };
  }
};

const DocumentationViewer: React.FC = () => {
  const [selectedDoc, setSelectedDoc] = useState<string>('');
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [documentationFiles, setDocumentationFiles] = useState<DocWithUI[]>([]);
  const [docsLoading, setDocsLoading] = useState(true);

  // Load available documentation files
  const loadDocumentationList = async () => {
    try {
      setDocsLoading(true);
      const response = await developerApi.getDocumentationList();
      console.log('Documentation API response:', response);
      
      // Handle both direct array and wrapped response
      const docs = Array.isArray(response) ? response : response?.data || [];
      
      const docsWithUI = docs.map(doc => ({
        ...doc,
        ...getIconAndColor(doc.category)
      }));
      setDocumentationFiles(docsWithUI);
      
      // Set first doc as default if none selected
      if (docsWithUI.length > 0 && !selectedDoc) {
        setSelectedDoc(docsWithUI[0].filename);
      }
    } catch (err) {
      console.error('Failed to load documentation list:', err);
      setError('Failed to load documentation list');
    } finally {
      setDocsLoading(false);
    }
  };

  const loadDocumentation = async (filename: string) => {
    if (!filename) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const docContent = await developerApi.getDocumentation(filename);
      setContent(docContent.content);
    } catch (err) {
      setError('Failed to load documentation');
      console.error('Documentation loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDocumentationList();
  }, []);

  useEffect(() => {
    if (selectedDoc) {
      loadDocumentation(selectedDoc);
    }
  }, [selectedDoc]);

  const filteredDocs = documentationFiles.filter(doc =>
    doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    doc.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    doc.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 h-full">
      {/* Sidebar */}
      <div className="lg:col-span-1">
        {/* Search */}
        <div className="relative mb-6">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
          <input
            type="text"
            placeholder="Search documentation..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-slate-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          />
        </div>

        {/* Categories */}
        <div className="space-y-2">
          <h3 className="text-sm font-semibold text-slate-500 uppercase tracking-wider mb-4">
            Documentation
          </h3>
          <div className="max-h-[70vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-slate-100">
          {docsLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 mx-auto"></div>
              <p className="mt-2 text-sm text-slate-600">Loading documentation...</p>
            </div>
          ) : filteredDocs.length === 0 ? (
            <div className="text-center py-8">
              <BookOpenIcon className="w-8 h-8 text-slate-400 mx-auto mb-2" />
              <p className="text-sm text-slate-600">No documentation found</p>
            </div>
          ) : (
            filteredDocs.map((doc) => {
              const Icon = doc.icon;
              const isSelected = selectedDoc === doc.filename;
              return (
                <button
                  key={doc.filename}
                  onClick={() => setSelectedDoc(doc.filename)}
                  className={`w-full flex items-start p-4 rounded-xl transition-all duration-200 text-left ${
                    isSelected
                      ? 'bg-gradient-to-r from-purple-50 to-pink-50 border-2 border-purple-200 shadow-md'
                      : 'bg-white border border-slate-200 hover:border-slate-300 hover:shadow-sm'
                  }`}
                >
                  <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${doc.color} flex items-center justify-center mr-3 flex-shrink-0`}>
                    <Icon className="w-5 h-5 text-white" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <h4 className={`font-semibold text-sm mb-1 ${isSelected ? 'text-purple-900' : 'text-slate-900'}`}>
                      {doc.title}
                    </h4>
                    <p className={`text-xs leading-relaxed ${isSelected ? 'text-purple-700' : 'text-slate-600'}`}>
                      {doc.description}
                    </p>
                    <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full mt-2 ${
                      isSelected ? 'bg-purple-100 text-purple-700' : 'bg-slate-100 text-slate-600'
                    }`}>
                      {doc.category}
                    </span>
                  </div>
                </button>
              );
            })
          )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="lg:col-span-3">
        <div className="bg-white rounded-xl border border-slate-200 shadow-sm h-full">
          {loading ? (
            <div className="flex items-center justify-center h-96">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
                <p className="mt-4 text-slate-600">Loading documentation...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-96">
              <div className="text-center">
                <BookOpenIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
                <p className="text-red-600 mb-4">{error}</p>
                <button
                  onClick={() => loadDocumentation(selectedDoc)}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : (
            <div className="h-full overflow-auto max-h-[80vh] scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-slate-100">
              <div className="prose prose-slate max-w-none p-8">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeHighlight]}
                  components={{
                    // Custom styling for markdown elements
                    h1: ({ children }) => (
                      <h1 className="text-3xl font-bold text-slate-900 mb-6 pb-4 border-b border-slate-200">
                        {children}
                      </h1>
                    ),
                    h2: ({ children }) => (
                      <h2 className="text-2xl font-semibold text-slate-800 mt-8 mb-4">
                        {children}
                      </h2>
                    ),
                    h3: ({ children }) => (
                      <h3 className="text-xl font-semibold text-slate-800 mt-6 mb-3">
                        {children}
                      </h3>
                    ),
                    code: ({ inline, className, children, ...props }) => {
                      if (inline) {
                        return (
                          <code className="px-1.5 py-0.5 bg-slate-100 text-slate-800 rounded text-sm font-mono" {...props}>
                            {children}
                          </code>
                        );
                      }
                      return (
                        <code className={`${className} block`} {...props}>
                          {children}
                        </code>
                      );
                    },
                    pre: ({ children }) => (
                      <pre className="bg-slate-900 text-slate-100 p-4 rounded-lg overflow-x-auto my-4">
                        {children}
                      </pre>
                    ),
                    blockquote: ({ children }) => (
                      <blockquote className="border-l-4 border-purple-500 pl-6 py-2 bg-purple-50 text-purple-900 my-4">
                        {children}
                      </blockquote>
                    ),
                    table: ({ children }) => (
                      <div className="overflow-x-auto my-4">
                        <table className="min-w-full divide-y divide-slate-200">
                          {children}
                        </table>
                      </div>
                    ),
                    th: ({ children }) => (
                      <th className="px-6 py-3 bg-slate-50 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                        {children}
                      </th>
                    ),
                    td: ({ children }) => (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900 border-b border-slate-200">
                        {children}
                      </td>
                    ),
                  }}
                >
                  {content}
                </ReactMarkdown>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentationViewer;