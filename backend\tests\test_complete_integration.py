#!/usr/bin/env python3
"""
Complete Integration Test Suite
Runs all integration tests to verify the entire Revisionary backend system.
"""

import sys
import os
import subprocess
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

def run_test_suite(test_file, description):
    """Run a test suite and return results."""
    print(f"\n{'='*80}")
    print(f"RUNNING: {description}")
    print(f"{'='*80}")
    
    try:
        result = subprocess.run([
            sys.executable, str(backend_path / "tests" / test_file)
        ], capture_output=True, text=True, cwd=backend_path)
        
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        return result.returncode == 0
    
    except Exception as e:
        print(f"❌ Error running {test_file}: {e}")
        return False

def run_complete_integration_tests():
    """Run the complete integration test suite."""
    print("🚀 REVISIONARY BACKEND - COMPLETE INTEGRATION TEST SUITE")
    print("=" * 80)
    print("Testing all backend systems and their integration...")
    print()
    
    # Define all test suites
    test_suites = [
        ("test_persona_integration.py", "Persona System Integration"),
        ("test_websocket_health_integration.py", "WebSocket + Health Worker Integration"),
        ("test_llm_service_integration.py", "LLM Service Integration"),
        ("test_queue_worker_scaling.py", "Queue Management & Worker Auto-Scaling")
    ]
    
    results = {}
    passed_suites = 0
    total_suites = len(test_suites)
    
    # Run each test suite
    for test_file, description in test_suites:
        success = run_test_suite(test_file, description)
        results[description] = success
        if success:
            passed_suites += 1
    
    # Print final summary
    print("\n" + "="*80)
    print("🎯 FINAL INTEGRATION TEST RESULTS")
    print("="*80)
    
    for description, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status} {description}")
    
    print(f"\nOVERALL: {passed_suites}/{total_suites} test suites passed")
    
    if passed_suites == total_suites:
        print("\n🎉 ALL INTEGRATION TESTS PASSED!")
        print("✅ Revisionary backend system is fully integrated and ready")
        print("\n📋 IMPLEMENTATION SUMMARY:")
        print("   ✓ Phase 5: Persona System - COMPLETED")
        print("   ✓ Multi-reader feedback with cross-audience analysis")
        print("   ✓ WebSocket real-time persona feedback")
        print("   ✓ LLM service integration with all workers")
        print("   ✓ Queue management and auto-scaling")
        print("   ✓ Redis pub/sub for real-time updates")
        print("   ✓ Comprehensive error handling and monitoring")
        print("\n🚀 READY FOR PRODUCTION DEPLOYMENT!")
        return True
    else:
        print(f"\n❌ {total_suites - passed_suites} test suite(s) failed")
        print("🔧 Review failed tests and fix issues before deployment")
        return False

def test_environment_setup():
    """Test that environment is properly configured."""
    print("Testing environment setup...")
    
    # Check that all required files exist
    required_files = [
        "api/personas.py",
        "workers/persona_worker.py", 
        "websocket/server.py",
        "core/llm_service.py",
        "core/queue_manager.py",
        ".env.example"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (backend_path / file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    print("✅ All required files present")
    
    # Check .env.example has new variables
    env_example_path = backend_path / ".env.example"
    with open(env_example_path, 'r') as f:
        env_content = f.read()
    
    required_env_vars = [
        "MAX_WORKERS_PERSONA",
        "PERSONA_MAX_PER_REQUEST",
        "WORKER_SCALE_UP_THRESHOLD"
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if var not in env_content:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        return False
    
    print("✅ Environment configuration updated")
    return True

if __name__ == "__main__":
    print("🔧 Checking environment setup...")
    env_ok = test_environment_setup()
    
    if not env_ok:
        print("❌ Environment setup failed")
        sys.exit(1)
    
    print("✅ Environment setup passed\n")
    
    # Run complete integration tests
    success = run_complete_integration_tests()
    sys.exit(0 if success else 1)