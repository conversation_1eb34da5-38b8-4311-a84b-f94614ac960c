# Revisionary - Deployment Guide

## 1. Overview

This guide covers the complete deployment process for <PERSON>ision<PERSON>, from development setup to production deployment. The system is designed to be cloud-native with Infrastructure as Code (IaC) principles.

## 2. Prerequisites

### 2.1 Required Accounts & Services
- **Google Cloud Platform**: Main infrastructure provider
- **Firebase**: Authentication and hosting
- **Supabase**: PostgreSQL database with real-time features
- **GitHub**: Source code management and CI/CD
- **Sentry**: Error monitoring and performance tracking
- **OpenAI**: AI model access
- **Google AI**: Gemini models

### 2.2 Required Tools
```bash
# Core tools
curl -fsSL https://get.docker.com | sh  # Docker
brew install terraform                   # Terraform
brew install kubectl                     # Kubernetes CLI
brew install helm                        # Helm package manager

# Google Cloud SDK
curl https://sdk.cloud.google.com | bash
gcloud auth login
gcloud config set project YOUR_PROJECT_ID

# Node.js and Python
nvm install 18
pyenv install 3.11.0
```

### 2.3 Environment Variables
Create a `.env.template` file:
```bash
# Firebase Configuration
FIREBASE_PROJECT_ID=revisionary-prod
FIREBASE_PRIVATE_KEY_ID=
FIREBASE_PRIVATE_KEY=
FIREBASE_CLIENT_EMAIL=
FIREBASE_CLIENT_ID=
FIREBASE_CLIENT_CERT_URL=

# Database
DATABASE_URL=********************************/revisionary
REDIS_URL=redis://:password@host:6379/0

# AI Services (OpenAI & Google only)
OPENAI_API_KEY=sk-
GOOGLE_AI_API_KEY=

# External Services
SENTRY_DSN=https://...@sentry.io/...
# STRIPE_SECRET_KEY=sk_...         # Billing integration (post-launch)
# STRIPE_WEBHOOK_SECRET=whsec_...   # Billing integration (post-launch)

# Application
ENVIRONMENT=production
API_URL=https://api.revisionary.app
WEB_URL=https://revisionary.app
SECRET_KEY=your-256-bit-secret

# Infrastructure
GCP_PROJECT=revisionary-prod
GCP_REGION=us-central1
DOMAIN=revisionary.app
```

## 3. Infrastructure Setup

### 3.1 Deployment Strategy Decision

**Primary Approach: Budget-Optimized (≤$150/month)**
- Single e2-standard-4 VM with local Redis
- Cloud Run workers (min-instances=0)
- Suitable for: 0-10k users, development, small teams

**Enterprise Upgrade Path (>$500/month)**
- GKE cluster with Redis Sentinel
- Always-on worker instances
- Suitable for: 10k+ users, enterprise customers

This guide covers the **budget-optimized approach first**, with enterprise upgrade notes.

### 3.2 Terraform Configuration (Budget-Optimized)

**main.tf**
```hcl
terraform {
  required_version = ">= 1.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 3.0"
    }
  }
  
  backend "gcs" {
    bucket = "revisionary-terraform-state"
    prefix = "production"
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

# Variables
variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "region" {
  description = "GCP Region"
  type        = string
  default     = "us-central1"
}

variable "domain" {
  description = "Primary domain"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}
```

**compute.tf**
```hcl
# Single VM for budget-optimized deployment
resource "google_compute_instance" "main" {
  name         = "revisionary-main"
  machine_type = "e2-standard-4"
  zone         = "${var.region}-a"
  
  boot_disk {
    initialize_params {
      image = "ubuntu-os-cloud/ubuntu-2204-lts"
      size  = 30
      type  = "pd-ssd"
    }
  }
  
  network_interface {
    network = "default"
    access_config {
      # Ephemeral public IP
    }
  }
  
  # Startup script to install dependencies
  metadata_startup_script = file("${path.module}/startup-script.sh")
  
  service_account {
    email  = google_service_account.main.email
    scopes = ["cloud-platform"]
  }
  
  tags = ["revisionary-main"]
}

# Firewall rules
resource "google_compute_firewall" "allow_http" {
  name    = "allow-http"
  network = "default"
  
  allow {
    protocol = "tcp"
    ports    = ["80", "443", "8000"]
  }
  
  source_ranges = ["0.0.0.0/0"]
  target_tags   = ["revisionary-main"]
}

# Enterprise upgrade path: Uncomment for GKE deployment
/*
resource "google_container_cluster" "enterprise" {
  count = var.enterprise_mode ? 1 : 0
  name     = "revisionary-cluster"
  location = var.region
  # ... GKE configuration
}
*/
```

**startup-script.sh**
```bash
#!/bin/bash
# VM initialization script

# Update system
apt-get update && apt-get upgrade -y

# Install Docker
curl -fsSL https://get.docker.com | sh
usermod -aG docker $USER

# Install Redis locally (see §6.2 for full config)
apt-get install -y redis-server
systemctl enable redis-server

# Install other dependencies
apt-get install -y nginx certbot python3-certbot-nginx

# Setup Docker Compose for API + Workers
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
```

**storage.tf**
```hcl
# Cloud Storage Buckets
resource "google_storage_bucket" "documents" {
  name     = "${var.project_id}-documents"
  location = var.region
  
  versioning {
    enabled = true
  }
  
  lifecycle_rule {
    condition {
      age = 30
    }
    action {
      type          = "SetStorageClass"
      storage_class = "NEARLINE"
    }
  }
  
  lifecycle_rule {
    condition {
      age = 365
    }
    action {
      type          = "SetStorageClass"
      storage_class = "COLDLINE"
    }
  }
}

resource "google_storage_bucket" "exports" {
  name     = "${var.project_id}-exports"
  location = var.region
  
  lifecycle_rule {
    condition {
      age = 7
    }
    action {
      type = "Delete"
    }
  }
}

resource "google_storage_bucket" "backups" {
  name     = "${var.project_id}-backups"
  location = var.region
  
  versioning {
    enabled = true
  }
}
```

**security.tf**
```hcl
# Service Accounts
resource "google_service_account" "gke_nodes" {
  account_id   = "gke-nodes"
  display_name = "GKE Nodes Service Account"
}

resource "google_service_account" "api_service" {
  account_id   = "api-service"
  display_name = "API Service Account"
}

resource "google_service_account" "workers" {
  account_id   = "workers"
  display_name = "Worker Service Account"
}

# IAM Bindings
resource "google_project_iam_member" "gke_nodes" {
  for_each = toset([
    "roles/storage.objectViewer",
    "roles/logging.logWriter",
    "roles/monitoring.metricWriter",
  ])
  
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.gke_nodes.email}"
}

# Secrets
resource "google_secret_manager_secret" "api_secrets" {
  secret_id = "api-secrets"
  
  replication {
    automatic = true
  }
}

resource "google_secret_manager_secret_version" "api_secrets" {
  secret      = google_secret_manager_secret.api_secrets.id
  secret_data = jsonencode({
    OPENAI_API_KEY     = var.openai_api_key
    GOOGLE_AI_API_KEY  = var.google_ai_api_key
    SECRET_KEY         = var.secret_key
  })
}
```

### 3.2 Deploy Infrastructure

```bash
# Initialize Terraform
cd infra/
terraform init

# Plan deployment
terraform plan -var="project_id=revisionary-prod" \
               -var="domain=revisionary.app" \
               -var="openai_api_key=$OPENAI_API_KEY"

# Apply configuration
terraform apply

# Get cluster credentials
gcloud container clusters get-credentials revisionary-cluster --region us-central1
```

## 4. Application Deployment

### 4.1 Docker Images

**API Dockerfile**
```dockerfile
FROM python:3.11-slim as base

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY api/ ./api/
COPY common/ ./common/

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

EXPOSE 8000
CMD ["uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

**Worker Dockerfile**
```dockerfile
FROM python:3.11-slim as base

WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY workers/ ./workers/
COPY common/ ./common/

# Worker configuration
ARG AGENT_TYPE
ENV AGENT_TYPE=${AGENT_TYPE}

CMD ["python", "-m", "workers.main"]
```

**Frontend Dockerfile**
```dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 4.2 Kubernetes Manifests

**namespace.yaml**
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: revisionary
  labels:
    name: revisionary
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: api-service
  namespace: revisionary
  annotations:
    iam.gke.io/gcp-service-account: <EMAIL>
```

**secrets.yaml**
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: api-secrets
  namespace: revisionary
type: Opaque
data:
  DATABASE_URL: <base64-encoded>
  REDIS_URL: <base64-encoded>
  OPENAI_API_KEY: <base64-encoded>
  GOOGLE_AI_API_KEY: <base64-encoded>
```

**api-deployment.yaml**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
  namespace: revisionary
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api
  template:
    metadata:
      labels:
        app: api
    spec:
      serviceAccountName: api-service
      containers:
      - name: api
        image: gcr.io/revisionary-prod/api:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: DATABASE_URL
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: REDIS_URL
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: api-service
  namespace: revisionary
spec:
  selector:
    app: api
  ports:
  - port: 80
    targetPort: 8000
  type: ClusterIP
```

**worker-deployment.yaml**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: worker-grammar
  namespace: revisionary
spec:
  replicas: 2
  selector:
    matchLabels:
      app: worker-grammar
  template:
    metadata:
      labels:
        app: worker-grammar
    spec:
      containers:
      - name: worker
        image: gcr.io/revisionary-prod/worker:latest
        env:
        - name: AGENT_TYPE
          value: "grammar"
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: REDIS_URL
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: OPENAI_API_KEY
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
# Similar deployments for style, structure, and content workers
# with appropriate resource allocations
```

**ingress.yaml**
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: revisionary-ingress
  namespace: revisionary
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "revisionary-ip"
    networking.gke.io/managed-certificates: "revisionary-ssl"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  rules:
  - host: api.revisionary.app
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-service
            port:
              number: 80
  - host: revisionary.app
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 80
---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: revisionary-ssl
  namespace: revisionary
spec:
  domains:
  - revisionary.app
  - api.revisionary.app
  - ws.revisionary.app
```

### 4.3 Helm Charts

**Chart.yaml**
```yaml
apiVersion: v2
name: revisionary
description: AI Writing Assistant
type: application
version: 1.0.0
appVersion: "1.0.0"
```

**values.yaml**
```yaml
global:
  environment: production
  domain: revisionary.app

api:
  image:
    repository: gcr.io/revisionary-prod/api
    tag: latest
  replicas: 3
  resources:
    requests:
      memory: 512Mi
      cpu: 500m
    limits:
      memory: 1Gi
      cpu: 1000m

workers:
  grammar:
    replicas: 2
    resources:
      requests:
        memory: 256Mi
        cpu: 200m
      limits:
        memory: 512Mi
        cpu: 500m
  
  style:
    replicas: 2
    resources:
      requests:
        memory: 512Mi
        cpu: 300m
      limits:
        memory: 1Gi
        cpu: 750m
        
  structure:
    replicas: 1
    resources:
      requests:
        memory: 1Gi
        cpu: 500m
      limits:
        memory: 2Gi
        cpu: 1000m
        
  content:
    replicas: 1
    resources:
      requests:
        memory: 2Gi
        cpu: 1000m
      limits:
        memory: 4Gi
        cpu: 2000m

frontend:
  image:
    repository: gcr.io/revisionary-prod/frontend
    tag: latest
  replicas: 2

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
```

## 5. CI/CD Pipeline

### 5.1 GitHub Actions

**.github/workflows/deploy.yml**
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  GCP_PROJECT: revisionary-prod
  GKE_CLUSTER: revisionary-cluster
  GKE_ZONE: us-central1

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Run tests
      run: |
        pytest --cov=api tests/
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      with:
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ env.GCP_PROJECT }}
    
    - name: Configure Docker
      run: gcloud auth configure-docker
    
    - name: Build and push API image
      run: |
        docker build -t gcr.io/$GCP_PROJECT/api:$GITHUB_SHA -t gcr.io/$GCP_PROJECT/api:latest ./api
        docker push gcr.io/$GCP_PROJECT/api:$GITHUB_SHA
        docker push gcr.io/$GCP_PROJECT/api:latest
    
    - name: Build and push Worker images
      run: |
        for agent in grammar style structure content; do
          docker build --build-arg AGENT_TYPE=$agent \
            -t gcr.io/$GCP_PROJECT/worker-$agent:$GITHUB_SHA \
            -t gcr.io/$GCP_PROJECT/worker-$agent:latest ./workers
          docker push gcr.io/$GCP_PROJECT/worker-$agent:$GITHUB_SHA
          docker push gcr.io/$GCP_PROJECT/worker-$agent:latest
        done
    
    - name: Build and push Frontend image
      run: |
        docker build -t gcr.io/$GCP_PROJECT/frontend:$GITHUB_SHA -t gcr.io/$GCP_PROJECT/frontend:latest ./frontend
        docker push gcr.io/$GCP_PROJECT/frontend:$GITHUB_SHA
        docker push gcr.io/$GCP_PROJECT/frontend:latest

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      with:
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ env.GCP_PROJECT }}
    
    - name: Get GKE credentials
      run: |
        gcloud container clusters get-credentials $GKE_CLUSTER --zone $GKE_ZONE
    
    - name: Deploy with Helm
      run: |
        helm upgrade --install revisionary ./helm/revisionary \
          --namespace revisionary \
          --create-namespace \
          --set global.version=$GITHUB_SHA \
          --set api.image.tag=$GITHUB_SHA \
          --set frontend.image.tag=$GITHUB_SHA \
          --wait
    
    - name: Verify deployment
      run: |
        kubectl rollout status deployment/api -n revisionary
        kubectl rollout status deployment/frontend -n revisionary
```

### 5.2 Staging Pipeline

**.github/workflows/staging.yml**
```yaml
name: Deploy to Staging

on:
  push:
    branches: [develop]

env:
  GCP_PROJECT: revisionary-staging
  GKE_CLUSTER: revisionary-staging-cluster
  GKE_ZONE: us-central1

jobs:
  deploy-staging:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    # Similar steps to production but with staging environment
    - name: Deploy to staging
      run: |
        helm upgrade --install revisionary ./helm/revisionary \
          --namespace revisionary-staging \
          --create-namespace \
          --values ./helm/revisionary/values-staging.yaml \
          --set global.version=$GITHUB_SHA
    
    - name: Run integration tests
      run: |
        pytest tests/integration/ --staging
    
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

## 6. Database Setup

### 6.1 Supabase Configuration

```sql
-- Create Supabase project
-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "ltree";
CREATE EXTENSION IF NOT EXISTS "vector";

-- Apply schema from database-schema.md
\i schema.sql

-- Set up Row Level Security policies
-- (Included in schema.sql)

-- Create database backups
SELECT cron.schedule('backup-daily', '0 2 * * *', 'pg_dump revisionary | gzip > /backups/$(date +%Y%m%d).sql.gz');
```

### 6.2 Local Redis Configuration (Production)

**Single-Node Setup (Launch Configuration):**
```bash
# Redis configuration for production
cat > redis.conf << EOF
# Network
bind 0.0.0.0
port 6379
protected-mode yes
requirepass ${REDIS_PASSWORD}

# Memory
maxmemory 2gb
maxmemory-policy allkeys-lfu

# Persistence
appendonly yes
appendfsync everysec
save 900 1
save 300 10
save 60 10000

# Logging
loglevel notice
logfile /var/log/redis/redis.log

# Performance
tcp-keepalive 300
timeout 0
tcp-backlog 511

# Keyspace notifications for real-time features
notify-keyspace-events Ex
EOF

# Install and configure Redis
sudo apt install redis-server
sudo cp redis.conf /etc/redis/redis.conf
sudo systemctl restart redis
sudo systemctl enable redis
```

**High-Availability Upgrade Path (3-Node Sentinel):**

For enterprise deployments requiring HA (>50k DAU):

**redis-sentinel.tf**
```hcl
# Redis Sentinel Cluster (3 nodes)
resource "google_compute_instance" "redis-replica" {
  count        = 2
  name         = "redis-replica-${count.index + 1}"
  machine_type = "e2-micro"  # Small replicas
  zone         = var.zone

  boot_disk {
    initialize_params {
      image = "ubuntu-os-cloud/ubuntu-2204-lts"
      size  = 20
    }
  }

  network_interface {
    network = "default"
    access_config {}
  }

  metadata_startup_script = templatefile("${path.module}/redis-replica-startup.sh", {
    master_ip     = google_compute_instance.main.network_interface[0].network_ip
    redis_password = var.redis_password
  })

  tags = ["redis-replica"]
}
```

**redis-replica-startup.sh**
```bash
#!/bin/bash
# Install Redis
apt-get update && apt-get install -y redis-server

# Configure as replica
cat > /etc/redis/redis.conf << EOF
replicaof ${master_ip} 6379
masterauth ${redis_password}
requirepass ${redis_password}
appendonly yes
maxmemory 512mb
maxmemory-policy allkeys-lfu
EOF

# Sentinel configuration (3 sentinels minimum)
cat > /etc/redis/sentinel.conf << EOF
port 26379
sentinel monitor revisionary-master ${master_ip} 6379 2
sentinel auth-pass revisionary-master ${redis_password}
sentinel down-after-milliseconds revisionary-master 5000
sentinel failover-timeout revisionary-master 60000
sentinel parallel-syncs revisionary-master 1
EOF

# Start services
systemctl enable redis-server redis-sentinel
systemctl start redis-server redis-sentinel
```

**Local Redis Service Unit:**
```bash
# Ensure Redis is properly configured as systemd service
cat > /etc/systemd/system/redis-revisionary.service << 'EOF'
[Unit]
Description=Advanced key-value store for Revisionary
After=network.target
Documentation=http://redis.io/documentation

[Service]
Type=notify
ExecStart=/usr/bin/redis-server /etc/redis/redis.conf
ExecReload=/bin/kill -USR2 $MAINPID
TimeoutStopSec=0
Restart=always
User=redis
Group=redis
RuntimeDirectory=redis
RuntimeDirectoryMode=0755

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable redis-revisionary
systemctl start redis-revisionary
```

**AOF Backup to GCS (15-minute RPO):**
```bash
# Cron job for AOF shipping
cat > /usr/local/bin/redis-backup.sh << 'EOF'
#!/bin/bash
TIMESTAMP=$(date +%Y%m%d_%H%M)
cp /var/lib/redis/appendonly.aof /tmp/redis-${TIMESTAMP}.aof
gzip /tmp/redis-${TIMESTAMP}.aof
gsutil cp /tmp/redis-${TIMESTAMP}.aof.gz gs://revisionary-backups/redis/
# Keep as latest backup
gsutil cp /tmp/redis-${TIMESTAMP}.aof.gz gs://revisionary-backups/redis/latest.aof.gz
rm /tmp/redis-${TIMESTAMP}.aof.gz
EOF

chmod +x /usr/local/bin/redis-backup.sh

# Run every 15 minutes
echo "*/15 * * * * /usr/local/bin/redis-backup.sh" | crontab -
```

## 7. Monitoring Setup

### 7.1 Prometheus Configuration

**prometheus.yaml**
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alerts.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)

  - job_name: 'api'
    static_configs:
      - targets: ['api:8000']
    metrics_path: /metrics

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
```

### 7.2 Grafana Dashboards

**api-dashboard.json**
```json
{
  "dashboard": {
    "title": "Revisionary API Dashboard",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(api_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(api_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(api_requests_total{status=~\"5..\"}[5m]) / rate(api_requests_total[5m])",
            "legendFormat": "Error Rate"
          }
        ]
      }
    ]
  }
}
```

### 7.3 Alerting Rules

**alerts.yml**
```yaml
groups:
- name: revisionary.rules
  rules:
  - alert: HighErrorRate
    expr: rate(api_requests_total{status=~"5.."}[5m]) / rate(api_requests_total[5m]) > 0.05
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value | humanizePercentage }}"

  - alert: HighLatency
    expr: histogram_quantile(0.95, rate(api_request_duration_seconds_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High API latency"
      description: "95th percentile latency is {{ $value }}s"

  - alert: RedisDown
    expr: up{job="redis"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Redis instance is down"

  - alert: HighTokenUsage
    expr: increase(llm_tokens_used_total[1h]) > 100000
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High token usage detected"
      description: "Token usage in last hour: {{ $value }}"
```

## 8. Security Configuration

### 8.1 Network Policies

**network-policy.yaml**
```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-network-policy
  namespace: revisionary
spec:
  podSelector:
    matchLabels:
      app: api
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
    - protocol: TCP
      port: 443   # HTTPS
```

### 8.2 Pod Security Policy

**pod-security-policy.yaml**
```yaml
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: revisionary-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
```

## 9. Backup and Recovery

### Enterprise SLA Targets
- **RPO (Recovery Point Objective)**: 15 minutes maximum data loss
  - Redis AOF backups every 15 minutes to GCS
  - Supabase point-in-time recovery (continuous WAL)
  - Hourly `pg_dump | gzip` snapshots to GCS
- **RTO (Recovery Time Objective)**: 1 hour maximum downtime  
  - VM image restoration: ~20 minutes
  - Supabase PITR restoration: ~30 minutes
  - Application startup and health checks: ~10 minutes

### Disaster Recovery Runbook

**Complete Infrastructure Loss:**
1. **Restore VM** (20 min):
   ```bash
   # Create new VM from latest image snapshot
   gcloud compute instances create revisionary-main-restored \
     --source-instance-template=revisionary-template \
     --zone=us-central1-a
   ```

2. **Restore Redis** (5 min):
   ```bash
   # Download latest AOF backup
   gsutil cp gs://revisionary-backups/redis/latest.aof.gz /var/lib/redis/
   gunzip /var/lib/redis/latest.aof.gz
   mv /var/lib/redis/latest.aof /var/lib/redis/appendonly.aof
   systemctl restart redis
   ```

3. **Restore Supabase** (30 min):
   ```bash
   # Use Supabase dashboard PITR to restore to 15 minutes before incident
   # Or restore from hourly pg_dump backup:
   gsutil cp gs://revisionary-backups/postgres/latest.sql.gz ./
   gunzip latest.sql.gz
   psql $DATABASE_URL < latest.sql
   ```

4. **Verify Recovery** (5 min):
   ```bash
   # Health checks
   curl -f http://localhost:8000/health
   redis-cli ping
   psql $DATABASE_URL -c "SELECT COUNT(*) FROM documents;"
   ```

### 9.1 Database Backups

```bash
#!/bin/bash
# backup-database.sh

BACKUP_DIR="/backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# PostgreSQL backup
pg_dump $DATABASE_URL | gzip > $BACKUP_DIR/postgres-$(date +%H%M).sql.gz

# Redis backup
redis-cli --rdb $BACKUP_DIR/redis-$(date +%H%M).rdb

# Upload to Google Cloud Storage
gsutil -m cp -r $BACKUP_DIR gs://revisionary-backups/

# Cleanup old local backups
find /backups -type d -mtime +7 -exec rm -rf {} \;
```

### 9.2 Disaster Recovery

**Recovery Objectives:**
- **RPO (Recovery Point Objective)**: ≤15 minutes - maximum data loss acceptable
- **RTO (Recovery Time Objective)**: ≤1 hour - maximum downtime for full service restoration

**Backup Strategy:**
- Redis AOF: Every 1 second → GCS (RPO: 1 second)
- PostgreSQL: Hourly pg_dump → GCS (RPO: 1 hour, supplemented by Supabase PITR)
- Application containers: Immutable images in Container Registry

```bash
#!/bin/bash
# disaster-recovery.sh

# Restore from backup
RESTORE_DATE=${1:-$(date +%Y%m%d)}
BACKUP_PATH="gs://revisionary-backups/$RESTORE_DATE"

# Download backup
gsutil -m cp -r $BACKUP_PATH ./restore/

# Restore PostgreSQL
gunzip -c ./restore/postgres-*.sql.gz | psql $DATABASE_URL

# Restore Redis
redis-cli --rdb ./restore/redis-*.rdb

# Verify restoration
echo "Running health checks..."
curl -f http://api.revisionary.app/health
```

## 10. Performance Optimization

### 10.1 Kubernetes Resource Optimization

```yaml
# HorizontalPodAutoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-hpa
  namespace: revisionary
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api
  minReplicas: 2
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 10.2 CDN Configuration

```javascript
// Cloudflare Workers configuration
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url)
  
  // Cache static assets
  if (url.pathname.match(/\.(js|css|png|jpg|ico|woff2)$/)) {
    return fetch(request, {
      cf: {
        cacheTtl: 86400, // 24 hours
        cacheEverything: true
      }
    })
  }
  
  // API requests
  if (url.pathname.startsWith('/api/')) {
    return fetch(request, {
      cf: {
        cacheTtl: 300 // 5 minutes for API responses
      }
    })
  }
  
  return fetch(request)
}
```

## 11. Maintenance Procedures

### 11.1 Rolling Updates

```bash
#!/bin/bash
# rolling-update.sh

NEW_VERSION=$1

# Update API
kubectl set image deployment/api api=gcr.io/revisionary-prod/api:$NEW_VERSION -n revisionary
kubectl rollout status deployment/api -n revisionary

# Update workers
for worker in grammar style structure content; do
  kubectl set image deployment/worker-$worker worker=gcr.io/revisionary-prod/worker-$worker:$NEW_VERSION -n revisionary
  kubectl rollout status deployment/worker-$worker -n revisionary
done

# Update frontend
kubectl set image deployment/frontend frontend=gcr.io/revisionary-prod/frontend:$NEW_VERSION -n revisionary
kubectl rollout status deployment/frontend -n revisionary

echo "Rolling update completed successfully"
```

### 11.2 Scaling Operations

```bash
#!/bin/bash
# scale-cluster.sh

ACTION=$1  # scale-up or scale-down
PEAK_HOURS="9-17"

if [ "$ACTION" = "scale-up" ]; then
  kubectl scale deployment api --replicas=5 -n revisionary
  kubectl scale deployment worker-grammar --replicas=4 -n revisionary
  kubectl scale deployment worker-style --replicas=3 -n revisionary
  gcloud container clusters resize revisionary-cluster --num-nodes=6 --zone=us-central1
elif [ "$ACTION" = "scale-down" ]; then
  kubectl scale deployment api --replicas=2 -n revisionary
  kubectl scale deployment worker-grammar --replicas=1 -n revisionary
  kubectl scale deployment worker-style --replicas=1 -n revisionary
  gcloud container clusters resize revisionary-cluster --num-nodes=3 --zone=us-central1
fi
```

## 12. Troubleshooting Guide

### 12.1 Common Issues

**High API Latency**
```bash
# Check pod resources
kubectl top pods -n revisionary

# Check node resources
kubectl top nodes

# Scale up if needed
kubectl scale deployment api --replicas=5 -n revisionary
```

**Redis Connection Issues**
```bash
# Check local Redis status
sudo systemctl status redis
redis-cli -h localhost -p 6379 -a ${REDIS_PASSWORD} ping

# Check Redis memory usage
redis-cli -h localhost -p 6379 -a ${REDIS_PASSWORD} info memory

# Check Redis configuration
redis-cli -h localhost -p 6379 -a ${REDIS_PASSWORD} config get maxmemory

# If Redis is down, restart following the local Redis recipe from §6.2
sudo systemctl restart redis
tail -f /var/log/redis/redis.log
```

**Database Connection Pool Exhaustion**
```bash
# Check active connections
kubectl logs deployment/api -n revisionary | grep "connection pool"

# Restart API pods to reset connections
kubectl rollout restart deployment/api -n revisionary
```

### 12.2 Emergency Procedures

**Complete Service Outage**
```bash
#!/bin/bash
# emergency-restore.sh

# Check cluster status
kubectl get nodes
kubectl get pods --all-namespaces

# Restore from backup if needed
./disaster-recovery.sh

# Enable maintenance mode
kubectl apply -f maintenance-mode.yaml

# Gradually restore services
kubectl scale deployment api --replicas=1 -n revisionary
# Monitor and scale up incrementally
```

This deployment guide provides a comprehensive approach to deploying and maintaining the Revisionary application in production with proper monitoring, security, and scalability considerations.