# Revisionary - Architecture Overview

## 1. Data Architecture: Redis + Supabase Strategy

Revisionary uses a hybrid data architecture combining Supabase (PostgreSQL) for persistence and Redis for performance optimization.

### 1.1 Why Both Redis and Supabase?

**Supabase (PostgreSQL) - Source of Truth:**
- **Documents & Versions**: Persistent storage of all document content
- **User Management**: Account data, subscriptions, preferences
- **Collaboration Data**: Multi-user editing metadata and permissions (real-time handled via Redis Pub/Sub)
- **Suggestions History**: Long-term storage of AI suggestions and user feedback
- **Vector Search**: pgvector for semantic search and content similarity
- **ACID Transactions**: Data consistency for critical operations
- **Row-Level Security**: Multi-tenant data isolation

**Redis - Performance Layer:**
- **Hot Cache**: Frequently accessed documents and blocks (TTL: 30 min)
- **LLM Response Cache**: Expensive AI responses cached for reuse (TTL: 1-24 hours)
- **Message Queues**: Agent job distribution via Redis Streams (budget tier)
- **Enterprise Queues**: Cloud Tasks replaces Redis Streams for enterprise tier (see queue matrix in Backend Architecture)
- **Real-time Pub/Sub**: Live collaboration events and notifications
- **Session Storage**: User sessions and temporary state
- **Rate Limiting**: Token usage counters and API rate limits
- **Context Caching**: Paragraph/section summaries for agent context building

### 1.2 Data Flow Pattern

```
User Request → FastAPI → Check Redis Cache → 
                ↓ (Cache Miss)
              Supabase Query → Update Redis Cache → Response
```

**Write Pattern:**
```
User Edit → FastAPI → Update Supabase → Invalidate Redis Cache → 
          → Publish Real-time Event → Update Other Users
```

**AI Generation Pattern:**
```
AI Request → Build Context (Redis + Supabase) → 
           → Queue Job (Redis) → Worker Process → 
           → Cache Result (Redis) → Store in Supabase → 
           → Notify Client (Redis Pub/Sub)
```

## 2. Caching Strategy

### 2.1 Cache Levels

**Level 1: Redis (Application Cache)**
- Document blocks: `block:{id}` → Block content + metadata
- Suggestions: `suggestions:{block_id}` → Active AI suggestions
- Context summaries: `summary:{block_id}:{level}` → Paragraph/section summaries
- User sessions: `session:{user_id}` → Authentication and preferences

**Level 2: Gemini Context Cache**
- Large document contexts cached in Gemini 2.5 Flash for 1 hour
- Reduces token costs for repeated operations on same content
- Automatically invalidated when document content changes

**Level 3: Browser Cache**
- Static assets cached for 24 hours
- API responses cached for 5 minutes (with ETags)

### 2.2 Cache Invalidation

**Content Changes:**
```python
async def update_block(block_id: str, new_content: str):
    # Update database
    await db.update_block(block_id, new_content)
    
    # Invalidate related caches
    await redis.delete(f"block:{block_id}")
    await redis.delete(f"suggestions:{block_id}")
    await redis.delete(f"summary:{block_id}:*")
    
    # Mark suggestions as stale
    await redis.publish(f"stale:suggestions:{block_id}", "content_changed")
    
    # Invalidate Gemini context cache
    doc_id = await get_document_id(block_id)
    gemini_cache.invalidate(doc_id)
```

## 3. Real-time Architecture

**All real-time communication flows through Redis Pub/Sub channels:**

**AI Processing Events:**
- Job progress updates: `ai:jobs:{job_id}:progress`
- Suggestion completions: `ai:suggestions:{block_id}:complete`
- Error notifications: `ai:errors:{job_id}`

**Document Collaboration:**
- User presence (cursors, selections): `document:{doc_id}:presence`
- Content changes: `document:{doc_id}:updates`
- Suggestion invalidation: `stale:suggestions:{block_id}`

**User Notifications:**
- Personal notifications: `user:{user_id}:notifications`
- Token usage warnings: `user:{user_id}:limits`

**Channel Pattern:**
```
document:{doc_id}:presence  → User cursors and selections
document:{doc_id}:updates   → Content changes  
document:{doc_id}:ai        → AI processing events
stale:suggestions:{block_id} → Suggestion invalidation
user:{user_id}:notifications → Personal notifications
user:{user_id}:limits       → Token budget warnings
```

## 4. AI Agent Architecture

### 4.1 Outline Cache Service

**Purpose**: The Outline Worker maintains the `outline_cache` table to provide fast document navigation without hitting the database on every load.

**Operation**:
- Refreshes outline cache after structural edits (chapter/section changes)
- Triggered by Redis keyspace events when blocks with `type='chapter'` or `type='section'` are modified
- Updates the cached outline JSON in Redis (`outline:{doc_id}`) and persists structure in PostgreSQL

**Cache Pattern**:
```python
async def refresh_outline_cache(doc_id: str):
    # Fetch current outline structure from DB
    outline = await db.get_document_outline(doc_id)
    
    # Update Redis cache
    await redis.setex(f"outline:{doc_id}", 600, json.dumps(outline))  # 10 min TTL
    
    # Publish update to connected clients
    await redis.publish(f"document:{doc_id}:outline", outline)
```

### 4.2 Context Building with Caching

```