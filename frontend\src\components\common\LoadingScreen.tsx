import React from 'react';

const LoadingScreen: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-white">
      <div className="flex flex-col items-center space-y-4">
        {/* Revisionary logo/spinner */}
        <div className="relative">
          <div className="w-12 h-12 border-4 border-primary-200 rounded-full animate-spin border-t-primary-600"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-4 h-4 bg-primary-600 rounded-full animate-pulse"></div>
          </div>
        </div>
        
        {/* Loading text */}
        <div className="text-center">
          <h2 className="text-lg font-semibold text-gray-900 mb-1">
            Revisionary
          </h2>
          <p className="text-sm text-gray-500">
            Loading your writing workspace<span className="loading-dots"></span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;