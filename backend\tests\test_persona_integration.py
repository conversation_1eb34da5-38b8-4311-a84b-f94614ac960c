#!/usr/bin/env python3
"""
Integration test for Persona System components.
Tests the integration between API, Worker, and WebSocket components.
"""

import sys
import os
import ast
import inspect
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

def test_persona_api_structure():
    """Test that persona API has required endpoints and structure."""
    print("Testing Persona API structure...")
    
    try:
        # Read the personas.py file and analyze its AST
        personas_file = backend_path / "api" / "personas.py"
        with open(personas_file, 'r') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        # Check for required functions by searching content directly
        required_endpoints = [
            'list_personas',
            'create_persona', 
            'get_persona',
            'update_persona',
            'delete_persona',
            'request_persona_feedback',
            'request_multi_persona_feedback'
        ]
        
        found_functions = []
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                found_functions.append(node.name)
        
        # Also check content directly for function definitions
        for endpoint in required_endpoints:
            if f"def {endpoint}(" in content:
                if endpoint not in found_functions:
                    found_functions.append(endpoint)
        
        missing_endpoints = set(required_endpoints) - set(found_functions)
        if missing_endpoints:
            print(f"❌ Missing endpoints: {missing_endpoints}")
            return False
        
        print("✓ All required API endpoints found")
        
        # Check for required Pydantic models
        required_models = [
            'PersonaCreate',
            'PersonaUpdate', 
            'PersonaFeedbackRequest',
            'MultiFeedbackRequest',
            'PersonaResponse',
            'MultiFeedbackResponse'
        ]
        
        found_classes = []
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                found_classes.append(node.name)
        
        missing_models = set(required_models) - set(found_classes)
        if missing_models:
            print(f"❌ Missing Pydantic models: {missing_models}")
            return False
        
        print("✓ All required Pydantic models found")
        return True
        
    except Exception as e:
        print(f"❌ Error testing API structure: {e}")
        return False

def test_persona_worker_structure():
    """Test that persona worker has required methods."""
    print("Testing Persona Worker structure...")
    
    try:
        # Read the persona_worker.py file and analyze its AST
        worker_file = backend_path / "workers" / "persona_worker.py"
        with open(worker_file, 'r') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        # Check for PersonaWorker class
        persona_worker_class = None
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == 'PersonaWorker':
                persona_worker_class = node
                break
        
        if not persona_worker_class:
            print("❌ PersonaWorker class not found")
            return False
        
        print("✓ PersonaWorker class found")
        
        # Check for required methods
        required_methods = [
            'process_job',
            '_load_persona_data',
            '_generate_single_feedback',
            '_generate_multi_feedback',
            '_generate_cross_audience_analysis'
        ]
        
        found_methods = []
        for node in ast.walk(persona_worker_class):
            if isinstance(node, ast.FunctionDef):
                found_methods.append(node.name)
        
        # Also check content directly for method definitions
        for method in required_methods:
            if f"def {method}(" in content:
                if method not in found_methods:
                    found_methods.append(method)
        
        missing_methods = set(required_methods) - set(found_methods)
        if missing_methods:
            print(f"❌ Missing worker methods: {missing_methods}")
            return False
        
        print("✓ All required worker methods found")
        return True
        
    except Exception as e:
        print(f"❌ Error testing worker structure: {e}")
        return False

def test_websocket_persona_events():
    """Test that WebSocket server has persona events."""
    print("Testing WebSocket persona events...")
    
    try:
        # Read the websocket server file
        websocket_file = backend_path / "websocket" / "server.py"
        with open(websocket_file, 'r') as f:
            content = f.read()
        
        # Check for persona-related content
        required_patterns = [
            'request_persona_feedback',
            'persona:feedback:',
            'persona_feedback_started',
            'persona_feedback_result'
        ]
        
        for pattern in required_patterns:
            if pattern not in content:
                print(f"❌ Missing WebSocket pattern: {pattern}")
                return False
        
        print("✓ All required WebSocket persona patterns found")
        return True
        
    except Exception as e:
        print(f"❌ Error testing WebSocket structure: {e}")
        return False

def test_main_app_persona_routes():
    """Test that main app includes persona routes."""
    print("Testing main app persona routes...")
    
    try:
        # Read the main.py file
        main_file = backend_path / "api" / "main.py"
        with open(main_file, 'r') as f:
            content = f.read()
        
        # Check for persona imports and router registration
        required_patterns = [
            'from api.personas import router as personas_router',
            'personas_router',
            'prefix="/api/v1/personas"',
            'tags=["personas"]'
        ]
        
        for pattern in required_patterns:
            if pattern not in content:
                print(f"❌ Missing main app pattern: {pattern}")
                return False
        
        print("✓ Persona routes properly registered in main app")
        return True
        
    except Exception as e:
        print(f"❌ Error testing main app: {e}")
        return False

def test_mock_data_consistency():
    """Test that persona mock data exists and is consistent."""
    print("Testing persona mock data...")
    
    try:
        # Check 004_advanced_features.sql for personas
        advanced_features_file = backend_path / "mock_data" / "004_advanced_features.sql"
        with open(advanced_features_file, 'r') as f:
            content = f.read()
        
        if 'INSERT INTO personas' not in content:
            print("❌ No persona INSERT statements in 004_advanced_features.sql")
            return False
        
        print("✓ Persona mock data found in 004_advanced_features.sql")
        
        # Check 010_persona_feedback.sql for feedback data
        feedback_file = backend_path / "mock_data" / "010_persona_feedback.sql"
        with open(feedback_file, 'r') as f:
            content = f.read()
        
        if 'INSERT INTO persona_feedback' not in content:
            print("❌ No persona feedback INSERT statements in 010_persona_feedback.sql")
            return False
        
        print("✓ Persona feedback mock data found in 010_persona_feedback.sql")
        return True
        
    except Exception as e:
        print(f"❌ Error testing mock data: {e}")
        return False

def test_data_flow_consistency():
    """Test that data models are consistent across components."""
    print("Testing data flow consistency...")
    
    try:
        # Check that API models match worker expectations
        # This is a basic structure test - in a real environment we'd test actual data flow
        
        # Read API file for request/response models
        api_file = backend_path / "api" / "personas.py"
        with open(api_file, 'r') as f:
            api_content = f.read()
        
        # Read worker file for job processing  
        worker_file = backend_path / "workers" / "persona_worker.py"
        with open(worker_file, 'r') as f:
            worker_content = f.read()
        
        # Check for consistent field names
        key_fields = [
            'text',
            'personas', 
            'analysis_type',
            'context',
            'options',
            'feedback_text',
            'overall_score',
            'comprehension_score'
        ]
        
        for field in key_fields:
            if field not in api_content or field not in worker_content:
                print(f"❌ Inconsistent field usage: {field}")
                return False
        
        print("✓ Data flow consistency verified")
        return True
        
    except Exception as e:
        print(f"❌ Error testing data flow: {e}")
        return False

def run_integration_tests():
    """Run all integration tests."""
    print("=" * 60)
    print("PERSONA SYSTEM INTEGRATION TESTS")
    print("=" * 60)
    
    tests = [
        test_persona_api_structure,
        test_persona_worker_structure, 
        test_websocket_persona_events,
        test_main_app_persona_routes,
        test_mock_data_consistency,
        test_data_flow_consistency
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n{test.__name__.replace('_', ' ').title()}:")
        try:
            if test():
                passed += 1
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test error: {e}")
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL PERSONA INTEGRATION TESTS PASSED!")
        print("✅ Phase 5: Persona System implementation is complete and integrated")
        return True
    else:
        print("❌ Some integration tests failed")
        return False

if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)