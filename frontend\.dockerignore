# Dependencies
node_modules
npm-debug.log*
pnpm-debug.log*
.pnpm-store

# Platform-specific binaries
.npm/_cacache

# Build outputs (but keep dist for static deployment)
build

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode
.idea
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Testing
coverage
.nyc_output
playwright-report
test-results

# Logs
*.log

# Runtime files
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
CHANGELOG.md
LICENSE