#!/usr/bin/env python3
"""
Revisionary API Endpoint Testing Script

This script tests all available API endpoints and provides detailed feedback
on what's working and what isn't.
"""

import requests
import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import sys

@dataclass
class EndpointTest:
    method: str
    path: str
    description: str
    requires_auth: bool = True
    test_data: Optional[Dict] = None
    query_params: Optional[Dict] = None
    expected_status: int = 200

@dataclass
class TestResult:
    endpoint: str
    method: str
    status_code: int
    success: bool
    response_time: float
    error_message: Optional[str] = None
    response_data: Optional[Dict] = None

class RevisionaryAPITester:
    def __init__(self, base_url: str = "http://localhost:8000/api/v1", auth_token: str = "dev-token"):
        self.base_url = base_url.rstrip('/')
        self.auth_token = auth_token
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        })
        self.results: List[TestResult] = []

    def test_endpoint(self, test: EndpointTest) -> TestResult:
        """Test a single endpoint and return results."""
        url = f"{self.base_url}{test.path}"
        start_time = time.time()
        
        try:
            # Prepare request
            kwargs = {
                'timeout': 30,
                'params': test.query_params or {}
            }
            
            if test.test_data and test.method.upper() in ['POST', 'PUT', 'PATCH']:
                kwargs['json'] = test.test_data
            
            # Make request
            response = self.session.request(test.method.upper(), url, **kwargs)
            response_time = time.time() - start_time
            
            # Parse response
            try:
                response_data = response.json()
            except:
                response_data = {"raw_response": response.text[:500]}
            
            success = response.status_code == test.expected_status
            error_message = None if success else f"Expected {test.expected_status}, got {response.status_code}"
            
            return TestResult(
                endpoint=test.path,
                method=test.method,
                status_code=response.status_code,
                success=success,
                response_time=response_time,
                error_message=error_message,
                response_data=response_data
            )
            
        except Exception as e:
            response_time = time.time() - start_time
            return TestResult(
                endpoint=test.path,
                method=test.method,
                status_code=0,
                success=False,
                response_time=response_time,
                error_message=str(e)
            )

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all endpoint tests and return comprehensive results."""
        
        # Define all endpoints to test
        endpoints = [
            # Authentication endpoints
            EndpointTest("GET", "/auth/me", "Get current user profile"),
            
            # Document endpoints
            EndpointTest("GET", "/documents", "List documents", query_params={"limit": 5}),
            EndpointTest("POST", "/documents", "Create document", 
                        test_data={"title": "Test Doc", "type": "general", "content": "Test content"}),
            
            # Analytics endpoints  
            EndpointTest("GET", "/analytics/token-usage", "Get token usage", query_params={"days": 30}),
            
            # Health endpoints - using actual available endpoints
            EndpointTest("POST", "/health/analyze", "Health analysis", 
                        test_data={"text": "Test content", "analysis_type": "comprehensive", "preferences": {}}, expected_status=200),
            
            # Developer Portal endpoints
            EndpointTest("GET", "/developer/docs", "List documentation files"),
            EndpointTest("GET", "/developer/docs/api-documentation.md", "Get API documentation"),
            EndpointTest("GET", "/developer/openapi-spec", "Get OpenAPI specification"),
            EndpointTest("GET", "/developer/api-stats", "Get API statistics"),
            EndpointTest("GET", "/developer/code-examples/python", "Get Python code examples"),
            
            # Personas endpoints
            EndpointTest("GET", "/personas", "List personas"),
            
            # Blocks endpoints
            EndpointTest("POST", "/blocks/generate", "Generate content block", expected_status=405,
                        test_data={"prompt": "Test prompt", "type": "paragraph"}),
            
            # Agent endpoints  
            EndpointTest("GET", "/agents", "List agents"),
            
            # Monitoring endpoints
            EndpointTest("GET", "/monitoring/status", "Get monitoring status"),
        ]

        print(f"🚀 Starting API endpoint testing...")
        print(f"📍 Base URL: {self.base_url}")
        print(f"🔑 Auth Token: {self.auth_token[:20]}...")
        print(f"📊 Testing {len(endpoints)} endpoints\n")

        # Run tests
        for i, endpoint in enumerate(endpoints, 1):
            print(f"[{i:2d}/{len(endpoints)}] Testing {endpoint.method:6} {endpoint.path:<40} ", end="")
            
            result = self.test_endpoint(endpoint)
            self.results.append(result)
            
            # Status indicator
            if result.success:
                print(f"✅ {result.status_code} ({result.response_time:.3f}s)")
            else:
                print(f"❌ {result.status_code} ({result.response_time:.3f}s) - {result.error_message}")
            
            # Brief delay to be nice to the server
            time.sleep(0.1)

        return self.generate_report()

    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if r.success)
        failed_tests = total_tests - successful_tests
        
        # Group results by status
        status_groups = {}
        for result in self.results:
            status = result.status_code
            if status not in status_groups:
                status_groups[status] = []
            status_groups[status].append(result)
        
        # Calculate average response time
        avg_response_time = sum(r.response_time for r in self.results) / total_tests if total_tests > 0 else 0
        
        return {
            "summary": {
                "total_tests": total_tests,
                "successful": successful_tests,
                "failed": failed_tests,
                "success_rate": (successful_tests / total_tests * 100) if total_tests > 0 else 0,
                "avg_response_time": avg_response_time
            },
            "status_breakdown": status_groups,
            "failed_endpoints": [r for r in self.results if not r.success],
            "successful_endpoints": [r for r in self.results if r.success],
            "detailed_results": self.results
        }

    def print_detailed_report(self, report: Dict[str, Any]):
        """Print a detailed, formatted report."""
        
        summary = report["summary"]
        
        print("\n" + "="*80)
        print("📊 DETAILED TEST REPORT")
        print("="*80)
        
        # Summary
        print(f"\n📈 SUMMARY:")
        print(f"   Total Tests:     {summary['total_tests']}")
        print(f"   Successful:      {summary['successful']} ✅")
        print(f"   Failed:          {summary['failed']} ❌")
        print(f"   Success Rate:    {summary['success_rate']:.1f}%")
        print(f"   Avg Response:    {summary['avg_response_time']:.3f}s")
        
        # Status breakdown
        print(f"\n📋 STATUS CODE BREAKDOWN:")
        for status_code in sorted(report["status_breakdown"].keys()):
            results = report["status_breakdown"][status_code]
            count = len(results)
            emoji = "✅" if status_code in [200, 201, 204] else "❌" if status_code >= 400 else "⚠️"
            print(f"   {status_code}: {count:2d} endpoints {emoji}")
        
        # Failed endpoints
        if report["failed_endpoints"]:
            print(f"\n❌ FAILED ENDPOINTS:")
            for result in report["failed_endpoints"]:
                print(f"   {result.method:6} {result.endpoint:<40} → {result.status_code} ({result.error_message})")
        
        # Successful endpoints
        if report["successful_endpoints"]:
            print(f"\n✅ SUCCESSFUL ENDPOINTS:")
            for result in report["successful_endpoints"]:
                print(f"   {result.method:6} {result.endpoint:<40} → {result.status_code} ({result.response_time:.3f}s)")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        if summary["failed"] == 0:
            print("   🎉 All endpoints are working perfectly!")
        else:
            print(f"   🔧 Fix {summary['failed']} failing endpoints")
            
        if summary["avg_response_time"] > 1.0:
            print("   ⚡ Consider optimizing response times (>1s average)")
        elif summary["avg_response_time"] < 0.5:
            print("   🚀 Excellent response times (<0.5s average)")
            
        if summary["success_rate"] > 90:
            print("   ✨ Great API reliability!")
        elif summary["success_rate"] > 75:
            print("   📈 Good API reliability, room for improvement")
        else:
            print("   🚨 API reliability needs attention")
        
        print("\n" + "="*80)

def main():
    """Main function to run the API tests."""
    
    # Configuration
    BASE_URL = "http://localhost:8000/api/v1"
    AUTH_TOKEN = "dev-token"
    
    # Allow command line overrides
    if len(sys.argv) > 1:
        BASE_URL = sys.argv[1]
    if len(sys.argv) > 2:
        AUTH_TOKEN = sys.argv[2]
    
    # Run tests
    tester = RevisionaryAPITester(BASE_URL, AUTH_TOKEN)
    
    try:
        report = tester.run_all_tests()
        tester.print_detailed_report(report)
        
        # Save detailed results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"api_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            # Convert results to JSON-serializable format
            json_report = {
                "timestamp": timestamp,
                "base_url": BASE_URL,
                "summary": report["summary"],
                "results": [
                    {
                        "endpoint": r.endpoint,
                        "method": r.method,
                        "status_code": r.status_code,
                        "success": r.success,
                        "response_time": r.response_time,
                        "error_message": r.error_message,
                        "response_data": r.response_data
                    }
                    for r in report["detailed_results"]
                ]
            }
            json.dump(json_report, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: {filename}")
        
        # Exit code based on success rate
        success_rate = report["summary"]["success_rate"]
        if success_rate == 100:
            sys.exit(0)  # All tests passed
        elif success_rate >= 75:
            sys.exit(1)  # Most tests passed
        else:
            sys.exit(2)  # Many tests failed
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Testing interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n\n💥 Testing failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()