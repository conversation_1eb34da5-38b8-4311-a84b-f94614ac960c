export interface ReaderPersona {
  id: string;
  name: string;
  description: string;
  category: PersonaCategory;
  
  // Demographics
  demographics: {
    age?: number;
    education?: EducationLevel;
    profession?: string;
    culturalBackground?: string;
    location?: string;
  };
  
  // Reading Preferences
  readingPreferences: {
    genres?: string[];
    readingSpeed?: 'slow' | 'average' | 'fast';
    attentionSpan?: 'short' | 'medium' | 'long';
    preferredLength?: 'brief' | 'moderate' | 'detailed';
    complexity?: 'simple' | 'moderate' | 'complex';
  };
  
  // Personality & Psychology
  personality: {
    mbtiType?: string;
    traits?: string[];
    emotionalSensitivity?: 'low' | 'medium' | 'high';
    criticalThinking?: 'low' | 'medium' | 'high';
    openness?: 'low' | 'medium' | 'high';
  };
  
  // Context & Purpose
  context: {
    relationship?: 'stranger' | 'acquaintance' | 'colleague' | 'friend' | 'family';
    purpose?: 'entertainment' | 'education' | 'work' | 'evaluation' | 'personal';
    expertise?: 'novice' | 'intermediate' | 'expert' | 'specialist';
    timeConstraints?: 'none' | 'limited' | 'urgent';
  };
  
  // Feedback Style
  feedbackStyle: {
    tone?: 'harsh' | 'constructive' | 'encouraging' | 'gentle';
    focus?: 'content' | 'style' | 'structure' | 'emotion' | 'technical';
    detail?: 'high-level' | 'specific' | 'line-by-line';
    priorities?: string[];
  };
  
  // Metadata
  isBuiltIn: boolean;
  isActive: boolean;
  tags: string[];
  color: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export type PersonaCategory = 
  | 'academic'
  | 'professional' 
  | 'creative'
  | 'personal'
  | 'technical'
  | 'cultural'
  | 'age-specific'
  | 'custom';

export type EducationLevel = 
  | 'elementary'
  | 'high-school'
  | 'some-college'
  | 'bachelors'
  | 'masters'
  | 'doctorate'
  | 'professional';

export interface PersonaFeedback {
  personaId: string;
  documentId: string;
  documentSection?: string;
  
  // Overall Reaction
  overallReaction: PersonaReaction;
  engagementScore: number; // 0-100
  comprehensionScore: number; // 0-100
  
  // Specific Feedback
  feedback: PersonaComment[];
  
  // Predictions
  predictions: {
    willFinishReading: number; // 0-100 probability
    wouldRecommend: number; // 0-100 probability
    emotionalImpact: 'none' | 'low' | 'medium' | 'high';
    memorability: 'forgettable' | 'memorable' | 'unforgettable';
  };
  
  // Metadata
  confidence: number; // 0-100 how confident the AI is in this feedback
  generatedAt: Date;
}

export type PersonaReaction = 
  | 'love'
  | 'like' 
  | 'neutral'
  | 'confused'
  | 'bored'
  | 'annoyed'
  | 'offended'
  | 'excited'
  | 'moved'
  | 'inspired';

export interface PersonaComment {
  id: string;
  type: CommentType;
  severity: 'low' | 'medium' | 'high';
  
  // Location
  startPosition?: number;
  endPosition?: number;
  lineNumber?: number;
  
  // Content
  title: string;
  message: string;
  suggestion?: string;
  
  // Context
  category: FeedbackCategory;
  tags: string[];
  
  // Reaction
  emotionalReaction?: string;
  cognitiveLoad?: 'easy' | 'moderate' | 'difficult';
}

export type CommentType = 
  | 'praise'
  | 'concern'
  | 'confusion'
  | 'suggestion'
  | 'question'
  | 'warning'
  | 'emotional-reaction';

export type FeedbackCategory =
  | 'comprehension'
  | 'engagement'
  | 'emotion'
  | 'style'
  | 'content'
  | 'structure'
  | 'relevance'
  | 'cultural'
  | 'technical'
  | 'accessibility';

export interface PersonaInsights {
  documentId: string;
  activePersonas: string[];
  
  // Consensus Analysis
  consensus: {
    agreements: PersonaComment[];
    disagreements: PersonaComment[];
    universalIssues: PersonaComment[];
    polarizingElements: PersonaComment[];
  };
  
  // Audience Analysis
  audienceCompatibility: {
    primaryAudience: {
      personaId: string;
      compatibilityScore: number;
    };
    crossAudienceAppeal: number; // 0-100
    audienceConflicts: AudienceConflict[];
  };
  
  // Optimization Suggestions
  optimizations: OptimizationSuggestion[];
  
  // Predictions
  marketPredictions?: {
    successProbability: number;
    targetRating: number;
    marketFit: 'poor' | 'fair' | 'good' | 'excellent';
  };
  
  generatedAt: Date;
}

export interface AudienceConflict {
  description: string;
  conflictingPersonas: string[];
  impactLevel: 'low' | 'medium' | 'high';
  resolution?: string;
}

export interface OptimizationSuggestion {
  id: string;
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'easy' | 'moderate' | 'difficult';
  affectedPersonas: string[];
  category: FeedbackCategory;
  priority: number;
}

// Built-in Persona Templates
export interface PersonaTemplate {
  id: string;
  name: string;
  description: string;
  category: PersonaCategory;
  template: Partial<ReaderPersona>;
  isPopular: boolean;
  usageCount: number;
}

// Store State and Actions
export interface PersonaState {
  personas: ReaderPersona[];
  selectedPersonas: string[];
  templates: PersonaTemplate[];
  feedback: PersonaFeedback[];
  insights: PersonaInsights | null;
  
  // UI State
  activeFeedbackMode: 'realtime' | 'onDemand' | 'scheduled';
  maxConcurrentPersonas: number;
  showConsensusOnly: boolean;
  
  lastUpdated: Date;
}

export interface PersonaActions {
  // Persona Management
  createPersona: (persona: Omit<ReaderPersona, 'id' | 'createdAt' | 'updatedAt'>) => ReaderPersona;
  updatePersona: (id: string, updates: Partial<ReaderPersona>) => void;
  deletePersona: (id: string) => void;
  duplicatePersona: (id: string) => void;
  togglePersonaActive: (id: string) => void;
  
  // Selection
  selectPersona: (id: string) => void;
  toggleSelectedPersona: (id: string) => void;
  deselectPersona: (id: string) => void;
  selectAllPersonas: () => void;
  deselectAllPersonas: () => void;
  
  // Templates
  createTemplate: (template: Omit<PersonaTemplate, 'id' | 'usageCount'>) => void;
  useTemplate: (templateId: string, customizations?: Partial<ReaderPersona>) => void;
  
  // Feedback Generation
  generateFeedback: (documentId: string, content: string, selectedPersonas?: string[]) => Promise<PersonaFeedback[]>;
  generateInsights: (documentId: string) => Promise<PersonaInsights>;
  clearFeedback: (documentId?: string) => void;
  
  // Import/Export
  exportPersonas: (personaIds?: string[]) => string;
  importPersonas: (data: string) => void;
  
  // Settings
  updateSettings: (settings: Partial<Pick<PersonaState, 'activeFeedbackMode' | 'maxConcurrentPersonas' | 'showConsensusOnly'>>) => void;
  
  // Getters
  getPersona: (id: string) => ReaderPersona | undefined;
  getActivePersonas: () => ReaderPersona[];
  getPersonasByCategory: (category: PersonaCategory) => ReaderPersona[];
  getFeedbackForDocument: (documentId: string) => PersonaFeedback[];
}

export type PersonaStore = PersonaState & PersonaActions;

// Chart and Visualization Data
export interface PersonaAnalyticsData {
  engagementByPersona: { personaId: string; score: number; name: string }[];
  reactionDistribution: { reaction: PersonaReaction; count: number; percentage: number }[];
  feedbackByCategory: { category: FeedbackCategory; count: number; severity: CommentType }[];
  audienceCompatibility: { score: number; personaName: string; category: PersonaCategory }[];
  consensusLevel: number; // 0-100 how much personas agree
}

// Real-time Feedback Events
export interface PersonaFeedbackEvent {
  type: 'feedback-generated' | 'insights-updated' | 'persona-reaction';
  documentId: string;
  personaId?: string;
  data: PersonaFeedback | PersonaInsights | PersonaReaction;
  timestamp: Date;
}