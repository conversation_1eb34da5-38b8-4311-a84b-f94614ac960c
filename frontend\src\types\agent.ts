// Agent capability types
export type AgentAction = 'edit' | 'delete' | 'add' | 'restructure' | 'find-duplicates' | 'fact-check' | 'tone-adjust';

export interface AgentCapability {
  id: string;
  action: AgentAction;
  name: string;
  description: string;
  enabled: boolean;
}

// Style rule types
export type RuleType = 'grammar' | 'style' | 'formatting' | 'vocabulary' | 'structure';

export interface StyleRule {
  id: string;
  name: string;
  type: RuleType;
  description: string;
  rule: string;
  examples?: string[];
  enabled: boolean;
  severity: 'error' | 'warning' | 'suggestion';
}

// Agent analysis tools and capabilities
export interface AgentTool {
  id: string;
  name: string;
  description: string;
  category: 'grammar' | 'style' | 'structure' | 'content' | 'verification' | 'optimization';
  enabled: boolean;
  config?: Record<string, any>;
}

// Pre-defined style rules (kept for backward compatibility but enhanced)
export interface PredefinedRules {
  oxfordComma: boolean;
  sentenceLength: {
    max: number;
    preferred: number;
  };
  passiveVoice: {
    maxPercentage: number;
  };
  paragraphLength: {
    maxSentences: number;
  };
  vocabulary: {
    preferredTerms: Record<string, string>;
    avoidTerms: string[];
  };
}

// Main agent interface
export interface CustomAgent {
  id: string;
  name: string;
  description: string;
  specialty: string;
  color: string;
  icon?: string;
  systemPrompt: string;
  capabilities: AgentCapability[];
  tools?: AgentTool[]; // New: flexible tools instead of just style rules
  styleRules: StyleRule[];
  predefinedRules: PredefinedRules;
  customInstructions: string;
  examples?: {
    input: string;
    output: string;
    explanation: string;
  }[];
  isBuiltIn: boolean;
  isActive: boolean;
  priority: number; // For ordering agents
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

// Agent template for quick setup
export interface AgentTemplate {
  id: string;
  name: string;
  description: string;
  category: 'academic' | 'business' | 'creative' | 'technical' | 'general';
  baseAgent: Partial<CustomAgent>;
  thumbnail?: string;
}

// Agent analysis result
export interface AgentAnalysisResult {
  agentId: string;
  agentName: string;
  findings: {
    id: string;
    type: AgentAction;
    severity: 'low' | 'medium' | 'high' | 'critical';
    location: {
      start: number;
      end: number;
      line?: number;
    };
    originalText: string;
    suggestion?: string;
    explanation: string;
    confidence: number;
  }[];
  summary: {
    totalIssues: number;
    byType: Record<AgentAction, number>;
    bySeverity: Record<string, number>;
  };
}