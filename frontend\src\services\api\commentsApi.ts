/**
 * Comments API Service
 * 
 * Handles all comment-related API calls including:
 * - Comment CRUD operations
 * - Threaded comment management
 * - Comment resolution tracking
 * - Real-time comment updates
 */

import { baseApi, ApiResponse } from './baseApi';

export interface Comment {
  id: string;
  block_id: string;
  author_id: string;
  parent_id?: string;
  content: string;
  position?: {
    start: number;
    end: number;
    line?: number;
  };
  type: 'general' | 'suggestion' | 'question' | 'issue';
  status: 'open' | 'resolved' | 'archived';
  resolved_by?: string;
  resolved_at?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  
  // Populated fields
  author?: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  replies?: Comment[];
  reply_count?: number;
}

export interface CommentCreate {
  block_id: string;
  parent_id?: string;
  content: string;
  position?: {
    start: number;
    end: number;
    line?: number;
  };
  type?: Comment['type'];
}

export interface CommentUpdate {
  content?: string;
  status?: Comment['status'];
  type?: Comment['type'];
}

export interface CommentThread {
  root_comment: Comment;
  replies: Comment[];
  total_replies: number;
  unresolved_count: number;
}

export interface CommentFilters {
  type?: Comment['type'];
  status?: Comment['status'];
  author_id?: string;
  date_range?: {
    start: string;
    end: string;
  };
  include_resolved?: boolean;
  include_deleted?: boolean;
}

class CommentsApiService {
  /**
   * Get comments for a specific block
   */
  async getBlockComments(blockId: string, params?: {
    include_replies?: boolean;
    status?: Comment['status'];
    type?: Comment['type'];
  }): Promise<Comment[]> {
    const response = await baseApi.get<Comment[]>(`/blocks/${blockId}/comments`, { params });
    return response.data;
  }

  /**
   * Get comments as threaded structure
   */
  async getBlockCommentThreads(blockId: string, params?: {
    status?: Comment['status'];
    type?: Comment['type'];
    limit?: number;
  }): Promise<CommentThread[]> {
    const response = await baseApi.get<CommentThread[]>(`/blocks/${blockId}/comments/threads`, { params });
    return response.data;
  }

  /**
   * Get comments for a document
   */
  async getDocumentComments(documentId: string, filters?: CommentFilters): Promise<Comment[]> {
    const response = await baseApi.get<Comment[]>(`/documents/${documentId}/comments`, { 
      params: filters 
    });
    return response.data;
  }

  /**
   * Get a specific comment
   */
  async getComment(commentId: string, includeReplies = false): Promise<Comment> {
    const response = await baseApi.get<Comment>(`/comments/${commentId}`, {
      params: { include_replies: includeReplies }
    });
    return response.data;
  }

  /**
   * Create a new comment
   */
  async createComment(comment: CommentCreate): Promise<Comment> {
    const response = await baseApi.post<Comment>('/comments', comment);
    return response.data;
  }

  /**
   * Update a comment
   */
  async updateComment(commentId: string, updates: CommentUpdate): Promise<Comment> {
    const response = await baseApi.put<Comment>(`/comments/${commentId}`, updates);
    return response.data;
  }

  /**
   * Delete a comment (soft delete)
   */
  async deleteComment(commentId: string): Promise<{ success: boolean }> {
    const response = await baseApi.delete(`/comments/${commentId}`);
    return response.data;
  }

  /**
   * Resolve a comment
   */
  async resolveComment(commentId: string): Promise<Comment> {
    const response = await baseApi.post<Comment>(`/comments/${commentId}/resolve`);
    return response.data;
  }

  /**
   * Unresolve a comment
   */
  async unresolveComment(commentId: string): Promise<Comment> {
    const response = await baseApi.post<Comment>(`/comments/${commentId}/unresolve`);
    return response.data;
  }

  /**
   * Archive a comment
   */
  async archiveComment(commentId: string): Promise<Comment> {
    const response = await baseApi.post<Comment>(`/comments/${commentId}/archive`);
    return response.data;
  }

  /**
   * Get comment replies
   */
  async getCommentReplies(commentId: string): Promise<Comment[]> {
    const response = await baseApi.get<Comment[]>(`/comments/${commentId}/replies`);
    return response.data;
  }

  /**
   * Reply to a comment
   */
  async replyToComment(parentId: string, content: string): Promise<Comment> {
    // Get the parent comment to inherit block_id
    const parentComment = await this.getComment(parentId);
    
    const reply: CommentCreate = {
      block_id: parentComment.block_id,
      parent_id: parentId,
      content,
      type: 'general'
    };

    return this.createComment(reply);
  }

  /**
   * Batch resolve comments
   */
  async batchResolveComments(commentIds: string[]): Promise<{
    resolved: number;
    failed: number;
    errors?: string[];
  }> {
    const response = await baseApi.post('/comments/batch/resolve', { comment_ids: commentIds });
    return response.data;
  }

  /**
   * Batch archive comments
   */
  async batchArchiveComments(commentIds: string[]): Promise<{
    archived: number;
    failed: number;
    errors?: string[];
  }> {
    const response = await baseApi.post('/comments/batch/archive', { comment_ids: commentIds });
    return response.data;
  }

  /**
   * Get comment statistics for a document
   */
  async getDocumentCommentStats(documentId: string): Promise<{
    total_comments: number;
    comments_by_status: Record<Comment['status'], number>;
    comments_by_type: Record<Comment['type'], number>;
    unresolved_count: number;
    recent_activity: Comment[];
  }> {
    const response = await baseApi.get(`/documents/${documentId}/comments/stats`);
    return response.data;
  }

  /**
   * Search comments
   */
  async searchComments(query: string, filters?: CommentFilters & {
    document_id?: string;
    block_id?: string;
  }): Promise<Comment[]> {
    const response = await baseApi.post<Comment[]>('/comments/search', {
      query,
      filters
    });
    return response.data;
  }
}

// Export singleton instance
export const commentsApi = new CommentsApiService();
export default commentsApi;