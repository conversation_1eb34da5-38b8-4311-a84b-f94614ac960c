-- Revisionary Mock Data - Part 8: Citations
-- Academic and web citations in documents

DO $$
DECLARE
    -- Get user IDs from firebase_uid (from 001_core_data.sql)
    sarah_id UUID;
    kim_id UUID;
    james_id UUID;
    
    -- Get document IDs by title (from 001_core_data.sql)
    sarah_climate_doc_id UUID;
    kim_neural_doc_id UUID;
    kim_collab_doc_id UUID;
    james_q4_doc_id UUID;
    
    -- Get block IDs for citations
    sarah_climate_para1_id UUID;
    kim_neural_para_id UUID;
    james_q4_para_id UUID;
    
BEGIN
    -- Get user IDs from existing users
    SELECT id INTO sarah_id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1;
    SELECT id INTO kim_id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1;
    SELECT id INTO james_id FROM users WHERE firebase_uid = 'firebase_user_005' LIMIT 1;
    
    -- Get document IDs from existing documents
    SELECT id INTO sarah_climate_doc_id FROM documents WHERE title = 'Climate Change Research Paper' LIMIT 1;
    SELECT id INTO kim_neural_doc_id FROM documents WHERE title = 'Neural Networks in Medical Diagnosis' LIMIT 1;
    SELECT id INTO kim_collab_doc_id FROM documents WHERE title = 'Industry Research Collaboration' LIMIT 1;
    SELECT id INTO james_q4_doc_id FROM documents WHERE title = 'Q4 Marketing Strategy Report' LIMIT 1;
    
    -- Get block IDs
    SELECT id INTO sarah_climate_para1_id FROM blocks WHERE content LIKE 'Climate change represents one of the most pressing%' AND document_id = sarah_climate_doc_id LIMIT 1;
    SELECT id INTO kim_neural_para_id FROM blocks WHERE content LIKE 'The integration of artificial neural networks%' AND document_id = kim_neural_doc_id LIMIT 1;
    SELECT id INTO james_q4_para_id FROM blocks WHERE content LIKE 'Q4 2024 demonstrated exceptional growth%' AND document_id = james_q4_doc_id LIMIT 1;

    -- =================================================================
    -- CITATIONS - Academic and web citations in documents
    -- =================================================================
    
    INSERT INTO citations (document_id, block_id, citation_key, citation_type, title, authors, publication_year, journal_name, volume, issue, page_numbers, doi, url, isbn, citation_style, formatted_citation) 
    VALUES
    (sarah_climate_doc_id, sarah_climate_para1_id, 'johnson2023sea', 'journal_article', 
     'Global Sea Level Rise: Past, Present, and Future Projections', 
     '{"Johnson, M.K.", "Liu, X.", "Thompson, R.J."}', 2023, 'Climate Science Quarterly', '45', '3', '234-251', 
     '10.1016/j.clisci.2023.03.015', NULL, NULL, 'apa', 
     'Johnson, M. K., Liu, X., & Thompson, R. J. (2023). Global sea level rise: Past, present, and future projections. Climate Science Quarterly, 45(3), 234-251. https://doi.org/10.1016/j.clisci.2023.03.015'),
    
    (sarah_climate_doc_id, sarah_climate_para1_id, 'ipcc2023synthesis', 'report', 
     'Climate Change 2023: Synthesis Report', 
     '{"IPCC Working Group"}', 2023, NULL, NULL, NULL, NULL, NULL, 
     'https://www.ipcc.ch/report/ar6/syr/', NULL, 'apa', 
     'IPCC Working Group. (2023). Climate Change 2023: Synthesis Report. Intergovernmental Panel on Climate Change. https://www.ipcc.ch/report/ar6/syr/'),
    
    (kim_neural_doc_id, kim_neural_para_id, 'chen2022deep', 'journal_article', 
     'Deep Learning in Medical Image Analysis: A Comprehensive Survey', 
     '{"Chen, H.", "Patel, S.", "Kumar, A.", "Rodriguez, M."}', 2022, 'Medical AI Review', '12', '4', '145-189', 
     '10.1089/mai.2022.0156', NULL, NULL, 'ieee', 
     'H. Chen, S. Patel, A. Kumar, and M. Rodriguez, "Deep learning in medical image analysis: A comprehensive survey," Medical AI Review, vol. 12, no. 4, pp. 145-189, 2022, doi: 10.1089/mai.2022.0156.'),
    
    (kim_neural_doc_id, kim_neural_para_id, 'wang2023improving', 'conference_paper', 
     'Improving Diagnostic Accuracy with Ensemble Neural Networks', 
     '{"Wang, L.", "Davis, K."}', 2023, 'Proceedings of Medical AI Conference', NULL, NULL, '78-84', 
     '10.1145/3583678.3583689', NULL, NULL, 'ieee', 
     'L. Wang and K. Davis, "Improving diagnostic accuracy with ensemble neural networks," in Proc. Medical AI Conf., 2023, pp. 78-84, doi: 10.1145/3583678.3583689.'),
    
    (kim_collab_doc_id, kim_neural_para_id, 'martinez2022ai', 'book', 
     'Artificial Intelligence in Healthcare: Implementation and Ethics', 
     '{"Martinez, C.", "Thompson, D.K."}', 2022, NULL, NULL, NULL, '120-145', NULL, NULL, '978-0-123456-78-9', 'apa', 
     'Martinez, C., & Thompson, D. K. (2022). Artificial Intelligence in Healthcare: Implementation and Ethics (2nd ed.). Academic Press.'),
    
    (james_q4_doc_id, james_q4_para_id, 'mri2024trends', 'website', 
     'Digital Marketing Trends 2024: Analytics and Performance', 
     '{"Marketing Research Institute"}', 2024, NULL, NULL, NULL, NULL, NULL, 
     'https://marketingresearch.org/trends-2024-analytics', NULL, 'apa', 
     'Marketing Research Institute. (2024, January 15). Digital marketing trends 2024: Analytics and performance. https://marketingresearch.org/trends-2024-analytics');

END $$;