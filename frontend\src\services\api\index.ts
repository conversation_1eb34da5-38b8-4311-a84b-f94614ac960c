/**
 * API Services Index
 * 
 * Central export point for all API services
 */

// Base API service
export { default as baseApi } from './baseApi';
export type { ApiResponse, ApiError } from './baseApi';

// Authentication API
export { default as authApi } from './authApi';
export type { 
  User, 
  TokenResponse, 
  UserPreferences 
} from './authApi';

// Documents API
export { default as documentsApi } from './documentsApi';
export type { 
  Document, 
  DocumentCreate, 
  DocumentUpdate,
  DocumentListResponse,
  DocumentTemplate,
  DocumentStats,
  DocumentVersion,
  DocumentCollaboration
} from './documentsApi';

// Blocks API
export { default as blocksApi } from './blocksApi';
export type { 
  Block, 
  BlockCreate, 
  BlockUpdate,
  BlockStructure,
  BlockStatistics,
  AutoSaveData
} from './blocksApi';

// Health API
export { default as healthApi } from './healthApi';
export type { 
  HealthMetrics, 
  HealthAnalysisRequest,
  HealthIssue,
  HealthTrends,
  HealthPreferences
} from './healthApi';

// Personas API
export { default as personasApi } from './personasApi';
export type { 
  Persona, 
  PersonaCreate, 
  PersonaUpdate,
  PersonaFeedback,
  PersonaFeedbackRequest,
  MultiFeedbackRequest,
  MultiFeedbackResponse,
  PersonaTemplate,
  PersonaAnalytics
} from './personasApi';

// Agents API
export { default as agentsApi } from './agentsApi';
export type { 
  CustomAgent, 
  AgentCreate, 
  AgentUpdate,
  AgentExecution,
  AgentExecutionRequest,
  AgentBatchRequest,
  AgentBatchResponse,
  AgentTemplate,
  AgentAnalytics
} from './agentsApi';

// Suggestions API
export { default as suggestionsApi } from './suggestionsApi';
export type {
  AISuggestion,
  SuggestionRequest,
  SuggestionResponse,
  SuggestionJob
} from './suggestionsApi';

// Comments API
export { default as commentsApi } from './commentsApi';
export type {
  Comment,
  CommentCreate,
  CommentUpdate,
  CommentThread,
  CommentFilters
} from './commentsApi';

// Analytics API
export { default as analyticsApi } from './analyticsApi';
export type {
  TokenUsage,
  WritingSession,
  WritingGoal,
  UserAchievement,
  DashboardSummary
} from './analyticsApi';

// Developer API
export { developerApi } from './developerApi';
export type {
  DocumentationFile,
  DocumentationContent,
  ApiStats,
  CodeExample
} from './developerApi';

// WebSocket Service
export { default as websocketService } from '../websocketService';
export type {
  DocumentCollaborationEvent,
  HealthAnalysisEvent,
  PersonaFeedbackEvent,
  AgentExecutionEvent,
  SystemNotificationEvent,
  UserPresence
} from '../websocketService';

// Import services for convenience object
import suggestionsApi from './suggestionsApi';
import commentsApi from './commentsApi';
import analyticsApi from './analyticsApi';
import { developerApi } from './developerApi';

// Re-export all services as a single object for convenience
// Using lazy initialization to avoid circular dependency issues
export const api = {
  get auth() { return authApi; },
  get documents() { return documentsApi; },
  get blocks() { return blocksApi; },
  get health() { return healthApi; },
  get personas() { return personasApi; },
  get agents() { return agentsApi; },
  get suggestions() { return suggestionsApi; },
  get comments() { return commentsApi; },
  get analytics() { return analyticsApi; },
  get developer() { return developerApi; },
  get websocket() { return websocketService; },
};

export default api;