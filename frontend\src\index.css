@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }

  body {
    @apply bg-white text-gray-900 antialiased;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    body {
      @apply bg-gray-900 text-gray-100;
    }

    ::-webkit-scrollbar-track {
      @apply bg-gray-800;
    }

    ::-webkit-scrollbar-thumb {
      @apply bg-gray-600;
    }

    ::-webkit-scrollbar-thumb:hover {
      @apply bg-gray-500;
    }
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn-primary {
    @apply bg-purple-600 text-white hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed px-4 py-2 rounded-md font-medium transition-colors;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed px-4 py-2 rounded-md font-medium transition-colors;
  }

  .btn-ghost {
    @apply text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed px-4 py-2 rounded-md font-medium transition-colors;
  }

  /* Input styles */
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 focus:outline-none disabled:bg-gray-50 disabled:text-gray-500;
  }

  /* Editor styles */
  .editor-container {
    @apply bg-white border border-gray-200 rounded-lg shadow-sm;
  }

  .editor-toolbar {
    @apply flex items-center space-x-2 px-4 py-2 border-b border-gray-200 bg-gray-50;
  }

  .editor-content {
    @apply min-h-96 p-4 focus:outline-none;
  }

  /* AI suggestion styles */
  .suggestion-card {
    @apply bg-blue-50 border border-blue-200 rounded-lg p-4 space-y-2;
  }

  .suggestion-stale {
    @apply bg-yellow-50 border-yellow-200 relative;
  }

  .suggestion-stale::before {
    content: '';
    @apply absolute top-0 left-0 w-1 h-full bg-yellow-400 rounded-l-lg;
  }

  /* Token budget widget */
  .token-budget-low {
    @apply text-red-600 bg-red-50 border-red-200;
  }

  .token-budget-medium {
    @apply text-yellow-600 bg-yellow-50 border-yellow-200;
  }

  .token-budget-high {
    @apply text-green-600 bg-green-50 border-green-200;
  }

  /* Loading states */
  .loading-skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  .loading-dots::after {
    content: '';
    animation: dots 1.5s steps(5, end) infinite;
  }

  /* Responsive text sizes */
  .text-responsive-sm {
    @apply text-sm md:text-base;
  }

  .text-responsive-lg {
    @apply text-lg md:text-xl lg:text-2xl;
  }

  /* Focus visible for accessibility */
  .focus-visible:focus-visible {
    @apply ring-2 ring-purple-500 ring-offset-2 outline-none;
  }

  /* Advanced Editor Styles */
  .editor-content {
    font-variant-ligatures: common-ligatures;
    font-feature-settings: "liga" 1, "calt" 1;
  }

  .editor-content h1 {
    @apply text-4xl font-bold text-slate-900 mt-8 mb-4 leading-tight;
  }

  .editor-content h2 {
    @apply text-3xl font-bold text-slate-800 mt-7 mb-3 leading-tight;
  }

  .editor-content h3 {
    @apply text-2xl font-semibold text-slate-800 mt-6 mb-3 leading-tight;
  }

  .editor-content p {
    @apply text-lg text-slate-700 leading-relaxed mb-4;
  }

  .editor-content blockquote {
    @apply border-l-4 border-purple-500 pl-6 py-2 my-6 bg-purple-50 italic text-slate-700 rounded-r-lg;
  }

  .editor-content ul, .editor-content ol {
    @apply ml-6 mb-4 space-y-2;
  }

  .editor-content li {
    @apply text-lg text-slate-700 leading-relaxed;
  }

  .editor-content code {
    @apply bg-slate-100 text-purple-700 px-2 py-1 rounded text-sm font-mono;
  }

  .editor-content pre {
    @apply bg-slate-900 text-green-400 p-4 rounded-xl overflow-x-auto my-6 font-mono text-sm;
  }

  .editor-content a {
    @apply text-purple-600 underline hover:text-purple-800 transition-colors;
  }

  /* Glass morphism effects */
  .glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  /* Gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Magnetic hover effect */
  .magnetic {
    transition: transform 0.2s ease-out;
  }

  .magnetic:hover {
    transform: translate3d(var(--magnetic-x, 0), var(--magnetic-y, 0), 0);
  }

  /* Neon glow effect */
  .neon-glow {
    text-shadow: 0 0 5px rgba(139, 92, 246, 0.5),
                 0 0 10px rgba(139, 92, 246, 0.5),
                 0 0 15px rgba(139, 92, 246, 0.5);
  }

  /* Parallax scroll effect */
  .parallax {
    transform: translateZ(0);
    will-change: transform;
  }
}

/* Utility classes */
@layer utilities {
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.15s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-pulse-soft {
    animation: pulseSoft 2s ease-in-out infinite;
  }

  .animate-blob {
    animation: blob 7s infinite;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-shimmer {
    animation: shimmer 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  /* Layout utilities */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Typography utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Mobile-specific utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }
  
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  .mobile-full-height {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
  }
  
  .mobile-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
  
  .mobile-keyboard-resize {
    height: 100vh;
    height: 100dvh;
    resize: none;
  }
  
  .mobile-scroll-lock {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }
  
  .mobile-modal {
    @apply fixed inset-0 z-50 overflow-y-auto;
  }
  
  .mobile-bottom-sheet {
    @apply fixed bottom-0 left-0 right-0 z-50 max-h-[85vh] overflow-y-auto;
  }
  
  .mobile-tab-bar {
    @apply fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200;
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  /* Mobile-optimized text sizes */
  .text-mobile-xs {
    @apply text-xs leading-4;
  }
  
  .text-mobile-sm {
    @apply text-sm leading-5;
  }
  
  .text-mobile-base {
    @apply text-base leading-6;
  }
  
  .text-mobile-lg {
    @apply text-lg leading-7;
  }
  
  .text-mobile-xl {
    @apply text-xl leading-8;
  }
  
  /* Mobile spacing utilities */
  .mobile-px {
    @apply px-4;
  }
  
  .mobile-py {
    @apply py-3;
  }
  
  .mobile-p {
    @apply p-4;
  }
  
  /* Mobile grid utilities */
  .mobile-grid-1 {
    @apply grid-cols-1 sm:grid-cols-2 md:grid-cols-3;
  }
  
  .mobile-grid-2 {
    @apply grid-cols-2 sm:grid-cols-3 md:grid-cols-4;
  }
  
  /* Mobile button utilities */
  .mobile-btn {
    @apply touch-target px-6 py-3 text-base font-medium rounded-lg transition-colors;
  }
  
  .mobile-icon-btn {
    @apply touch-target flex items-center justify-center p-2 rounded-lg transition-colors;
  }
}

/* Keyframe animations */
@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulseSoft {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(139, 92, 246, 0.6);
  }
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink {
  from, to { border-color: transparent }
  50% { border-color: rgba(139, 92, 246, 0.8) }
}

@keyframes sparkle {
  0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
  50% { opacity: 1; transform: scale(1) rotate(180deg); }
}

@keyframes wave {
  0%, 100% { transform: translateY(0px); }
  25% { transform: translateY(-10px); }
  50% { transform: translateY(0px); }
  75% { transform: translateY(-5px); }
}

@keyframes morphing {
  0%, 100% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
  25% { border-radius: 30% 70% 40% 60% / 70% 60% 40% 30%; }
  50% { border-radius: 70% 30% 60% 40% / 40% 70% 30% 60%; }
  75% { border-radius: 40% 60% 70% 30% / 30% 40% 60% 70%; }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes levitate {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(-5px) rotate(-1deg); }
}

/* Mobile styles */
@media (max-width: 768px) {
  /* Mobile-first responsive text */
  .editor-content h1 {
    @apply text-2xl font-bold;
  }
  
  .editor-content h2 {
    @apply text-xl font-bold;
  }
  
  .editor-content h3 {
    @apply text-lg font-semibold;
  }
  
  .editor-content p {
    @apply text-base leading-relaxed;
  }
  
  /* Mobile scrollbars - thinner */
  ::-webkit-scrollbar {
    @apply w-1;
  }
  
  /* Mobile modal optimization */
  .modal-content {
    @apply rounded-t-2xl rounded-b-none;
    max-height: 85vh;
  }
  
  /* Mobile editor adjustments */
  .editor-toolbar {
    @apply flex-wrap gap-1 px-2 py-2;
  }
  
  /* Mobile button sizes */
  .btn-primary, .btn-secondary, .btn-ghost {
    @apply mobile-btn;
  }
}

@media (max-width: 640px) {
  /* Extra small screens */
  .container {
    @apply px-4;
  }
  
  /* Stack flex items on very small screens */
  .mobile-stack {
    @apply flex-col space-y-2 space-x-0;
  }
  
  /* Full width buttons on mobile */
  .mobile-full-btn {
    @apply w-full justify-center;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .hover\:bg-gray-100:hover {
    background-color: transparent;
  }
  
  /* Increase touch targets */
  button, a, [role="button"] {
    @apply touch-target;
  }
  
  /* Optimize tap highlights */
  * {
    -webkit-tap-highlight-color: rgba(139, 92, 246, 0.1);
  }
}

/* Landscape phone orientation */
@media screen and (max-height: 500px) and (orientation: landscape) {
  .landscape-compact {
    @apply py-1;
  }
  
  .mobile-full-height {
    height: 100vh;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .editor-content {
    box-shadow: none !important;
    border: none !important;
  }
}