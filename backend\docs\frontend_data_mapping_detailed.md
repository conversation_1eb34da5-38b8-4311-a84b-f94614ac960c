
# Frontend Data Mapping (Detailed)

This document provides a detailed mapping of frontend files to the database tables and columns they interact with, based on the frontend's type definitions and state management stores.

## Data Stores

### `authStore.ts`

*   **Purpose:** Manages user authentication state.
*   **Related Tables:** `users`
*   **Data Points:**
    *   `User`: Maps to the `users` table.
        *   `id`: `users.firebase_uid`
        *   `email`: `users.email`
        *   `displayName`: `users.display_name`
        *   `photoURL`: `users.avatar_url`
        *   `subscriptionTier`: `users.subscription_tier`
        *   `tokenUsage`: Not directly mapped to a single column, but related to `usage_events` and `token_usage_daily`.
        *   `preferences`: `users.preferences`

### `agentStore.ts`

*   **Purpose:** Manages AI agents.
*   **Related Tables:** `custom_agents`, `agent_style_rules`, `agent_usage_stats`
*   **Data Points:**
    *   `CustomAgent`: Maps to the `custom_agents` table.
        *   `id`: `custom_agents.id`
        *   `name`: `custom_agents.name`
        *   `description`: `custom_agents.description`
        *   `specialty`: `custom_agents.type`
        *   `capabilities`: `custom_agents.capabilities`
        *   `styleRules`: Maps to the `agent_style_rules` table.

### `analyticsStore.ts`

*   **Purpose:** Manages user and document analytics.
*   **Related Tables:** `user_writing_stats`, `writing_achievements`, `document_scores`, `writing_challenges`, `usage_events`, `token_usage_daily`
*   **Data Points:**
    *   `WritingSession`: Related to `usage_events`.
    *   `DailyStats`: Maps to `user_writing_stats`.
    *   `WritingGoal`: Maps to `writing_challenges`.
    *   `Achievement`: Maps to `writing_achievements`.
    *   `DocumentAnalytics`: Maps to `document_scores`.

### `personaStore.ts`

*   **Purpose:** Manages reader personas.
*   **Related Tables:** `personas`, `persona_feedback`
*   **Data Points:**
    *   `ReaderPersona`: Maps to the `personas` table.
    *   `PersonaFeedback`: Maps to the `persona_feedback` table.

## Page Components

### `Analytics.tsx`

*   **Purpose:** Displays user analytics.
*   **Related Tables:** `user_writing_stats`, `writing_achievements`, `document_scores`, `writing_challenges`
*   **Data Points:**
    *   Uses `useAnalyticsStore` to display charts and statistics related to writing progress, goals, and achievements.

### `Dashboard.tsx`

*   **Purpose:** Main user dashboard.
*   **Related Tables:** `documents`, `user_writing_stats`, `writing_achievements`
*   **Data Points:**
    *   Displays a list of recent documents (`documents` table).
    *   Shows summary statistics from `user_writing_stats`.
    *   Shows recent achievements from `writing_achievements`.

### `Editor.tsx`

*   **Purpose:** The main document editor.
*   **Related Tables:** `documents`, `blocks`, `suggestions`, `comments`, `citations`, `versions`, `personas`, `custom_agents`
*   **Data Points:**
    *   Loads and saves document content to the `documents` and `blocks` tables.
    *   Displays AI suggestions from the `suggestions` table.
    *   Manages comments via the `comments` table.
    *   Handles citations, which are stored in the `citations` table.
    *   Interacts with version history, which is stored in the `versions` table.
    *   Uses `personas` and `custom_agents` for feedback and AI features.

### `Login.tsx` and `Signup.tsx`

*   **Purpose:** User authentication.
*   **Related Tables:** `users`
*   **Data Points:**
    *   Creates and authenticates users, which corresponds to entries in the `users` table.

### `Settings.tsx`

*   **Purpose:** User and application settings.
*   **Related Tables:** `users`, `custom_agents`, `personas`
*   **Data Points:**
    *   Updates user preferences in the `users` table.
    *   Manages custom AI agents in the `custom_agents` table.
    *   Manages reader personas in the `personas` table.
