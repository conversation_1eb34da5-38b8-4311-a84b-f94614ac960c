-- Revisionary Mock Data - Part 9: Audience Analysis
-- Document audience feedback analysis

DO $$
DECLARE
    -- Get user IDs from firebase_uid (from 001_core_data.sql)
    sarah_id UUID;
    alex_id UUID;
    kim_id UUID;
    
    -- Get document IDs by title (from 001_core_data.sql)
    sarah_climate_doc_id UUID;
    alex_cafe_doc_id UUID;
    kim_neural_doc_id UUID;
    
BEGIN
    -- Get user IDs from existing users
    SELECT id INTO sarah_id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1;
    SELECT id INTO alex_id FROM users WHERE firebase_uid = 'firebase_user_002' LIMIT 1;
    SELECT id INTO kim_id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1;
    
    -- Get document IDs from existing documents
    SELECT id INTO sarah_climate_doc_id FROM documents WHERE title = 'Climate Change Research Paper' LIMIT 1;
    SELECT id INTO alex_cafe_doc_id FROM documents WHERE title = 'The Midnight Café' LIMIT 1;
    SELECT id INTO kim_neural_doc_id FROM documents WHERE title = 'Neural Networks in Medical Diagnosis' LIMIT 1;

    -- =================================================================
    -- AUDIENCE ANALYSIS - Document audience feedback analysis
    -- =================================================================
    
    INSERT INTO audience_analysis (document_id, persona_ids, overall_appeal_score, audience_alignment, analysis_summary, optimization_suggestions) 
    VALUES
    (sarah_climate_doc_id, 
     ARRAY[(SELECT id FROM personas WHERE name = 'Environmental Researcher' AND user_id = sarah_id LIMIT 1)]::UUID[], 
     0.89, 
     '{"academic_researchers": {"appeal": 0.92, "clarity": 4.2, "relevance": 4.7}, "policy_makers": {"appeal": 0.85, "utility": 4.3, "actionability": 4.1}}'::jsonb, 
     'Strong academic reception with high policy relevance. Methodology approval excellent but accessibility for policy makers could be improved.',
     '["Add executive summary for policy makers", "Include more visual data representations", "Consider companion brief for general public"]'::jsonb),
    
    (alex_cafe_doc_id, 
     ARRAY[(SELECT id FROM personas WHERE name = 'Urban Fantasy Storyteller' AND user_id = alex_id LIMIT 1)]::UUID[], 
     0.85, 
     '{"urban_fantasy_readers": {"appeal": 0.88, "atmosphere": 4.8, "engagement": 4.6}, "young_adult": {"appeal": 0.82, "accessibility": 4.4, "pacing": 4.1}}'::jsonb, 
     'Excellent atmospheric writing with strong mystery engagement. Character development and pacing could be enhanced for broader appeal.',
     '["Expand sensory descriptions", "Develop Elena backstory hints", "Consider chapter breaks for pacing"]'::jsonb),
    
    (kim_neural_doc_id, 
     ARRAY[(SELECT id FROM personas WHERE name = 'Medical Research Scholar' AND user_id = kim_id LIMIT 1)]::UUID[], 
     0.93, 
     '{"medical_professionals": {"appeal": 0.95, "technical_accuracy": 4.9, "clinical_relevance": 4.6}, "ai_researchers": {"appeal": 0.91, "methodology": 4.8, "contribution": 4.7}}'::jsonb, 
     'Outstanding technical accuracy and research contribution. High potential for peer review and citation. Implementation focus could be strengthened.',
     '["Add implementation cost analysis", "Include more case studies", "Expand ethical considerations section"]'::jsonb);

END $$;