-- Revisionary Mock Data - Part 15: Writing Sessions and Goals
-- Mock data for analytics features

DO $$
DECLARE
    -- Get user IDs from firebase_uid
    sarah_id UUID;
    alex_id UUID;
    kim_id UUID;
    maya_id UUID;
    james_id UUID;
    
    -- Document IDs (from previous mock data)
    sarah_doc1 UUID;
    alex_doc1 UUID;
    kim_doc1 UUID;
    maya_doc1 UUID;
    james_doc1 UUID;
    
BEGIN
    -- Get user IDs
    SELECT id INTO sarah_id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1;
    SELECT id INTO alex_id FROM users WHERE firebase_uid = 'firebase_user_002' LIMIT 1;
    SELECT id INTO kim_id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1;
    SELECT id INTO maya_id FROM users WHERE firebase_uid = 'firebase_user_004' LIMIT 1;
    SELECT id INTO james_id FROM users WHERE firebase_uid = 'firebase_user_005' LIMIT 1;
    
    -- Get document IDs (first document for each user)
    SELECT id INTO sarah_doc1 FROM documents WHERE owner_id = sarah_id ORDER BY created_at LIMIT 1;
    SELECT id INTO alex_doc1 FROM documents WHERE owner_id = alex_id ORDER BY created_at LIMIT 1;
    SELECT id INTO kim_doc1 FROM documents WHERE owner_id = kim_id ORDER BY created_at LIMIT 1;
    SELECT id INTO maya_doc1 FROM documents WHERE owner_id = maya_id ORDER BY created_at LIMIT 1;
    SELECT id INTO james_doc1 FROM documents WHERE owner_id = james_id ORDER BY created_at LIMIT 1;

    -- =================================================================
    -- WRITING SESSIONS - Track actual writing activity
    -- =================================================================
    
    INSERT INTO writing_sessions (
        user_id, document_id, start_time, end_time, words_written, words_at_start,
        time_spent_minutes, ai_suggestions_accepted, ai_suggestions_dismissed,
        ai_generations_used, focus_mode_used, features_used, session_goal, target_word_count
    ) VALUES
    -- Sarah's sessions (academic writer)
    (sarah_id, sarah_doc1, CURRENT_TIMESTAMP - INTERVAL '7 days 2 hours', CURRENT_TIMESTAMP - INTERVAL '7 days', 
     850, 2340, 120, 12, 3, 5, FALSE, ARRAY['grammar', 'citations'], 'Complete methodology section', 1000),
    
    (sarah_id, sarah_doc1, CURRENT_TIMESTAMP - INTERVAL '5 days 3 hours', CURRENT_TIMESTAMP - INTERVAL '5 days 1 hour',
     1250, 3190, 120, 18, 2, 8, TRUE, ARRAY['grammar', 'style', 'citations'], 'Write results section', 1500),
    
    (sarah_id, sarah_doc1, CURRENT_TIMESTAMP - INTERVAL '2 days 4 hours', CURRENT_TIMESTAMP - INTERVAL '2 days 2 hours',
     680, 4440, 120, 8, 1, 3, FALSE, ARRAY['grammar', 'structure'], 'Edit introduction', NULL),
    
    -- Alex's sessions (creative writer)
    (alex_id, alex_doc1, CURRENT_TIMESTAMP - INTERVAL '10 days 1 hour', CURRENT_TIMESTAMP - INTERVAL '10 days',
     2150, 15230, 60, 5, 8, 12, TRUE, ARRAY['creative', 'style'], 'Write climax scene', 2000),
    
    (alex_id, alex_doc1, CURRENT_TIMESTAMP - INTERVAL '8 days 2 hours', CURRENT_TIMESTAMP - INTERVAL '8 days',
     1850, 17380, 120, 3, 5, 8, TRUE, ARRAY['creative', 'character'], 'Develop antagonist backstory', 2000),
    
    -- Kim's sessions (research papers)
    (kim_id, kim_doc1, CURRENT_TIMESTAMP - INTERVAL '3 days 5 hours', CURRENT_TIMESTAMP - INTERVAL '3 days 2 hours',
     1420, 8900, 180, 22, 4, 15, TRUE, ARRAY['research', 'citations', 'technical'], 'Complete literature review', 1500),
    
    (kim_id, kim_doc1, CURRENT_TIMESTAMP - INTERVAL '1 day 3 hours', CURRENT_TIMESTAMP - INTERVAL '1 day 1 hour',
     980, 10320, 120, 15, 2, 10, FALSE, ARRAY['grammar', 'citations'], 'Revise abstract', 1000),
    
    -- Maya's sessions (novel writing)
    (maya_id, maya_doc1, CURRENT_TIMESTAMP - INTERVAL '6 days 4 hours', CURRENT_TIMESTAMP - INTERVAL '6 days 1 hour',
     3250, 45200, 180, 8, 12, 20, TRUE, ARRAY['creative', 'world_building', 'character'], 'Write chapter 12', 3000),
    
    (maya_id, maya_doc1, CURRENT_TIMESTAMP - INTERVAL '4 days 2 hours', CURRENT_TIMESTAMP - INTERVAL '4 days',
     2100, 48450, 120, 6, 8, 15, TRUE, ARRAY['creative', 'dialogue', 'pacing'], 'Edit dialogue in chapter 10', NULL),
    
    -- James's sessions (business writing)
    (james_id, james_doc1, CURRENT_TIMESTAMP - INTERVAL '2 days 6 hours', CURRENT_TIMESTAMP - INTERVAL '2 days 3 hours',
     1100, 3200, 180, 14, 1, 8, FALSE, ARRAY['business', 'clarity', 'structure'], 'Draft executive summary', 1200),
    
    (james_id, james_doc1, CURRENT_TIMESTAMP - INTERVAL '1 day 4 hours', CURRENT_TIMESTAMP - INTERVAL '1 day 2 hours',
     890, 4300, 120, 10, 0, 6, FALSE, ARRAY['business', 'tone', 'conciseness'], 'Polish recommendations', 1000);

    -- =================================================================
    -- WRITING GOALS - User-defined targets
    -- =================================================================
    
    INSERT INTO writing_goals (user_id, type, target, current, status, description, start_date, end_date) VALUES
    -- Sarah's goals
    (sarah_id, 'daily_words', 500, 350, 'active', 'Write at least 500 words daily for thesis', 
     CURRENT_DATE - INTERVAL '14 days', NULL),
    
    (sarah_id, 'weekly_words', 3000, 2780, 'active', 'Complete 3000 words per week',
     CURRENT_DATE - INTERVAL '7 days', CURRENT_DATE),
    
    -- Alex's goals
    (alex_id, 'daily_words', 1000, 850, 'active', 'NaNoWriMo daily target',
     CURRENT_DATE - INTERVAL '30 days', CURRENT_DATE + INTERVAL '20 days'),
    
    (alex_id, 'weekly_time', 600, 480, 'active', 'Write for 10 hours per week',
     CURRENT_DATE - INTERVAL '7 days', CURRENT_DATE),
    
    -- Kim's goals
    (kim_id, 'weekly_words', 5000, 4200, 'active', 'Research paper progress',
     CURRENT_DATE - INTERVAL '21 days', CURRENT_DATE + INTERVAL '7 days'),
    
    (kim_id, 'daily_time', 120, 90, 'active', 'Write for 2 hours daily',
     CURRENT_DATE - INTERVAL '7 days', NULL),
    
    -- Maya's goals
    (maya_id, 'daily_words', 2000, 2100, 'completed', 'Professional writer daily target',
     CURRENT_DATE - INTERVAL '30 days', CURRENT_DATE - INTERVAL '1 day'),
    
    (maya_id, 'weekly_words', 10000, 8500, 'active', 'Novel completion sprint',
     CURRENT_DATE - INTERVAL '7 days', CURRENT_DATE + INTERVAL '7 days'),
    
    -- James's goals
    (james_id, 'weekly_words', 4000, 3800, 'active', 'Business plan completion',
     CURRENT_DATE - INTERVAL '14 days', CURRENT_DATE + INTERVAL '14 days'),
    
    (james_id, 'daily_time', 90, 75, 'paused', 'Daily writing habit',
     CURRENT_DATE - INTERVAL '30 days', NULL);

    -- Update achieved_at for completed goals
    UPDATE writing_goals 
    SET achieved_at = CURRENT_TIMESTAMP - INTERVAL '1 day'
    WHERE status = 'completed';

    -- =================================================================
    -- USER ACHIEVEMENTS - Unlocked achievements
    -- =================================================================
    
    INSERT INTO user_achievements (user_id, achievement_id, unlocked_at, progress_data) VALUES
    -- Sarah's achievements
    (sarah_id, 'first_document', CURRENT_TIMESTAMP - INTERVAL '30 days', '{"documents_created": 1}'::jsonb),
    (sarah_id, 'early_bird', CURRENT_TIMESTAMP - INTERVAL '15 days', '{"sessions_before_6am": 5}'::jsonb),
    
    -- Alex's achievements
    (alex_id, 'first_document', CURRENT_TIMESTAMP - INTERVAL '45 days', '{"documents_created": 1}'::jsonb),
    (alex_id, 'night_owl', CURRENT_TIMESTAMP - INTERVAL '20 days', '{"sessions_after_midnight": 8}'::jsonb),
    (alex_id, 'week_streak', CURRENT_TIMESTAMP - INTERVAL '10 days', '{"consecutive_days": 7}'::jsonb),
    
    -- Kim's achievements
    (kim_id, 'first_document', CURRENT_TIMESTAMP - INTERVAL '60 days', '{"documents_created": 1}'::jsonb),
    (kim_id, 'ai_collaborator', CURRENT_TIMESTAMP - INTERVAL '25 days', '{"suggestions_accepted": 52}'::jsonb),
    (kim_id, 'quality_master', CURRENT_TIMESTAMP - INTERVAL '5 days', '{"quality_score": 92.5}'::jsonb),
    
    -- Maya's achievements
    (maya_id, 'first_document', CURRENT_TIMESTAMP - INTERVAL '90 days', '{"documents_created": 1}'::jsonb),
    (maya_id, 'marathon_writer', CURRENT_TIMESTAMP - INTERVAL '30 days', '{"daily_words": 10250}'::jsonb),
    (maya_id, 'speed_demon', CURRENT_TIMESTAMP - INTERVAL '20 days', '{"words_per_minute": 35}'::jsonb),
    (maya_id, 'prolific_author', CURRENT_TIMESTAMP - INTERVAL '10 days', '{"documents_completed": 10}'::jsonb),
    
    -- James's achievements
    (james_id, 'first_document', CURRENT_TIMESTAMP - INTERVAL '45 days', '{"documents_created": 1}'::jsonb),
    (james_id, 'perfectionist', CURRENT_TIMESTAMP - INTERVAL '15 days', '{"grammar_score": 100}'::jsonb),
    (james_id, 'ai_collaborator', CURRENT_TIMESTAMP - INTERVAL '7 days', '{"suggestions_accepted": 68}'::jsonb);

END $$;