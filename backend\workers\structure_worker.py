"""
Structure Worker for Revisionary

Purpose: Specialized worker for document structure analysis
- Uses GPT-4.1-mini for structural analysis and organization
- Focuses on logical flow, hierarchy, transitions
- Provides structural improvements and reorganization suggestions
- Supports different document types and formats

Features:
- Document flow analysis
- Hierarchy and organization assessment
- Transition quality evaluation
- Structural reorganization suggestions
- Outline generation and improvement
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import structlog

from workers.base_worker import BaseWorker

logger = structlog.get_logger(__name__)


class StructureWorker(BaseWorker):
    """Worker specialized for document structure analysis."""
    
    def __init__(self, worker_id: Optional[str] = None):
        """Initialize structure worker."""
        super().__init__('structure', worker_id)
        
        # Structure-specific configuration
        self.max_text_length = 3000  # More context needed for structural analysis
        self.prompt_templates = self._load_prompt_templates()
        
        logger.info(f"Structure worker {self.worker_id} initialized")
    
    def _load_prompt_templates(self) -> Dict[str, str]:
        """Load optimized prompt templates for structure analysis."""
        return {
            'structure_analysis': """Analyze the document structure and provide improvement suggestions.

Text: {text}

Document Type: {doc_type}

Analyze:
1. Logical flow and organization
2. Paragraph structure and length
3. Transitions between sections
4. Information hierarchy
5. Missing or misplaced content

Provide:
Structure Score: [1-10]

Issues:
- [specific structural problem 1]
- [specific structural problem 2]

Improvements:
- [specific suggestion 1]
- [specific suggestion 2]

Organization Strengths:
- [what works well structurally]""",
            
            'flow_analysis': """Analyze the logical flow and coherence of this text.

Text: {text}

Evaluate:
1. Logical progression of ideas
2. Coherence between paragraphs
3. Transition quality
4. Missing connections
5. Information gaps

Format:
Flow Score: [1-10]
Coherence Issues: [list problems]
Missing Transitions: [identify where needed]
Suggested Reorganization: [specific recommendations]""",
            
            'outline_generation': """Create a detailed outline for this content to improve its structure.

Text: {text}

Content Type: {content_type}

Generate:
1. Hierarchical outline (main points, subpoints)
2. Suggested section titles
3. Content redistribution recommendations
4. Missing sections that should be added

Format:
Proposed Outline:
I. [Main section 1]
   A. [Subsection]
   B. [Subsection]
II. [Main section 2]
   A. [Subsection]

Redistribution Notes:
- [how to reorganize existing content]

Missing Elements:
- [what should be added]""",
            
            'paragraph_analysis': """Analyze paragraph structure and suggest improvements.

Text: {text}

Evaluate each paragraph for:
1. Length appropriateness
2. Focus and unity
3. Topic sentence clarity
4. Supporting detail organization
5. Conclusion effectiveness

Format:
Paragraph Assessment:
Para 1: [analysis and suggestions]
Para 2: [analysis and suggestions]
...

Overall Recommendations:
- [general paragraph improvements]""",
            
            'hierarchy_check': """Analyze information hierarchy and heading structure.

Text: {text}

Check for:
1. Heading consistency and levels
2. Information prioritization
3. Visual hierarchy effectiveness
4. Content categorization
5. Navigation clarity

Format:
Hierarchy Score: [1-10]
Heading Issues: [problems with current headings]
Prioritization Problems: [information ordering issues]
Recommended Structure:
[suggested heading hierarchy]

Content Categorization:
- [how to better group information]""",
            
            'transition_improvement': """Improve transitions between sections and paragraphs.

Text: {text}

Current Transitions: [analyze existing transitions]
Missing Connections: [identify where transitions needed]

Suggested Transitions:
Between Para 1-2: [suggested transition]
Between Para 2-3: [suggested transition]
...

Transition Types Needed:
- [chronological, cause-effect, comparison, etc.]"""
        }
    
    async def process_job(self, job: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a structure analysis job.
        
        Args:
            job: Job data containing text and structure analysis options
            
        Returns:
            Dict with structure analysis results and suggestions
        """
        job_id = job.get('job_id', 'unknown')
        text = job.get('text', '')
        options = job.get('options', {})
        
        # Validate input
        if not text or not text.strip():
            return {
                'job_id': job_id,
                'success': False,
                'error': 'Empty text provided'
            }
        
        # Limit text length for cost control
        original_text = text
        if len(text) > self.max_text_length:
            text = text[:self.max_text_length] + "..."
            logger.info(f"Text truncated for cost control", 
                       original_length=len(original_text),
                       truncated_length=len(text))
        
        try:
            # Determine analysis type
            analysis_type = options.get('analysis_type', 'structure_analysis')
            doc_type = options.get('doc_type', 'general document')
            content_type = options.get('content_type', 'article')
            
            # Choose appropriate prompt
            if analysis_type == 'flow_analysis':
                prompt = self.prompt_templates['flow_analysis'].format(text=text)
            elif analysis_type == 'outline_generation':
                prompt = self.prompt_templates['outline_generation'].format(
                    text=text, 
                    content_type=content_type
                )
            elif analysis_type == 'paragraph_analysis':
                prompt = self.prompt_templates['paragraph_analysis'].format(text=text)
            elif analysis_type == 'hierarchy_check':
                prompt = self.prompt_templates['hierarchy_check'].format(text=text)
            elif analysis_type == 'transition_improvement':
                prompt = self.prompt_templates['transition_improvement'].format(text=text)
            else:
                prompt = self.prompt_templates['structure_analysis'].format(
                    text=text, 
                    doc_type=doc_type
                )
            
            # Get completion from LLM service
            result = await self.llm_service.get_completion(
                agent_type='structure',
                prompt=prompt,
                context={
                    'job_id': job_id,
                    'text_length': len(text),
                    'analysis_type': analysis_type,
                    'doc_type': doc_type,
                    'options': options
                }
            )
            
            # Parse the response
            response_text = result['text'].strip()
            analysis_result = self._parse_structure_response(response_text, analysis_type)
            
            # Track cost
            await self.cost_tracker.track_usage(
                model=result['model'],
                usage=result['usage'],
                agent_type='structure',
                user_id=job.get('user_id')
            )
            
            # Build result
            structure_result = {
                'job_id': job_id,
                'success': True,
                'original_text': original_text,
                'analysis_type': analysis_type,
                'doc_type': doc_type,
                'analysis_result': analysis_result,
                'processing_time': result.get('latency', 0),
                'tokens_used': result['usage']['input_tokens'] + result['usage']['output_tokens'],
                'cached_tokens': result['usage'].get('cached_tokens', 0),
                'model_used': result['model']
            }
            
            # Calculate confidence score
            structure_result['confidence'] = self._calculate_confidence(result, analysis_type)
            
            logger.info(
                f"Structure job {job_id} completed",
                analysis_type=analysis_type,
                tokens_used=structure_result['tokens_used'],
                processing_time=structure_result['processing_time']
            )
            
            return structure_result
            
        except Exception as e:
            logger.error(f"Structure job {job_id} failed", error=str(e), exc_info=True)
            return {
                'job_id': job_id,
                'success': False,
                'error': str(e),
                'original_text': original_text,
                'analysis_type': analysis_type
            }
    
    def _parse_structure_response(self, response: str, analysis_type: str) -> Dict[str, Any]:
        """Parse the structure analysis response."""
        analysis_result = {
            'raw_response': response,
            'analysis_type': analysis_type
        }
        
        try:
            lines = response.split('\n')
            current_section = None
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # Parse different sections based on analysis type
                if 'Score:' in line:
                    score_text = line.split('Score:')[-1].strip()
                    try:
                        analysis_result['score'] = int(score_text.split()[0])
                    except (ValueError, IndexError):
                        analysis_result['score'] = 5  # Default
                
                elif 'Issues:' in line:
                    current_section = 'issues'
                    analysis_result['issues'] = []
                
                elif 'Improvements:' in line or 'Recommendations:' in line:
                    current_section = 'improvements'
                    analysis_result['improvements'] = []
                
                elif 'Organization Strengths:' in line or 'Strengths:' in line:
                    current_section = 'strengths'
                    analysis_result['strengths'] = []
                
                elif 'Coherence Issues:' in line:
                    current_section = 'coherence_issues'
                    analysis_result['coherence_issues'] = []
                
                elif 'Missing Transitions:' in line:
                    current_section = 'missing_transitions'
                    analysis_result['missing_transitions'] = []
                
                elif 'Suggested Reorganization:' in line:
                    current_section = 'reorganization'
                    analysis_result['reorganization'] = []
                
                elif 'Proposed Outline:' in line:
                    current_section = 'outline'
                    analysis_result['outline'] = []
                
                elif 'Redistribution Notes:' in line:
                    current_section = 'redistribution'
                    analysis_result['redistribution'] = []
                
                elif 'Missing Elements:' in line:
                    current_section = 'missing_elements'
                    analysis_result['missing_elements'] = []
                
                elif 'Paragraph Assessment:' in line:
                    current_section = 'paragraph_assessment'
                    analysis_result['paragraph_assessment'] = []
                
                elif 'Heading Issues:' in line:
                    current_section = 'heading_issues'
                    analysis_result['heading_issues'] = []
                
                elif 'Recommended Structure:' in line:
                    current_section = 'recommended_structure'
                    analysis_result['recommended_structure'] = []
                
                elif line.startswith('-') and current_section:
                    # Extract bullet point
                    item = line[1:].strip()
                    if current_section not in analysis_result:
                        analysis_result[current_section] = []
                    analysis_result[current_section].append(item)
                
                elif (line.startswith(('I.', 'II.', 'III.', '1.', '2.', '3.', 'A.', 'B.', 'C.')) 
                      and current_section in ['outline', 'recommended_structure']):
                    # Outline item
                    if current_section not in analysis_result:
                        analysis_result[current_section] = []
                    analysis_result[current_section].append(line)
                
                elif line.startswith('Para ') and current_section == 'paragraph_assessment':
                    # Paragraph assessment
                    analysis_result['paragraph_assessment'].append(line)
                
                elif current_section and not line.startswith(('Score:', 'Issues:', 'Improvements:')):
                    # Continue accumulating content for current section
                    if current_section not in analysis_result:
                        analysis_result[current_section] = []
                    if isinstance(analysis_result[current_section], list):
                        analysis_result[current_section].append(line)
        
        except Exception as e:
            logger.warning(f"Failed to parse structure response", error=str(e))
            analysis_result['parse_error'] = str(e)
        
        return analysis_result
    
    def _calculate_confidence(self, llm_result: Dict, analysis_type: str) -> float:
        """Calculate confidence score for the structure analysis."""
        confidence = 0.88  # Base confidence for structure analysis
        
        # Adjust based on analysis type complexity
        if analysis_type == 'outline_generation':
            confidence *= 0.9  # More complex task
        elif analysis_type == 'paragraph_analysis':
            confidence *= 1.05  # More focused analysis
        elif analysis_type == 'flow_analysis':
            confidence *= 0.95  # Subjective analysis
        
        # Adjust based on cached tokens
        cached_ratio = llm_result['usage'].get('cached_tokens', 0) / max(llm_result['usage']['input_tokens'], 1)
        if cached_ratio > 0.5:
            confidence = min(0.98, confidence * 1.1)
        
        return round(confidence, 2)
    
    async def get_structure_stats(self) -> Dict[str, Any]:
        """Get structure-specific worker statistics."""
        base_stats = await self.get_worker_stats()
        
        # Add structure-specific metrics
        structure_stats = {
            **base_stats,
            'max_text_length': self.max_text_length,
            'analysis_types': [
                'structure_analysis', 'flow_analysis', 'outline_generation',
                'paragraph_analysis', 'hierarchy_check', 'transition_improvement'
            ],
            'supports_batch': False,  # Structure analysis requires individual attention
            'model_used': 'gpt-4.1-mini-2025-04-14'
        }
        
        return structure_stats


# Standalone function to run the worker
async def run_structure_worker():
    """Run a structure worker instance."""
    worker = StructureWorker()
    await worker.initialize()
    await worker.start()


if __name__ == "__main__":
    import asyncio
    asyncio.run(run_structure_worker())