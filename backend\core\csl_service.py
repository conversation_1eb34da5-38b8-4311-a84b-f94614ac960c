"""
Citation Style Language (CSL) Service for Revisionary

Purpose: Provides citation formatting using CSL styles
- Formats citations according to academic standards
- Supports 15 most common citation styles initially
- Generates formatted bibliographies
- Handles CSL-JSON conversion

Features:
- CSL processor integration with citeproc-py
- Multiple output formats (text, HTML, RTF)
- Style validation and error handling
- Bibliography generation and sorting
- Custom CSL style support
"""

import os
import json
import yaml
from typing import Dict, List, Any, Optional, Literal
from pathlib import Path
import structlog
from citeproc import Citation, CitationItem, CitationStylesStyle, CitationStylesBibliography
from citeproc.source.json import CiteProcJSON

from core.settings import settings

logger = structlog.get_logger(__name__)


class CSLService:
    """Citation Style Language processing service."""
    
    def __init__(self):
        """Initialize CSL service with supported styles."""
        self.supported_styles = {
            "apa": "american-psychological-association",
            "mla": "modern-language-association",
            "chicago-author-date": "chicago-author-date",
            "chicago-notes": "chicago-fullnote-bibliography",
            "harvard": "harvard-cite-them-right",
            "vancouver": "vancouver",
            "ieee": "institute-of-electrical-and-electronics-engineers",
            "nature": "nature",
            "science": "science",
            "cell": "cell",
            "ama": "american-medical-association",
            "turabian": "turabian-fullnote-bibliography",
            "oscola": "oxford-university-standard-citation-of-legal-authorities",
            "bluebook": "the-bluebook",
            "asa": "american-sociological-association"
        }
        
        # Initialize CSL styles directory
        self.styles_dir = Path(__file__).parent.parent / "citation_styles"
        self.styles_dir.mkdir(exist_ok=True)
        
        # Cache for loaded styles
        self._style_cache: Dict[str, CitationStylesStyle] = {}
    
    def _citation_to_csl_item(self, citation: Dict[str, Any]) -> Dict[str, Any]:
        """Convert citation data to CSL-JSON format."""
        try:
            # Base CSL item structure
            csl_item = {
                "id": citation.get("citation_key", citation.get("id", "")),
                "type": self._map_citation_type(citation.get("citation_type", "book")),
                "title": citation.get("title", ""),
                "author": self._format_authors(citation.get("authors", [])),
            }
            
            # Add optional fields
            if citation.get("publication_year"):
                csl_item["issued"] = {"date-parts": [[citation["publication_year"]]]}
            
            if citation.get("publisher"):
                csl_item["publisher"] = citation["publisher"]
            
            if citation.get("page_numbers"):
                csl_item["page"] = citation["page_numbers"]
            
            if citation.get("volume"):
                csl_item["volume"] = citation["volume"]
            
            if citation.get("issue"):
                csl_item["issue"] = citation["issue"]
            
            if citation.get("journal_name"):
                csl_item["container-title"] = citation["journal_name"]
            
            if citation.get("doi"):
                csl_item["DOI"] = citation["doi"]
            
            if citation.get("url"):
                csl_item["URL"] = citation["url"]
            
            if citation.get("isbn"):
                csl_item["ISBN"] = citation["isbn"]
            
            if citation.get("access_date"):
                # Convert access_date to CSL format
                access_date = citation["access_date"]
                if isinstance(access_date, str):
                    # Parse date string if needed
                    try:
                        from datetime import datetime
                        date_obj = datetime.fromisoformat(access_date.replace('Z', '+00:00'))
                        csl_item["accessed"] = {
                            "date-parts": [[date_obj.year, date_obj.month, date_obj.day]]
                        }
                    except:
                        pass
            
            return csl_item
            
        except Exception as e:
            logger.error("Error converting citation to CSL format", error=str(e), citation_id=citation.get("id"))
            raise ValueError(f"Failed to convert citation to CSL format: {str(e)}")
    
    def _map_citation_type(self, citation_type: str) -> str:
        """Map internal citation types to CSL types."""
        type_mapping = {
            "book": "book",
            "journal_article": "article-journal",
            "conference_paper": "paper-conference",
            "thesis": "thesis",
            "website": "webpage",
            "report": "report",
            "patent": "patent",
            "software": "software",
            "dataset": "dataset",
            "preprint": "manuscript"
        }
        return type_mapping.get(citation_type, "book")
    
    def _format_authors(self, authors: List[str]) -> List[Dict[str, str]]:
        """Format author names for CSL."""
        formatted_authors = []
        
        for author in authors:
            if not author.strip():
                continue
                
            # Simple name parsing (can be enhanced)
            author = author.strip()
            
            if "," in author:
                # Format: "Last, First Middle"
                parts = author.split(",", 1)
                family = parts[0].strip()
                given = parts[1].strip() if len(parts) > 1 else ""
            else:
                # Format: "First Middle Last"
                parts = author.split()
                if len(parts) >= 2:
                    family = parts[-1]
                    given = " ".join(parts[:-1])
                else:
                    family = author
                    given = ""
            
            formatted_authors.append({
                "family": family,
                "given": given
            })
        
        return formatted_authors
    
    def _get_style(self, style_name: str) -> CitationStylesStyle:
        """Get or load a CSL style."""
        try:
            # Check cache first
            if style_name in self._style_cache:
                return self._style_cache[style_name]
            
            # Get full style name
            full_style_name = self.supported_styles.get(style_name, style_name)
            
            # Try to load from local file first
            style_file = self.styles_dir / f"{full_style_name}.csl"
            
            if style_file.exists():
                style = CitationStylesStyle(str(style_file))
            else:
                # Fallback: try to create a basic style for common ones
                style = self._create_basic_style(style_name)
            
            # Cache the style
            self._style_cache[style_name] = style
            return style
            
        except Exception as e:
            logger.error("Error loading CSL style", style=style_name, error=str(e))
            # Fallback to a basic style
            return self._create_basic_style("basic")
    
    def _create_basic_style(self, style_name: str) -> CitationStylesStyle:
        """Create a basic CSL style for common citation formats."""
        
        # Basic APA-like style template
        basic_csl = """<?xml version="1.0" encoding="utf-8"?>
<style xmlns="http://purl.org/net/xbiblio/csl" class="in-text" version="1.0">
  <info>
    <title>Basic Citation Style</title>
    <id>http://www.revisionary.app/basic</id>
    <updated>2024-01-01T00:00:00+00:00</updated>
  </info>
  <macro name="author">
    <names variable="author">
      <name name-as-sort-order="first" and="symbol" sort-separator=", " initialize-with=". " delimiter=", " delimiter-precedes-last="always"/>
    </names>
  </macro>
  <macro name="issued">
    <date variable="issued">
      <date-part name="year"/>
    </date>
  </macro>
  <citation>
    <layout prefix="(" suffix=")" delimiter="; ">
      <text macro="author" suffix=", "/>
      <text macro="issued"/>
    </layout>
  </citation>
  <bibliography>
    <layout>
      <text macro="author" suffix=" "/>
      <text macro="issued" prefix="(" suffix="). "/>
      <text variable="title" suffix=". "/>
      <text variable="container-title" font-style="italic" suffix=". "/>
      <text variable="publisher" suffix="."/>
    </layout>
  </bibliography>
</style>"""
        
        # Save basic style to file
        basic_style_file = self.styles_dir / f"basic-{style_name}.csl"
        with open(basic_style_file, 'w', encoding='utf-8') as f:
            f.write(basic_csl)
        
        return CitationStylesStyle(str(basic_style_file))
    
    def format_citation(
        self, 
        citation: Dict[str, Any], 
        style: str = "apa",
        format_type: Literal["text", "html"] = "text"
    ) -> str:
        """
        Format a single citation in the specified style.
        
        Args:
            citation: Citation data dictionary
            style: Citation style to use
            format_type: Output format (text or html)
            
        Returns:
            str: Formatted citation
        """
        try:
            logger.debug("Formatting citation", citation_id=citation.get("id"), style=style)
            
            # Convert to CSL format
            csl_item = self._citation_to_csl_item(citation)
            
            # Create citation source
            bib_source = CiteProcJSON([csl_item])
            
            # Get style
            csl_style = self._get_style(style)
            
            # Create bibliography
            bib = CitationStylesBibliography(
                csl_style,
                bib_source,
                format_type
            )
            
            # Register citation
            citation_item = CitationItem(csl_item["id"])
            bib.register(Citation([citation_item]))
            
            # Generate bibliography
            bibliography = bib.bibliography()
            
            if bibliography:
                # Return first (and only) entry
                return str(bibliography[0]).strip()
            else:
                return f"Error formatting citation {citation.get('citation_key', 'unknown')}"
                
        except Exception as e:
            logger.error("Error formatting citation", error=str(e), citation_id=citation.get("id"))
            # Return fallback format
            return self._format_fallback_citation(citation)
    
    def format_bibliography(
        self,
        citations: List[Dict[str, Any]],
        style: str = "apa",
        format_type: Literal["text", "html", "rtf"] = "text",
        sort_by: Literal["author", "year", "title", "citation_key"] = "author"
    ) -> Dict[str, Any]:
        """
        Generate a formatted bibliography from multiple citations.
        
        Args:
            citations: List of citation data dictionaries
            style: Citation style to use
            format_type: Output format
            sort_by: Sort order for bibliography
            
        Returns:
            Dict: Bibliography data including formatted text and metadata
        """
        try:
            logger.info("Generating bibliography", citation_count=len(citations), style=style)
            
            if not citations:
                return {
                    "bibliography": "",
                    "citation_count": 0,
                    "style": style,
                    "format": format_type,
                    "entries": []
                }
            
            # Convert all citations to CSL format
            csl_items = []
            for citation in citations:
                try:
                    csl_item = self._citation_to_csl_item(citation)
                    csl_items.append(csl_item)
                except Exception as e:
                    logger.warning("Skipping invalid citation", citation_id=citation.get("id"), error=str(e))
                    continue
            
            if not csl_items:
                return {
                    "bibliography": "No valid citations found.",
                    "citation_count": 0,
                    "style": style,
                    "format": format_type,
                    "entries": []
                }
            
            # Sort citations
            csl_items = self._sort_citations(csl_items, sort_by)
            
            # Create citation source
            bib_source = CiteProcJSON(csl_items)
            
            # Get style
            csl_style = self._get_style(style)
            
            # Create bibliography
            formatter = format_type
            bib = CitationStylesBibliography(csl_style, bib_source, formatter)
            
            # Register all citations
            for csl_item in csl_items:
                citation_item = CitationItem(csl_item["id"])
                bib.register(Citation([citation_item]))
            
            # Generate bibliography
            bibliography_entries = bib.bibliography()
            
            # Format output
            if format_type == "html":
                bibliography_text = "<ol>\n" + "\n".join(
                    f"<li>{entry}</li>" for entry in bibliography_entries
                ) + "\n</ol>"
            elif format_type == "rtf":
                # Basic RTF formatting
                rtf_entries = []
                for i, entry in enumerate(bibliography_entries, 1):
                    rtf_entries.append(f"{i}. {entry}")
                bibliography_text = "\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}}\\f0\\fs24 " + \
                                  "\\par ".join(rtf_entries) + "}"
            else:
                # Plain text
                bibliography_text = "\n".join(
                    f"{i}. {entry}" for i, entry in enumerate(bibliography_entries, 1)
                )
            
            return {
                "bibliography": bibliography_text,
                "citation_count": len(bibliography_entries),
                "style": style,
                "format": format_type,
                "entries": [str(entry) for entry in bibliography_entries],
                "sort_order": sort_by
            }
            
        except Exception as e:
            logger.error("Error generating bibliography", error=str(e), citation_count=len(citations))
            return {
                "bibliography": f"Error generating bibliography: {str(e)}",
                "citation_count": 0,
                "style": style,
                "format": format_type,
                "entries": [],
                "error": str(e)
            }
    
    def _sort_citations(self, csl_items: List[Dict[str, Any]], sort_by: str) -> List[Dict[str, Any]]:
        """Sort citations according to specified criteria."""
        try:
            if sort_by == "author":
                return sorted(csl_items, key=lambda x: self._get_first_author_last_name(x).lower())
            elif sort_by == "year":
                return sorted(csl_items, key=lambda x: self._get_year(x), reverse=True)
            elif sort_by == "title":
                return sorted(csl_items, key=lambda x: x.get("title", "").lower())
            elif sort_by == "citation_key":
                return sorted(csl_items, key=lambda x: x.get("id", "").lower())
            else:
                return csl_items
        except Exception as e:
            logger.warning("Error sorting citations", error=str(e))
            return csl_items
    
    def _get_first_author_last_name(self, csl_item: Dict[str, Any]) -> str:
        """Get the first author's last name for sorting."""
        authors = csl_item.get("author", [])
        if authors and isinstance(authors, list) and len(authors) > 0:
            first_author = authors[0]
            if isinstance(first_author, dict):
                return first_author.get("family", "")
        return ""
    
    def _get_year(self, csl_item: Dict[str, Any]) -> int:
        """Get publication year for sorting."""
        issued = csl_item.get("issued", {})
        if isinstance(issued, dict):
            date_parts = issued.get("date-parts", [])
            if date_parts and len(date_parts) > 0 and len(date_parts[0]) > 0:
                return int(date_parts[0][0])
        return 0
    
    def _format_fallback_citation(self, citation: Dict[str, Any]) -> str:
        """Create a fallback citation format when CSL processing fails."""
        try:
            authors = ", ".join(citation.get("authors", ["Unknown"]))
            title = citation.get("title", "Untitled")
            year = citation.get("publication_year", "n.d.")
            
            # Basic author-date format
            return f"{authors} ({year}). {title}."
            
        except Exception:
            return f"Citation {citation.get('citation_key', 'unknown')}"
    
    def validate_csl_data(self, citation: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate citation data for CSL processing.
        
        Args:
            citation: Citation data to validate
            
        Returns:
            Dict: Validation results with errors and warnings
        """
        errors = []
        warnings = []
        
        # Required fields
        if not citation.get("title"):
            errors.append("Title is required")
        
        if not citation.get("authors") or len(citation.get("authors", [])) == 0:
            errors.append("At least one author is required")
        
        if not citation.get("citation_key"):
            errors.append("Citation key is required")
        
        # Recommended fields
        if not citation.get("publication_year"):
            warnings.append("Publication year is recommended")
        
        if citation.get("citation_type") == "journal_article" and not citation.get("journal_name"):
            warnings.append("Journal name is recommended for journal articles")
        
        # DOI validation
        if citation.get("doi") and not citation["doi"].startswith(("10.", "doi:", "DOI:")):
            warnings.append("DOI format may be invalid")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def get_supported_styles(self) -> Dict[str, str]:
        """Get list of supported citation styles."""
        return {
            "apa": "American Psychological Association (APA)",
            "mla": "Modern Language Association (MLA)",
            "chicago-author-date": "Chicago Manual of Style (Author-Date)",
            "chicago-notes": "Chicago Manual of Style (Notes-Bibliography)",
            "harvard": "Harvard Reference Format",
            "vancouver": "Vancouver Style",
            "ieee": "IEEE Style",
            "nature": "Nature Style",
            "science": "Science Style",
            "cell": "Cell Style",
            "ama": "American Medical Association",
            "turabian": "Turabian Style",
            "oscola": "Oxford Standard for Legal Citations",
            "bluebook": "The Bluebook",
            "asa": "American Sociological Association"
        }


# Global CSL service instance
_csl_service: Optional[CSLService] = None


def get_csl_service() -> CSLService:
    """Get or create the global CSL service instance."""
    global _csl_service
    if _csl_service is None:
        _csl_service = CSLService()
    return _csl_service