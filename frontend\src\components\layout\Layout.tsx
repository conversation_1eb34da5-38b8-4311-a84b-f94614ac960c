import React, { useState } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import Header from './Header';
import Sidebar from './Sidebar';

const Layout: React.FC = () => {
  const location = useLocation();
  const isEditorPage = location.pathname.startsWith('/app/editor');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // For editor pages, render without layout chrome
  if (isEditorPage) {
    return (
      <div className="mobile-full-height">
        <Outlet />
      </div>
    );
  }

  // For other pages, render with full layout
  return (
    <div className="mobile-full-height bg-gradient-to-br from-slate-50 via-white to-blue-50/30 relative overflow-hidden">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.03),transparent_50%)] pointer-events-none"></div>
      
      {/* Floating background elements - hidden on mobile for performance */}
      <div className="absolute inset-0 pointer-events-none hidden md:block">
        <div className="absolute top-20 left-10 w-32 h-32 bg-purple-200/20 rounded-full mix-blend-multiply filter blur-xl animate-float"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-blue-200/20 rounded-full mix-blend-multiply filter blur-xl animate-float animation-delay-2000"></div>
        <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-pink-200/20 rounded-full mix-blend-multiply filter blur-xl animate-float animation-delay-4000"></div>
      </div>

      {/* Header */}
      <Header onMobileMenuOpen={() => setIsMobileMenuOpen(true)} />
      
      <div className="flex relative z-10">
        {/* Sidebar */}
        <Sidebar 
          isMobileMenuOpen={isMobileMenuOpen}
          onMobileMenuClose={() => setIsMobileMenuOpen(false)}
        />
        
        {/* Main content */}
        <main className="flex-1 min-w-0">
          <div className="pt-16 lg:pt-20 pb-16 lg:pb-0 min-h-screen-safe">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;