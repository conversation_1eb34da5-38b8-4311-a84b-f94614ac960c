-- Revisionary Mock Data - Analytics and Scoring
-- Part 3: Document scores, user statistics, achievements, and challenges
-- Depends on 001_core_data.sql for user and document references

DO $$
DECLARE
    -- Get user IDs from firebase_uid (from 001_core_data.sql)
    sarah_id UUID;
    alex_id UUID;
    kim_id UUID;
    maya_id UUID;
    james_id UUID;
    
    -- Get document IDs by title (from 001_core_data.sql)
    sarah_climate_doc_id UUID;
    sarah_blog_doc_id UUID;
    alex_cafe_doc_id UUID;
    alex_chars_doc_id UUID;
    kim_neural_doc_id UUID;
    kim_conf_doc_id UUID;
    maya_chap1_doc_id UUID;
    maya_world_doc_id UUID;
    maya_blog_doc_id UUID;
    james_q4_doc_id UUID;
    james_launch_doc_id UUID;
    
BEGIN
    -- Get user IDs from existing users
    SELECT id INTO sarah_id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1;
    SELECT id INTO alex_id FROM users WHERE firebase_uid = 'firebase_user_002' LIMIT 1;
    SELECT id INTO kim_id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1;
    SELECT id INTO maya_id FROM users WHERE firebase_uid = 'firebase_user_004' LIMIT 1;
    SELECT id INTO james_id FROM users WHERE firebase_uid = 'firebase_user_005' LIMIT 1;
    
    -- Get document IDs from existing documents
    SELECT id INTO sarah_climate_doc_id FROM documents WHERE title = 'Climate Change Research Paper' LIMIT 1;
    SELECT id INTO sarah_blog_doc_id FROM documents WHERE title = 'Personal Blog Draft' LIMIT 1;
    SELECT id INTO alex_cafe_doc_id FROM documents WHERE title = 'The Midnight Café' LIMIT 1;
    SELECT id INTO alex_chars_doc_id FROM documents WHERE title = 'Character Development Notes' LIMIT 1;
    SELECT id INTO kim_neural_doc_id FROM documents WHERE title = 'Neural Networks in Medical Diagnosis' LIMIT 1;
    SELECT id INTO kim_conf_doc_id FROM documents WHERE title = 'Conference Presentation Outline' LIMIT 1;
    SELECT id INTO maya_chap1_doc_id FROM documents WHERE title = 'Chronicles of Aetheria: Chapter 1' LIMIT 1;
    SELECT id INTO maya_world_doc_id FROM documents WHERE title = 'World Building Bible' LIMIT 1;
    SELECT id INTO maya_blog_doc_id FROM documents WHERE title = 'Writing Process Blog Series' LIMIT 1;
    SELECT id INTO james_q4_doc_id FROM documents WHERE title = 'Q4 Marketing Strategy Report' LIMIT 1;
    SELECT id INTO james_launch_doc_id FROM documents WHERE title = 'Product Launch Proposal' LIMIT 1;

    -- =================================================================
    -- DOCUMENT SCORES - Writing quality metrics and detailed breakdowns
    -- =================================================================
    
    -- Sarah's documents (free tier user - academic focus)
    INSERT INTO document_scores (document_id, user_id, total_score, grammar_score, style_score, structure_score, content_score, word_count, document_type, detailed_breakdown, improvement_suggestions, scoring_version) 
    VALUES
    (sarah_climate_doc_id, sarah_id, 82.5, 21.0, 19.5, 20.0, 22.0, 1250, 'academic', 
     '{"grammar": {"errors": 3, "clarity": 88, "passive_voice": 12}, "style": {"readability": 85, "tone_consistency": 92, "vocabulary_level": "academic"}, "structure": {"logical_flow": 95, "paragraph_coherence": 88, "citation_format": 85}, "content": {"depth": 90, "evidence_support": 88, "argument_strength": 85}}'::jsonb,
     '{"Focus on reducing passive voice usage", "Add more transitional phrases between paragraphs", "Consider varying sentence length for better flow"}',
     '1.2');
    
    INSERT INTO document_scores (document_id, user_id, total_score, grammar_score, style_score, structure_score, content_score, word_count, document_type, detailed_breakdown, improvement_suggestions, scoring_version)
    VALUES
    (sarah_blog_doc_id, sarah_id, 75.0, 19.0, 18.0, 17.5, 20.5, 450, 'general', 
     '{"grammar": {"errors": 2, "clarity": 82, "passive_voice": 8}, "style": {"readability": 92, "tone_consistency": 85, "vocabulary_level": "general"}, "structure": {"logical_flow": 80, "paragraph_coherence": 85, "organization": 75}, "content": {"engagement": 88, "personal_voice": 90, "topic_development": 85}}'::jsonb,
     '{"Strengthen opening paragraph impact", "Add more specific examples", "Consider restructuring middle section"}',
     '1.2');
    
    -- Alex's documents (free tier user - creative writing)
    INSERT INTO document_scores (document_id, user_id, total_score, grammar_score, style_score, structure_score, content_score, word_count, document_type, detailed_breakdown, improvement_suggestions, scoring_version)
    VALUES
    (alex_cafe_doc_id, alex_id, 88.5, 22.5, 23.0, 21.5, 21.5, 2100, 'creative', 
     '{"grammar": {"errors": 1, "clarity": 95, "dialogue_punctuation": 90}, "style": {"voice_consistency": 95, "atmospheric_language": 92, "character_voice": 88}, "structure": {"pacing": 90, "scene_transitions": 85, "narrative_flow": 88}, "content": {"character_development": 85, "world_building": 90, "emotional_impact": 88}}'::jsonb,
     '{"Enhance character motivations in dialogue", "Consider adding more sensory details", "Strengthen subplot connections"}',
     '1.2');
    
    INSERT INTO document_scores (document_id, user_id, total_score, grammar_score, style_score, structure_score, content_score, word_count, document_type, detailed_breakdown, improvement_suggestions, scoring_version)
    VALUES
    (alex_chars_doc_id, alex_id, 71.0, 18.5, 17.0, 16.5, 19.0, 800, 'creative', 
     '{"grammar": {"errors": 4, "clarity": 75, "consistency": 80}, "style": {"voice_development": 70, "descriptive_language": 75, "tone": 80}, "structure": {"organization": 65, "logical_progression": 70, "notes_coherence": 75}, "content": {"character_depth": 85, "creative_originality": 90, "development_potential": 88}}'::jsonb,
     '{"Improve organizational structure", "Develop consistent character voice", "Add more detailed physical descriptions"}',
     '1.2');
    
    -- Dr. Kim's documents (professional tier - academic)
    INSERT INTO document_scores (document_id, user_id, total_score, grammar_score, style_score, structure_score, content_score, word_count, document_type, detailed_breakdown, improvement_suggestions, scoring_version)
    VALUES
    (kim_neural_doc_id, kim_id, 94.0, 24.0, 23.5, 23.0, 23.5, 4500, 'academic', 
     '{"grammar": {"errors": 0, "clarity": 98, "technical_language": 95}, "style": {"academic_tone": 96, "precision": 94, "objectivity": 98}, "structure": {"methodology_clarity": 95, "logical_argument": 92, "citation_quality": 98}, "content": {"research_depth": 96, "evidence_quality": 95, "contribution_significance": 92}}'::jsonb,
     '{"Consider adding visual aids for complex concepts", "Expand discussion section slightly", "Add implications for future research"}',
     '1.2');
    
    INSERT INTO document_scores (document_id, user_id, total_score, grammar_score, style_score, structure_score, content_score, word_count, document_type, detailed_breakdown, improvement_suggestions, scoring_version)
    VALUES
    (kim_conf_doc_id, kim_id, 86.0, 22.0, 21.0, 21.5, 21.5, 750, 'professional', 
     '{"grammar": {"errors": 1, "clarity": 90, "professional_tone": 88}, "style": {"presentation_appropriate": 92, "audience_awareness": 85, "engagement": 80}, "structure": {"outline_clarity": 90, "time_management": 85, "flow": 88}, "content": {"technical_accuracy": 95, "practical_applications": 85, "audience_relevance": 88}}'::jsonb,
     '{"Add more interactive elements", "Include real-world case studies", "Strengthen conclusion with actionable insights"}',
     '1.2');
    
    -- Maya's documents (professional tier - creative)
    INSERT INTO document_scores (document_id, user_id, total_score, grammar_score, style_score, structure_score, content_score, word_count, document_type, detailed_breakdown, improvement_suggestions, scoring_version)
    VALUES
    (maya_chap1_doc_id, maya_id, 91.5, 23.0, 24.0, 22.0, 22.5, 3200, 'creative', 
     '{"grammar": {"errors": 2, "clarity": 92, "dialogue_mechanics": 95}, "style": {"voice_uniqueness": 96, "genre_consistency": 95, "prose_quality": 92}, "structure": {"chapter_pacing": 88, "plot_development": 85, "scene_structure": 90}, "content": {"character_depth": 90, "world_building": 95, "emotional_resonance": 88}}'::jsonb,
     '{"Develop secondary character arcs", "Add more foreshadowing elements", "Enhance action scene descriptions"}',
     '1.2');
    
    INSERT INTO document_scores (document_id, user_id, total_score, grammar_score, style_score, structure_score, content_score, word_count, document_type, detailed_breakdown, improvement_suggestions, scoring_version)
    VALUES
    (maya_world_doc_id, maya_id, 89.0, 22.0, 22.5, 22.0, 22.5, 5600, 'creative', 
     '{"grammar": {"errors": 3, "clarity": 88, "consistency": 90}, "style": {"reference_quality": 95, "world_building_depth": 92, "systematic_approach": 85}, "structure": {"organization": 90, "cross_references": 88, "usability": 85}, "content": {"completeness": 90, "creativity": 95, "practical_utility": 88}}'::jsonb,
     '{"Add more cross-referencing between sections", "Include visual maps or diagrams", "Expand cultural details"}',
     '1.2');
    
    -- James's documents (studio tier - business)
    INSERT INTO document_scores (document_id, user_id, total_score, grammar_score, style_score, structure_score, content_score, word_count, document_type, detailed_breakdown, improvement_suggestions, scoring_version)
    VALUES
    (james_q4_doc_id, james_id, 93.5, 23.5, 23.0, 23.5, 23.5, 3800, 'professional', 
     '{"grammar": {"errors": 0, "clarity": 96, "business_language": 94}, "style": {"executive_tone": 95, "persuasiveness": 92, "data_presentation": 96}, "structure": {"report_format": 96, "logical_progression": 92, "executive_summary": 95}, "content": {"analytical_depth": 94, "strategic_insights": 95, "actionability": 92}}'::jsonb,
     '{"Add competitive analysis section", "Include risk assessment", "Strengthen ROI projections"}',
     '1.2');
    
    INSERT INTO document_scores (document_id, user_id, total_score, grammar_score, style_score, structure_score, content_score, word_count, document_type, detailed_breakdown, improvement_suggestions, scoring_version)
    VALUES
    (james_launch_doc_id, james_id, 87.0, 22.0, 21.5, 21.5, 22.0, 2400, 'professional', 
     '{"grammar": {"errors": 1, "clarity": 88, "technical_accuracy": 90}, "style": {"proposal_tone": 85, "persuasiveness": 88, "stakeholder_focus": 90}, "structure": {"proposal_format": 85, "logical_flow": 88, "supporting_evidence": 85}, "content": {"market_analysis": 90, "feasibility": 85, "innovation": 92}}'::jsonb,
     '{"Strengthen market size analysis", "Add implementation timeline", "Include more detailed budget breakdown"}',
     '1.2');

    -- =================================================================
    -- USER WRITING STATS - Daily and periodic performance tracking
    -- =================================================================
    
    -- Sarah's writing statistics (academic focus)
    INSERT INTO user_writing_stats (user_id, document_type, date, average_score, average_grammar, average_style, average_structure, average_content, documents_scored, total_words, improvement_trend, streak_days) 
    VALUES
    (sarah_id, 'academic', CURRENT_DATE - INTERVAL '7 days', 78.5, 19.5, 18.5, 19.0, 21.5, 2, 1200, 2.3, 3),
    (sarah_id, 'academic', CURRENT_DATE - INTERVAL '6 days', 80.0, 20.0, 19.0, 19.5, 21.5, 1, 800, 1.5, 4),
    (sarah_id, 'academic', CURRENT_DATE - INTERVAL '5 days', 81.2, 20.5, 19.2, 19.8, 21.7, 3, 1850, 1.2, 5),
    (sarah_id, 'general', CURRENT_DATE - INTERVAL '3 days', 75.0, 19.0, 18.0, 17.5, 20.5, 1, 450, -1.5, 2),
    (sarah_id, 'academic', CURRENT_DATE - INTERVAL '1 day', 82.5, 21.0, 19.5, 20.0, 22.0, 1, 1250, 1.3, 1);
    
    -- Alex's writing statistics (creative focus)
    INSERT INTO user_writing_stats (user_id, document_type, date, average_score, average_grammar, average_style, average_structure, average_content, documents_scored, total_words, improvement_trend, streak_days)
    VALUES
    (alex_id, 'creative', CURRENT_DATE - INTERVAL '10 days', 84.0, 21.0, 22.0, 20.5, 20.5, 1, 1800, 0.0, 1),
    (alex_id, 'creative', CURRENT_DATE - INTERVAL '8 days', 86.5, 22.0, 22.5, 21.0, 21.0, 2, 2100, 2.5, 3),
    (alex_id, 'creative', CURRENT_DATE - INTERVAL '5 days', 88.5, 22.5, 23.0, 21.5, 21.5, 1, 2100, 2.0, 6),
    (alex_id, 'creative', CURRENT_DATE - INTERVAL '2 days', 71.0, 18.5, 17.0, 16.5, 19.0, 1, 800, -17.5, 1);
    
    -- Dr. Kim's writing statistics (academic professional)
    INSERT INTO user_writing_stats (user_id, document_type, date, average_score, average_grammar, average_style, average_structure, average_content, documents_scored, total_words, improvement_trend, streak_days)
    VALUES
    (kim_id, 'academic', CURRENT_DATE - INTERVAL '14 days', 90.0, 23.0, 22.5, 22.0, 22.5, 1, 3200, 0.0, 1),
    (kim_id, 'academic', CURRENT_DATE - INTERVAL '10 days', 92.5, 23.5, 23.0, 22.5, 23.5, 2, 4100, 2.5, 5),
    (kim_id, 'academic', CURRENT_DATE - INTERVAL '6 days', 94.0, 24.0, 23.5, 23.0, 23.5, 1, 4500, 1.5, 9),
    (kim_id, 'professional', CURRENT_DATE - INTERVAL '3 days', 86.0, 22.0, 21.0, 21.5, 21.5, 1, 750, -8.0, 1);
    
    -- Maya's writing statistics (creative professional)
    INSERT INTO user_writing_stats (user_id, document_type, date, average_score, average_grammar, average_style, average_structure, average_content, documents_scored, total_words, improvement_trend, streak_days)
    VALUES
    (maya_id, 'creative', CURRENT_DATE - INTERVAL '21 days', 87.0, 21.5, 22.0, 21.0, 22.5, 1, 2800, 0.0, 1),
    (maya_id, 'creative', CURRENT_DATE - INTERVAL '14 days', 89.5, 22.5, 23.0, 22.0, 22.0, 2, 4200, 2.5, 8),
    (maya_id, 'creative', CURRENT_DATE - INTERVAL '7 days', 91.5, 23.0, 24.0, 22.0, 22.5, 1, 3200, 2.0, 15),
    (maya_id, 'creative', CURRENT_DATE - INTERVAL '3 days', 89.0, 22.0, 22.5, 22.0, 22.5, 1, 5600, -2.5, 19);
    
    -- James's writing statistics (business professional)
    INSERT INTO user_writing_stats (user_id, document_type, date, average_score, average_grammar, average_style, average_structure, average_content, documents_scored, total_words, improvement_trend, streak_days)
    VALUES
    (james_id, 'professional', CURRENT_DATE - INTERVAL '30 days', 88.0, 22.0, 21.5, 22.0, 22.5, 2, 2800, 0.0, 1),
    (james_id, 'professional', CURRENT_DATE - INTERVAL '21 days', 90.5, 22.5, 22.0, 23.0, 23.0, 3, 4200, 2.5, 10),
    (james_id, 'professional', CURRENT_DATE - INTERVAL '14 days', 92.0, 23.0, 22.5, 23.0, 23.5, 2, 3100, 1.5, 17),
    (james_id, 'professional', CURRENT_DATE - INTERVAL '7 days', 93.5, 23.5, 23.0, 23.5, 23.5, 1, 3800, 1.5, 24),
    (james_id, 'professional', CURRENT_DATE - INTERVAL '3 days', 87.0, 22.0, 21.5, 21.5, 22.0, 1, 2400, -6.5, 28);

    -- =================================================================
    -- WRITING ACHIEVEMENTS - Badges and milestones earned by users
    -- =================================================================
    
    -- Sarah's achievements (free tier - academic focus)
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, score_requirement, document_count_requirement, streak_requirement, document_id, metadata) 
    VALUES
    (sarah_id, 'grammar_master', 'Grammar Novice', 'bronze', 'Achieved consistent grammar scores above 19 points', 19.0, NULL, NULL, sarah_climate_doc_id, 
     '{"category": "grammar", "threshold_met": 19.5, "improvement_shown": true}'::jsonb);
    
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, document_count_requirement, metadata)
    VALUES
    (sarah_id, 'consistent_improver', 'Steady Progress', 'bronze', 'Showed consistent improvement over 5 consecutive documents', 5, 
     '{"improvement_rate": 1.8, "documents_analyzed": 5, "category": "overall"}'::jsonb);
    
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, score_requirement, document_id, metadata)
    VALUES
    (sarah_id, 'academic_scholar', 'Research Writer', 'silver', 'Demonstrated excellence in academic writing with proper citations', 80.0, sarah_climate_doc_id, 
     '{"citation_accuracy": 95, "academic_tone": 92, "research_depth": 88}'::jsonb);
    
    -- Alex's achievements (free tier - creative focus)
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, document_id, metadata)
    VALUES
    (alex_id, 'creative_genius', 'Atmospheric Storyteller', 'silver', 'Created compelling atmospheric descriptions in creative writing', alex_cafe_doc_id, 
     '{"atmosphere_score": 92, "world_building": 90, "emotional_impact": 88}'::jsonb);
    
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, document_id, metadata)
    VALUES
    (alex_id, 'style_savant', 'Voice Consistency Master', 'gold', 'Maintained consistent narrative voice across multiple scenes', alex_cafe_doc_id, 
     '{"voice_consistency": 95, "character_development": 88, "dialogue_quality": 90}'::jsonb);
    
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, streak_requirement, metadata)
    VALUES
    (alex_id, 'rising_star', 'Creative Streak', 'bronze', 'Maintained writing streak for 6 consecutive days', 6, 
     '{"streak_length": 6, "average_quality": 85.5, "genres": ["urban fantasy", "character development"]}'::jsonb);
    
    -- Dr. Kim's achievements (professional tier - academic excellence)
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, score_requirement, document_id, metadata)
    VALUES
    (kim_id, 'perfect_score', 'Academic Excellence', 'platinum', 'Achieved near-perfect scores in academic writing', 94.0, kim_neural_doc_id, 
     '{"total_score": 94.0, "grammar_perfection": 24.0, "research_quality": 96, "citation_excellence": 98}'::jsonb);
    
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, score_requirement, document_count_requirement, metadata)
    VALUES
    (kim_id, 'grammar_master', 'Grammar Perfection', 'platinum', 'Achieved perfect grammar scores across multiple documents', 24.0, 3, 
     '{"perfect_grammar_count": 3, "error_rate": 0.02, "technical_accuracy": 98}'::jsonb);
    
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, document_count_requirement, metadata)
    VALUES
    (kim_id, 'prolific_writer', 'Research Productivity', 'gold', 'Completed multiple high-quality research documents', 5, 
     '{"documents_completed": 5, "average_score": 91.2, "specialization": "medical AI research"}'::jsonb);
    
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, streak_requirement, metadata)
    VALUES
    (kim_id, 'consistent_improver', 'Excellence Streak', 'gold', 'Maintained high-quality writing for 9 consecutive days', 9, 
     '{"streak_length": 9, "average_score": 92.8, "consistency_rating": 98}'::jsonb);
    
    -- Maya's achievements (professional tier - creative mastery)
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, document_id, metadata)
    VALUES
    (maya_id, 'creative_genius', 'World Builder Supreme', 'platinum', 'Created comprehensive and engaging fictional worlds', maya_world_doc_id, 
     '{"world_building_score": 95, "creativity_rating": 95, "systematic_approach": 90}'::jsonb);
    
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, score_requirement, document_id, metadata)
    VALUES
    (maya_id, 'style_savant', 'Prose Master', 'gold', 'Demonstrated exceptional prose quality and style', 91.5, maya_chap1_doc_id, 
     '{"prose_quality": 92, "style_consistency": 95, "narrative_voice": 96}'::jsonb);
    
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, streak_requirement, metadata)
    VALUES
    (maya_id, 'consistent_improver', 'Epic Dedication', 'platinum', 'Maintained exceptional writing quality for 19 consecutive days', 19, 
     '{"streak_length": 19, "average_score": 90.0, "project_dedication": true}'::jsonb);
    
    -- James's achievements (studio tier - business excellence)
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, score_requirement, document_id, metadata)
    VALUES
    (james_id, 'professional_powerhouse', 'Business Communication Expert', 'platinum', 'Achieved excellence in professional business communication', 93.5, james_q4_doc_id, 
     '{"business_score": 93.5, "executive_communication": 95, "strategic_clarity": 94}'::jsonb);
    
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, streak_requirement, metadata)
    VALUES
    (james_id, 'consistent_improver', 'Executive Consistency', 'platinum', 'Maintained professional excellence for 28 consecutive days', 28, 
     '{"streak_length": 28, "average_score": 91.2, "leadership_communication": true}'::jsonb);
    
    INSERT INTO writing_achievements (user_id, achievement_type, achievement_name, achievement_level, description, document_count_requirement, metadata)
    VALUES
    (james_id, 'prolific_writer', 'Strategic Documentation', 'gold', 'Produced comprehensive business documentation suite', 8, 
     '{"documents_completed": 8, "business_impact": "high", "strategic_value": 95}'::jsonb);

    -- =================================================================
    -- WRITING CHALLENGES - Active and completed writing challenges
    -- =================================================================
    
    -- Sarah's challenges (academic focus)
    INSERT INTO writing_challenges (user_id, challenge_type, challenge_title, challenge_description, target_metric, target_value, difficulty, document_type_filter, assigned_date, completion_value, document_id, reward_points) 
    VALUES
    (sarah_id, 'grammar_perfection', 'Academic Grammar Challenge', 'Achieve grammar scores above 20 for 5 consecutive academic documents', 'grammar_score', 20.0, 'medium', 'academic', CURRENT_DATE - INTERVAL '10 days', 3.0, sarah_climate_doc_id, 250);
    
    INSERT INTO writing_challenges (user_id, challenge_type, challenge_title, challenge_description, target_metric, target_value, difficulty, assigned_date, completion_value, reward_points)
    VALUES
    (sarah_id, 'word_count', 'Research Paper Sprint', 'Write 3000 words of academic content in one week', 'word_count', 3000.0, 'hard', CURRENT_DATE - INTERVAL '5 days', 1700.0, 400);
    
    -- Alex's challenges (creative focus)
    INSERT INTO writing_challenges (user_id, challenge_type, challenge_title, challenge_description, target_metric, target_value, difficulty, assigned_date, completion_value, reward_points)
    VALUES
    (alex_id, 'style_improvement', 'Atmospheric Writing Challenge', 'Achieve style scores above 22 in creative writing', 'style_score', 22.0, 'medium', CURRENT_DATE - INTERVAL '7 days', 23.0, 300);
    
    INSERT INTO writing_challenges (user_id, challenge_type, challenge_title, challenge_description, target_metric, target_value, difficulty, assigned_date, completion_value, reward_points)
    VALUES
    (alex_id, 'vocabulary_diversity', 'Rich Language Challenge', 'Use 50 unique advanced vocabulary words in creative writing', 'vocabulary_diversity', 50.0, 'easy', CURRENT_DATE - INTERVAL '14 days', 32.0, 200);
    
    -- Dr. Kim's challenges (academic excellence)
    INSERT INTO writing_challenges (user_id, challenge_type, challenge_title, challenge_description, target_metric, target_value, difficulty, document_type_filter, assigned_date, completion_value, reward_points)
    VALUES
    (kim_id, 'content_quality', 'Research Excellence Challenge', 'Maintain total scores above 90 for 10 consecutive documents', 'total_score', 90.0, 'expert', 'academic', CURRENT_DATE - INTERVAL '21 days', 94.0, 600);
    
    INSERT INTO writing_challenges (user_id, challenge_type, challenge_title, challenge_description, target_metric, target_value, difficulty, assigned_date, completion_value, reward_points)
    VALUES
    (kim_id, 'error_reduction', 'Minimal Error Challenge', 'Write 5 documents with maximum 1 grammar error each', 'error_count', 1.0, 'expert', CURRENT_DATE - INTERVAL '14 days', 0.6, 500);
    
    -- Maya's challenges (creative mastery)
    INSERT INTO writing_challenges (user_id, challenge_type, challenge_title, challenge_description, target_metric, target_value, difficulty, assigned_date, completion_value, document_id, reward_points)
    VALUES
    (maya_id, 'structure_mastery', 'Epic Narrative Challenge', 'Complete a 50,000-word novel draft with consistent quality', 'word_count', 50000.0, 'hard', CURRENT_DATE - INTERVAL '45 days', 28500.0, maya_chap1_doc_id, 800);
    
    INSERT INTO writing_challenges (user_id, challenge_type, challenge_title, challenge_description, target_metric, target_value, difficulty, assigned_date, completion_value, reward_points)
    VALUES
    (maya_id, 'readability_target', 'Accessible Fantasy Challenge', 'Maintain readability scores suitable for young adult audience', 'readability_score', 85.0, 'medium', CURRENT_DATE - INTERVAL '30 days', 88.0, 350);
    
    -- James's challenges (business excellence)
    INSERT INTO writing_challenges (user_id, challenge_type, challenge_title, challenge_description, target_metric, target_value, difficulty, assigned_date, completion_value, reward_points)
    VALUES
    (james_id, 'grammar_perfection', 'Executive Communication Challenge', 'Achieve perfect grammar in all business communications', 'grammar_score', 23.0, 'hard', CURRENT_DATE - INTERVAL '21 days', 23.5, 450);
    
    INSERT INTO writing_challenges (user_id, challenge_type, challenge_title, challenge_description, target_metric, target_value, difficulty, assigned_date, completion_value, reward_points)
    VALUES
    (james_id, 'content_quality', 'Strategic Impact Challenge', 'Create business documents with measurable strategic impact', 'content_score', 23.0, 'expert', CURRENT_DATE - INTERVAL '30 days', 23.5, 600);
    
    -- Completed challenges
    INSERT INTO writing_challenges (user_id, challenge_type, challenge_title, challenge_description, target_metric, target_value, difficulty, assigned_date, completed_at, completion_value, reward_points)
    VALUES
    (alex_id, 'word_count', 'Short Story Sprint', 'Complete a 2000-word short story in 3 days', 'word_count', 2000.0, 'easy', CURRENT_DATE - INTERVAL '20 days', CURRENT_DATE - INTERVAL '17 days', 2100.0, 150);
    
    INSERT INTO writing_challenges (user_id, challenge_type, challenge_title, challenge_description, target_metric, target_value, difficulty, assigned_date, completed_at, completion_value, reward_points)
    VALUES
    (kim_id, 'vocabulary_diversity', 'Technical Terminology Challenge', 'Demonstrate mastery of medical AI terminology', 'vocabulary_diversity', 100.0, 'medium', CURRENT_DATE - INTERVAL '35 days', CURRENT_DATE - INTERVAL '28 days', 127.0, 300);

END $$;