#!/usr/bin/env python3
"""
WebSocket + Health Worker Integration Test
Tests the integration between WebSocket real-time events and Health Worker processing.
"""

import sys
import os
import ast
import json
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

def test_websocket_health_events():
    """Test that WebSocket server has health analysis events."""
    print("Testing WebSocket health analysis events...")
    
    try:
        websocket_file = backend_path / "websocket" / "server.py"
        with open(websocket_file, 'r') as f:
            content = f.read()
        
        # Check for health-specific WebSocket events
        required_health_patterns = [
            'request_health_analysis',
            'health_analysis_started', 
            'health_metrics_update',
            'debounced_health_service',
            'health_update'
        ]
        
        for pattern in required_health_patterns:
            if pattern not in content:
                print(f"❌ Missing WebSocket health pattern: {pattern}")
                return False
        
        print("✓ All WebSocket health analysis patterns found")
        
        # Check for Redis pub/sub integration
        redis_patterns = [
            'document:updates:',
            '_redis_subscriber',
            'health_metrics_update'
        ]
        
        for pattern in redis_patterns:
            if pattern not in content:
                print(f"❌ Missing Redis integration pattern: {pattern}")
                return False
        
        print("✓ Redis pub/sub health integration found")
        return True
        
    except Exception as e:
        print(f"❌ Error testing WebSocket health events: {e}")
        return False

def test_health_worker_integration():
    """Test health worker structure and methods."""
    print("Testing Health Worker structure...")
    
    try:
        health_worker_file = backend_path / "workers" / "health_worker.py"
        with open(health_worker_file, 'r') as f:
            content = f.read()
        
        # Check for HealthWorker class
        if 'class HealthWorker(BaseWorker):' not in content:
            print("❌ HealthWorker class not found")
            return False
        
        print("✓ HealthWorker class found")
        
        # Check for required methods and attributes
        required_patterns = [
            'def __init__',
            'def process_job',
            'health_metrics',
            'max_text_length',
            'cache_ttl'
        ]
        
        for pattern in required_patterns:
            if pattern not in content:
                print(f"❌ Missing health worker pattern: {pattern}")
                return False
        
        print("✓ All health worker patterns found")
        return True
        
    except Exception as e:
        print(f"❌ Error testing health worker: {e}")
        return False

def test_debounced_health_service():
    """Test debounced health service integration."""
    print("Testing debounced health service...")
    
    try:
        debounced_file = backend_path / "core" / "debounced_health_service.py"
        
        if not debounced_file.exists():
            print("❌ debounced_health_service.py not found")
            return False
        
        with open(debounced_file, 'r') as f:
            content = f.read()
        
        # Check for required debouncing patterns
        required_patterns = [
            'class DebouncedHealthService',
            'request_health_analysis',
            'debounce_delay',
            'websocket_session',
            'priority'
        ]
        
        for pattern in required_patterns:
            if pattern not in content:
                print(f"❌ Missing debounced service pattern: {pattern}")
                return False
        
        print("✓ Debounced health service found and structured correctly")
        return True
        
    except Exception as e:
        print(f"❌ Error testing debounced service: {e}")
        return False

def test_queue_health_integration():
    """Test queue manager health worker integration."""
    print("Testing queue manager health integration...")
    
    try:
        queue_file = backend_path / "core" / "queue_manager.py"
        
        if not queue_file.exists():
            print("❌ queue_manager.py not found")
            return False
        
        with open(queue_file, 'r') as f:
            content = f.read()
        
        # Check for health worker integration
        health_queue_patterns = [
            'health',
            'enqueue_job',
            'agent_type',
            'priority'
        ]
        
        for pattern in health_queue_patterns:
            if pattern not in content:
                print(f"❌ Missing queue health pattern: {pattern}")
                return False
        
        print("✓ Queue manager health integration found")
        return True
        
    except Exception as e:
        print(f"❌ Error testing queue integration: {e}")
        return False

def test_redis_pub_sub_flow():
    """Test Redis pub/sub message flow for health updates."""
    print("Testing Redis pub/sub health flow...")
    
    try:
        # Check WebSocket server for Redis subscription
        websocket_file = backend_path / "websocket" / "server.py"
        with open(websocket_file, 'r') as f:
            websocket_content = f.read()
        
        # Verify Redis subscription patterns
        if 'document:updates:' not in websocket_content:
            print("❌ Missing document updates subscription")
            return False
        
        if 'health_update' not in websocket_content:
            print("❌ Missing health update handling")
            return False
        
        # Check for message forwarding
        if 'health_metrics_update' not in websocket_content:
            print("❌ Missing health metrics forwarding")
            return False
        
        print("✓ Redis pub/sub health flow properly configured")
        
        # Check Redis service structure
        redis_file = backend_path / "core" / "redis_service.py"
        if redis_file.exists():
            with open(redis_file, 'r') as f:
                redis_content = f.read()
            
            if 'publish' in redis_content and 'subscribe' in redis_content:
                print("✓ Redis service has pub/sub capabilities")
            else:
                print("⚠️  Redis service may be missing pub/sub methods")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Redis flow: {e}")
        return False

def test_end_to_end_flow():
    """Test conceptual end-to-end flow for health analysis."""
    print("Testing end-to-end health analysis flow...")
    
    try:
        # Verify the complete flow exists:
        # 1. WebSocket receives health analysis request
        # 2. Request is sent to debounced health service  
        # 3. Service enqueues job to health worker via queue manager
        # 4. Worker processes and publishes result to Redis
        # 5. WebSocket forwards result to client
        
        flow_steps = [
            {
                "file": "websocket/server.py",
                "patterns": ["request_health_analysis", "debounced_health_service"],
                "description": "WebSocket receives and delegates request"
            },
            {
                "file": "core/debounced_health_service.py", 
                "patterns": ["request_health_analysis", "queue_manager"],
                "description": "Debounced service manages request"
            },
            {
                "file": "core/queue_manager.py",
                "patterns": ["enqueue_job", "health"],
                "description": "Queue manager handles job"
            },
            {
                "file": "workers/health_worker.py",
                "patterns": ["process_job", "health_metrics"],
                "description": "Health worker processes analysis"
            },
            {
                "file": "websocket/server.py",
                "patterns": ["_redis_subscriber", "health_metrics_update"],
                "description": "WebSocket forwards results"
            }
        ]
        
        for step in flow_steps:
            file_path = backend_path / step["file"]
            if not file_path.exists():
                print(f"❌ Missing file for flow step: {step['file']}")
                return False
            
            with open(file_path, 'r') as f:
                content = f.read()
            
            for pattern in step["patterns"]:
                if pattern not in content:
                    print(f"❌ Missing pattern '{pattern}' in {step['file']}")
                    return False
            
            print(f"✓ {step['description']} - verified")
        
        print("✓ End-to-end health analysis flow verified")
        return True
        
    except Exception as e:
        print(f"❌ Error testing end-to-end flow: {e}")
        return False

def run_websocket_health_tests():
    """Run all WebSocket + Health Worker integration tests."""
    print("=" * 70)
    print("WEBSOCKET + HEALTH WORKER INTEGRATION TESTS")
    print("=" * 70)
    
    tests = [
        test_websocket_health_events,
        test_health_worker_integration,
        test_debounced_health_service,
        test_queue_health_integration, 
        test_redis_pub_sub_flow,
        test_end_to_end_flow
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n{test.__name__.replace('_', ' ').title()}:")
        try:
            if test():
                passed += 1
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test error: {e}")
    
    print("\n" + "=" * 70)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL WEBSOCKET + HEALTH INTEGRATION TESTS PASSED!")
        print("✅ Real-time health analysis system is properly integrated")
        return True
    else:
        print("❌ Some integration tests failed")
        return False

if __name__ == "__main__":
    success = run_websocket_health_tests()
    sys.exit(0 if success else 1)