-- Revisionary Mock Data - Part 13: Token Usage Daily
-- Daily API token consumption aggregations

DO $$
DECLARE
    -- Get user IDs from firebase_uid (from 001_core_data.sql)
    sarah_id UUID;
    alex_id UUID;
    kim_id UUID;
    maya_id UUID;
    james_id UUID;
    
BEGIN
    -- Get user IDs from existing users
    SELECT id INTO sarah_id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1;
    SELECT id INTO alex_id FROM users WHERE firebase_uid = 'firebase_user_002' LIMIT 1;
    SELECT id INTO kim_id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1;
    SELECT id INTO maya_id FROM users WHERE firebase_uid = 'firebase_user_004' LIMIT 1;
    SELECT id INTO james_id FROM users WHERE firebase_uid = 'firebase_user_005' LIMIT 1;

    -- =================================================================
    -- TOKEN USAGE DAILY - Daily API token consumption aggregations
    -- =================================================================
    
    INSERT INTO token_usage_daily (user_id, date, tokens_total, tokens_by_model, tokens_by_agent, operations_count, documents_edited) 
    VALUES
    -- Dr. Kim's usage (professional tier - high research activity)
    (kim_id, CURRENT_DATE - INTERVAL '7 days', 15420, 
     '{"gpt-4": 12340, "gpt-3.5-turbo": 3080}'::jsonb, 
     '{"suggestions": 4200, "research": 3850, "citations": 2100, "health_metrics": 1890, "summaries": 3380}'::jsonb, 45, 3),
    
    (kim_id, CURRENT_DATE - INTERVAL '6 days', 18760, 
     '{"gpt-4": 15680, "gpt-3.5-turbo": 3080}'::jsonb, 
     '{"suggestions": 5100, "research": 4200, "citations": 2860, "health_metrics": 2100, "summaries": 4500}'::jsonb, 52, 4),
    
    (kim_id, CURRENT_DATE - INTERVAL '5 days', 12890, 
     '{"gpt-4": 10120, "gpt-3.5-turbo": 2770}'::jsonb, 
     '{"suggestions": 3450, "research": 3100, "citations": 1890, "health_metrics": 1650, "summaries": 2800}'::jsonb, 38, 2),
    
    -- Maya's usage (professional tier - creative writing)
    (maya_id, CURRENT_DATE - INTERVAL '10 days', 13250, 
     '{"gpt-4": 10980, "gpt-3.5-turbo": 2270}'::jsonb, 
     '{"suggestions": 4200, "personas": 3100, "world_building": 2850, "summaries": 2050, "consistency": 1050}'::jsonb, 42, 2),
    
    (maya_id, CURRENT_DATE - INTERVAL '8 days', 16780, 
     '{"gpt-4": 13540, "gpt-3.5-turbo": 3240}'::jsonb, 
     '{"suggestions": 5100, "personas": 3890, "world_building": 3200, "summaries": 2680, "consistency": 1910}'::jsonb, 48, 3),
    
    (maya_id, CURRENT_DATE - INTERVAL '3 days', 14560, 
     '{"gpt-4": 11890, "gpt-3.5-turbo": 2670}'::jsonb, 
     '{"suggestions": 4650, "personas": 3200, "world_building": 2980, "summaries": 2180, "consistency": 1550}'::jsonb, 44, 2),
    
    -- James's usage (studio tier - business documents)
    (james_id, CURRENT_DATE - INTERVAL '14 days', 22340, 
     '{"gpt-4": 18760, "gpt-3.5-turbo": 3580}'::jsonb, 
     '{"suggestions": 6800, "business_analysis": 5200, "export": 3100, "templates": 2840, "summaries": 4400}'::jsonb, 58, 4),
    
    (james_id, CURRENT_DATE - INTERVAL '7 days', 19680, 
     '{"gpt-4": 16200, "gpt-3.5-turbo": 3480}'::jsonb, 
     '{"suggestions": 5890, "business_analysis": 4650, "export": 2780, "templates": 2560, "summaries": 3800}'::jsonb, 51, 3),
    
    (james_id, CURRENT_DATE - INTERVAL '2 days', 17250, 
     '{"gpt-4": 14100, "gpt-3.5-turbo": 3150}'::jsonb, 
     '{"suggestions": 5200, "business_analysis": 4100, "export": 2450, "templates": 2300, "summaries": 3200}'::jsonb, 47, 3),
    
    -- Sarah's usage (free tier - limited but consistent)
    (sarah_id, CURRENT_DATE - INTERVAL '12 days', 4850, 
     '{"gpt-3.5-turbo": 4850}'::jsonb, 
     '{"suggestions": 2100, "research": 1850, "summaries": 900}'::jsonb, 18, 1),
    
    (sarah_id, CURRENT_DATE - INTERVAL '5 days', 5680, 
     '{"gpt-3.5-turbo": 5680}'::jsonb, 
     '{"suggestions": 2450, "research": 2180, "summaries": 1050}'::jsonb, 22, 2),
    
    (sarah_id, CURRENT_DATE - INTERVAL '1 day', 3920, 
     '{"gpt-3.5-turbo": 3920}'::jsonb, 
     '{"suggestions": 1680, "research": 1540, "summaries": 700}'::jsonb, 15, 1),
    
    -- Alex's usage (free tier - creative focus)
    (alex_id, CURRENT_DATE - INTERVAL '9 days', 4200, 
     '{"gpt-3.5-turbo": 4200}'::jsonb, 
     '{"suggestions": 1890, "creative": 1650, "summaries": 660}'::jsonb, 16, 1),
    
    (alex_id, CURRENT_DATE - INTERVAL '4 days', 5430, 
     '{"gpt-3.5-turbo": 5430}'::jsonb, 
     '{"suggestions": 2200, "creative": 2180, "summaries": 1050}'::jsonb, 21, 2),
    
    (alex_id, CURRENT_DATE - INTERVAL '1 day', 3650, 
     '{"gpt-3.5-turbo": 3650}'::jsonb, 
     '{"suggestions": 1580, "creative": 1420, "summaries": 650}'::jsonb, 14, 1);

END $$;