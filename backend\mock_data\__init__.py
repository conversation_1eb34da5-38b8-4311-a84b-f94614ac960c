"""
Mock Data Package

Purpose: Provides comprehensive mock data for development and testing including:
- Realistic user profiles and authentication responses
- Sample documents and text blocks across different writing types
- AI agent responses and analysis results
- Analytics events, achievements, and progress data

Responsibilities:
- Mock data generation for all application entities
- Realistic response simulation for external services
- Development environment data seeding
- Test scenario data provision
- Zero-dependency development support

Used by: All API endpoints in mock mode, testing infrastructure
Dependencies: None (self-contained mock data)
"""

import json
import os
from typing import Dict, List, Any
from datetime import datetime, timedelta
from uuid import uuid4

def get_mock_user() -> Dict[str, Any]:
    """Get mock user profile data."""
    return {
        "id": "14285842-26d6-4a48-8b7c-1ac76fa5c488",
        "firebase_uid": "14285842-26d6-4a48-8b7c-1ac76fa5c488",
        "email": "<EMAIL>",
        "display_name": "Mock User",
        "avatar_url": "https://example.com/avatar.jpg",
        "subscription_tier": "professional",
        "subscription_status": "active",
        "created_at": "2024-01-01T00:00:00Z",
        "last_active_at": datetime.utcnow().isoformat() + "Z",
        "preferences": {
            "theme": "dark",
            "notifications": True,
            "language": "en",
            "ai_suggestions": {
                "grammar": True,
                "style": True,
                "health": True
            },
            "writing_goals": {
                "daily_words": 500,
                "quality_score": 85
            }
        }
    }

def get_mock_token_response() -> Dict[str, Any]:
    """Get mock authentication token response."""
    return {
        "access_token": "mock_access_token_abc123",
        "refresh_token": "mock_refresh_token_def456",
        "expires_in": 3600,
        "token_type": "Bearer",
        "user": get_mock_user()
    }

def get_mock_custom_agents() -> List[Dict[str, Any]]:
    """Get mock custom agent configurations."""
    return [
        {
            "id": "agent_001",
            "user_id": "14285842-26d6-4a48-8b7c-1ac76fa5c488",
            "name": "Academic Paper Assistant",
            "description": "Specialized agent for academic writing with APA style compliance",
            "type": "academic",
            "capabilities": ["citation_check", "argument_analysis", "methodology_review"],
            "style_rules": {
                "citation_style": "APA",
                "formality_level": "academic",
                "tone": "objective",
                "voice": "third_person"
            },
            "priority": 80,
            "is_active": True,
            "created_at": "2024-01-15T10:00:00Z",
            "updated_at": "2024-01-20T14:30:00Z",
            "usage_stats": {
                "total_executions": 45,
                "average_execution_time": 1.8,
                "success_rate": 94.2,
                "last_used": "2024-06-25T09:15:00Z"
            }
        },
        {
            "id": "agent_002",
            "user_id": "14285842-26d6-4a48-8b7c-1ac76fa5c488",
            "name": "Creative Fiction Editor",
            "description": "Agent focused on character development and narrative flow",
            "type": "creative",
            "capabilities": ["character_consistency", "dialogue_improvement", "pacing_analysis"],
            "style_rules": {
                "narrative_voice": "consistent",
                "dialogue_style": "natural",
                "tense": "maintain_existing",
                "perspective": "maintain_existing"
            },
            "priority": 70,
            "is_active": True,
            "created_at": "2024-02-01T08:00:00Z",
            "updated_at": "2024-02-15T16:45:00Z",
            "usage_stats": {
                "total_executions": 32,
                "average_execution_time": 2.1,
                "success_rate": 91.8,
                "last_used": "2024-06-24T20:30:00Z"
            }
        },
        {
            "id": "agent_003",
            "user_id": "14285842-26d6-4a48-8b7c-1ac76fa5c488",
            "name": "Business Report Optimizer",
            "description": "Professional writing agent for executive communications",
            "type": "professional",
            "capabilities": ["executive_summary", "data_visualization_text", "recommendation_clarity"],
            "style_rules": {
                "formality_level": "professional",
                "tone": "confident",
                "structure": "executive_first",
                "length": "concise"
            },
            "priority": 90,
            "is_active": True,
            "created_at": "2024-01-10T12:00:00Z",
            "updated_at": "2024-01-25T11:20:00Z",
            "usage_stats": {
                "total_executions": 67,
                "average_execution_time": 1.5,
                "success_rate": 96.7,
                "last_used": "2024-06-25T15:45:00Z"
            }
        }
    ]

def get_mock_agent_templates() -> List[Dict[str, Any]]:
    """Get mock agent template definitions."""
    return [
        {
            "id": "template_001",
            "name": "Academic Research Assistant",
            "description": "Template for academic writing with citation management",
            "category": "academic",
            "type": "research",
            "default_capabilities": ["citation_check", "methodology_review", "argument_analysis"],
            "default_style_rules": {
                "citation_style": "APA",
                "formality_level": "academic",
                "tone": "objective"
            },
            "usage_count": 150,
            "rating": 4.7
        },
        {
            "id": "template_002",
            "name": "Creative Writing Mentor",
            "description": "Template for fiction and creative non-fiction writing",
            "category": "creative",
            "type": "creative",
            "default_capabilities": ["character_development", "plot_consistency", "dialogue_enhancement"],
            "default_style_rules": {
                "narrative_voice": "consistent",
                "character_voice": "distinct",
                "pacing": "dynamic"
            },
            "usage_count": 89,
            "rating": 4.5
        },
        {
            "id": "template_003",
            "name": "Executive Communications",
            "description": "Template for business and professional writing",
            "category": "professional",
            "type": "business",
            "default_capabilities": ["executive_summary", "clarity_optimization", "persuasion_enhancement"],
            "default_style_rules": {
                "formality_level": "professional",
                "tone": "confident",
                "structure": "executive_first"
            },
            "usage_count": 203,
            "rating": 4.8
        }
    ]

def get_mock_agent_execution_result() -> Dict[str, Any]:
    """Get mock agent execution result."""
    return {
        "execution_id": str(uuid4()),
        "agent_id": "agent_001",
        "status": "completed",
        "execution_time": 1.8,
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "analysis": {
            "overall_score": 87,
            "issues_found": 3,
            "improvements_suggested": 5,
            "confidence": 0.92
        },
        "suggestions": [
            {
                "id": "sugg_001",
                "type": "grammar",
                "severity": "medium",
                "position": {"start": 45, "end": 67},
                "original_text": "The data shows that",
                "suggested_text": "The data demonstrate that",
                "explanation": "Use 'demonstrate' for plural subject 'data'",
                "confidence": 0.95
            },
            {
                "id": "sugg_002",
                "type": "style",
                "severity": "low",
                "position": {"start": 120, "end": 145},
                "original_text": "very important findings",
                "suggested_text": "significant findings",
                "explanation": "Avoid weak intensifiers in academic writing",
                "confidence": 0.88
            },
            {
                "id": "sugg_003",
                "type": "clarity",
                "severity": "high",
                "position": {"start": 200, "end": 250},
                "original_text": "The methodology that was used in this study",
                "suggested_text": "The study methodology",
                "explanation": "Remove redundant phrasing for clarity",
                "confidence": 0.91
            }
        ],
        "metrics": {
            "readability_score": 65,
            "complexity_score": 72,
            "formality_score": 85,
            "clarity_score": 78
        }
    }

def get_mock_agent_analytics() -> Dict[str, Any]:
    """Get mock agent performance analytics."""
    return {
        "agent_id": "agent_001",
        "time_period": "30_days",
        "summary": {
            "total_executions": 45,
            "average_execution_time": 1.8,
            "success_rate": 94.2,
            "user_satisfaction": 4.6,
            "suggestions_accepted": 78.3
        },
        "performance_trends": [
            {"date": "2024-06-01", "executions": 2, "avg_time": 1.9, "success_rate": 100.0},
            {"date": "2024-06-02", "executions": 1, "avg_time": 1.7, "success_rate": 100.0},
            {"date": "2024-06-03", "executions": 3, "avg_time": 1.8, "success_rate": 91.7},
            {"date": "2024-06-04", "executions": 0, "avg_time": 0.0, "success_rate": 0.0},
            {"date": "2024-06-05", "executions": 2, "avg_time": 2.1, "success_rate": 95.0}
        ],
        "suggestion_breakdown": {
            "grammar": {"count": 23, "acceptance_rate": 89.1},
            "style": {"count": 18, "acceptance_rate": 72.2},
            "clarity": {"count": 15, "acceptance_rate": 80.0},
            "structure": {"count": 8, "acceptance_rate": 62.5}
        },
        "user_feedback": {
            "average_rating": 4.6,
            "total_ratings": 28,
            "feedback_distribution": {
                "5_star": 18,
                "4_star": 7,
                "3_star": 2,
                "2_star": 1,
                "1_star": 0
            }
        }
    }

def get_mock_health_metrics() -> Dict[str, Any]:
    """Get mock writing health analysis results."""
    return {
        "analysis_id": str(uuid4()),
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "overall_score": 83,
        "metrics": {
            "readability": {
                "score": 78,
                "flesch_kincaid_grade": 9.2,
                "flesch_reading_ease": 65.4,
                "gunning_fog_index": 10.1,
                "target_audience": "General Adult"
            },
            "clarity": {
                "score": 85,
                "wordiness_issues": 3,
                "redundancy_count": 1,
                "unclear_pronouns": 2,
                "passive_voice_percentage": 12.5
            },
            "voice_consistency": {
                "score": 88,
                "tense_consistency": 95,
                "person_consistency": 92,
                "tone_variation": 8,
                "formality_consistency": 85
            },
            "brand_alignment": {
                "score": 79,
                "terminology_compliance": 85,
                "style_guide_adherence": 78,
                "voice_match": 75,
                "messaging_consistency": 82
            }
        },
        "issues": [
            {
                "id": "issue_001",
                "type": "readability",
                "severity": "medium",
                "position": {"start": 45, "end": 89},
                "description": "Sentence too long (32 words)",
                "suggestion": "Consider breaking into two sentences",
                "impact": "Reduces reading comprehension"
            },
            {
                "id": "issue_002",
                "type": "clarity",
                "severity": "low",
                "position": {"start": 150, "end": 165},
                "description": "Vague pronoun reference",
                "suggestion": "Replace 'it' with specific noun",
                "impact": "May confuse readers"
            }
        ],
        "improvement_suggestions": [
            "Break long sentences (>25 words) into shorter ones",
            "Replace weak intensifiers with stronger, specific language",
            "Ensure all pronouns have clear antecedents",
            "Consider using active voice for better clarity"
        ],
        "trends": {
            "score_change": "+5.2",
            "period": "7_days",
            "improvement_areas": ["clarity", "voice_consistency"],
            "declining_areas": []
        }
    }