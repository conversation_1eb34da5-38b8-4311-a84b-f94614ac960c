"""
Agent Management API Endpoints

Purpose: Provides REST API endpoints for managing AI agents including:
- Creating, reading, updating, and deleting custom agents
- Executing individual agents on text content
- Bulk execution of multiple agents
- Agent performance analytics and usage statistics

Responsibilities:
- Agent CRUD operations via HTTP endpoints
- Agent execution request handling
- Response formatting and error handling
- Request validation and authentication
- Agent template management and instantiation

Used by: Frontend agent management UI, agent execution requests
Dependencies: core.multi_agent_engine, agents.*, core.auth
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from datetime import datetime
import structlog

from api.auth import get_current_user
from core.auth import UserClaims
from core.multi_agent_engine import MultiAgentEngine
from core.database import get_database, DatabaseService

# Configure logging
logger = structlog.get_logger(__name__)

# Create router
router = APIRouter()

# Request/Response Models
class CustomAgentCreate(BaseModel):
    """Request model for creating a custom agent."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    type: str = Field(..., description="Agent type: grammar, style, content, etc.")
    capabilities: List[str] = Field(default_factory=list)
    style_rules: Dict[str, Any] = Field(default_factory=dict)
    priority: int = Field(default=50, ge=0, le=100)
    llm_config: Optional[Dict[str, Any]] = Field(default_factory=dict)

class CustomAgentUpdate(BaseModel):
    """Request model for updating a custom agent."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    capabilities: Optional[List[str]] = None
    style_rules: Optional[Dict[str, Any]] = None
    priority: Optional[int] = Field(None, ge=0, le=100)
    is_active: Optional[bool] = None
    llm_config: Optional[Dict[str, Any]] = None

class AgentExecutionRequest(BaseModel):
    """Request model for executing an agent."""
    text: str = Field(..., min_length=1)
    context: Optional[Dict[str, Any]] = Field(default_factory=dict)
    options: Optional[Dict[str, Any]] = Field(default_factory=dict)

class BulkAgentExecutionRequest(BaseModel):
    """Request model for executing multiple agents."""
    text: str = Field(..., min_length=1)
    agent_ids: List[UUID] = Field(..., min_items=1)
    context: Optional[Dict[str, Any]] = Field(default_factory=dict)
    options: Optional[Dict[str, Any]] = Field(default_factory=dict)

class AgentResponse(BaseModel):
    """Response model for agent data."""
    success: bool
    data: Dict[str, Any]

class AgentListResponse(BaseModel):
    """Response model for agent list."""
    success: bool
    data: List[Dict[str, Any]]
    meta: Dict[str, Any]

@router.get("/", response_model=AgentListResponse)
async def list_user_agents(
    current_user: UserClaims = Depends(get_current_user),
    limit: int = Query(default=50, le=100),
    offset: int = Query(default=0, ge=0),
    agent_type: Optional[str] = Query(default=None),
    is_active: Optional[bool] = Query(default=None)
):
    """
    List all custom agents for the authenticated user.
    
    Args:
        current_user: Current authenticated user
        limit: Maximum number of agents to return
        offset: Number of agents to skip for pagination
        agent_type: Filter by agent type
        is_active: Filter by active status
        
    Returns:
        AgentListResponse: List of user's custom agents
    """
    try:
        logger.info(
            "Listing user agents",
            user_id=current_user.user_id,
            limit=limit,
            offset=offset,
            agent_type=agent_type,
            is_active=is_active
        )
        
        # Get agents from database
        db = await get_database()
        
        query = """
            SELECT 
                id, user_id, name, description, type, capabilities,
                priority, is_active, created_at, updated_at
            FROM custom_agents 
            WHERE user_id = $1
        """
        params = [current_user.user_id]
        param_index = 2
        
        # Apply filters
        if agent_type:
            query += f" AND type = ${param_index}"
            params.append(agent_type)
            param_index += 1
            
        if is_active is not None:
            query += f" AND is_active = ${param_index}"
            params.append(is_active)
            param_index += 1
        
        # Add ordering and pagination
        query += f" ORDER BY created_at DESC LIMIT ${param_index} OFFSET ${param_index + 1}"
        params.extend([limit, offset])
        
        async with db.get_connection() as conn:
            rows = await conn.fetch(query, *params)
            agents_data = [dict(row) for row in rows]
        
        # Get total count
        count_query = "SELECT COUNT(*) FROM custom_agents WHERE user_id = $1"
        count_params = [current_user.user_id]
        
        if agent_type:
            count_query += " AND type = $2"
            count_params.append(agent_type)
        if is_active is not None:
            param_idx = len(count_params) + 1
            count_query += f" AND is_active = ${param_idx}"
            count_params.append(is_active)
            
        async with db.get_connection() as conn:
            total_agents = await conn.fetchval(count_query, *count_params) or 0
        
        agents = {
            "data": agents_data,
            "meta": {
                "total": total_agents,
                "limit": limit,
                "offset": offset,
                "has_more": offset + limit < total_agents
            }
        }
        
        return AgentListResponse(
            success=True,
            data=agents["data"],
            meta=agents["meta"]
        )
        
    except Exception as e:
        logger.error("Failed to list user agents", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve agents"
        )

@router.post("/", response_model=AgentResponse)
async def create_custom_agent(
    agent_data: CustomAgentCreate,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Create a new custom agent for the authenticated user.
    
    Args:
        agent_data: Agent creation data
        current_user: Current authenticated user
        
    Returns:
        AgentResponse: Created agent data
    """
    try:
        logger.info(
            "Creating custom agent",
            user_id=current_user.user_id,
            agent_name=agent_data.name,
            agent_type=agent_data.type
        )
        
        # Create agent in database
        db = await get_database()
        agent_id = uuid4()
        now = datetime.utcnow()
        
        insert_query = """
            INSERT INTO custom_agents (
                id, user_id, name, description, type, capabilities,
                priority, is_active, created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $9
            ) RETURNING *
        """
        
        params = [
            agent_id, current_user.user_id, agent_data.name, agent_data.description,
            agent_data.type, agent_data.capabilities,
            agent_data.priority, True, now
        ]
        
        async with db.get_connection() as conn:
            agent_row = await conn.fetchrow(insert_query, *params)
            
        agent = dict(agent_row)
        
        logger.info("Custom agent created successfully", agent_id=agent["id"])
        
        return AgentResponse(
            success=True,
            data=agent
        )
        
    except Exception as e:
        logger.error("Failed to create custom agent", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=500,
            detail="Failed to create agent"
        )

@router.get("/{agent_id}", response_model=AgentResponse)
async def get_agent_details(
    agent_id: UUID,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Get details for a specific custom agent.
    
    Args:
        agent_id: Agent ID to retrieve
        current_user: Current authenticated user
        
    Returns:
        AgentResponse: Agent details
    """
    try:
        logger.info("Getting agent details", agent_id=str(agent_id), user_id=current_user.user_id)
        
        # Get agent from database
        db = await get_database()
        
        query = """
            SELECT 
                id, user_id, name, description, type, capabilities,
                priority, is_active, created_at, updated_at
            FROM custom_agents 
            WHERE id = $1 AND user_id = $2
        """
        
        async with db.get_connection() as conn:
            agent_row = await conn.fetchrow(query, str(agent_id), current_user.user_id)
            
        agent = dict(agent_row) if agent_row else None
        
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        return AgentResponse(
            success=True,
            data=agent
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get agent details", error=str(e), agent_id=str(agent_id))
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve agent details"
        )

@router.put("/{agent_id}", response_model=AgentResponse)
async def update_custom_agent(
    agent_id: UUID,
    agent_updates: CustomAgentUpdate,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Update a custom agent.
    
    Args:
        agent_id: Agent ID to update
        agent_updates: Agent update data
        current_user: Current authenticated user
        
    Returns:
        AgentResponse: Updated agent data
    """
    try:
        logger.info("Updating custom agent", agent_id=str(agent_id), user_id=current_user.user_id)
        
        # Update agent in database
        db = await get_database()
        
        # Build dynamic update query
        update_fields = []
        params = []
        param_index = 1
        
        for field, value in agent_updates.dict(exclude_unset=True).items():
            if value is not None:
                update_fields.append(f"{field} = ${param_index}")
                params.append(value)
                param_index += 1
        
        if not update_fields:
            raise HTTPException(
                status_code=400,
                detail="No valid fields to update"
            )
        
        # Add updated_at
        update_fields.append(f"updated_at = ${param_index}")
        params.append(datetime.utcnow())
        param_index += 1
        
        # Add WHERE conditions
        params.extend([str(agent_id), current_user.user_id])
        
        query = f"""
            UPDATE custom_agents 
            SET {', '.join(update_fields)}
            WHERE id = ${param_index - 1} AND user_id = ${param_index}
            RETURNING *
        """
        
        async with db.get_connection() as conn:
            agent_row = await conn.fetchrow(query, *params)
            
        agent = dict(agent_row) if agent_row else None
        
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        logger.info("Custom agent updated successfully", agent_id=str(agent_id))
        
        return AgentResponse(
            success=True,
            data=agent
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update custom agent", error=str(e), agent_id=str(agent_id))
        raise HTTPException(
            status_code=500,
            detail="Failed to update agent"
        )

@router.delete("/{agent_id}")
async def delete_custom_agent(
    agent_id: UUID,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Delete (deactivate) a custom agent.
    
    Args:
        agent_id: Agent ID to delete
        current_user: Current authenticated user
        
    Returns:
        dict: Success response
    """
    try:
        logger.info("Deleting custom agent", agent_id=str(agent_id), user_id=current_user.user_id)
        
        # Delete agent from database (soft delete)
        db = await get_database()
        
        query = """
            UPDATE custom_agents 
            SET is_active = false, updated_at = $1
            WHERE id = $2 AND user_id = $3
            RETURNING id
        """
        
        async with db.get_connection() as conn:
            result = await conn.fetchrow(query, datetime.utcnow(), str(agent_id), current_user.user_id)
        
        if not result:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        logger.info("Custom agent deleted successfully", agent_id=str(agent_id))
        
        return {"success": True, "message": "Agent deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete custom agent", error=str(e), agent_id=str(agent_id))
        raise HTTPException(
            status_code=500,
            detail="Failed to delete agent"
        )

@router.post("/{agent_id}/execute", response_model=AgentResponse)
async def execute_agent(
    agent_id: UUID,
    execution_request: AgentExecutionRequest,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Execute a specific agent on provided text.
    
    Args:
        agent_id: Agent ID to execute
        execution_request: Execution request data
        current_user: Current authenticated user
        
    Returns:
        AgentResponse: Agent execution results
    """
    try:
        logger.info(
            "Executing agent",
            agent_id=str(agent_id),
            user_id=current_user.user_id,
            text_length=len(execution_request.text)
        )
        
        # Execute agent
        result = await MultiAgentEngine.execute_single_agent(
            agent_id=str(agent_id),
            user_id=current_user.user_id,
            text=execution_request.text,
            context=execution_request.context,
            options=execution_request.options
        )
        
        logger.info("Agent execution completed", agent_id=str(agent_id))
        
        return AgentResponse(
            success=True,
            data=result
        )
        
    except Exception as e:
        logger.error("Agent execution failed", error=str(e), agent_id=str(agent_id))
        raise HTTPException(
            status_code=500,
            detail="Agent execution failed"
        )

@router.post("/execute-batch", response_model=AgentResponse)
async def execute_multiple_agents(
    execution_request: BulkAgentExecutionRequest,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Execute multiple agents on the same text.
    
    Args:
        execution_request: Bulk execution request data
        current_user: Current authenticated user
        
    Returns:
        AgentResponse: Combined execution results
    """
    try:
        logger.info(
            "Executing multiple agents",
            agent_count=len(execution_request.agent_ids),
            user_id=current_user.user_id,
            text_length=len(execution_request.text)
        )
        
        # Execute multiple agents
        results = await MultiAgentEngine.execute_multiple_agents(
            agent_ids=[str(aid) for aid in execution_request.agent_ids],
            user_id=current_user.user_id,
            text=execution_request.text,
            context=execution_request.context,
            options=execution_request.options
        )
        
        logger.info("Multiple agent execution completed", agent_count=len(execution_request.agent_ids))
        
        return AgentResponse(
            success=True,
            data=results
        )
        
    except Exception as e:
        logger.error("Bulk agent execution failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail="Bulk agent execution failed"
        )

@router.get("/{agent_id}/analytics", response_model=AgentResponse)
async def get_agent_analytics(
    agent_id: UUID,
    current_user: UserClaims = Depends(get_current_user),
    days: int = Query(default=30, ge=1, le=365)
):
    """
    Get performance analytics for a specific agent.
    
    Args:
        agent_id: Agent ID to get analytics for
        current_user: Current authenticated user
        days: Number of days to include in analytics
        
    Returns:
        AgentResponse: Agent performance analytics
    """
    try:
        logger.info("Getting agent analytics", agent_id=str(agent_id), days=days)
        
        # Get analytics from database
        db = await get_database()
        
        # Get agent usage statistics
        query = """
            SELECT 
                COUNT(*) as total_executions,
                AVG(processing_time_ms) as avg_execution_time,
                COUNT(*) FILTER (WHERE event_data->>'success' = 'true') * 100.0 / COUNT(*) as success_rate,
                MAX(created_at) as last_used
            FROM usage_events 
            WHERE event_data->>'agent_id' = $1 
                AND user_id = $2 
                AND created_at >= CURRENT_DATE - INTERVAL '%s days'
                AND event_type = 'agent_execution'
        """ % days
        
        try:
            async with db.get_connection() as conn:
                analytics_row = await conn.fetchrow(query, str(agent_id), current_user.user_id)
            analytics = dict(analytics_row) if analytics_row else None
        except Exception as e:
            logger.warning("Failed to get analytics from database", error=str(e))
            analytics = None
        
        if not analytics:
            analytics = {
                "total_executions": 0,
                "avg_execution_time": 0.0,
                "success_rate": 100.0,
                "last_used": None
            }
        
        return AgentResponse(
            success=True,
            data=analytics
        )
        
    except Exception as e:
        logger.error("Failed to get agent analytics", error=str(e), agent_id=str(agent_id))
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve agent analytics"
        )

@router.get("/templates", response_model=AgentListResponse)
async def get_agent_templates(
    current_user: UserClaims = Depends(get_current_user),
    category: Optional[str] = Query(default=None)
):
    """
    Get available agent templates.
    
    Args:
        current_user: Current authenticated user
        category: Filter by template category
        
    Returns:
        AgentListResponse: Available agent templates
    """
    try:
        logger.info("Getting agent templates", category=category)
        
        # Get templates from database
        db = await get_database()
        
        query = """
            SELECT 
                id, name, description, type, capabilities,
                priority, is_template, created_at
            FROM custom_agents 
            WHERE is_template = true
        """
        params = []
        
        if category:
            query += " AND category = $1"
            params.append(category)
        
        query += " ORDER BY name"
        
        async with db.get_connection() as conn:
            rows = await conn.fetch(query, *params)
            templates_data = [dict(row) for row in rows]
        
        templates = {
            "data": templates_data,
            "meta": {"total": len(templates_data)}
        }
        
        return AgentListResponse(
            success=True,
            data=templates["data"],
            meta=templates["meta"]
        )
        
    except Exception as e:
        logger.error("Failed to get agent templates", error=str(e))
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve agent templates"
        )