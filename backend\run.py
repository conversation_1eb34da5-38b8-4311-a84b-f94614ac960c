# This script is the recommended way to run the application locally for development.
# It loads environment variables, configures the server, and starts the application defined in `api/main.py`.

#!/usr/bin/env python3
"""
Development Server Runner

Purpose: Simple development server runner for local testing and development:
- Starts FastAPI application with uvicorn
- Configures hot reloading for development
- Sets up proper logging and error handling
- Loads environment variables and configuration

Responsibilities:
- Development server startup and configuration
- Environment variable loading
- Logging setup for development
- Hot reload configuration
- Port and host management

Used by: Local development, testing, debugging
Dependencies: uvicorn, python-dotenv, core application
"""

import os
import sys
from pathlib import Path
import uvicorn
from dotenv import load_dotenv

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def main():
    """Run the development server."""
    
    # Load environment variables
    env_file = backend_dir / ".env"
    if env_file.exists():
        load_dotenv(env_file)
        print(f"✅ Loaded environment from: {env_file}")
    else:
        print(f"⚠️  No .env file found at: {env_file}")
        print("💡 Copy .env.example to .env and configure your settings")
    
    # Get configuration from environment
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))
    debug = os.getenv("DEBUG", "true").lower() == "true"
    workers = int(os.getenv("API_WORKERS", "1"))
    
    print("\n🚀 Starting Revisionary Backend API")
    print(f"📍 URL: http://{host}:{port}")
    print(f"🔧 Debug Mode: {debug}")
    print(f"🔄 Hot Reload: {debug}")
    print(f"👥 Workers: {workers}")
    print(f"📚 API Docs: http://{host}:{port}/docs")
    print(f"📖 ReDoc: http://{host}:{port}/redoc")
    print(f"❤️  Health Check: http://{host}:{port}/health")
    
    if debug:
        print("\n🎯 Mock Mode Configuration:")
        print(f"   • Mock Auth: {os.getenv('USE_MOCK_AUTH', 'true')}")
        print(f"   • Mock Data: {os.getenv('USE_MOCK_DATA', 'true')}")
        print(f"   • Mock LLM: {os.getenv('USE_MOCK_LLM', 'true')}")
        print(f"   • Mock Redis: {os.getenv('USE_MOCK_REDIS', 'true')}")
    
    print("\n" + "="*50)
    
    try:
        # Run the server
        uvicorn.run(
            "api.main:app",
            host=host,
            port=port,
            reload=debug,
            workers=workers if not debug else 1,  # Use 1 worker in debug mode for reload
            reload_dirs=[str(backend_dir)] if debug else None,
            log_level="info" if debug else "warning"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()