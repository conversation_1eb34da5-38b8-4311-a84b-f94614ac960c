/**
 * Personas API Service
 * 
 * Handles all persona-related API calls including:
 * - Persona CRUD operations
 * - Multi-persona feedback generation
 * - Cross-audience analysis
 * - Persona templates and customization
 */

import { baseApi, ApiResponse } from './baseApi';

export interface Persona {
  id: string;
  user_id: string;
  name: string;
  description: string;
  type: 'academic' | 'professional' | 'creative' | 'general' | 'custom';
  demographics: {
    age_range?: string;
    education_level?: string;
    profession?: string;
    interests?: string[];
    location?: string;
  };
  reading_preferences: {
    complexity_level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    preferred_length: 'short' | 'medium' | 'long';
    attention_span: 'low' | 'medium' | 'high';
    reading_speed: 'slow' | 'average' | 'fast';
    preferred_tone: string[];
  };
  personality_traits: {
    analytical: number; // 1-10 scale
    emotional: number;
    detail_oriented: number;
    skeptical: number;
    open_minded: number;
    time_conscious: number;
  };
  feedback_style: {
    depth: 'surface' | 'moderate' | 'deep';
    focus_areas: string[];
    criticism_style: 'gentle' | 'direct' | 'constructive';
    suggestion_preference: 'specific' | 'general' | 'actionable';
  };
  is_active: boolean;
  is_template: boolean;
  usage_count: number;
  effectiveness_score: number;
  created_at: string;
  updated_at: string;
}

export interface PersonaCreate {
  name: string;
  description: string;
  type: Persona['type'];
  demographics: Persona['demographics'];
  reading_preferences: Persona['reading_preferences'];
  personality_traits: Persona['personality_traits'];
  feedback_style: Persona['feedback_style'];
}

export interface PersonaUpdate {
  name?: string;
  description?: string;
  demographics?: Partial<Persona['demographics']>;
  reading_preferences?: Partial<Persona['reading_preferences']>;
  personality_traits?: Partial<Persona['personality_traits']>;
  feedback_style?: Partial<Persona['feedback_style']>;
  is_active?: boolean;
}

export interface PersonaFeedback {
  id: string;
  persona_id: string;
  block_id: string;
  user_id: string;
  feedback_text: string;
  scores: {
    comprehension: number; // 1-10
    engagement: number;
    emotional_response: number;
    clarity: number;
    relevance: number;
    overall: number;
  };
  specific_comments: Array<{
    location: { start: number; end: number };
    comment: string;
    type: 'positive' | 'concern' | 'suggestion' | 'question';
    severity?: 'low' | 'medium' | 'high';
  }>;
  suggestions: Array<{
    type: 'structure' | 'content' | 'tone' | 'clarity' | 'engagement';
    priority: 'low' | 'medium' | 'high';
    title: string;
    description: string;
    specific_change?: string;
  }>;
  emotional_impact: {
    primary_emotion: string;
    emotional_intensity: number;
    engagement_level: string;
    trust_level: number;
  };
  created_at: string;
}

export interface PersonaFeedbackRequest {
  text: string;
  context?: {
    document_type?: string;
    purpose?: string;
    target_outcome?: string;
  };
  options?: {
    feedback_depth?: 'quick' | 'standard' | 'comprehensive';
    focus_areas?: string[];
    include_emotional_analysis?: boolean;
    include_suggestions?: boolean;
  };
}

export interface MultiFeedbackRequest {
  text: string;
  persona_ids: string[];
  context?: PersonaFeedbackRequest['context'];
  options?: PersonaFeedbackRequest['options'] & {
    enable_cross_analysis?: boolean;
    max_personas?: number;
  };
}

export interface MultiFeedbackResponse {
  feedback_results: PersonaFeedback[];
  cross_audience_analysis?: {
    consistency_score: number;
    conflicting_feedback: Array<{
      feedback_1: string;
      feedback_2: string;
      conflict_type: 'tone' | 'content' | 'structure' | 'style';
      severity: 'minor' | 'moderate' | 'major';
    }>;
    universal_strengths: string[];
    universal_concerns: string[];
    optimization_suggestions: Array<{
      change: string;
      impact_on_personas: Record<string, 'positive' | 'negative' | 'neutral'>;
      priority: 'low' | 'medium' | 'high';
    }>;
    overall_appeal_score: number;
  };
  processing_time: number;
  personas_analyzed: number;
}

export interface PersonaTemplate {
  id: string;
  name: string;
  description: string;
  type: Persona['type'];
  preview_image?: string;
  category: 'academic' | 'business' | 'creative' | 'technical' | 'general';
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  template_data: Omit<PersonaCreate, 'name' | 'description'>;
  usage_count: number;
  rating: number;
  is_premium: boolean;
}

export interface PersonaAnalytics {
  persona_id: string;
  usage_stats: {
    total_feedback_sessions: number;
    avg_scores: PersonaFeedback['scores'];
    most_common_suggestions: Array<{ type: string; count: number }>;
    effectiveness_trends: Array<{ date: string; score: number }>;
  };
  impact_analysis: {
    documents_improved: number;
    avg_improvement_score: number;
    user_satisfaction: number;
    most_helpful_feedback_types: string[];
  };
  comparison_with_others: {
    rank_among_user_personas: number;
    performance_vs_templates: number;
    unique_insights_provided: number;
  };
}

class PersonasApiService {
  /**
   * Get list of user personas
   */
  async getPersonas(params?: {
    type?: Persona['type'];
    is_active?: boolean;
    include_templates?: boolean;
  }): Promise<Persona[]> {
    const response = await baseApi.get<Persona[]>('/personas', { params });
    return response.data;
  }

  /**
   * Get a specific persona by ID
   */
  async getPersona(id: string): Promise<Persona> {
    const response = await baseApi.get<Persona>(`/personas/${id}`);
    return response.data;
  }

  /**
   * Create a new persona
   */
  async createPersona(persona: PersonaCreate): Promise<Persona> {
    const response = await baseApi.post<Persona>('/personas', persona);
    return response.data;
  }

  /**
   * Update an existing persona
   */
  async updatePersona(id: string, updates: PersonaUpdate): Promise<Persona> {
    const response = await baseApi.put<Persona>(`/personas/${id}`, updates);
    return response.data;
  }

  /**
   * Delete a persona
   */
  async deletePersona(id: string): Promise<{ success: boolean }> {
    const response = await baseApi.delete(`/personas/${id}`);
    return response.data;
  }

  /**
   * Request feedback from a single persona
   */
  async requestPersonaFeedback(personaId: string, request: PersonaFeedbackRequest): Promise<PersonaFeedback> {
    const response = await baseApi.post<PersonaFeedback>(`/personas/${personaId}/feedback`, request);
    return response.data;
  }

  /**
   * Request feedback from multiple personas
   */
  async requestMultiFeedback(request: MultiFeedbackRequest): Promise<MultiFeedbackResponse> {
    const response = await baseApi.post<MultiFeedbackResponse>('/personas/multi-feedback', request);
    return response.data;
  }

  /**
   * Get persona feedback history
   */
  async getFeedbackHistory(personaId: string, params?: {
    limit?: number;
    offset?: number;
    document_id?: string;
    date_range?: { start: string; end: string };
  }): Promise<{
    feedback: PersonaFeedback[];
    total: number;
    has_more: boolean;
  }> {
    const response = await baseApi.get(`/personas/${personaId}/feedback/history`, { params });
    return response.data;
  }

  /**
   * Get available persona templates
   */
  async getPersonaTemplates(category?: PersonaTemplate['category']): Promise<PersonaTemplate[]> {
    const response = await baseApi.get<PersonaTemplate[]>('/personas/templates', {
      params: { category }
    });
    return response.data;
  }

  /**
   * Create persona from template
   */
  async createFromTemplate(templateId: string, customization: {
    name: string;
    description?: string;
    modifications?: Partial<PersonaCreate>;
  }): Promise<Persona> {
    const response = await baseApi.post<Persona>(`/personas/templates/${templateId}/instantiate`, customization);
    return response.data;
  }

  /**
   * Get persona analytics and performance data
   */
  async getPersonaAnalytics(personaId: string, timeRange?: 'week' | 'month' | 'quarter' | 'year'): Promise<PersonaAnalytics> {
    const response = await baseApi.get<PersonaAnalytics>(`/personas/${personaId}/analytics`, {
      params: { time_range: timeRange }
    });
    return response.data;
  }

  /**
   * Test persona configuration
   */
  async testPersona(personaData: PersonaCreate, testText: string): Promise<{
    test_feedback: PersonaFeedback;
    configuration_suggestions: Array<{
      area: string;
      suggestion: string;
      impact: 'low' | 'medium' | 'high';
    }>;
    effectiveness_prediction: number;
  }> {
    const response = await baseApi.post('/personas/test', {
      persona_data: personaData,
      test_text: testText
    });
    return response.data;
  }

  /**
   * Duplicate persona with modifications
   */
  async duplicatePersona(id: string, modifications?: {
    name: string;
    changes?: Partial<PersonaCreate>;
  }): Promise<Persona> {
    const response = await baseApi.post<Persona>(`/personas/${id}/duplicate`, modifications);
    return response.data;
  }

  /**
   * Archive/unarchive persona
   */
  async togglePersonaActive(id: string, isActive: boolean): Promise<Persona> {
    const response = await baseApi.patch<Persona>(`/personas/${id}/toggle-active`, {
      is_active: isActive
    });
    return response.data;
  }

  /**
   * Get persona effectiveness over time
   */
  async getPersonaEffectiveness(personaId: string): Promise<{
    effectiveness_score: number;
    score_history: Array<{ date: string; score: number }>;
    improvement_areas: string[];
    strengths: string[];
    usage_frequency: number;
    user_satisfaction: number;
  }> {
    const response = await baseApi.get(`/personas/${personaId}/effectiveness`);
    return response.data;
  }

  /**
   * Compare multiple personas
   */
  async comparePersonas(personaIds: string[], testText?: string): Promise<{
    comparison_results: Array<{
      persona: Persona;
      effectiveness_score: number;
      unique_insights: string[];
      overlap_with_others: number;
    }>;
    recommendations: {
      keep: string[];
      modify: Array<{ persona_id: string; suggestions: string[] }>;
      consider_removing: string[];
    };
    test_feedback?: MultiFeedbackResponse;
  }> {
    const response = await baseApi.post('/personas/compare', {
      persona_ids: personaIds,
      test_text: testText
    });
    return response.data;
  }

  /**
   * Get persona suggestions based on user's writing
   */
  async getPersonaSuggestions(params?: {
    document_types?: string[];
    target_audiences?: string[];
    writing_goals?: string[];
  }): Promise<Array<{
    template: PersonaTemplate;
    relevance_score: number;
    reasons: string[];
    customization_suggestions: Record<string, any>;
  }>> {
    const response = await baseApi.get('/personas/suggestions', { params });
    return response.data;
  }
}

// Export singleton instance
export const personasApi = new PersonasApiService();
export default personasApi;