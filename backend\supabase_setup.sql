-- REVISIONARY DATABASE SETUP - COMPLETE SCHEMA
-- Copy and paste this entire file into Supabase SQL Editor
-- This creates all tables, indexes, functions, and RLS policies

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "ltree";
CREATE EXTENSION IF NOT EXISTS "vector";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create all enums first
CREATE TYPE subscription_tier_enum AS ENUM ('free', 'professional', 'studio', 'enterprise');
CREATE TYPE subscription_status_enum AS ENUM ('active', 'canceled', 'past_due', 'paused');
CREATE TYPE document_type_enum AS ENUM ('creative', 'academic', 'professional', 'general', 'technical', 'legal');
CREATE TYPE document_status_enum AS ENUM ('draft', 'editing', 'review', 'published', 'archived');
CREATE TYPE block_type_enum AS ENUM ('document', 'chapter', 'section', 'subsection', 'paragraph', 'heading', 'list', 'quote', 'code');
CREATE TYPE version_change_type_enum AS ENUM ('create', 'edit', 'ai_suggestion', 'collaboration', 'import', 'restore');
CREATE TYPE agent_type_enum AS ENUM ('grammar', 'style', 'structure', 'content', 'research', 'citation', 'technical', 'creative');
CREATE TYPE suggestion_severity_enum AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE suggestion_status_enum AS ENUM ('pending', 'accepted', 'rejected', 'stale', 'expired');
CREATE TYPE suggestion_feedback_enum AS ENUM ('helpful', 'not_helpful', 'incorrect');

-- Users Table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    firebase_uid VARCHAR(128) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    avatar_url TEXT,
    subscription_tier subscription_tier_enum NOT NULL DEFAULT 'free',
    subscription_status subscription_status_enum NOT NULL DEFAULT 'active',
    subscription_period_start TIMESTAMPTZ,
    subscription_period_end TIMESTAMPTZ,
    onboarding_completed BOOLEAN DEFAULT FALSE,
    preferences JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_active_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT users_email_valid CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT users_preferences_valid CHECK (jsonb_typeof(preferences) = 'object')
);

-- Documents Table
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    type document_type_enum NOT NULL,
    status document_status_enum DEFAULT 'draft',
    description TEXT,
    word_count INTEGER DEFAULT 0,
    character_count INTEGER DEFAULT 0,
    version INTEGER DEFAULT 1,
    is_template BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    language VARCHAR(10) DEFAULT 'en',
    metadata JSONB DEFAULT '{}'::jsonb,
    settings JSONB DEFAULT '{}'::jsonb,
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    published_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ,
    
    -- Computed columns
    slug VARCHAR(255) GENERATED ALWAYS AS (
        LOWER(REGEXP_REPLACE(title, '[^a-zA-Z0-9]+', '-', 'g'))
    ) STORED,
    
    -- Constraints
    CONSTRAINT documents_title_not_empty CHECK (LENGTH(TRIM(title)) > 0),
    CONSTRAINT documents_word_count_positive CHECK (word_count >= 0),
    CONSTRAINT documents_character_count_positive CHECK (character_count >= 0),
    CONSTRAINT documents_metadata_valid CHECK (jsonb_typeof(metadata) = 'object'),
    CONSTRAINT documents_settings_valid CHECK (jsonb_typeof(settings) = 'object')
);

-- Blocks Table (Hierarchical Document Structure)
CREATE TABLE blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    type block_type_enum NOT NULL,
    position INTEGER NOT NULL,
    depth INTEGER DEFAULT 0,
    path LTREE, -- Materialized path for efficient tree queries
    content TEXT,
    content_hash VARCHAR(64), -- SHA256 hash for conflict detection
    content_length INTEGER GENERATED ALWAYS AS (LENGTH(content)) STORED,
    word_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT blocks_position_positive CHECK (position >= 0),
    CONSTRAINT blocks_depth_positive CHECK (depth >= 0),
    CONSTRAINT blocks_word_count_positive CHECK (word_count >= 0),
    CONSTRAINT blocks_content_hash_valid CHECK (content_hash ~ '^[a-f0-9]{64}$'),
    CONSTRAINT blocks_metadata_valid CHECK (jsonb_typeof(metadata) = 'object'),
    
    -- Tree structure constraints
    CONSTRAINT blocks_parent_different CHECK (id != parent_id),
    CONSTRAINT blocks_root_no_parent CHECK (
        (type = 'document' AND parent_id IS NULL) OR 
        (type != 'document' AND parent_id IS NOT NULL)
    )
);

-- Suggestions Table (AI Suggestions)
CREATE TABLE suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    agent_type agent_type_enum NOT NULL,
    severity suggestion_severity_enum NOT NULL,
    category VARCHAR(50),
    original_text TEXT NOT NULL,
    suggested_text TEXT NOT NULL,
    explanation TEXT,
    confidence DECIMAL(3,2) CHECK (confidence >= 0 AND confidence <= 1),
    position JSONB NOT NULL, -- {start: int, end: int, line: int}
    original_hash VARCHAR(64) NOT NULL, -- Hash of the original block content
    status suggestion_status_enum DEFAULT 'pending',
    user_feedback suggestion_feedback_enum,
    feedback_note TEXT,
    tokens_used INTEGER DEFAULT 0,
    model_used VARCHAR(50),
    processing_time_ms INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    resolved_at TIMESTAMPTZ,
    resolved_by UUID REFERENCES users(id),
    expires_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT suggestions_position_valid CHECK (
        jsonb_typeof(position) = 'object' AND
        position ? 'start' AND position ? 'end'
    ),
    CONSTRAINT suggestions_original_hash_valid CHECK (original_hash ~ '^[a-f0-9]{64}$'),
    CONSTRAINT suggestions_tokens_positive CHECK (tokens_used >= 0),
    CONSTRAINT suggestions_processing_time_positive CHECK (processing_time_ms >= 0),
    CONSTRAINT suggestions_confidence_range CHECK (confidence IS NULL OR (confidence >= 0 AND confidence <= 1))
);

-- Add basic indexes
CREATE INDEX idx_users_firebase_uid ON users(firebase_uid);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_documents_owner ON documents(owner_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_documents_updated ON documents(updated_at DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_blocks_document ON blocks(document_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_blocks_parent ON blocks(parent_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_suggestions_block_status ON suggestions(block_id, status);

-- Functions and Triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blocks_updated_at BEFORE UPDATE ON blocks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE suggestions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY users_own_data ON users
    FOR ALL USING (firebase_uid = auth.jwt() ->> 'sub');

CREATE POLICY documents_owner_access ON documents
    FOR ALL USING (
        owner_id IN (
            SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
        )
    );

CREATE POLICY documents_public_read ON documents
    FOR SELECT USING (is_public = TRUE AND deleted_at IS NULL);

CREATE POLICY blocks_document_access ON blocks
    FOR ALL USING (
        document_id IN (
            SELECT id FROM documents WHERE owner_id IN (
                SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
            )
        )
    );

CREATE POLICY suggestions_block_access ON suggestions
    FOR ALL USING (
        block_id IN (
            SELECT id FROM blocks WHERE document_id IN (
                SELECT id FROM documents WHERE owner_id IN (
                    SELECT id FROM users WHERE firebase_uid = auth.jwt() ->> 'sub'
                )
            )
        )
    );

-- Insert a test record to verify everything works
INSERT INTO users (firebase_uid, email, display_name) 
VALUES ('test-firebase-uid', '<EMAIL>', 'Test User')
ON CONFLICT (firebase_uid) DO NOTHING;

-- Success message
SELECT 'Revisionary database schema created successfully!' as message;