# Revisionary - API Documentation

## 1. Overview

The Revisionary API provides programmatic access to all writing assistance features. This RESTful API uses JSON for request and response bodies, with additional WebSocket endpoints for real-time features.

### Base URL
```
Production: https://api.revisionary.app/v1
Staging: https://api-staging.revisionary.app/v1
```

### Authentication
All API requests require authentication via access tokens obtained through Firebase ID token exchange:

1. **First, exchange your Firebase ID token for an access token:**
```
POST /auth/token
Content-Type: application/json
{
  "firebase_token": "<firebase-id-token>"
}
```

2. **Then use the access token for all subsequent requests:**
```
Authorization: Bearer <access-token>
```

Access tokens expire after 1 hour and can be refreshed using the refresh token.

### Rate Limiting
Rate limits are applied per user based on subscription tier:
- **Free**: 100 requests/hour
- **Professional**: 1,000 requests/hour
- **Studio**: 10,000 requests/hour
- **Enterprise**: Unlimited

### Response Format
```json
{
  "success": true,
  "data": { ... },
  "meta": {
    "request_id": "req_123abc",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### Error Format
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "The request body is invalid",
    "details": { ... }
  },
  "meta": {
    "request_id": "req_123abc",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## 2. Authentication Endpoints

### POST /auth/token
Exchange Firebase ID token for API access token.

**Request:**
```json
{
  "firebase_token": "eyJhbGciOiJIUzI1NiIs..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "access_token": "at_abc123...",
    "refresh_token": "rt_def456...",
    "expires_in": 3600,
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "subscription_tier": "professional"
    }
  }
}
```

### POST /auth/refresh
Refresh an expired access token.

**Request:**
```json
{
  "refresh_token": "rt_def456..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "access_token": "at_ghi789...",
    "expires_in": 3600
  }
}
```

## 3. Document Management

### GET /documents
List all documents for the authenticated user.

**Query Parameters:**
- `page` (integer): Page number (default: 1)
- `limit` (integer): Items per page (default: 20, max: 100)
- `sort` (string): Sort field (created_at, updated_at, title)
- `order` (string): Sort order (asc, desc)
- `search` (string): Search in title and content
- `type` (string): Filter by document type (creative, academic, professional, general)

**Response:**
```json
{
  "success": true,
  "data": {
    "documents": [
      {
        "id": "doc_123",
        "title": "My Novel",
        "type": "creative",
        "word_count": 45230,
        "created_at": "2024-01-10T08:00:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "metadata": {
          "genre": "fantasy",
          "status": "draft"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 145,
      "pages": 8
    }
  }
}
```

### POST /documents
Create a new document.

**Request:**
```json
{
  "title": "Research Paper",
  "type": "academic",
  "content": "# Introduction\n\nThis paper explores...",
  "metadata": {
    "subject": "Computer Science",
    "deadline": "2024-02-01"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "doc_456",
    "title": "Research Paper",
    "type": "academic",
    "word_count": 150,
    "created_at": "2024-01-15T10:35:00Z",
    "updated_at": "2024-01-15T10:35:00Z",
    "blocks": [
      {
        "id": "block_789",
        "type": "section",
        "content": "# Introduction\n\nThis paper explores...",
        "position": 0
      }
    ]
  }
}
```

### GET /documents/{document_id}
Get a specific document with all blocks.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "doc_123",
    "title": "My Novel",
    "type": "creative",
    "word_count": 45230,
    "created_at": "2024-01-10T08:00:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "metadata": { ... },
    "blocks": [
      {
        "id": "block_001",
        "type": "chapter",
        "content": "# Chapter 1: The Beginning",
        "position": 0,
        "children": ["block_002", "block_003"]
      },
      {
        "id": "block_002",
        "type": "paragraph",
        "parent_id": "block_001",
        "content": "It was a dark and stormy night...",
        "position": 0
      }
    ],
    "outline": [
      {
        "id": "block_001",
        "title": "Chapter 1: The Beginning",
        "type": "chapter",
        "children": [...]
      }
    ]
  }
}
```

### PATCH /documents/{document_id}
Update document metadata.

**Request:**
```json
{
  "title": "My Novel - Revised",
  "metadata": {
    "status": "editing"
  }
}
```

### DELETE /documents/{document_id}
Delete a document.

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Document deleted successfully"
  }
}
```

## 4. Block Operations

### GET /blocks/{block_id}
Get a specific block with suggestions.

**Query Parameters:**
- `include_suggestions` (boolean): Include AI suggestions (default: true)
- `suggestion_status` (string): Filter suggestions by status (pending, accepted, rejected)

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "block_123",
    "document_id": "doc_123",
    "type": "paragraph",
    "content": "The protagonist walked slowly through the forest.",
    "position": 5,
    "hash": "a1b2c3d4...",
    "suggestions": [
      {
        "id": "sug_001",
        "agent": "style",
        "severity": "medium",
        "original_text": "walked slowly",
        "suggested_text": "ambled",
        "explanation": "More concise and evocative verb choice",
        "confidence": 0.85,
        "position": {
          "start": 17,
          "end": 29
        },
        "status": "pending"
      }
    ]
  }
}
```

### PATCH /blocks/{block_id}
Update block content.

**Request:**
```json
{
  "content": "The protagonist ambled through the misty forest.",
  "cursor_position": 48,
  "client_hash": "a1b2c3d4..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "block_123",
    "content": "The protagonist ambled through the misty forest.",
    "hash": "e5f6g7h8...",
    "version": 2,
    "suggestions_invalidated": ["sug_001"]
  }
}
```

### POST /blocks/{block_id}/suggestions
Request AI suggestions for a block.

**Request:**
```json
{
  "agents": ["grammar", "style", "structure"],
  "intent": "improve",
  "context_level": "paragraph",
  "options": {
    "style_preferences": ["concise", "vivid"],
    "formality": "casual"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "job_id": "job_789",
    "status": "processing",
    "estimated_time": 2.5,
    "stream_url": "/v1/ai/stream/job_789"
  }
}
```

## 5. Agent Management

Note: The AI operations are now handled through the agent system. See Agent Management endpoints below for current AI functionality.

## 6. Persona System Endpoints

### GET /personas
List all personas for the authenticated user.

**Query Parameters:**
- `type` (string): Filter by persona type (academic, professional, creative, etc.)
- `active` (boolean): Filter by active status (default: true)
- `template` (boolean): Include template personas

**Response:**
```json
{
  "success": true,
  "data": {
    "personas": [
      {
        "id": "persona_123",
        "name": "Academic Reviewer",
        "type": "academic",
        "description": "Graduate-level peer reviewer focused on methodology",
        "demographics": {
          "age_range": "25-35",
          "education": "PhD",
          "expertise": "Research methodology"
        },
        "is_active": true,
        "usage_count": 15,
        "effectiveness_score": 0.87,
        "created_at": "2024-01-10T08:00:00Z"
      }
    ]
  }
}
```

### POST /personas
Create a new persona.

**Request:**
```json
{
  "name": "Young Adult Reader",
  "type": "age_specific",
  "description": "Target audience for YA fiction",
  "demographics": {
    "age_range": "16-24",
    "reading_level": "high_school",
    "interests": ["fantasy", "romance", "adventure"]
  },
  "reading_preferences": {
    "pace": "fast",
    "complexity": "medium",
    "emotional_engagement": "high"
  },
  "personality_traits": {
    "openness": 0.8,
    "analytical_thinking": 0.6,
    "emotional_sensitivity": 0.9
  },
  "feedback_style": {
    "tone": "encouraging",
    "detail_level": "medium",
    "focus_areas": ["engagement", "relatability"]
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "persona_456",
    "name": "Young Adult Reader",
    "type": "age_specific",
    "is_active": true,
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

### GET /personas/{persona_id}
Get detailed persona information.

### PUT /personas/{persona_id}
Update persona configuration.

### DELETE /personas/{persona_id}
Deactivate a persona.

### POST /personas/{persona_id}/feedback
Request feedback from a specific persona.

**Request:**
```json
{
  "block_id": "block_789",
  "text": "The protagonist's journey through the enchanted forest...",
  "context": {
    "genre": "fantasy",
    "target_audience": "young_adult",
    "chapter": 3
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "feedback_id": "feedback_123",
    "persona_id": "persona_456",
    "session_id": "session_789",
    "comprehension_score": 0.85,
    "engagement_score": 0.92,
    "emotional_score": 0.88,
    "style_score": 0.79,
    "overall_score": 0.86,
    "feedback_text": "This passage effectively captures the wonder and mystery that young adult readers crave...",
    "specific_issues": [
      {
        "type": "pacing",
        "description": "The description could be more concise to maintain engagement",
        "position": {"start": 45, "end": 128}
      }
    ],
    "suggestions": [
      {
        "type": "engagement",
        "suggestion": "Add more sensory details to help readers visualize the scene",
        "priority": "medium"
      }
    ],
    "confidence": 0.91
  }
}
```

### POST /personas/multi-feedback
Request feedback from multiple personas simultaneously.

**Request:**
```json
{
  "block_id": "block_789",
  "persona_ids": ["persona_123", "persona_456", "persona_789"],
  "text": "Content to analyze...",
  "options": {
    "include_cross_analysis": true,
    "conflict_detection": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "session_id": "session_abc",
    "feedback": [
      {
        "persona_id": "persona_123",
        "feedback": { /* Individual feedback object */ }
      }
    ],
    "cross_audience_analysis": {
      "overall_appeal_score": 0.82,
      "conflicts": [
        {
          "type": "complexity_mismatch",
          "description": "Content may be too complex for younger personas",
          "affected_personas": ["persona_456"]
        }
      ],
      "optimization_suggestions": [
        {
          "suggestion": "Simplify technical language in paragraph 2",
          "impact": "Improves accessibility for younger readers"
        }
      ]
    }
  }
}
```

## 7. Custom Agent System Endpoints

### GET /agents
List all custom agents for the authenticated user.

**Query Parameters:**
- `type` (string): Filter by agent type
- `active` (boolean): Filter by active status
- `template` (boolean): Include template agents

**Response:**
```json
{
  "success": true,
  "data": {
    "agents": [
      {
        "id": "agent_123",
        "name": "Academic Style Enforcer",
        "type": "style",
        "description": "Ensures academic writing standards",
        "capabilities": ["edit", "tone_adjustment", "consistency_check"],
        "priority": 75,
        "is_active": true,
        "model_preference": "gpt-4.1-mini",
        "usage_stats": {
          "total_invocations": 156,
          "acceptance_rate": 0.78,
          "avg_processing_time": 1200
        },
        "created_at": "2024-01-05T14:20:00Z",
        "last_used_at": "2024-01-15T09:15:00Z"
      }
    ]
  }
}
```

### POST /agents
Create a new custom agent.

**Request:**
```json
{
  "name": "Business Report Optimizer",
  "type": "professional",
  "description": "Optimizes content for executive consumption",
  "capabilities": ["edit", "condense", "tone_adjustment"],
  "priority": 60,
  "model_preference": "gpt-4.1-full",
  "max_context_tokens": 8000,
  "temperature": 0.2,
  "style_rules": [
    {
      "category": "tone",
      "rule_name": "executive_formality",
      "rule_value": {
        "formality_level": "high",
        "avoid_jargon": true,
        "prefer_active_voice": true
      }
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "agent_456",
    "name": "Business Report Optimizer",
    "type": "professional",
    "is_active": true,
    "created_at": "2024-01-15T11:00:00Z"
  }
}
```

### PUT /agents/{agent_id}
Update agent configuration.

### DELETE /agents/{agent_id}
Deactivate a custom agent.

### POST /agents/{agent_id}/execute
Execute a custom agent on specific content.

**Request:**
```json
{
  "block_id": "block_789",
  "text": "The quarterly results demonstrate significant growth...",
  "context": {
    "document_type": "business_report",
    "audience": "executives"
  },
  "options": {
    "max_suggestions": 10,
    "min_confidence": 0.7
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "execution_id": "exec_123",
    "suggestions": [
      {
        "id": "sug_001",
        "type": "edit",
        "original_text": "demonstrate significant growth",
        "suggested_text": "show 23% year-over-year growth",
        "explanation": "More specific and quantifiable for executive audience",
        "confidence": 0.87,
        "position": {"start": 25, "end": 52}
      }
    ],
    "processing_time_ms": 1150,
    "tokens_used": 245
  }
}
```

### GET /agents/{agent_id}/analytics
Get performance analytics for a specific agent.

**Response:**
```json
{
  "success": true,
  "data": {
    "usage_stats": {
      "total_invocations": 156,
      "avg_processing_time_ms": 1200,
      "acceptance_rate": 0.78,
      "avg_confidence": 0.84,
      "tokens_used": 12450,
      "error_rate": 0.02
    },
    "performance_trends": [
      {
        "date": "2024-01-15",
        "invocations": 12,
        "acceptance_rate": 0.83
      }
    ],
    "most_common_suggestions": [
      {
        "type": "tone_adjustment",
        "count": 45,
        "avg_confidence": 0.89
      }
    ]
  }
}
```

## 8. Writing Health Analysis Endpoints

### POST /health/analyze
Analyze writing health for a text block.

**Request:**
```json
{
  "block_id": "block_789",
  "text": "The comprehensive analysis of the aforementioned data demonstrates...",
  "options": {
    "include_suggestions": true,
    "target_audience": "general",
    "brand_voice": "professional"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "health_id": "health_123",
    "overall_score": 72.5,
    "metrics": {
      "readability": {
        "score": 65.2,
        "flesch_kincaid_grade": 12.3,
        "flesch_reading_ease": 52.1,
        "target_grade": "college"
      },
      "clarity": {
        "score": 68.8,
        "avg_sentence_length": 22.5,
        "complex_words_percentage": 18.2,
        "passive_voice_percentage": 25.0
      },
      "voice_consistency": {
        "score": 78.3,
        "tone_variation": 0.12,
        "formality_consistency": 0.85
      },
      "brand_alignment": {
        "score": 71.2,
        "voice_match": 0.73,
        "terminology_consistency": 0.69
      }
    },
    "issues": [
      {
        "id": "issue_001",
        "type": "clarity",
        "severity": "medium",
        "category": "wordiness",
        "description": "Sentence contains unnecessary filler words",
        "position": {"start": 0, "end": 65},
        "suggested_fix": "Remove 'aforementioned' and 'comprehensive'",
        "confidence": 0.89
      }
    ],
    "suggestions": [
      {
        "type": "readability",
        "suggestion": "Break long sentences into shorter, clearer statements",
        "impact": "Improves comprehension for broader audience"
      }
    ],
    "processing_time_ms": 340
  }
}
```

### GET /health/trends
Get writing health trends for a user.

**Query Parameters:**
- `period` (string): day, week, month (default: month)
- `document_id` (string): Filter by specific document

**Response:**
```json
{
  "success": true,
  "data": {
    "period": "month",
    "trends": [
      {
        "date": "2024-01-15",
        "avg_score": 74.2,
        "analyses_count": 23,
        "top_issues": ["readability", "clarity"]
      }
    ],
    "improvement_rate": 8.5,
    "focus_areas": [
      {
        "metric": "readability",
        "current_score": 72.1,
        "trend": "improving",
        "change_percentage": 12.3
      }
    ]
  }
}
```

### POST /health/batch-analyze
Analyze multiple blocks simultaneously.

**Request:**
```json
{
  "blocks": [
    {
      "block_id": "block_123",
      "text": "First paragraph content..."
    },
    {
      "block_id": "block_456",
      "text": "Second paragraph content..."
    }
  ],
  "options": {
    "priority": "normal",
    "include_cross_block_analysis": true
  }
}
```

## 9. Analytics & Achievement Endpoints

### GET /analytics/sessions
Get writing session analytics.

**Query Parameters:**
- `start_date` (string): ISO 8601 date
- `end_date` (string): ISO 8601 date
- `document_id` (string): Filter by document

**Response:**
```json
{
  "success": true,
  "data": {
    "sessions": [
      {
        "id": "session_123",
        "start_time": "2024-01-15T09:00:00Z",
        "end_time": "2024-01-15T11:30:00Z",
        "words_written": 847,
        "time_writing_ms": 6420000,
        "time_thinking_ms": 2580000,
        "ai_interactions": 8,
        "revisions_count": 23,
        "productivity_score": 0.78,
        "focus_score": 0.71
      }
    ],
    "summary": {
      "total_sessions": 45,
      "total_words": 23456,
      "avg_session_length_ms": 5400000,
      "avg_productivity_score": 0.73
    }
  }
}
```

### GET /analytics/goals
Get user's writing goals and progress.

**Response:**
```json
{
  "success": true,
  "data": {
    "goals": [
      {
        "id": "goal_123",
        "type": "daily_words",
        "target": 500,
        "current": 347,
        "progress_percentage": 69.4,
        "timeframe": "daily",
        "status": "in_progress",
        "streak_days": 5,
        "created_at": "2024-01-10T00:00:00Z"
      }
    ],
    "streaks": {
      "current_writing_streak": 5,
      "longest_writing_streak": 23,
      "current_goal_streak": 3
    }
  }
}
```

### POST /analytics/goals
Create a new writing goal.

**Request:**
```json
{
  "type": "weekly_words",
  "target": 2500,
  "timeframe": "weekly",
  "start_date": "2024-01-15"
}
```

### GET /analytics/achievements
Get user achievements.

**Response:**
```json
{
  "success": true,
  "data": {
    "achievements": [
      {
        "id": "achievement_123",
        "name": "First Thousand",
        "description": "Write your first 1,000 words",
        "category": "milestone",
        "rarity": "common",
        "progress": 100,
        "unlocked_at": "2024-01-12T15:30:00Z",
        "points": 100
      }
    ],
    "stats": {
      "total_achievements": 15,
      "total_points": 2340,
      "common_achievements": 8,
      "rare_achievements": 5,
      "epic_achievements": 2,
      "legendary_achievements": 0
    }
  }
}
```

### POST /analytics/events
Track a custom analytics event.

**Request:**
```json
{
  "event_type": "feature_used",
  "event_data": {
    "feature": "persona_feedback",
    "persona_count": 3,
    "session_id": "session_123"
  },
  "document_id": "doc_456"
}
```

## 10. Cross-Reference Intelligence Endpoints

### GET /entities
List entities in a document.

**Query Parameters:**
- `document_id` (string): Required document ID
- `type` (string): Filter by entity type
- `search` (string): Search entity names

**Response:**
```json
{
  "success": true,
  "data": {
    "entities": [
      {
        "id": "entity_123",
        "name": "Professor Smith",
        "type": "character",
        "aliases": ["Prof. Smith", "Dr. Smith"],
        "description": "Physics professor and mentor figure",
        "attributes": {
          "age": "mid-50s",
          "occupation": "University Professor",
          "expertise": "Quantum Physics"
        },
        "mention_count": 23,
        "importance_score": 0.85,
        "first_mentioned_block_id": "block_012",
        "relationships": [
          {
            "target_entity_id": "entity_456",
            "relationship_type": "professional",
            "relationship_name": "mentor"
          }
        ]
      }
    ]
  }
}
```

### POST /entities
Create a new entity.

**Request:**
```json
{
  "document_id": "doc_123",
  "name": "Central University",
  "type": "location",
  "description": "Prestigious research university",
  "attributes": {
    "type": "educational_institution",
    "size": "large",
    "location": "urban"
  },
  "first_mentioned_block_id": "block_045"
}
```

### GET /entities/{entity_id}/consistency
Check consistency for a specific entity.

**Response:**
```json
{
  "success": true,
  "data": {
    "entity_id": "entity_123",
    "consistency_score": 0.82,
    "violations": [
      {
        "id": "violation_001",
        "type": "attribute_conflict",
        "severity": "moderate",
        "description": "Character age inconsistency between chapters 3 and 7",
        "conflicting_blocks": ["block_123", "block_456"],
        "suggested_resolution": "Standardize age reference to 'mid-50s'"
      }
    ],
    "timeline_issues": [],
    "relationship_conflicts": []
  }
}
```

### POST /documents/{document_id}/consistency-check
Run full consistency analysis on a document.

**Response:**
```json
{
  "success": true,
  "data": {
    "job_id": "consistency_job_123",
    "status": "processing",
    "estimated_time_ms": 15000,
    "stream_url": "/v1/consistency/stream/consistency_job_123"
  }
}
```

## 11. Citation Management Endpoints

### GET /citations
List citations in a document.

**Query Parameters:**
- `document_id` (string): Required document ID
- `type` (string): Filter by citation type
- `style` (string): Filter by citation style
- `verified` (boolean): Filter by verification status

**Response:**
```json
{
  "success": true,
  "data": {
    "citations": [
      {
        "id": "citation_123",
        "citation_key": "smith2023quantum",
        "type": "journal_article",
        "title": "Advances in Quantum Computing Applications",
        "authors": ["Dr. Jane Smith", "Prof. John Doe"],
        "publication_year": 2023,
        "journal_name": "Journal of Quantum Research",
        "volume": "15",
        "issue": "3",
        "page_numbers": "245-267",
        "doi": "10.1000/xyz123",
        "formatted_citation": "Smith, J., & Doe, J. (2023). Advances in Quantum Computing Applications. Journal of Quantum Research, 15(3), 245-267.",
        "citation_style": "apa",
        "is_verified": true,
        "position": {"start": 156, "end": 178}
      }
    ]
  }
}
```

### POST /citations
Add a new citation.

**Request:**
```json
{
  "document_id": "doc_123",
  "block_id": "block_456",
  "citation_key": "doe2024research",
  "type": "book",
  "title": "Modern Research Methods",
  "authors": ["Dr. John Doe"],
  "publication_year": 2024,
  "publisher": "Academic Press",
  "isbn": "978-0-123456-78-9",
  "citation_style": "apa",
  "position": {"start": 245, "end": 267}
}
```

### POST /citations/search
Search for citations in external databases.

**Request:**
```json
{
  "query": "quantum computing applications",
  "filters": {
    "year_min": 2020,
    "year_max": 2024,
    "type": "journal_article"
  },
  "sources": ["crossref", "pubmed", "arxiv"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "title": "Quantum Computing in Artificial Intelligence",
        "authors": ["Dr. Alice Johnson", "Prof. Bob Wilson"],
        "publication_year": 2023,
        "journal": "AI Research Quarterly",
        "doi": "10.1000/ai2023.456",
        "abstract": "This paper explores the intersection of quantum computing...",
        "relevance_score": 0.92,
        "source": "crossref"
      }
    ],
    "total_results": 156,
    "search_time_ms": 234
  }
}
```

### GET /reference-library
Get user's personal reference library.

### POST /reference-library
Add a reference to personal library.

### POST /citations/{citation_id}/verify
Verify citation accuracy.

**Response:**
```json
{
  "success": true,
  "data": {
    "is_valid": true,
    "verification_details": {
      "doi_valid": true,
      "publication_exists": true,
      "metadata_accurate": true,
      "accessibility": "open_access"
    },
    "suggestions": [],
    "verified_at": "2024-01-15T12:00:00Z"
  }
}
```

## 12. Real-time Endpoints

### SSE /ai/stream/{job_id}
Stream AI processing results via Server-Sent Events.

**Event Types:**
```
event: progress
data: {"progress": 0.45, "stage": "analyzing_context"}

event: suggestion
data: {"agent": "grammar", "suggestion": {...}}

event: complete
data: {"total_suggestions": 12, "processing_time": 1.8}

event: error
data: {"error": "Processing failed", "code": "AI_ERROR"}
```

### WebSocket Connection
Real-time document collaboration via Socket.io.

**Connection:**
```javascript
import { io } from 'socket.io-client';

const socket = io('wss://api.revisionary.app', {
  auth: {
    token: 'your-access-token'
  }
});

// Join a document room
socket.emit('join_document', {
  document_id: 'doc_123',
  user_id: 'user_456'
});
```

**Message Types:**

**Cursor Update:**
```json
{
  "type": "cursor",
  "user_id": "user_456",
  "position": {
    "block_id": "block_123",
    "offset": 45
  }
}
```

**Content Update:**
```json
{
  "type": "update",
  "block_id": "block_123",
  "operation": {
    "type": "insert",
    "position": 45,
    "text": "new text"
  },
  "version": 15
}
```

**Presence:**
```json
{
  "type": "presence",
  "users": [
    {
      "id": "user_456",
      "name": "John Doe",
      "color": "#FF5733",
      "cursor": {...}
    }
  ]
}
```

## 7. Export Endpoints

### POST /export
Export document in various formats.

**Request:**
```json
{
  "document_id": "doc_123",
  "format": "docx",
  "options": {
    "include_comments": true,
    "include_suggestions": false,
    "template": "manuscript",
    "styling": {
      "font": "Times New Roman",
      "size": 12,
      "line_spacing": 2.0
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "export_id": "exp_789",
    "status": "processing",
    "estimated_time": 10,
    "webhook_url": "https://your-domain.com/webhook/exp_789"
  }
}
```

### GET /export/{export_id}
Check export status or download.

**Response (Processing):**
```json
{
  "success": true,
  "data": {
    "status": "processing",
    "progress": 0.75
  }
}
```

**Response (Complete):**
```json
{
  "success": true,
  "data": {
    "status": "complete",
    "download_url": "https://storage.revisionary.app/exports/exp_789.docx",
    "expires_at": "2024-01-15T11:35:00Z",
    "file_size": 145230,
    "page_count": 89
  }
}
```

## 8. User & Subscription

### GET /user/profile
Get current user profile.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user_123",
    "email": "<EMAIL>",
    "name": "Jane Doe",
    "subscription": {
      "tier": "professional",
      "status": "active",
      "renews_at": "2024-02-15T00:00:00Z",
      "limits": {
        "monthly_tokens": 225000,
        "tokens_used": 45230,
        "documents": "unlimited",
        "collaborators": 10
      }
    },
    "preferences": {
      "default_document_type": "creative",
      "ui_theme": "dark",
      "editor_font": "monospace"
    }
  }
}
```

### GET /user/usage
Get usage statistics.

**Query Parameters:**
- `period` (string): day, week, month, year
- `start_date` (string): ISO 8601 date
- `end_date` (string): ISO 8601 date

**Response:**
```json
{
  "success": true,
  "data": {
    "period": "month",
    "usage": {
      "tokens": {
        "total": 45230,
        "by_model": {
          "gpt-4.1-nano": 12500,
          "gpt-4.1-mini": 18230,
          "gpt-4.1": 10500,
          "gemini-2.5-flash": 4000
        },
        "by_agent": {
          "grammar": 8500,
          "style": 15230,
          "structure": 12000,
          "content": 9500
        }
      },
      "documents": {
        "created": 15,
        "edited": 87,
        "words_written": 125000
      },
      "ai_operations": {
        "suggestions": 1250,
        "generations": 89,
        "rewrites": 156
      }
    }
  }
}
```

## 13. Enhanced Search Endpoints

### POST /search
Search across documents.

**Request:**
```json
{
  "query": "character development",
  "scope": "all_documents",
  "filters": {
    "document_types": ["creative"],
    "date_range": {
      "start": "2024-01-01",
      "end": "2024-01-31"
    }
  },
  "options": {
    "include_context": true,
    "highlight": true,
    "limit": 20
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "document_id": "doc_123",
        "document_title": "My Novel",
        "block_id": "block_456",
        "score": 0.95,
        "highlight": "...the <mark>character development</mark> in this chapter...",
        "context": "Previous paragraph... [match] ...Next paragraph"
      }
    ],
    "total_results": 45,
    "facets": {
      "document_types": {
        "creative": 38,
        "academic": 7
      }
    }
  }
}
```

## 14. Mobile Sync Endpoints

### POST /mobile/sync
Synchronize mobile changes with server.

**Request:**
```json
{
  "device_id": "device_abc123",
  "last_sync_timestamp": "2024-01-15T10:00:00Z",
  "changes": [
    {
      "type": "block_update",
      "block_id": "block_123",
      "content": "Updated content from mobile...",
      "timestamp": "2024-01-15T10:30:00Z",
      "client_id": "client_456"
    }
  ],
  "conflicts_resolution": "server_wins"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "sync_id": "sync_789",
    "conflicts": [
      {
        "block_id": "block_123",
        "conflict_type": "concurrent_edit",
        "server_version": "Server content...",
        "client_version": "Mobile content...",
        "resolution": "merged",
        "merged_content": "Final merged content..."
      }
    ],
    "applied_changes": 15,
    "rejected_changes": 1,
    "next_sync_timestamp": "2024-01-15T11:00:00Z"
  }
}
```

### GET /mobile/delta
Get changes since last sync.

**Query Parameters:**
- `since_timestamp` (string): ISO 8601 timestamp
- `document_ids` (array): Filter by specific documents
- `include_metadata` (boolean): Include document metadata

**Response:**
```json
{
  "success": true,
  "data": {
    "changes": [
      {
        "type": "document_created",
        "document_id": "doc_new",
        "timestamp": "2024-01-15T10:45:00Z",
        "data": { /* Document object */ }
      },
      {
        "type": "block_updated",
        "block_id": "block_456",
        "document_id": "doc_123",
        "timestamp": "2024-01-15T10:50:00Z",
        "data": { /* Updated block */ }
      }
    ],
    "has_more": false,
    "sync_timestamp": "2024-01-15T11:00:00Z"
  }
}
```

### POST /mobile/offline-queue
Submit offline actions queue.

**Request:**
```json
{
  "device_id": "device_abc123",
  "queue": [
    {
      "action": "create_document",
      "temp_id": "temp_doc_1",
      "data": {
        "title": "Offline Document",
        "type": "creative"
      },
      "timestamp": "2024-01-15T08:30:00Z"
    },
    {
      "action": "update_block",
      "temp_id": "temp_block_1",
      "parent_temp_id": "temp_doc_1",
      "data": {
        "content": "Content created offline..."
      },
      "timestamp": "2024-01-15T08:32:00Z"
    }
  ]
}
```

## 15. Webhook Events

Configure webhooks to receive real-time notifications.

### Event Types

**document.created**
```json
{
  "event": "document.created",
  "timestamp": "2024-01-15T10:40:00Z",
  "data": {
    "document_id": "doc_789",
    "user_id": "user_123",
    "title": "New Document"
  }
}
```

**ai.suggestion.complete**
```json
{
  "event": "ai.suggestion.complete",
  "timestamp": "2024-01-15T10:41:00Z",
  "data": {
    "job_id": "job_456",
    "document_id": "doc_123",
    "block_id": "block_789",
    "suggestions_count": 5,
    "processing_time": 1.8
  }
}
```

**export.complete**
```json
{
  "event": "export.complete",
  "timestamp": "2024-01-15T10:42:00Z",
  "data": {
    "export_id": "exp_123",
    "document_id": "doc_456",
    "format": "pdf",
    "download_url": "https://storage.revisionary.app/exports/exp_123.pdf"
  }
}
```

## 16. Advanced Error Codes

### New Error Codes for Advanced Features

| Code | HTTP Status | Description |
|------|-------------|-------------|
| PERSONA_LIMIT_EXCEEDED | 429 | Maximum number of personas reached for subscription tier |
| AGENT_EXECUTION_TIMEOUT | 408 | Custom agent execution timed out |
| HEALTH_ANALYSIS_FAILED | 500 | Writing health analysis service error |
| CONSISTENCY_CHECK_IN_PROGRESS | 409 | Consistency check already running for this document |
| CITATION_VERIFICATION_FAILED | 422 | Unable to verify citation against external sources |
| CROSS_AUDIENCE_CONFLICT | 422 | Unresolvable conflicts between persona feedback |
| ENTITY_RELATIONSHIP_INVALID | 400 | Invalid entity relationship configuration |
| MOBILE_SYNC_CONFLICT | 409 | Unresolvable sync conflict between mobile and server |
| ACHIEVEMENT_ALREADY_UNLOCKED | 409 | Attempt to unlock already achieved achievement |
| AGENT_TEMPLATE_INVALID | 400 | Custom agent template contains invalid configuration |
| PERSONA_FEEDBACK_LIMIT | 429 | Daily persona feedback limit exceeded |
| HEALTH_WORKER_UNAVAILABLE | 503 | Writing health analysis workers at capacity |

## 17. Error Codes (Core)

| Code | HTTP Status | Description |
|------|-------------|-------------|
| UNAUTHORIZED | 401 | Invalid or expired authentication token |
| FORBIDDEN | 403 | Insufficient permissions for this operation |
| NOT_FOUND | 404 | Requested resource not found |
| RATE_LIMITED | 429 | Rate limit exceeded |
| INVALID_REQUEST | 400 | Request body or parameters invalid |
| CONFLICT | 409 | Resource conflict (e.g., stale content hash) |
| AI_ERROR | 500 | AI service error |
| SERVICE_UNAVAILABLE | 503 | Service temporarily unavailable |

## 18. SDK Examples (Enhanced)

### JavaScript/TypeScript (Enhanced)
```typescript
import { RevisionaryClient } from '@revisionary/sdk';

const client = new RevisionaryClient({
  apiKey: 'your-api-key',
  environment: 'production'
});

// Create a document
const doc = await client.documents.create({
  title: 'My Story',
  type: 'creative'
});

// Create personas for feedback
const academiPersona = await client.personas.create({
  name: 'Academic Reviewer',
  type: 'academic',
  demographics: { education: 'PhD', expertise: 'Literature' }
});

const youngAdultPersona = await client.personas.create({
  name: 'YA Reader',
  type: 'age_specific',
  demographics: { age_range: '16-24' }
});

// Create custom agent
const styleAgent = await client.agents.create({
  name: 'Creative Style Enhancer',
  type: 'style',
  capabilities: ['edit', 'tone_adjustment'],
  styleRules: {
    tone: { creativity: 'high', formality: 'low' }
  }
});

// Get multi-persona feedback
const feedback = await client.personas.getMultiFeedback({
  blockId: 'block_123',
  personaIds: [academiPersona.id, youngAdultPersona.id],
  text: 'Once upon a time...'
});

// Execute custom agent
const suggestions = await client.agents.execute(styleAgent.id, {
  blockId: 'block_123',
  text: 'The story begins...'
});

// Analyze writing health
const health = await client.health.analyze({
  blockId: 'block_123',
  text: 'Content to analyze...'
});

// Track analytics event
await client.analytics.trackEvent({
  eventType: 'feature_used',
  eventData: { feature: 'persona_feedback' }
});

// Check entity consistency
const consistency = await client.crossReference.checkConsistency('doc_123');

// Listen for real-time updates
const unsubscribe = client.documents.subscribe(doc.id, (event) => {
  console.log('Document updated:', event);
});
```

### Python (Enhanced)
```python
from revisionary import RevisionaryClient
from datetime import datetime, timedelta

client = RevisionaryClient(api_key="your-api-key")

# Create document
doc = client.documents.create(
    title="Research Paper",
    type="academic"
)

# Create academic personas
academic_persona = client.personas.create(
    name="Peer Reviewer",
    type="academic",
    demographics={"education": "PhD", "field": "Computer Science"}
)

# Create custom agents
grammar_agent = client.agents.create(
    name="Academic Grammar Enforcer",
    type="grammar",
    capabilities=["edit", "consistency_check"],
    style_rules={
        "formality": {"level": "high", "avoid_contractions": True}
    }
)

# Get persona feedback
feedback = client.personas.get_feedback(
    persona_id=academic_persona.id,
    block_id="block_123",
    text="The methodology employed in this study..."
)

print(f"Academic feedback score: {feedback.overall_score}")
print(f"Suggestions: {feedback.suggestions}")

# Execute custom agent
agent_result = client.agents.execute(
    agent_id=grammar_agent.id,
    block_id="block_123",
    text="The data demonstrates significant results."
)

# Analyze writing health
health_analysis = client.health.analyze(
    block_id="block_123",
    text="Content to analyze...",
    options={"target_audience": "academic"}
)

print(f"Health score: {health_analysis.overall_score}")
print(f"Issues: {len(health_analysis.issues)}")

# Track writing session
session = client.analytics.start_session()
client.analytics.track_words_written(session.id, 250)
client.analytics.end_session(session.id)

# Check achievements
achievements = client.analytics.get_achievements()
print(f"Unlocked achievements: {len(achievements)}")

# Manage entities
character = client.entities.create(
    document_id=doc.id,
    name="Dr. Smith",
    type="character",
    attributes={"role": "research_supervisor", "age": "50s"}
)

# Check consistency
consistency_report = client.cross_reference.check_consistency(doc.id)
print(f"Consistency violations: {len(consistency_report.violations)}")

# Add citations
citation = client.citations.create(
    document_id=doc.id,
    citation_key="smith2023",
    title="Advanced Research Methods",
    authors=["Dr. J. Smith"],
    type="journal_article",
    publication_year=2023
)

# Verify citation
verification = client.citations.verify(citation.id)
print(f"Citation valid: {verification.is_valid}")

# Stream results for complex operations
for event in client.ai.stream(job.id):
    print(f"Suggestion from {event.agent}: {event.suggestion}")
```

### cURL
```bash
# Create document
curl -X POST https://api.revisionary.app/v1/documents \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "My Document",
    "type": "general"
  }'

# Get suggestions
curl -X POST https://api.revisionary.app/v1/blocks/block_123/suggestions \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "agents": ["grammar", "style"],
    "intent": "improve"
  }'
```

## 19. Best Practices (Enhanced)

### Advanced Feature Best Practices

1. **Persona System**:
   - Limit active personas to 3-5 for optimal performance
   - Cache persona feedback for similar content
   - Use batch feedback requests for multiple blocks
   - Handle cross-audience conflicts gracefully

2. **Custom Agents**:
   - Set appropriate priorities to avoid conflicts
   - Monitor agent performance and adjust configurations
   - Use templates for common agent patterns
   - Implement circuit breakers for failing agents

3. **Writing Health**:
   - Debounce health analysis requests (500ms minimum)
   - Process health analysis in background workers
   - Cache health results for unchanged content
   - Provide progressive feedback during analysis

4. **Cross-Reference Intelligence**:
   - Batch entity updates for better performance
   - Run consistency checks during idle periods
   - Cache entity relationships for faster lookups
   - Use incremental consistency checking

5. **Mobile Synchronization**:
   - Implement exponential backoff for sync retries
   - Use delta synchronization for large documents
   - Handle offline scenarios gracefully
   - Prioritize user-initiated changes in conflicts

6. **Citation Management**:
   - Verify citations asynchronously
   - Cache external citation lookups
   - Provide fallback formatting for failed verifications
   - Batch citation verification requests

## 20. Best Practices (Core)

1. **Caching**: Cache document structure and suggestions client-side
2. **Pagination**: Always paginate list endpoints
3. **Retries**: Implement exponential backoff for failed requests
4. **Webhooks**: Use webhooks for async operations instead of polling
5. **Batch Operations**: Group multiple operations when possible
6. **Error Handling**: Always handle rate limit and service errors gracefully

## 21. API Versioning

The API uses URL versioning (e.g., `/v1/`, `/v2/`). Breaking changes will result in a new API version. Deprecated endpoints will be supported for at least 6 months with advance notice.

### Deprecation Headers
```
X-API-Deprecation-Date: 2024-12-31
X-API-Deprecation-Info: https://docs.revisionary.app/deprecations/endpoint-name
X-Feature-Support: personas=v1.2,agents=v1.1,health=v1.0
```

### Feature Flags
```
X-Enable-Personas: true
X-Enable-Custom-Agents: true
X-Enable-Health-Analysis: true
X-Enable-Cross-Reference: true
X-Enable-Mobile-Sync: true
```

## 22. Rate Limiting for Advanced Features

### Feature-Specific Limits

| Feature | Free Tier | Professional | Studio | Enterprise |
|---------|-----------|--------------|--------|-----------|
| Persona Feedback | 10/day | 100/day | 500/day | Unlimited |
| Custom Agent Executions | 50/day | 500/day | 2000/day | Unlimited |
| Health Analyses | 100/day | 1000/day | 5000/day | Unlimited |
| Consistency Checks | 5/day | 25/day | 100/day | Unlimited |
| Citation Verifications | 25/day | 200/day | 1000/day | Unlimited |
| Cross-Reference Operations | 100/day | 1000/day | 5000/day | Unlimited |

### Rate Limit Headers
```
X-RateLimit-Personas-Remaining: 85
X-RateLimit-Personas-Reset: **********
X-RateLimit-Agents-Remaining: 450
X-RateLimit-Health-Remaining: 850
```

This comprehensive API documentation now covers all the advanced features discovered in the frontend implementation, providing developers with complete information needed to integrate with the Revisionary backend services.