"""
Analytics API Module

Purpose: Analytics and achievement tracking for writing metrics
- Writing session management
- Goal tracking and progress
- Achievement system
- Dashboard analytics

Features:
- Session lifecycle management
- Goal creation and progress tracking
- Achievement unlocking logic
- Analytics dashboard data
"""

import asyncio
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Union
from uuid import UUID, uuid4
import structlog
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field

from api.auth import get_current_user
from core.auth import UserClaims
from core.database import get_database, DatabaseService

logger = structlog.get_logger(__name__)

router = APIRouter(tags=["analytics"])


# =============================================================================
# Pydantic Models
# =============================================================================

class WritingSessionCreate(BaseModel):
    document_id: UUID
    target_word_count: Optional[int] = None
    session_goal: Optional[str] = None
    focus_mode: bool = False

class WritingSessionUpdate(BaseModel):
    end_time: Optional[datetime] = None
    words_written: Optional[int] = None
    words_at_start: Optional[int] = None
    ai_suggestions_accepted: Optional[int] = None
    ai_suggestions_dismissed: Optional[int] = None
    ai_generations_used: Optional[int] = None
    focus_mode_used: Optional[bool] = None
    features_used: Optional[List[str]] = None

class WritingSession(BaseModel):
    id: UUID
    user_id: UUID
    document_id: UUID
    start_time: datetime
    end_time: Optional[datetime] = None
    words_written: int = 0
    words_at_start: int = 0
    time_spent_minutes: int = 0
    ai_suggestions_accepted: int = 0
    ai_suggestions_dismissed: int = 0
    ai_generations_used: int = 0
    focus_mode_used: bool = False
    features_used: List[str] = Field(default_factory=list)
    session_goal: Optional[str] = None
    target_word_count: Optional[int] = None
    created_at: datetime
    updated_at: datetime

class WritingGoalCreate(BaseModel):
    type: str = Field(..., description="daily_words, weekly_words, daily_time, weekly_time")
    target: int = Field(..., gt=0)
    description: Optional[str] = None

class WritingGoalUpdate(BaseModel):
    target: Optional[int] = Field(None, gt=0)
    current: Optional[int] = Field(None, ge=0)
    status: Optional[str] = Field(None, pattern="^(active|completed|paused)$")
    description: Optional[str] = None

class WritingGoal(BaseModel):
    id: UUID
    user_id: UUID
    type: str
    target: int
    current: int = 0
    status: str = "active"
    description: Optional[str] = None
    start_date: datetime
    end_date: Optional[datetime] = None
    achieved_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

class Achievement(BaseModel):
    id: str
    title: str
    description: str
    icon: str
    category: str
    rarity: str = Field(..., pattern="^(common|rare|epic|legendary)$")
    unlock_criteria: Dict[str, Any]
    is_template: bool = True

class UserAchievement(BaseModel):
    id: UUID
    user_id: UUID
    achievement_id: str
    unlocked_at: datetime
    progress_data: Optional[Dict[str, Any]] = None

class UsageEvent(BaseModel):
    id: UUID
    user_id: UUID
    document_id: Optional[UUID] = None
    event_type: str
    event_data: Dict[str, Any]
    tokens_used: Optional[int] = None
    model_used: Optional[str] = None
    processing_time_ms: Optional[int] = None
    created_at: datetime

class DashboardSummary(BaseModel):
    total_sessions: int
    total_words_written: int
    total_time_spent_minutes: int
    active_goals: int
    completed_goals: int
    unlocked_achievements: int
    current_streak: int
    today_words: int
    today_time: int
    week_words: int
    week_time: int
    ai_acceptance_rate: float
    quality_score_average: float

class TokenUsage(BaseModel):
    user_id: UUID
    total_tokens_used: int
    tokens_limit: int
    tokens_remaining: int
    usage_percentage: float
    current_period_start: datetime
    current_period_end: datetime
    daily_breakdown: List[Dict[str, Any]]
    tokens_by_model: Dict[str, int]
    tokens_by_agent: Dict[str, int]

class AnalyticsResponse(BaseModel):
    """Standard response wrapper for single analytics items."""
    success: bool = True
    data: Any

class AnalyticsListResponse(BaseModel):
    """Standard response wrapper for lists of analytics items."""
    success: bool = True
    data: List[Any]
    meta: Dict[str, Any] = Field(default_factory=dict)


# =============================================================================
# API Endpoints
# =============================================================================

@router.get("/sessions", response_model=AnalyticsListResponse)
async def get_writing_sessions(
    limit: int = Query(50, ge=1, le=200),
    offset: int = Query(0, ge=0),
    document_id: Optional[UUID] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: DatabaseService = Depends(get_database),
    current_user: UserClaims = Depends(get_current_user)
):
    """Get user's writing sessions with optional filters."""
    try:
        query = """
            SELECT 
                id, user_id, document_id, start_time, end_time,
                words_written, words_at_start, time_spent_minutes,
                ai_suggestions_accepted, ai_suggestions_dismissed, ai_generations_used,
                focus_mode_used, features_used, session_goal, target_word_count,
                created_at, updated_at
            FROM writing_sessions 
            WHERE user_id = $1
        """
        params = [current_user.user_id]
        param_index = 2
        
        if document_id:
            query += f" AND document_id = ${param_index}"
            params.append(document_id)
            param_index += 1
        
        if start_date:
            query += f" AND start_time >= ${param_index}"
            params.append(start_date)
            param_index += 1
            
        if end_date:
            query += f" AND start_time <= ${param_index}"
            params.append(end_date)
            param_index += 1
        
        query += f" ORDER BY start_time DESC LIMIT ${param_index} OFFSET ${param_index + 1}"
        params.extend([limit, offset])
        
        sessions = await db.execute_query(query, params, fetch="all")
        
        logger.info(
            "Retrieved writing sessions",
            user_id=current_user.user_id,
            count=len(sessions),
            document_id=document_id
        )
        
        writing_sessions = [WritingSession(**session) for session in sessions]
        
        return AnalyticsListResponse(
            success=True,
            data=writing_sessions,
            meta={
                "total": len(writing_sessions),
                "limit": limit,
                "offset": offset
            }
        )
        
    except Exception as e:
        logger.error("Failed to get writing sessions", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve writing sessions"
        )

@router.post("/sessions", response_model=AnalyticsResponse)
async def start_writing_session(
    session_data: WritingSessionCreate,
    db: DatabaseService = Depends(get_database),
    current_user: UserClaims = Depends(get_current_user)
):
    """Start a new writing session."""
    try:
        session_id = uuid4()
        now = datetime.utcnow()
        
        query = """
            INSERT INTO writing_sessions (
                id, user_id, document_id, start_time, session_goal, 
                target_word_count, focus_mode_used, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $8)
            RETURNING *
        """
        
        params = [
            session_id, current_user.user_id, session_data.document_id, now,
            session_data.session_goal, session_data.target_word_count,
            session_data.focus_mode, now
        ]
        
        session = await db.execute_query(query, params, fetch="one")
        
        logger.info(
            "Started writing session",
            session_id=session_id,
            user_id=current_user.user_id,
            document_id=session_data.document_id
        )
        
        return AnalyticsResponse(success=True, data=WritingSession(**session))
        
    except Exception as e:
        logger.error("Failed to start writing session", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start writing session"
        )

@router.put("/sessions/{session_id}", response_model=AnalyticsResponse)
async def update_writing_session(
    session_id: UUID,
    updates: WritingSessionUpdate,
    db: DatabaseService = Depends(get_database),
    current_user: UserClaims = Depends(get_current_user)
):
    """Update a writing session."""
    try:
        # Build dynamic update query
        update_fields = []
        params = []
        param_index = 1
        
        for field, value in updates.dict(exclude_unset=True).items():
            if value is not None:
                update_fields.append(f"{field} = ${param_index}")
                params.append(value)
                param_index += 1
        
        if not update_fields:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No valid fields to update"
            )
        
        # Add updated_at
        update_fields.append(f"updated_at = ${param_index}")
        params.append(datetime.utcnow())
        param_index += 1
        
        # Add WHERE conditions
        params.extend([session_id, current_user.user_id])
        
        query = f"""
            UPDATE writing_sessions 
            SET {', '.join(update_fields)}
            WHERE id = ${param_index - 1} AND user_id = ${param_index}
            RETURNING *
        """
        
        session = await db.execute_query(query, params, fetch="one")
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Writing session not found"
            )
        
        # Calculate time spent if end_time is provided
        if updates.end_time and session['start_time']:
            time_spent = int((updates.end_time - session['start_time']).total_seconds() / 60)
            await db.execute_query(
                "UPDATE writing_sessions SET time_spent_minutes = $1 WHERE id = $2",
                [time_spent, session_id],
                fetch="none"
            )
            session['time_spent_minutes'] = time_spent
        
        logger.info(
            "Updated writing session",
            session_id=session_id,
            user_id=current_user.user_id,
            updates=list(updates.dict(exclude_unset=True).keys())
        )
        
        return AnalyticsResponse(success=True, data=WritingSession(**session))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update writing session", error=str(e), session_id=session_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update writing session"
        )

@router.get("/goals", response_model=AnalyticsListResponse)
async def get_writing_goals(
    status: Optional[str] = Query(None, pattern="^(active|completed|paused)$"),
    db: DatabaseService = Depends(get_database),
    current_user: UserClaims = Depends(get_current_user)
):
    """Get user's writing goals."""
    try:
        query = """
            SELECT 
                id, user_id, type, target, current, status, description,
                start_date, end_date, achieved_at, created_at, updated_at
            FROM writing_goals 
            WHERE user_id = $1
        """
        params = [current_user.user_id]
        
        if status:
            query += " AND status = $2"
            params.append(status)
        
        query += " ORDER BY created_at DESC"
        
        goals = await db.execute_query(query, params, fetch="all")
        
        logger.info(
            "Retrieved writing goals",
            user_id=current_user.user_id,
            count=len(goals),
            status_filter=status
        )
        
        writing_goals = [WritingGoal(**goal) for goal in goals]
        
        return AnalyticsListResponse(
            success=True,
            data=writing_goals,
            meta={
                "total": len(writing_goals),
                "status_filter": status
            }
        )
        
    except Exception as e:
        logger.error("Failed to get writing goals", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve writing goals"
        )

@router.post("/goals", response_model=AnalyticsResponse)
async def create_writing_goal(
    goal_data: WritingGoalCreate,
    db: DatabaseService = Depends(get_database),
    current_user: UserClaims = Depends(get_current_user)
):
    """Create a new writing goal."""
    try:
        goal_id = uuid4()
        now = datetime.utcnow()
        
        # Calculate end date based on goal type
        end_date = None
        if goal_data.type.startswith('daily'):
            end_date = now.replace(hour=23, minute=59, second=59)
        elif goal_data.type.startswith('weekly'):
            days_until_sunday = (6 - now.weekday()) % 7
            end_date = (now + timedelta(days=days_until_sunday)).replace(hour=23, minute=59, second=59)
        
        query = """
            INSERT INTO writing_goals (
                id, user_id, type, target, description, start_date, 
                end_date, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $8)
            RETURNING *
        """
        
        params = [
            goal_id, current_user.user_id, goal_data.type, goal_data.target,
            goal_data.description, now, end_date, now
        ]
        
        goal = await db.execute_query(query, params, fetch="one")
        
        logger.info(
            "Created writing goal",
            goal_id=goal_id,
            user_id=current_user.user_id,
            goal_type=goal_data.type,
            target=goal_data.target
        )
        
        return AnalyticsResponse(success=True, data=WritingGoal(**goal))
        
    except Exception as e:
        logger.error("Failed to create writing goal", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create writing goal"
        )

@router.put("/goals/{goal_id}", response_model=AnalyticsResponse)
async def update_writing_goal(
    goal_id: UUID,
    updates: WritingGoalUpdate,
    db: DatabaseService = Depends(get_database),
    current_user: UserClaims = Depends(get_current_user)
):
    """Update a writing goal."""
    try:
        # Build dynamic update query
        update_fields = []
        params = []
        param_index = 1
        
        update_data = updates.dict(exclude_unset=True)
        for field, value in update_data.items():
            if value is not None:
                update_fields.append(f"{field} = ${param_index}")
                params.append(value)
                param_index += 1
        
        if not update_fields:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No valid fields to update"
            )
        
        # Check if goal is being completed
        if updates.status == "completed" and "achieved_at" not in update_data:
            update_fields.append(f"achieved_at = ${param_index}")
            params.append(datetime.utcnow())
            param_index += 1
        
        # Add updated_at
        update_fields.append(f"updated_at = ${param_index}")
        params.append(datetime.utcnow())
        param_index += 1
        
        # Add WHERE conditions
        params.extend([goal_id, current_user.user_id])
        
        query = f"""
            UPDATE writing_goals 
            SET {', '.join(update_fields)}
            WHERE id = ${param_index - 1} AND user_id = ${param_index}
            RETURNING *
        """
        
        goal = await db.execute_query(query, params, fetch="one")
        
        if not goal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Writing goal not found"
            )
        
        logger.info(
            "Updated writing goal",
            goal_id=goal_id,
            user_id=current_user.user_id,
            updates=list(update_data.keys())
        )
        
        return AnalyticsResponse(success=True, data=WritingGoal(**goal))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update writing goal", error=str(e), goal_id=goal_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update writing goal"
        )

@router.get("/achievements", response_model=AnalyticsListResponse)
async def get_user_achievements(
    db: DatabaseService = Depends(get_database),
    current_user: UserClaims = Depends(get_current_user)
):
    """Get user's unlocked achievements."""
    try:
        query = """
            SELECT ua.*, a.title, a.description, a.icon, a.category, a.rarity
            FROM user_achievements ua
            JOIN achievements a ON ua.achievement_id = a.id
            WHERE ua.user_id = $1
            ORDER BY ua.unlocked_at DESC
        """
        
        achievements = await db.execute_query(query, [current_user.user_id], fetch="all")
        
        logger.info(
            "Retrieved user achievements",
            user_id=current_user.user_id,
            count=len(achievements)
        )
        
        user_achievements = [UserAchievement(**achievement) for achievement in achievements]
        
        return AnalyticsListResponse(
            success=True,
            data=user_achievements,
            meta={
                "total": len(user_achievements)
            }
        )
        
    except Exception as e:
        logger.error("Failed to get user achievements", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve achievements"
        )

@router.post("/events")
async def track_usage_event(
    event_type: str,
    event_data: Dict[str, Any],
    document_id: Optional[UUID] = None,
    tokens_used: Optional[int] = None,
    model_used: Optional[str] = None,
    processing_time_ms: Optional[int] = None,
    db: DatabaseService = Depends(get_database),
    current_user: UserClaims = Depends(get_current_user)
):
    """Track a usage event."""
    try:
        event_id = uuid4()
        now = datetime.utcnow()
        
        query = """
            INSERT INTO usage_events (
                id, user_id, document_id, event_type, event_data,
                tokens_used, model_used, processing_time_ms, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        """
        
        params = [
            event_id, current_user.user_id, document_id, event_type, event_data,
            tokens_used, model_used, processing_time_ms, now
        ]
        
        await db.execute_query(query, params, fetch="none")
        
        # Trigger achievement check in background
        asyncio.create_task(check_achievement_progress(current_user.user_id, event_type, event_data, db))
        
        logger.info(
            "Tracked usage event",
            event_id=event_id,
            user_id=current_user.user_id,
            event_type=event_type,
            document_id=document_id
        )
        
        return {"success": True, "event_id": event_id}
        
    except Exception as e:
        logger.error("Failed to track usage event", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to track usage event"
        )

@router.get("/dashboard", response_model=AnalyticsResponse)
async def get_dashboard_summary(
    db: DatabaseService = Depends(get_database),
    current_user: UserClaims = Depends(get_current_user)
):
    """Get analytics dashboard summary."""
    try:
        user_id = current_user.user_id
        
        # Get session statistics
        session_stats = await db.execute_query("""
            SELECT 
                COUNT(*) as total_sessions,
                COALESCE(SUM(words_written), 0) as total_words_written,
                COALESCE(SUM(time_spent_minutes), 0) as total_time_spent_minutes,
                COALESCE(SUM(ai_suggestions_accepted), 0) as total_accepted,
                COALESCE(SUM(ai_suggestions_dismissed), 0) as total_dismissed
            FROM writing_sessions 
            WHERE user_id = $1
        """, [user_id], fetch="one")
        
        # Get goal statistics
        goal_stats = await db.execute_query("""
            SELECT 
                COUNT(*) FILTER (WHERE status = 'active') as active_goals,
                COUNT(*) FILTER (WHERE status = 'completed') as completed_goals
            FROM writing_goals 
            WHERE user_id = $1
        """, [user_id], fetch="one")
        
        # Get achievement count
        achievement_count = await db.execute_query("""
            SELECT COUNT(*) as unlocked_achievements
            FROM user_achievements 
            WHERE user_id = $1
        """, [user_id], fetch="val") or 0
        
        # Get today's stats
        today = datetime.utcnow().date()
        today_stats = await db.execute_query("""
            SELECT 
                COALESCE(SUM(words_written), 0) as today_words,
                COALESCE(SUM(time_spent_minutes), 0) as today_time
            FROM writing_sessions 
            WHERE user_id = $1 AND DATE(start_time) = $2
        """, [user_id, today], fetch="one")
        
        # Get week stats
        week_start = today - timedelta(days=today.weekday())
        week_stats = await db.execute_query("""
            SELECT 
                COALESCE(SUM(words_written), 0) as week_words,
                COALESCE(SUM(time_spent_minutes), 0) as week_time
            FROM writing_sessions 
            WHERE user_id = $1 AND DATE(start_time) >= $2
        """, [user_id, week_start], fetch="one")
        
        # Calculate AI acceptance rate
        total_suggestions = session_stats['total_accepted'] + session_stats['total_dismissed']
        ai_acceptance_rate = (session_stats['total_accepted'] / total_suggestions * 100) if total_suggestions > 0 else 0
        
        # Calculate current streak (simplified)
        current_streak = await calculate_writing_streak(user_id, db)
        
        summary = DashboardSummary(
            total_sessions=session_stats['total_sessions'],
            total_words_written=session_stats['total_words_written'],
            total_time_spent_minutes=session_stats['total_time_spent_minutes'],
            active_goals=goal_stats['active_goals'] or 0,
            completed_goals=goal_stats['completed_goals'] or 0,
            unlocked_achievements=achievement_count,
            current_streak=current_streak,
            today_words=today_stats['today_words'],
            today_time=today_stats['today_time'],
            week_words=week_stats['week_words'],
            week_time=week_stats['week_time'],
            ai_acceptance_rate=round(ai_acceptance_rate, 2),
            quality_score_average=await calculate_quality_score_average(user_id, db)
        )
        
        logger.info(
            "Generated dashboard summary",
            user_id=user_id,
            total_sessions=summary.total_sessions,
            total_words=summary.total_words_written
        )
        
        return AnalyticsResponse(success=True, data=summary)
        
    except Exception as e:
        logger.error("Failed to get dashboard summary", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard summary"
        )


# =============================================================================
# Background Tasks and Helpers
# =============================================================================

async def calculate_writing_streak(user_id: UUID, db: DatabaseService) -> int:
    """Calculate current writing streak for a user."""
    try:
        # Get daily writing counts for the last 30 days
        query = """
            SELECT 
                DATE(start_time) as write_date,
                SUM(words_written) as daily_words
            FROM writing_sessions 
            WHERE user_id = $1 
                AND start_time >= CURRENT_DATE - INTERVAL '30 days'
            GROUP BY DATE(start_time)
            ORDER BY write_date DESC
        """
        
        daily_counts = await db.execute_query(query, [user_id], fetch="all")
        
        # Calculate streak (days with at least 100 words)
        streak = 0
        current_date = datetime.utcnow().date()
        
        for day_data in daily_counts:
            if day_data['write_date'] == current_date and day_data['daily_words'] >= 100:
                streak += 1
                current_date -= timedelta(days=1)
            elif day_data['write_date'] == current_date - timedelta(days=1) and day_data['daily_words'] >= 100:
                streak += 1
                current_date -= timedelta(days=1)
            else:
                break
        
        return streak
        
    except Exception as e:
        logger.error("Failed to calculate writing streak", error=str(e), user_id=user_id)
        return 0

async def check_achievement_progress(user_id: UUID, event_type: str, event_data: Dict[str, Any], db: DatabaseService):
    """Check if user has unlocked any achievements based on the event."""
    try:
        # Get available achievements that user hasn't unlocked yet
        unlocked_achievements = await db.execute_query("""
            SELECT achievement_id FROM user_achievements WHERE user_id = $1
        """, [user_id], fetch="all")
        
        unlocked_ids = {achievement['achievement_id'] for achievement in unlocked_achievements}
        
        # Define achievement criteria
        achievement_checks = {
            'first_document': lambda: event_type == 'document_created',
            'ai_collaborator': lambda: check_ai_suggestion_count(user_id, db, 50),
            'marathon_writer': lambda: check_daily_word_count(user_id, db, 10000),
            'week_streak': lambda: calculate_writing_streak(user_id, db) >= 7,
            'quality_master': lambda: check_quality_master_achievement(user_id, db)
        }
        
        for achievement_id, check_func in achievement_checks.items():
            if achievement_id not in unlocked_ids:
                try:
                    if await check_func() if asyncio.iscoroutinefunction(check_func) else check_func():
                        await unlock_achievement(user_id, achievement_id, db)
                except Exception as e:
                    logger.error(f"Failed to check achievement {achievement_id}", error=str(e))
        
    except Exception as e:
        logger.error("Failed to check achievement progress", error=str(e), user_id=user_id)

async def check_ai_suggestion_count(user_id: UUID, db: DatabaseService, threshold: int) -> bool:
    """Check if user has accepted enough AI suggestions."""
    count = await db.execute_query("""
        SELECT SUM(ai_suggestions_accepted) 
        FROM writing_sessions 
        WHERE user_id = $1
    """, [user_id], fetch="val") or 0
    
    return count >= threshold

async def check_daily_word_count(user_id: UUID, db: DatabaseService, threshold: int) -> bool:
    """Check if user wrote enough words today."""
    today = datetime.utcnow().date()
    count = await db.execute_query("""
        SELECT SUM(words_written) 
        FROM writing_sessions 
        WHERE user_id = $1 AND DATE(start_time) = $2
    """, [user_id, today], fetch="val") or 0
    
    return count >= threshold

async def unlock_achievement(user_id: UUID, achievement_id: str, db: DatabaseService):
    """Unlock an achievement for a user."""
    try:
        achievement_record_id = uuid4()
        now = datetime.utcnow()
        
        await db.execute_query("""
            INSERT INTO user_achievements (id, user_id, achievement_id, unlocked_at)
            VALUES ($1, $2, $3, $4)
        """, [achievement_record_id, user_id, achievement_id, now], fetch="none")
        
        logger.info(
            "Achievement unlocked",
            user_id=user_id,
            achievement_id=achievement_id,
            unlocked_at=now
        )
        
    except Exception as e:
        logger.error("Failed to unlock achievement", error=str(e), user_id=user_id, achievement_id=achievement_id)

async def calculate_quality_score_average(user_id: UUID, db: DatabaseService) -> float:
    """Calculate the average quality score for a user's documents."""
    try:
        # Get average quality score from health metrics
        query = """
            SELECT AVG(hm.overall_score) as avg_quality
            FROM health_metrics hm
            JOIN documents d ON hm.document_id = d.id
            WHERE d.owner_id = $1 AND hm.overall_score IS NOT NULL
        """
        
        avg_score = await db.execute_query(query, [user_id], fetch="val")
        return round(float(avg_score), 2) if avg_score else 0.0
        
    except Exception as e:
        logger.error("Failed to calculate quality score average", error=str(e), user_id=user_id)
        return 0.0

async def check_quality_master_achievement(user_id: UUID, db: DatabaseService) -> bool:
    """Check if user has achieved 90+ quality score."""
    try:
        # Check if user has any document with 90+ quality score
        query = """
            SELECT COUNT(*) > 0 as has_quality_master
            FROM health_metrics hm
            JOIN documents d ON hm.document_id = d.id
            WHERE d.owner_id = $1 AND hm.overall_score >= 90.0
        """
        
        result = await db.execute_query(query, [user_id], fetch="val")
        return bool(result) if result is not None else False
        
    except Exception as e:
        logger.error("Failed to check quality master achievement", error=str(e), user_id=user_id)
        return False


@router.get("/token-usage", response_model=AnalyticsResponse)
async def get_user_token_usage(
    days: int = Query(30, ge=1, le=365),
    db: DatabaseService = Depends(get_database),
    current_user: UserClaims = Depends(get_current_user)
):
    """Get current user's token usage statistics."""
    try:
        user_id = current_user.user_id
        logger.info("Getting user token usage", user_id=user_id, days=days)
        
        # Calculate period dates
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=days-1)
        
        # Get total token usage for the period
        total_usage_query = """
            SELECT 
                COALESCE(SUM(tokens_total), 0) as total_tokens,
                COALESCE(SUM(operations_count), 0) as total_operations
            FROM token_usage_daily 
            WHERE user_id = $1 AND date >= $2 AND date <= $3
        """
        
        total_usage = await db.execute_query(
            total_usage_query, 
            [user_id, start_date, end_date], 
            fetch="one"
        )
        
        # Get tokens by model breakdown
        model_breakdown_query = """
            SELECT 
                jsonb_object_agg(model, tokens) as tokens_by_model
            FROM (
                SELECT 
                    model_key as model,
                    SUM((value::text)::integer) as tokens
                FROM token_usage_daily tud,
                     jsonb_each(tud.tokens_by_model) as tokens(model_key, value)
                WHERE tud.user_id = $1 AND tud.date >= $2 AND tud.date <= $3
                GROUP BY model_key
            ) model_totals
        """
        
        try:
            model_breakdown = await db.execute_query(
                model_breakdown_query, 
                [user_id, start_date, end_date], 
                fetch="one"
            )
        except Exception:
            model_breakdown = None
        
        # Get tokens by agent breakdown
        agent_breakdown_query = """
            SELECT 
                jsonb_object_agg(agent, tokens) as tokens_by_agent
            FROM (
                SELECT 
                    agent_key as agent,
                    SUM((value::text)::integer) as tokens
                FROM token_usage_daily tud,
                     jsonb_each(tud.tokens_by_agent) as tokens(agent_key, value)
                WHERE tud.user_id = $1 AND tud.date >= $2 AND tud.date <= $3
                GROUP BY agent_key
            ) agent_totals
        """
        
        try:
            agent_breakdown = await db.execute_query(
                agent_breakdown_query, 
                [user_id, start_date, end_date], 
                fetch="one"
            )
        except Exception:
            agent_breakdown = None
        
        # Get daily breakdown
        daily_breakdown_query = """
            SELECT 
                date,
                tokens_total,
                operations_count,
                documents_edited
            FROM token_usage_daily
            WHERE user_id = $1 AND date >= $2 AND date <= $3
            ORDER BY date ASC
        """
        
        daily_data = await db.execute_query(
            daily_breakdown_query, 
            [user_id, start_date, end_date], 
            fetch="all"
        )
        
        # Format daily breakdown with null safety
        daily_breakdown = []
        if daily_data:
            for day in daily_data:
                try:
                    daily_breakdown.append({
                        "date": day["date"].isoformat() if day.get("date") else str(start_date),
                        "tokens_used": day.get("tokens_total") or 0,
                        "operations": day.get("operations_count") or 0,
                        "documents_edited": day.get("documents_edited") or 0
                    })
                except Exception as e:
                    logger.warning("Failed to format daily breakdown entry", error=str(e), day=day)
        
        # Calculate limits (for free tier - 25,000 tokens per month)
        # TODO: Get this from user subscription tier
        tokens_limit = 25000  # Free tier default
        total_tokens_used = (total_usage.get("total_tokens") or 0) if total_usage else 0
        tokens_remaining = max(0, tokens_limit - total_tokens_used)
        usage_percentage = min(100.0, (total_tokens_used / tokens_limit) * 100.0)
        
        # Parse JSON strings from database with null safety
        tokens_by_model = {}
        if model_breakdown and model_breakdown.get("tokens_by_model"):
            model_data = model_breakdown["tokens_by_model"]
            if isinstance(model_data, str):
                try:
                    tokens_by_model = json.loads(model_data)
                except json.JSONDecodeError:
                    logger.warning("Failed to parse tokens_by_model JSON", data=model_data)
                    tokens_by_model = {}
            elif isinstance(model_data, dict):
                tokens_by_model = model_data
        
        tokens_by_agent = {}
        if agent_breakdown and agent_breakdown.get("tokens_by_agent"):
            agent_data = agent_breakdown["tokens_by_agent"]
            if isinstance(agent_data, str):
                try:
                    tokens_by_agent = json.loads(agent_data)
                except json.JSONDecodeError:
                    logger.warning("Failed to parse tokens_by_agent JSON", data=agent_data)
                    tokens_by_agent = {}
            elif isinstance(agent_data, dict):
                tokens_by_agent = agent_data

        # Create TokenUsage object with guaranteed valid data
        token_usage = TokenUsage(
            user_id=user_id,
            total_tokens_used=int(total_tokens_used),
            tokens_limit=int(tokens_limit),
            tokens_remaining=int(tokens_remaining),
            usage_percentage=float(usage_percentage),
            current_period_start=datetime.combine(start_date, datetime.min.time()),
            current_period_end=datetime.combine(end_date, datetime.max.time()),
            daily_breakdown=daily_breakdown or [],
            tokens_by_model=tokens_by_model or {},
            tokens_by_agent=tokens_by_agent or {}
        )
        
        logger.info(
            "Token usage retrieved successfully",
            user_id=user_id,
            total_tokens=total_tokens_used,
            has_model_breakdown=bool(tokens_by_model),
            has_agent_breakdown=bool(tokens_by_agent),
            daily_entries=len(daily_breakdown)
        )
        
        return AnalyticsResponse(success=True, data=token_usage)
        
    except Exception as e:
        logger.error("Failed to get user token usage", error=str(e), user_id=user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve token usage data"
        )