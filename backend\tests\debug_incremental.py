#!/usr/bin/env python3
"""
Debug script to run SQL files incrementally and show detailed output
"""

import asyncio
import os
import asyncpg
from pathlib import Path
from dotenv import load_dotenv

async def run_files_incrementally():
    """Run SQL files one by one with detailed output."""
    
    load_dotenv("../.env")
    database_url = os.getenv("DATABASE_URL")
    
    if not database_url:
        print("❌ No DATABASE_URL found")
        return
    
    conn = None
    try:
        conn = await asyncpg.connect(database_url, statement_cache_size=0)
        
        print("🚀 Incremental SQL File Testing")
        print("=" * 60)
        
        # Clear all tables first
        print("\n0️⃣ Clearing all tables...")
        all_tables = await conn.fetch("""
            SELECT tablename FROM pg_tables 
            WHERE schemaname = 'public'
            ORDER BY tablename
        """)
        
        table_names = [row['tablename'] for row in all_tables]
        if table_names:
            table_list = ', '.join(f'"{name}"' for name in table_names)
            await conn.execute(f"TRUNCATE TABLE {table_list} RESTART IDENTITY CASCADE")
            print(f"✅ Cleared {len(table_names)} tables")
        
        # List of files to run in order
        sql_files = [
            "001_core_data.sql",
            "002_ai_collaboration.sql", 
            "003_analytics_scoring.sql",
            "004_advanced_features.sql",
            "005_scoring_algorithms.sql",  # New broken down files
            "006_health_metrics.sql",     
            "007_health_issues.sql",      
            "008_citations.sql",          
            "009_audience_analysis.sql",  
            "010_persona_feedback.sql",   
            "011_export_jobs.sql",        
            "012_templates.sql",          
            "013_token_usage.sql",        
            "014_agent_usage.sql",        
        ]
        
        mock_data_dir = Path("mock_data")
        
        for i, filename in enumerate(sql_files, 1):
            print(f"\n{i}️⃣ Testing {filename}...")
            print("-" * 40)
            
            file_path = mock_data_dir / filename
            if not file_path.exists():
                print(f"⚠️ File not found: {file_path} - SKIPPING")
                continue
            
            # Read file
            with open(file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            print(f"📄 File size: {len(sql_content)} characters")
            
            # Show first few lines for context
            first_lines = sql_content.split('\n')[:3]
            print(f"📝 Preview: {' '.join(first_lines)[:100]}...")
            
            # Execute in transaction
            try:
                start_time = asyncio.get_event_loop().time()
                
                async with conn.transaction():
                    await conn.execute(sql_content)
                
                end_time = asyncio.get_event_loop().time()
                execution_time = round((end_time - start_time) * 1000, 2)
                
                print(f"✅ SUCCESS - Executed in {execution_time}ms")
                
                # Show what was created
                if filename == "001_core_data.sql":
                    users = await conn.fetchval("SELECT COUNT(*) FROM users")
                    docs = await conn.fetchval("SELECT COUNT(*) FROM documents")
                    blocks = await conn.fetchval("SELECT COUNT(*) FROM blocks")
                    print(f"   📊 Created: {users} users, {docs} documents, {blocks} blocks")
                    
                elif filename == "004_advanced_features.sql":
                    personas = await conn.fetchval("SELECT COUNT(*) FROM personas")
                    agents = await conn.fetchval("SELECT COUNT(*) FROM custom_agents")
                    print(f"   📊 Created: {personas} personas, {agents} agents")
                    
                elif filename == "005_scoring_algorithms.sql":
                    algorithms = await conn.fetchval("SELECT COUNT(*) FROM scoring_algorithms")
                    print(f"   📊 Created: {algorithms} scoring algorithms")
                    
                elif filename == "006_health_metrics.sql":
                    health = await conn.fetchval("SELECT COUNT(*) FROM health_metrics")
                    print(f"   📊 Created: {health} health metrics")
                    
                elif filename == "007_health_issues.sql":
                    issues = await conn.fetchval("SELECT COUNT(*) FROM health_issues")
                    print(f"   📊 Created: {issues} health issues")
                
            except Exception as e:
                print(f"❌ FAILED: {e}")
                print(f"🔍 Error type: {type(e).__name__}")
                
                # Show more details for specific errors
                if "query returned more than one row" in str(e):
                    print("🎯 This is the 'multiple rows' error we're hunting!")
                    print("   The issue is in this specific file.")
                elif "duplicate key" in str(e):
                    print("🔑 Duplicate key violation - data not cleared properly")
                elif "does not exist" in str(e):
                    print("🔍 Missing dependency - previous file may have failed")
                
                # Stop on first failure to isolate the issue
                print(f"\n🛑 Stopping at first failure to isolate the issue.")
                break
        
        else:  # This runs if the loop completes without breaking
            print(f"\n🎉 ALL FILES EXECUTED SUCCESSFULLY!")
            
            # Final summary
            print(f"\n📊 FINAL COUNTS:")
            counts = {
                "Users": await conn.fetchval("SELECT COUNT(*) FROM users"),
                "Documents": await conn.fetchval("SELECT COUNT(*) FROM documents"),
                "Blocks": await conn.fetchval("SELECT COUNT(*) FROM blocks"),
                "Personas": await conn.fetchval("SELECT COUNT(*) FROM personas"),
                "Custom Agents": await conn.fetchval("SELECT COUNT(*) FROM custom_agents"),
                "Scoring Algorithms": await conn.fetchval("SELECT COUNT(*) FROM scoring_algorithms"),
                "Health Metrics": await conn.fetchval("SELECT COUNT(*) FROM health_metrics"),
                "Health Issues": await conn.fetchval("SELECT COUNT(*) FROM health_issues"),
                "Citations": await conn.fetchval("SELECT COUNT(*) FROM citations"),
                "Export Jobs": await conn.fetchval("SELECT COUNT(*) FROM export_jobs"),
                "Templates": await conn.fetchval("SELECT COUNT(*) FROM templates"),
                "Token Usage": await conn.fetchval("SELECT COUNT(*) FROM token_usage_daily"),
                "Agent Stats": await conn.fetchval("SELECT COUNT(*) FROM agent_usage_stats"),
            }
            
            for name, count in counts.items():
                print(f"   {name}: {count}")
        
    except Exception as e:
        print(f"❌ Connection error: {e}")
    finally:
        if conn:
            await conn.close()

if __name__ == "__main__":
    asyncio.run(run_files_incrementally())