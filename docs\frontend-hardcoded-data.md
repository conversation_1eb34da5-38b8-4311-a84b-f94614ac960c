# Frontend Hardcoded Data Audit

This document outlines all the hardcoded values found in the frontend codebase that should be replaced with API calls to the backend.

## 1. Mock Data in Components

### `components/editor/AIPanel.tsx`
- **`comments`**: The `comments` array is hardcoded with mock data.
  - **Action**: Fetch comments from the `/api/v1/comments` endpoint.

### `components/editor/EnhancedAIPanel.tsx`
- **`comments`**: The `comments` array is hardcoded with mock data.
  - **Action**: Fetch comments from the `/api/v1/comments` endpoint.

### `components/editor/MobileEditor.tsx`
- **`aiSuggestions`**: The `aiSuggestions` array is hardcoded.
  - **Action**: Fetch AI suggestions from the `/api/v1/suggestions` endpoint.
- **`comments`**: The `comments` array is hardcoded.
  - **Action**: Fetch comments from the `/api/v1/comments` endpoint.
- **`outline`**: The `outline` array is hardcoded.
  - **Action**: Fetch the document outline from the `/api/v1/documents/{id}/outline` endpoint.

### `components/editor/SmartCitationSearch.tsx`
- **`mockCitations`**: The `mockCitations` array is hardcoded.
  - **Action**: Fetch citations from the `/api/v1/citations/search` endpoint.

### `pages/Dashboard.tsx`
- **`recentDocuments`**: The `recentDocuments` array is hardcoded.
  - **Action**: Fetch recent documents from the `/api/v1/documents` endpoint with appropriate sorting and limits.

### `pages/Editor.tsx`
- **`document`**: The initial `document` state is hardcoded.
  - **Action**: Fetch document data from the `/api/v1/documents/{documentId}` endpoint.
- **`suggestions`**: The `suggestions` array is hardcoded.
  - **Action**: Fetch AI suggestions from the `/api/v1/suggestions` endpoint.
- **`collaborators`**: The `collaborators` array is hardcoded.
  - **Action**: Fetch collaborators from the `/api/v1/documents/{documentId}/collaborators` endpoint.

## 2. Hardcoded Configuration and Options

### `components/analytics/AchievementNotification.tsx`
- **`rarityConfig`**: The configuration for achievement rarities (colors, icons) is hardcoded.
  - **Action**: This could be fetched from a `/api/v1/achievements/config` endpoint if it needs to be dynamic.

### `components/analytics/WritingGoalsManager.tsx`
- **`goalTypeConfig`**: The configuration for goal types (icons, labels, defaults) is hardcoded.
  - **Action**: Fetch goal configurations from a `/api/v1/analytics/goals/config` endpoint.

### `components/editor/EditorToolbar.tsx`
- **`formatGroups`**: The toolbar items and their properties are hardcoded.
  - **Action**: While some of this can remain, user-customizable toolbars would require fetching this configuration.

### `components/editor/EnhancedAIPanel.tsx`
- **`aiModes`**: The available AI modes are hardcoded.
  - **Action**: Fetch available AI modes from an API endpoint to allow for dynamic feature flagging.
- **`buildFromTemplates`**: The "Build-From" templates are hardcoded.
  - **Action**: Fetch these templates from a `/api/v1/agents/templates` endpoint.

### `components/persona/PersonaCreationWizard.tsx`
- **`categoryOptions`**: The persona categories are hardcoded.
  - **Action**: Fetch persona categories from the backend to allow for future expansion.
- **`colorOptions`**: The available colors for personas are hardcoded.
  - **Action**: This can likely remain hardcoded, but could be a configurable option.

### `components/settings/AgentEditor.tsx`
- **`defaultCapabilities`**: The default agent capabilities are hardcoded.
  - **Action**: Fetch available capabilities from a `/api/v1/agents/capabilities` endpoint.
- **`availableTools`**: The list of available agent tools is hardcoded.
  - **Action**: Fetch available tools from a `/api/v1/agents/tools` endpoint.
- **`availableColors`**: The color options for agents are hardcoded.
  - **Action**: This can likely remain hardcoded.

### `pages/Home.tsx`
- **`agentCategories`**: The categories and details of AI agents are hardcoded for the marketing page.
  - **Action**: Fetch this from a `/api/v1/marketing/agents` endpoint to keep it in sync with the product.
- **`writingTypes`**: The types of writing are hardcoded.
  - **Action**: Fetch from a `/api/v1/documents/types` endpoint.
- **`stats`**: The marketing statistics are hardcoded.
  - **Action**: These could be fetched from a public analytics endpoint.
- **`testimonials`**: Testimonials are hardcoded.
  - **Action**: Fetch from a CMS or a dedicated `/api/v1/marketing/testimonials` endpoint.
- **`pricing`**: Pricing plans are hardcoded.
  - **Action**: Fetch from a `/api/v1/billing/plans` endpoint to allow for dynamic pricing changes.

### `stores/agentStore.ts`
- **`builtInAgents`**: The default, built-in agents are hardcoded.
  - **Action**: These should be fetched from the `/api/v1/agents/templates` endpoint.

### `stores/personaStore.ts`
- **`builtInTemplates`**: The default persona templates are hardcoded.
  - **Action**: Fetch these from the `/api/v1/personas/templates` endpoint.

## 3. Logic and Calculations

### `components/analytics/ProgressChart.tsx`
- **`trendPercentage`**: The trend calculation is based on mock data.
  - **Action**: The component should receive trend data from its parent, which should fetch it from the `/api/v1/analytics/trends` endpoint.

### `components/editor/DocumentAnalyticsPanel.tsx`
- **Readability Score**: The Flesch Reading Ease score is calculated on the frontend.
  - **Action**: This calculation should be performed by the backend's health analysis service.
- **`handleCalculateInsights`**: This function simulates an async calculation and generates mock quality scores.
  - **Action**: This should make an API call to the `/api/v1/health/analyze` endpoint.

### `workers/WritingHealthWorker.ts`
- This entire file is a mock implementation of the writing health analysis.
  - **Action**: The frontend should not perform health analysis. It should send the text to the backend for analysis and receive the results.

## Conclusion

The frontend is heavily reliant on hardcoded data and mock implementations. To move forward, the development team needs to prioritize connecting these components to the backend API. The `docs/api-documentation.md` and `docs/database-schema.md` files provide the necessary information to make these connections.
