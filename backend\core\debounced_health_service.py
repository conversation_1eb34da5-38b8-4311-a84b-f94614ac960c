"""
Debounced Health Analysis Service for Revisionary

Purpose: Cost-efficient health analysis with intelligent debouncing
- 500ms debounce window to batch similar requests
- Request deduplication to prevent redundant analysis
- Real-time response caching for immediate feedback
- Cost optimization through smart batching

Features:
- Configurable debounce delay (default 500ms)
- Text similarity detection for deduplication
- Priority handling for real-time vs batch analysis
- Automatic cleanup of stale requests
- Memory-efficient request pooling
"""

import asyncio
import hashlib
import json
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from uuid import uuid4
import structlog

from core.redis_service import get_redis
from core.queue_manager import get_queue_manager

logger = structlog.get_logger(__name__)


@dataclass
class HealthAnalysisRequest:
    """Individual health analysis request."""
    request_id: str
    text: str
    document_id: Optional[str]
    block_id: Optional[str]
    analysis_type: str
    user_id: str
    user_tier: str
    preferences: Dict[str, Any]
    callback: Optional[Callable] = field(default=None)
    websocket_session: Optional[str] = field(default=None)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    priority: int = field(default=1)  # 1=low, 2=normal, 3=high
    
    def get_text_hash(self) -> str:
        """Get hash of text for deduplication."""
        return hashlib.md5(self.text.encode()).hexdigest()
    
    def get_cache_key(self) -> str:
        """Get cache key for this request."""
        options_hash = hashlib.md5(
            json.dumps({
                'analysis_type': self.analysis_type,
                'preferences': self.preferences
            }, sort_keys=True).encode()
        ).hexdigest()
        return f"health:{self.get_text_hash()}:{options_hash}"


@dataclass
class DebouncedBatch:
    """Batch of debounced health analysis requests."""
    batch_id: str
    requests: List[HealthAnalysisRequest] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.utcnow)
    processing_started: bool = field(default=False)
    
    def add_request(self, request: HealthAnalysisRequest):
        """Add request to batch, deduplicating by text hash."""
        # Check for duplicates
        text_hash = request.get_text_hash()
        for existing in self.requests:
            if existing.get_text_hash() == text_hash:
                # Merge callbacks and update priority
                if request.priority > existing.priority:
                    existing.priority = request.priority
                if request.callback:
                    existing.callback = request.callback
                if request.websocket_session:
                    existing.websocket_session = request.websocket_session
                logger.debug(
                    "Deduplicated health request",
                    text_hash=text_hash[:8],
                    existing_id=existing.request_id,
                    new_id=request.request_id
                )
                return
        
        # Add as new request
        self.requests.append(request)
    
    def get_priority(self) -> int:
        """Get highest priority in batch."""
        return max(req.priority for req in self.requests) if self.requests else 1
    
    def get_unique_texts(self) -> List[str]:
        """Get list of unique text content."""
        seen_hashes = set()
        unique_texts = []
        for req in self.requests:
            text_hash = req.get_text_hash()
            if text_hash not in seen_hashes:
                seen_hashes.add(text_hash)
                unique_texts.append(req.text)
        return unique_texts


class DebouncedHealthService:
    """Service for debounced health analysis."""
    
    def __init__(self, debounce_delay: float = 0.5):
        """Initialize debounced health service."""
        self.debounce_delay = debounce_delay  # 500ms default
        self.pending_batches: Dict[str, DebouncedBatch] = {}
        self.processing_tasks: Dict[str, asyncio.Task] = {}
        self.redis = None
        self.queue_manager = None
        self._cleanup_task = None
        self._initialized = False
        
        logger.info(
            "Debounced health service initialized",
            debounce_delay_ms=int(debounce_delay * 1000)
        )
    
    async def initialize(self):
        """Initialize async resources."""
        if self._initialized:
            return
        
        self.redis = await get_redis()
        self.queue_manager = await get_queue_manager()
        
        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._cleanup_stale_batches())
        
        self._initialized = True
        logger.info("Debounced health service async resources initialized")
    
    async def request_health_analysis(
        self,
        text: str,
        user_id: str,
        user_tier: str = 'professional',
        document_id: Optional[str] = None,
        block_id: Optional[str] = None,
        analysis_type: str = 'quick',
        preferences: Optional[Dict[str, Any]] = None,
        priority: int = 1,
        callback: Optional[Callable] = None,
        websocket_session: Optional[str] = None
    ) -> str:
        """
        Request debounced health analysis.
        
        Args:
            text: Text to analyze
            user_id: User requesting analysis
            user_tier: User's subscription tier
            document_id: Optional document ID
            block_id: Optional block ID
            analysis_type: Type of analysis (quick, comprehensive, focused)
            preferences: Analysis preferences
            priority: Request priority (1=low, 2=normal, 3=high)
            callback: Optional callback function for results
            websocket_session: Optional WebSocket session ID
            
        Returns:
            Request ID for tracking
        """
        if not self._initialized:
            await self.initialize()
        
        # Create request
        request = HealthAnalysisRequest(
            request_id=str(uuid4()),
            text=text,
            document_id=document_id,
            block_id=block_id,
            analysis_type=analysis_type,
            user_id=user_id,
            user_tier=user_tier,
            preferences=preferences or {},
            priority=priority,
            callback=callback,
            websocket_session=websocket_session
        )
        
        # Check cache first
        cache_key = request.get_cache_key()
        cached_result = await self._get_cached_result(cache_key)
        if cached_result:
            logger.info(
                "Health analysis served from cache",
                request_id=request.request_id,
                text_length=len(text),
                cache_hit=True
            )
            
            # Execute callback immediately if provided
            if callback:
                await self._execute_callback(callback, cached_result)
            
            return request.request_id
        
        # Determine batch key for grouping similar requests
        batch_key = self._get_batch_key(request)
        
        # Add to or create batch
        if batch_key not in self.pending_batches:
            self.pending_batches[batch_key] = DebouncedBatch(
                batch_id=str(uuid4())
            )
        
        batch = self.pending_batches[batch_key]
        batch.add_request(request)
        
        # Schedule processing if not already scheduled
        if batch_key not in self.processing_tasks:
            self.processing_tasks[batch_key] = asyncio.create_task(
                self._schedule_batch_processing(batch_key)
            )
        
        logger.debug(
            "Health analysis request added to batch",
            request_id=request.request_id,
            batch_id=batch.batch_id,
            batch_key=batch_key,
            batch_size=len(batch.requests),
            priority=priority
        )
        
        return request.request_id
    
    def _get_batch_key(self, request: HealthAnalysisRequest) -> str:
        """Get batch key for grouping similar requests."""
        # Group by analysis type and user tier for similar processing requirements
        return f"{request.analysis_type}:{request.user_tier}"
    
    async def _schedule_batch_processing(self, batch_key: str):
        """Schedule batch processing after debounce delay."""
        try:
            # Wait for debounce delay
            await asyncio.sleep(self.debounce_delay)
            
            # Process the batch
            if batch_key in self.pending_batches:
                batch = self.pending_batches[batch_key]
                await self._process_batch(batch)
                
                # Clean up
                del self.pending_batches[batch_key]
                if batch_key in self.processing_tasks:
                    del self.processing_tasks[batch_key]
        
        except Exception as e:
            logger.error(
                "Batch processing failed",
                batch_key=batch_key,
                error=str(e)
            )
            # Clean up on error
            if batch_key in self.pending_batches:
                del self.pending_batches[batch_key]
            if batch_key in self.processing_tasks:
                del self.processing_tasks[batch_key]
    
    async def _process_batch(self, batch: DebouncedBatch):
        """Process a batch of health analysis requests."""
        if batch.processing_started or not batch.requests:
            return
        
        batch.processing_started = True
        
        logger.info(
            "Processing health analysis batch",
            batch_id=batch.batch_id,
            request_count=len(batch.requests),
            unique_texts=len(batch.get_unique_texts()),
            priority=batch.get_priority()
        )
        
        # Process each unique text in the batch
        for request in batch.requests:
            try:
                # Check cache again in case another request filled it
                cache_key = request.get_cache_key()
                cached_result = await self._get_cached_result(cache_key)
                
                if cached_result:
                    logger.debug(
                        "Using cached result for batch request",
                        request_id=request.request_id
                    )
                    result = cached_result
                else:
                    # Process with health worker
                    result = await self._process_individual_request(request)
                
                # Execute callback if provided
                if request.callback:
                    await self._execute_callback(request.callback, result)
                
                # Send WebSocket response if session provided
                if request.websocket_session:
                    await self._send_websocket_response(request, result)
                
            except Exception as e:
                logger.error(
                    "Failed to process individual request in batch",
                    request_id=request.request_id,
                    error=str(e)
                )
    
    async def _process_individual_request(self, request: HealthAnalysisRequest) -> Dict[str, Any]:
        """Process individual health analysis request."""
        # Create job data
        job_data = {
            'text': request.text,
            'block_id': request.block_id,
            'document_id': request.document_id,
            'options': {
                'analysis_type': request.analysis_type,
                'user_preferences': request.preferences
            }
        }
        
        # Enqueue job with priority handling
        job_id = await self.queue_manager.enqueue_job(
            agent_type='health',
            job_data=job_data,
            user_tier=request.user_tier,
            user_id=request.user_id
        )
        
        # Wait for result with timeout based on priority
        timeout = 15 if request.priority >= 2 else 10
        result = await self._wait_for_result(job_id, timeout)
        
        if result and result.get('success'):
            # Cache successful result
            cache_key = request.get_cache_key()
            await self._cache_result(cache_key, result)
            
            logger.info(
                "Health analysis completed in batch",
                request_id=request.request_id,
                job_id=job_id,
                overall_score=result.get('overall_score', 0),
                cached=True
            )
        else:
            logger.warning(
                "Health analysis failed in batch",
                request_id=request.request_id,
                job_id=job_id,
                error=result.get('error') if result else 'timeout'
            )
        
        return result or {'success': False, 'error': 'Analysis failed'}
    
    async def _wait_for_result(self, job_id: str, timeout: int) -> Optional[Dict[str, Any]]:
        """Wait for job result with timeout."""
        start_time = datetime.utcnow()
        
        while (datetime.utcnow() - start_time).total_seconds() < timeout:
            try:
                job_status = await self.queue_manager.get_job_status(job_id)
                
                if job_status:
                    status = job_status.get('status')
                    
                    if status == 'completed':
                        job_data = job_status.get('job_data', {})
                        return job_data.get('result', job_status.get('job_data', {}))
                    
                    elif status == 'failed':
                        return {
                            'success': False,
                            'error': job_status.get('error', 'Job failed')
                        }
                
                await asyncio.sleep(0.5)  # Check every 500ms
                
            except Exception as e:
                logger.error(f"Error checking job status for {job_id}", error=str(e))
                await asyncio.sleep(0.5)
        
        return None  # Timeout
    
    async def _execute_callback(self, callback: Callable, result: Dict[str, Any]):
        """Execute callback function with result."""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(result)
            else:
                callback(result)
        except Exception as e:
            logger.error("Callback execution failed", error=str(e))
    
    async def _send_websocket_response(self, request: HealthAnalysisRequest, result: Dict[str, Any]):
        """Send result via WebSocket if session is provided."""
        try:
            from websocket.server import get_realtime_server
            
            server = await get_realtime_server()
            
            await server.sio.emit('health_analysis_result', {
                'request_id': request.request_id,
                'document_id': request.document_id,
                'block_id': request.block_id,
                'analysis_type': request.analysis_type,
                'result': result,
                'debounced': True,
                'timestamp': datetime.utcnow().isoformat()
            }, room=request.websocket_session)
            
        except Exception as e:
            logger.error(
                "Failed to send WebSocket response",
                session=request.websocket_session,
                error=str(e)
            )
    
    async def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached health analysis result."""
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                if isinstance(cached_data, str):
                    return json.loads(cached_data)
                return cached_data
        except Exception as e:
            logger.warning("Failed to get cached health result", error=str(e))
        return None
    
    async def _cache_result(self, cache_key: str, result: Dict[str, Any], ttl: int = 3600):
        """Cache health analysis result."""
        try:
            # Remove non-serializable fields
            cache_data = {k: v for k, v in result.items() 
                         if k not in ['cached', 'job_id', 'request_id']}
            await self.redis.set(cache_key, json.dumps(cache_data), ttl=ttl)
        except Exception as e:
            logger.warning("Failed to cache health result", error=str(e))
    
    async def _cleanup_stale_batches(self):
        """Clean up stale batches periodically."""
        while True:
            try:
                await asyncio.sleep(60)  # Run every minute
                
                current_time = datetime.utcnow()
                stale_keys = []
                
                for batch_key, batch in self.pending_batches.items():
                    # Consider batches stale after 5 minutes
                    if (current_time - batch.created_at).total_seconds() > 300:
                        stale_keys.append(batch_key)
                
                for key in stale_keys:
                    logger.warning(
                        "Cleaning up stale health analysis batch",
                        batch_key=key,
                        age_seconds=(current_time - self.pending_batches[key].created_at).total_seconds()
                    )
                    
                    # Cancel processing task if exists
                    if key in self.processing_tasks:
                        self.processing_tasks[key].cancel()
                        del self.processing_tasks[key]
                    
                    del self.pending_batches[key]
                
            except Exception as e:
                logger.error("Batch cleanup failed", error=str(e))
    
    async def get_service_stats(self) -> Dict[str, Any]:
        """Get service statistics."""
        total_requests = sum(len(batch.requests) for batch in self.pending_batches.values())
        unique_texts = sum(len(batch.get_unique_texts()) for batch in self.pending_batches.values())
        
        return {
            'debounce_delay_ms': int(self.debounce_delay * 1000),
            'pending_batches': len(self.pending_batches),
            'total_pending_requests': total_requests,
            'unique_pending_texts': unique_texts,
            'processing_tasks': len(self.processing_tasks),
            'deduplication_rate': 1 - (unique_texts / max(total_requests, 1)),
            'service_initialized': self._initialized
        }
    
    async def shutdown(self):
        """Shutdown the service."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
        
        # Cancel all processing tasks
        for task in self.processing_tasks.values():
            task.cancel()
        
        self._initialized = False
        logger.info("Debounced health service shutdown")


# Global service instance
_debounced_health_service: Optional[DebouncedHealthService] = None


async def get_debounced_health_service() -> DebouncedHealthService:
    """Get or create debounced health service instance."""
    global _debounced_health_service
    if _debounced_health_service is None:
        _debounced_health_service = DebouncedHealthService()
        await _debounced_health_service.initialize()
    return _debounced_health_service