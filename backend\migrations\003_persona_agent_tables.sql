-- Revisionary Persona and Agent System Tables
-- Part 3 of the schema migration

-- 16. Personas Table
CREATE TABLE personas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    type persona_type_enum NOT NULL,
    demographics JSONB NOT NULL DEFAULT '{}'::jsonb,
    reading_preferences JSONB NOT NULL DEFAULT '{}'::jsonb,
    personality_traits JSONB NOT NULL DEFAULT '{}'::jsonb,
    feedback_style JSONB NOT NULL DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT TRUE,
    is_template BOOLEAN DEFAULT FALSE,
    usage_count INTEGER DEFAULT 0,
    effectiveness_score DECIMAL(3,2),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT personas_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT personas_demographics_valid CHECK (jsonb_typeof(demographics) = 'object'),
    CONSTRAINT personas_reading_prefs_valid CHECK (jsonb_typeof(reading_preferences) = 'object'),
    CONSTRAINT personas_personality_valid CHECK (jsonb_typeof(personality_traits) = 'object'),
    CONSTRAINT personas_feedback_style_valid CHECK (jsonb_typeof(feedback_style) = 'object'),
    CONSTRAINT personas_usage_count_positive CHECK (usage_count >= 0),
    CONSTRAINT personas_effectiveness_range CHECK (effectiveness_score IS NULL OR (effectiveness_score >= 0 AND effectiveness_score <= 1))
);

-- 17. Persona Feedback Table
CREATE TABLE persona_feedback (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    persona_id UUID REFERENCES personas(id) ON DELETE CASCADE,
    session_id UUID NOT NULL,
    comprehension_score DECIMAL(3,2) NOT NULL,
    engagement_score DECIMAL(3,2) NOT NULL,
    emotional_score DECIMAL(3,2) NOT NULL,
    style_score DECIMAL(3,2) NOT NULL,
    overall_score DECIMAL(3,2) NOT NULL,
    feedback_text TEXT NOT NULL,
    specific_issues JSONB DEFAULT '[]'::jsonb,
    suggestions JSONB DEFAULT '[]'::jsonb,
    confidence DECIMAL(3,2) NOT NULL,
    processing_time_ms INTEGER NOT NULL,
    model_used VARCHAR(50),
    tokens_used INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT persona_feedback_scores_range CHECK (
        comprehension_score >= 0 AND comprehension_score <= 1 AND
        engagement_score >= 0 AND engagement_score <= 1 AND
        emotional_score >= 0 AND emotional_score <= 1 AND
        style_score >= 0 AND style_score <= 1 AND
        overall_score >= 0 AND overall_score <= 1 AND
        confidence >= 0 AND confidence <= 1
    ),
    CONSTRAINT persona_feedback_processing_time_positive CHECK (processing_time_ms >= 0),
    CONSTRAINT persona_feedback_tokens_positive CHECK (tokens_used >= 0),
    CONSTRAINT persona_feedback_text_not_empty CHECK (LENGTH(TRIM(feedback_text)) > 0),
    CONSTRAINT persona_feedback_issues_valid CHECK (jsonb_typeof(specific_issues) = 'array'),
    CONSTRAINT persona_feedback_suggestions_valid CHECK (jsonb_typeof(suggestions) = 'array')
);

-- 18. Audience Analysis Table
CREATE TABLE audience_analysis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    persona_ids UUID[] NOT NULL,
    overall_appeal_score DECIMAL(3,2) NOT NULL,
    cross_audience_conflicts JSONB DEFAULT '[]'::jsonb,
    optimization_suggestions JSONB DEFAULT '[]'::jsonb,
    audience_alignment JSONB NOT NULL,
    analysis_summary TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT audience_analysis_appeal_range CHECK (overall_appeal_score >= 0 AND overall_appeal_score <= 1),
    CONSTRAINT audience_analysis_conflicts_valid CHECK (jsonb_typeof(cross_audience_conflicts) = 'array'),
    CONSTRAINT audience_analysis_suggestions_valid CHECK (jsonb_typeof(optimization_suggestions) = 'array'),
    CONSTRAINT audience_analysis_alignment_valid CHECK (jsonb_typeof(audience_alignment) = 'object'),
    CONSTRAINT audience_analysis_personas_not_empty CHECK (array_length(persona_ids, 1) > 0)
);

-- 19. Custom Agents Table
CREATE TABLE custom_agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type agent_type_enum NOT NULL,
    capabilities agent_capability_enum[] DEFAULT '{}',
    priority INTEGER DEFAULT 50,
    is_active BOOLEAN DEFAULT TRUE,
    is_template BOOLEAN DEFAULT FALSE,
    model_preference VARCHAR(50),
    max_context_tokens INTEGER DEFAULT 4000,
    temperature DECIMAL(3,2) DEFAULT 0.3,
    execution_timeout INTEGER DEFAULT 30000,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_used_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT custom_agents_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT custom_agents_priority_range CHECK (priority >= 1 AND priority <= 100),
    CONSTRAINT custom_agents_temperature_range CHECK (temperature >= 0 AND temperature <= 2),
    CONSTRAINT custom_agents_timeout_positive CHECK (execution_timeout > 0),
    CONSTRAINT custom_agents_context_tokens_positive CHECK (max_context_tokens > 0)
);

-- 20. Agent Style Rules Table
CREATE TABLE agent_style_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID REFERENCES custom_agents(id) ON DELETE CASCADE,
    category style_rule_category_enum NOT NULL,
    rule_name VARCHAR(255) NOT NULL,
    rule_value JSONB NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 50,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT agent_style_rules_name_not_empty CHECK (LENGTH(TRIM(rule_name)) > 0),
    CONSTRAINT agent_style_rules_value_valid CHECK (jsonb_typeof(rule_value) = 'object'),
    CONSTRAINT agent_style_rules_priority_range CHECK (priority >= 1 AND priority <= 100),
    
    UNIQUE(agent_id, category, rule_name)
);

-- 21. Agent Usage Statistics Table
CREATE TABLE agent_usage_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID REFERENCES custom_agents(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    invocations INTEGER DEFAULT 0,
    total_processing_time_ms BIGINT DEFAULT 0,
    suggestions_generated INTEGER DEFAULT 0,
    suggestions_accepted INTEGER DEFAULT 0,
    suggestions_rejected INTEGER DEFAULT 0,
    avg_confidence DECIMAL(3,2),
    error_count INTEGER DEFAULT 0,
    tokens_used INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT agent_usage_stats_counts_positive CHECK (
        invocations >= 0 AND total_processing_time_ms >= 0 AND
        suggestions_generated >= 0 AND suggestions_accepted >= 0 AND
        suggestions_rejected >= 0 AND error_count >= 0 AND tokens_used >= 0
    ),
    CONSTRAINT agent_usage_stats_confidence_range CHECK (
        avg_confidence IS NULL OR (avg_confidence >= 0 AND avg_confidence <= 1)
    ),
    
    UNIQUE(agent_id, user_id, date)
);

-- 22. Health Metrics Table
CREATE TABLE health_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    readability_score DECIMAL(5,2) NOT NULL,
    clarity_score DECIMAL(5,2) NOT NULL,
    voice_consistency_score DECIMAL(5,2) NOT NULL,
    inclusivity_score DECIMAL(5,2) NOT NULL,
    brand_alignment_score DECIMAL(5,2),
    overall_score DECIMAL(5,2) NOT NULL,
    flesch_kincaid_grade DECIMAL(4,2),
    flesch_reading_ease DECIMAL(5,2),
    passive_voice_percentage DECIMAL(4,2),
    avg_sentence_length DECIMAL(5,2),
    complex_words_percentage DECIMAL(4,2),
    processing_time_ms INTEGER NOT NULL,
    analysis_version VARCHAR(10) DEFAULT '1.0',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT health_metrics_scores_range CHECK (
        readability_score >= 0 AND readability_score <= 100 AND
        clarity_score >= 0 AND clarity_score <= 100 AND
        voice_consistency_score >= 0 AND voice_consistency_score <= 100 AND
        inclusivity_score >= 0 AND inclusivity_score <= 100 AND
        overall_score >= 0 AND overall_score <= 100
    ),
    CONSTRAINT health_metrics_brand_range CHECK (
        brand_alignment_score IS NULL OR (brand_alignment_score >= 0 AND brand_alignment_score <= 100)
    ),
    CONSTRAINT health_metrics_processing_time_positive CHECK (processing_time_ms >= 0)
);

-- 23. Health Issues Table
CREATE TABLE health_issues (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    health_metric_id UUID REFERENCES health_metrics(id) ON DELETE CASCADE,
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    issue_type health_issue_type_enum NOT NULL,
    severity health_issue_severity_enum NOT NULL,
    category VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    position JSONB NOT NULL,
    suggested_fix TEXT,
    confidence DECIMAL(3,2) NOT NULL,
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMPTZ,
    resolved_method VARCHAR(50),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT health_issues_description_not_empty CHECK (LENGTH(TRIM(description)) > 0),
    CONSTRAINT health_issues_position_valid CHECK (
        jsonb_typeof(position) = 'object' AND
        position ? 'start' AND position ? 'end'
    ),
    CONSTRAINT health_issues_confidence_range CHECK (confidence >= 0 AND confidence <= 1)
);