import { useAnalyticsStore } from '../stores/analyticsStore';
import { WritingQualityScore } from '../types/analytics';

/**
 * Analytics service for tracking user interactions and writing metrics
 * This service integrates with the analytics store to provide seamless tracking
 */
export class AnalyticsService {
  private static instance: AnalyticsService;
  private activeSessionId: string | null = null;
  private wordCountTracker: Map<string, number> = new Map();
  private qualityScoreCache: Map<string, WritingQualityScore> = new Map();

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  // Session Management
  startWritingSession(documentId: string): void {
    const store = useAnalyticsStore.getState();
    
    // End any existing session
    if (this.activeSessionId) {
      this.endWritingSession();
    }

    this.activeSessionId = store.startSession(documentId);
    this.wordCountTracker.set(documentId, 0);
    
    console.log(`Started writing session: ${this.activeSessionId} for document: ${documentId}`);
  }

  endWritingSession(): void {
    if (!this.activeSessionId) return;

    const store = useAnalyticsStore.getState();
    store.endSession(this.activeSessionId);
    
    console.log(`Ended writing session: ${this.activeSessionId}`);
    this.activeSessionId = null;
  }

  updateWritingProgress(documentId: string, wordCount: number, content: string): void {
    if (!this.activeSessionId) return;

    const store = useAnalyticsStore.getState();
    const previousWordCount = this.wordCountTracker.get(documentId) || 0;
    const wordsWritten = Math.max(0, wordCount - previousWordCount);
    
    if (wordsWritten > 0) {
      store.updateSession(this.activeSessionId, {
        wordsWritten: wordsWritten,
        wordsAtStart: previousWordCount,
      });
      
      this.wordCountTracker.set(documentId, wordCount);
    }

    // Update document analytics
    this.updateDocumentMetrics(documentId, {
      wordCount,
      lastModified: new Date(),
      readingTime: this.calculateReadingTime(wordCount),
    });
  }

  // AI Interaction Tracking
  trackAISuggestionAccepted(suggestionType: string, agent?: string): void {
    const store = useAnalyticsStore.getState();
    store.recordAIInteraction('accepted', agent);
    
    // Update active session if exists
    if (this.activeSessionId) {
      const currentSession = store.sessions.find(s => s.id === this.activeSessionId);
      if (currentSession) {
        store.updateSession(this.activeSessionId, {
          aiSuggestionsAccepted: currentSession.aiSuggestionsAccepted + 1,
        });
      }
    }

    console.log(`AI suggestion accepted: ${suggestionType} by ${agent || 'unknown'}`);
  }

  trackAISuggestionDismissed(suggestionType: string, agent?: string): void {
    const store = useAnalyticsStore.getState();
    store.recordAIInteraction('dismissed', agent);
    
    // Update active session if exists
    if (this.activeSessionId) {
      const currentSession = store.sessions.find(s => s.id === this.activeSessionId);
      if (currentSession) {
        store.updateSession(this.activeSessionId, {
          aiSuggestionsDismissed: currentSession.aiSuggestionsDismissed + 1,
        });
      }
    }

    console.log(`AI suggestion dismissed: ${suggestionType} by ${agent || 'unknown'}`);
  }

  trackAIGeneration(mode: string, successful: boolean = true): void {
    if (!this.activeSessionId || !successful) return;

    const store = useAnalyticsStore.getState();
    const currentSession = store.sessions.find(s => s.id === this.activeSessionId);
    
    if (currentSession) {
      store.updateSession(this.activeSessionId, {
        aiGenerationsUsed: currentSession.aiGenerationsUsed + 1,
      });
    }

    console.log(`AI generation used: ${mode}`);
  }

  // Feature Usage Tracking
  trackFeatureUsage(feature: string): void {
    if (!this.activeSessionId) return;

    const store = useAnalyticsStore.getState();
    const currentSession = store.sessions.find(s => s.id === this.activeSessionId);
    
    if (currentSession && !currentSession.featuresUsed.includes(feature)) {
      store.updateSession(this.activeSessionId, {
        featuresUsed: [...currentSession.featuresUsed, feature],
      });
    }

    console.log(`Feature used: ${feature}`);
  }

  trackFocusModeUsage(enabled: boolean): void {
    if (!this.activeSessionId) return;

    const store = useAnalyticsStore.getState();
    store.updateSession(this.activeSessionId, {
      focusModeUsed: enabled,
    });

    this.trackFeatureUsage('focus_mode');
  }

  // Quality Score Tracking
  updateQualityScore(documentId: string, score: WritingQualityScore): void {
    const store = useAnalyticsStore.getState();
    store.updateQualityScore(documentId, score);
    this.qualityScoreCache.set(documentId, score);
    
    console.log(`Quality score updated for ${documentId}:`, score);
  }

  // Document Analytics
  updateDocumentMetrics(documentId: string, metrics: {
    title?: string;
    wordCount?: number;
    readingTime?: number;
    lastModified?: Date;
    category?: string;
    collaborators?: number;
    viewCount?: number;
  }): void {
    const store = useAnalyticsStore.getState();
    store.updateDocumentAnalytics(documentId, metrics);
  }

  trackDocumentCreated(documentId: string, title: string, category: string = 'general'): void {
    this.updateDocumentMetrics(documentId, {
      title,
      category,
      wordCount: 0,
      readingTime: 0,
      createdAt: new Date(),
      lastModified: new Date(),
      timeSpent: 0,
      revisionsCount: 0,
      aiSuggestionsAccepted: 0,
      collaborators: 0,
      viewCount: 1,
    });

    // Start a writing session for the new document
    this.startWritingSession(documentId);
  }

  trackDocumentOpened(documentId: string): void {
    const store = useAnalyticsStore.getState();
    const doc = store.documentAnalytics.find(d => d.documentId === documentId);
    
    if (doc) {
      store.updateDocumentAnalytics(documentId, {
        viewCount: doc.viewCount + 1,
      });
    }

    this.startWritingSession(documentId);
  }

  // Goals and Achievements
  createWritingGoal(type: 'daily_words' | 'weekly_words' | 'daily_time' | 'weekly_time', target: number): void {
    const store = useAnalyticsStore.getState();
    store.createGoal({
      type,
      target,
      current: 0,
      startDate: new Date(),
      isActive: true,
      achieved: false,
    });
  }

  checkGoalProgress(): void {
    const store = useAnalyticsStore.getState();
    const today = new Date().toISOString().split('T')[0];
    const todayStats = store.dailyStats.find(d => d.date === today);
    
    if (!todayStats) return;

    // Check and update goals
    store.goals.forEach(goal => {
      if (!goal.isActive || goal.achieved) return;

      let currentProgress = 0;
      
      switch (goal.type) {
        case 'daily_words':
          currentProgress = todayStats.wordsWritten;
          break;
        case 'daily_time':
          currentProgress = todayStats.timeSpent;
          break;
        case 'weekly_words':
          const weekStats = this.getWeekStats();
          currentProgress = weekStats.reduce((acc, day) => acc + day.wordsWritten, 0);
          break;
        case 'weekly_time':
          const weekTimeStats = this.getWeekStats();
          currentProgress = weekTimeStats.reduce((acc, day) => acc + day.timeSpent, 0);
          break;
      }

      store.updateGoal(goal.id, {
        current: currentProgress,
        achieved: currentProgress >= goal.target,
      });
    });
  }

  // Utility Methods
  calculateReadingTime(wordCount: number): number {
    // Average reading speed: 200 words per minute
    return Math.ceil(wordCount / 200);
  }

  private getWeekStats() {
    const store = useAnalyticsStore.getState();
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    return store.dailyStats.filter(d => new Date(d.date) >= weekAgo);
  }

  // Analytics Computation
  triggerAnalyticsUpdate(): void {
    const store = useAnalyticsStore.getState();
    store.calculateInsights();
    store.updateStreak();
    store.checkAchievements();
    this.checkGoalProgress();
  }

  // Data Export/Import
  exportAnalyticsData(): string {
    const store = useAnalyticsStore.getState();
    return store.exportData();
  }

  importAnalyticsData(data: string): boolean {
    try {
      const store = useAnalyticsStore.getState();
      store.importData(data);
      return true;
    } catch (error) {
      console.error('Failed to import analytics data:', error);
      return false;
    }
  }

  // Cleanup
  cleanup(): void {
    if (this.activeSessionId) {
      this.endWritingSession();
    }
    this.wordCountTracker.clear();
    this.qualityScoreCache.clear();
  }
}

// Export singleton instance
export const analyticsService = AnalyticsService.getInstance();

// React hook for easy access to analytics service
export const useAnalyticsService = () => {
  return analyticsService;
};

// Helper functions for common analytics operations
export const trackEvent = (event: string, properties?: Record<string, any>) => {
  console.log(`Analytics Event: ${event}`, properties);
  // This could be extended to send to external analytics if needed
};

export const trackPageView = (page: string) => {
  trackEvent('page_view', { page });
};

export const trackFeatureUsage = (feature: string, properties?: Record<string, any>) => {
  analyticsService.trackFeatureUsage(feature);
  trackEvent('feature_usage', { feature, ...properties });
};