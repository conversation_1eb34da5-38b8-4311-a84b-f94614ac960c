// Firebase configuration and initialization
import { initializeApp } from 'firebase/app';
import { 
  getAuth, 
  connectAuth<PERSON>mulator,
  Auth,
  User as FirebaseUser,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  updateProfile
} from 'firebase/auth';

// Check if we're in dev mode
const isDevMode = import.meta.env.VITE_DEV_MODE === 'true';

// Firebase config - only initialize if not in dev mode
let app: any = null;
let auth: Auth | null = null;

if (!isDevMode) {
  const firebaseConfig = {
    apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
    authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
    projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
    storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
    appId: import.meta.env.VITE_FIREBASE_APP_ID,
  };

  // Initialize Firebase
  app = initializeApp(firebaseConfig);
  auth = getAuth(app);

  // Connect to emulator in development
  if (import.meta.env.DEV && auth && !(auth as any)._delegate?.config?.emulator) {
    connectAuthEmulator(auth, 'http://localhost:9099');
  }
}

export { auth };

// Firebase Auth service class with optimized token management
export class FirebaseAuthService {
  private tokenCache: { token: string; expiry: number } | null = null;
  private userDataCache: { uid: string; data: any; expiry: number } | null = null;
  private isDevMode = import.meta.env.VITE_DEV_MODE === 'true';
  
  constructor() {
    // Pre-warm the auth state
    this.initializeAuth();
  }

  private initializeAuth() {
    if (this.isDevMode) {
      // Dev mode - don't initialize Firebase auth
      console.log('Dev mode: Skipping Firebase auth initialization');
      return;
    }

    if (!auth) {
      console.error('Firebase auth not initialized');
      return;
    }

    // Single auth state listener that caches user data
    onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        // Pre-fetch token when user is authenticated
        await this.getValidToken();
        // Cache user data
        await this.cacheUserData(firebaseUser);
      } else {
        this.clearCaches();
      }
    });
  }

  /**
   * Get cached token or fetch new one if expired
   * Minimizes Firebase getIdToken() calls
   */
  async getValidToken(): Promise<string | null> {
    if (!auth) return null;
    const user = auth.currentUser;
    if (!user) return null;

    const now = Date.now();
    
    // Return cached token if still valid (5 min buffer)
    if (this.tokenCache && now < this.tokenCache.expiry - 300000) {
      return this.tokenCache.token;
    }

    try {
      // Force refresh if token expires soon
      const forceRefresh = this.tokenCache && now > this.tokenCache.expiry - 300000;
      const token = await user.getIdToken(forceRefresh || false);
      
      // Cache token with 1 hour expiry
      this.tokenCache = {
        token,
        expiry: now + 3600000, // 1 hour
      };
      
      return token;
    } catch (error) {
      console.error('Failed to get Firebase token:', error);
      this.tokenCache = null;
      return null;
    }
  }

  /**
   * Cache user data to minimize profile fetches
   */
  private async cacheUserData(firebaseUser: FirebaseUser) {
    const now = Date.now();
    
    // Use cached data if still fresh (30 minutes)
    if (
      this.userDataCache && 
      this.userDataCache.uid === firebaseUser.uid && 
      now < this.userDataCache.expiry
    ) {
      return this.userDataCache.data;
    }

    try {
      // Fetch fresh user profile from our API
      const token = await this.getValidToken();
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/v1/users/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const userData = await response.json();
        this.userDataCache = {
          uid: firebaseUser.uid,
          data: userData,
          expiry: now + 1800000, // 30 minutes
        };
        return userData;
      }
    } catch (error) {
      console.error('Failed to fetch user data:', error);
    }

    return null;
  }

  /**
   * Get cached user data
   */
  getCachedUserData(): any | null {
    const now = Date.now();
    if (this.userDataCache && now < this.userDataCache.expiry) {
      return this.userDataCache.data;
    }
    return null;
  }

  /**
   * Sign in with email and password
   */
  async signIn(email: string, password: string): Promise<FirebaseUser> {
    try {
      if (!auth) throw new Error('Firebase auth not initialized');
      const result = await signInWithEmailAndPassword(auth, email, password);
      return result.user;
    } catch (error) {
      console.error('Sign in failed:', error);
      throw error;
    }
  }

  /**
   * Sign up with email and password
   */
  async signUp(email: string, password: string, displayName: string): Promise<FirebaseUser> {
    try {
      if (!auth) throw new Error('Firebase auth not initialized');
      const result = await createUserWithEmailAndPassword(auth, email, password);
      
      // Update profile with display name
      await updateProfile(result.user, { displayName });
      
      return result.user;
    } catch (error) {
      console.error('Sign up failed:', error);
      throw error;
    }
  }

  /**
   * Sign out and clear caches
   */
  async signOut(): Promise<void> {
    try {
      if (!auth) throw new Error('Firebase auth not initialized');
      await signOut(auth);
      this.clearCaches();
    } catch (error) {
      console.error('Sign out failed:', error);
      throw error;
    }
  }

  /**
   * Clear all caches
   */
  private clearCaches() {
    this.tokenCache = null;
    this.userDataCache = null;
  }

  /**
   * Force refresh user data
   */
  async refreshUserData(): Promise<any | null> {
    this.userDataCache = null;
    if (!auth) return null;
    const user = auth.currentUser;
    if (user) {
      return await this.cacheUserData(user);
    }
    return null;
  }
}

// Export singleton instance
export const firebaseAuth = new FirebaseAuthService();