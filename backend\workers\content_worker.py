"""
Content Worker for Revisionary

Purpose: Specialized worker for content analysis and enhancement
- Uses Gemini 2.5 Flash-Lite Preview for long context analysis
- Focuses on content quality, accuracy, completeness
- Provides content improvement and fact-checking suggestions
- Supports research-backed content enhancement

Features:
- Content quality assessment
- Fact accuracy evaluation
- Completeness analysis
- Research gap identification
- Content enhancement suggestions
- Long-form content analysis (up to 8K tokens)
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import structlog

from workers.base_worker import BaseWorker

logger = structlog.get_logger(__name__)


class ContentWorker(BaseWorker):
    """Worker specialized for content analysis and enhancement."""
    
    def __init__(self, worker_id: Optional[str] = None):
        """Initialize content worker."""
        super().__init__('content', worker_id)
        
        # Content-specific configuration
        self.max_text_length = 8000  # Gemini supports much longer context
        self.prompt_templates = self._load_prompt_templates()
        
        logger.info(f"Content worker {self.worker_id} initialized")
    
    def _load_prompt_templates(self) -> Dict[str, str]:
        """Load optimized prompt templates for content analysis."""
        return {
            'content_analysis': """Analyze the content quality and provide comprehensive improvement suggestions.

Text: {text}

Content Domain: {domain}

Analyze for:
1. Content accuracy and factual correctness
2. Completeness and depth
3. Relevance to topic
4. Supporting evidence quality
5. Information gaps
6. Credibility and authority

Provide:
Content Quality Score: [1-10]

Accuracy Assessment:
- [factual accuracy evaluation]

Completeness Analysis:
- [areas well covered]
- [areas needing more detail]

Information Gaps:
- [missing information 1]
- [missing information 2]

Enhancement Suggestions:
- [specific improvement 1]
- [specific improvement 2]

Research Recommendations:
- [suggested research areas]""",
            
            'fact_checking': """Perform fact-checking analysis on this content.

Text: {text}

Domain/Topic: {domain}

Evaluate:
1. Factual claims and their accuracy
2. Statistics and data points
3. References and citations
4. Outdated information
5. Potential misinformation

Format:
Fact Check Results:

Verified Claims:
- [claim 1]: ✓ Accurate
- [claim 2]: ✓ Accurate

Questionable Claims:
- [claim]: ⚠️ Needs verification - [reason]

Potential Issues:
- [issue 1]: ❌ [explanation]

Suggestions:
- [how to improve factual accuracy]

Sources Needed:
- [areas requiring citations]""",
            
            'research_gaps': """Identify research gaps and suggest content improvements.

Text: {text}

Topic Area: {topic}
Target Audience: {audience}

Identify:
1. Under-researched areas
2. Missing perspectives
3. Incomplete arguments
4. Unsupported claims
5. Areas needing more depth

Format:
Research Gap Analysis:

Well-Supported Areas:
- [area 1]: [description]

Research Gaps:
- [gap 1]: [what's missing and why important]
- [gap 2]: [what's missing and why important]

Recommended Research:
- [specific research suggestion 1]
- [specific research suggestion 2]

Content Enhancement Priorities:
1. [highest priority improvement]
2. [second priority improvement]
3. [third priority improvement]""",
            
            'completeness_check': """Assess content completeness for the given topic and audience.

Text: {text}

Topic: {topic}
Intended Audience: {audience}
Content Goal: {goal}

Evaluate:
1. Topic coverage breadth
2. Depth of explanation
3. Audience appropriateness
4. Missing key concepts
5. Logical progression

Format:
Completeness Score: [1-10]

Well-Covered Aspects:
- [aspect 1]
- [aspect 2]

Missing Key Elements:
- [missing element 1]: [why important]
- [missing element 2]: [why important]

Depth Assessment:
- Too shallow: [areas needing more detail]
- Too complex: [areas to simplify]

Audience Alignment:
- [how well content matches audience needs]

Recommended Additions:
- [specific content to add]""",
            
            'evidence_evaluation': """Evaluate the quality and strength of evidence presented.

Text: {text}

Assess:
1. Types of evidence used
2. Evidence relevance and strength
3. Source credibility
4. Evidence gaps
5. Logical reasoning

Format:
Evidence Quality Score: [1-10]

Strong Evidence:
- [evidence type]: [description and strength]

Weak Evidence:
- [evidence type]: [issues and improvements needed]

Missing Evidence Types:
- [type 1]: [why needed]
- [type 2]: [why needed]

Reasoning Assessment:
- [evaluation of logical flow]

Recommendations:
- [how to strengthen evidence]""",
            
            'content_enrichment': """Suggest ways to enrich and enhance the content.

Text: {text}

Content Type: {content_type}
Purpose: {purpose}

Suggest enrichment through:
1. Additional examples
2. Case studies
3. Visual elements
4. Interactive elements
5. Expert quotes
6. Data/statistics
7. Historical context

Format:
Enrichment Suggestions:

Examples to Add:
- [example 1]: [why valuable]
- [example 2]: [why valuable]

Case Studies:
- [case study suggestion]: [relevance]

Visual Enhancements:
- [visual element]: [purpose]

Expert Perspectives:
- [expert type]: [what they could contribute]

Data Opportunities:
- [data type]: [how to incorporate]

Interactive Elements:
- [interactive suggestion]: [engagement value]

Historical Context:
- [historical element]: [relevance to topic]"""
        }
    
    async def process_job(self, job: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a content analysis job.
        
        Args:
            job: Job data containing text and content analysis options
            
        Returns:
            Dict with content analysis results and suggestions
        """
        job_id = job.get('job_id', 'unknown')
        text = job.get('text', '')
        options = job.get('options', {})
        
        # Validate input
        if not text or not text.strip():
            return {
                'job_id': job_id,
                'success': False,
                'error': 'Empty text provided'
            }
        
        # Limit text length for cost control (Gemini can handle more)
        original_text = text
        if len(text) > self.max_text_length:
            text = text[:self.max_text_length] + "..."
            logger.info(f"Text truncated for cost control", 
                       original_length=len(original_text),
                       truncated_length=len(text))
        
        try:
            # Determine analysis type and extract options
            analysis_type = options.get('analysis_type', 'content_analysis')
            domain = options.get('domain', 'general')
            topic = options.get('topic', 'general topic')
            audience = options.get('audience', 'general audience')
            content_type = options.get('content_type', 'article')
            purpose = options.get('purpose', 'inform')
            goal = options.get('goal', 'provide information')
            
            # Choose appropriate prompt
            if analysis_type == 'fact_checking':
                prompt = self.prompt_templates['fact_checking'].format(
                    text=text, 
                    domain=domain
                )
            elif analysis_type == 'research_gaps':
                prompt = self.prompt_templates['research_gaps'].format(
                    text=text, 
                    topic=topic,
                    audience=audience
                )
            elif analysis_type == 'completeness_check':
                prompt = self.prompt_templates['completeness_check'].format(
                    text=text, 
                    topic=topic,
                    audience=audience,
                    goal=goal
                )
            elif analysis_type == 'evidence_evaluation':
                prompt = self.prompt_templates['evidence_evaluation'].format(text=text)
            elif analysis_type == 'content_enrichment':
                prompt = self.prompt_templates['content_enrichment'].format(
                    text=text, 
                    content_type=content_type,
                    purpose=purpose
                )
            else:
                prompt = self.prompt_templates['content_analysis'].format(
                    text=text, 
                    domain=domain
                )
            
            # Get completion from LLM service (using Gemini for long context)
            result = await self.llm_service.get_completion(
                agent_type='content',
                prompt=prompt,
                context={
                    'job_id': job_id,
                    'text_length': len(text),
                    'analysis_type': analysis_type,
                    'domain': domain,
                    'options': options
                }
            )
            
            # Parse the response
            response_text = result['text'].strip()
            analysis_result = self._parse_content_response(response_text, analysis_type)
            
            # Track cost
            await self.cost_tracker.track_usage(
                model=result['model'],
                usage=result['usage'],
                agent_type='content',
                user_id=job.get('user_id')
            )
            
            # Build result
            content_result = {
                'job_id': job_id,
                'success': True,
                'original_text': original_text,
                'analysis_type': analysis_type,
                'domain': domain,
                'analysis_result': analysis_result,
                'processing_time': result.get('latency', 0),
                'tokens_used': result['usage']['input_tokens'] + result['usage']['output_tokens'],
                'cached_tokens': result['usage'].get('cached_tokens', 0),
                'model_used': result['model']
            }
            
            # Calculate confidence score
            content_result['confidence'] = self._calculate_confidence(result, analysis_type)
            
            logger.info(
                f"Content job {job_id} completed",
                analysis_type=analysis_type,
                domain=domain,
                tokens_used=content_result['tokens_used'],
                processing_time=content_result['processing_time']
            )
            
            return content_result
            
        except Exception as e:
            logger.error(f"Content job {job_id} failed", error=str(e), exc_info=True)
            return {
                'job_id': job_id,
                'success': False,
                'error': str(e),
                'original_text': original_text,
                'analysis_type': analysis_type
            }
    
    def _parse_content_response(self, response: str, analysis_type: str) -> Dict[str, Any]:
        """Parse the content analysis response."""
        analysis_result = {
            'raw_response': response,
            'analysis_type': analysis_type
        }
        
        try:
            lines = response.split('\n')
            current_section = None
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # Parse different sections based on content
                if 'Score:' in line:
                    score_text = line.split('Score:')[-1].strip()
                    try:
                        analysis_result['score'] = int(score_text.split()[0])
                    except (ValueError, IndexError):
                        analysis_result['score'] = 5  # Default
                
                elif 'Accuracy Assessment:' in line:
                    current_section = 'accuracy_assessment'
                    analysis_result['accuracy_assessment'] = []
                
                elif 'Completeness Analysis:' in line:
                    current_section = 'completeness_analysis'
                    analysis_result['completeness_analysis'] = []
                
                elif 'Information Gaps:' in line:
                    current_section = 'information_gaps'
                    analysis_result['information_gaps'] = []
                
                elif 'Enhancement Suggestions:' in line:
                    current_section = 'enhancement_suggestions'
                    analysis_result['enhancement_suggestions'] = []
                
                elif 'Research Recommendations:' in line:
                    current_section = 'research_recommendations'
                    analysis_result['research_recommendations'] = []
                
                elif 'Verified Claims:' in line:
                    current_section = 'verified_claims'
                    analysis_result['verified_claims'] = []
                
                elif 'Questionable Claims:' in line:
                    current_section = 'questionable_claims'
                    analysis_result['questionable_claims'] = []
                
                elif 'Potential Issues:' in line:
                    current_section = 'potential_issues'
                    analysis_result['potential_issues'] = []
                
                elif 'Sources Needed:' in line:
                    current_section = 'sources_needed'
                    analysis_result['sources_needed'] = []
                
                elif 'Research Gaps:' in line:
                    current_section = 'research_gaps'
                    analysis_result['research_gaps'] = []
                
                elif 'Well-Supported Areas:' in line or 'Well-Covered Aspects:' in line:
                    current_section = 'well_supported'
                    analysis_result['well_supported'] = []
                
                elif 'Missing Key Elements:' in line:
                    current_section = 'missing_elements'
                    analysis_result['missing_elements'] = []
                
                elif 'Recommended Additions:' in line:
                    current_section = 'recommended_additions'
                    analysis_result['recommended_additions'] = []
                
                elif 'Strong Evidence:' in line:
                    current_section = 'strong_evidence'
                    analysis_result['strong_evidence'] = []
                
                elif 'Weak Evidence:' in line:
                    current_section = 'weak_evidence'
                    analysis_result['weak_evidence'] = []
                
                elif 'Examples to Add:' in line:
                    current_section = 'examples_to_add'
                    analysis_result['examples_to_add'] = []
                
                elif line.startswith('-') and current_section:
                    # Extract bullet point
                    item = line[1:].strip()
                    if current_section not in analysis_result:
                        analysis_result[current_section] = []
                    analysis_result[current_section].append(item)
                
                elif line.startswith(('1.', '2.', '3.', '4.', '5.')) and current_section:
                    # Numbered list item
                    item = line[2:].strip()
                    if current_section not in analysis_result:
                        analysis_result[current_section] = []
                    analysis_result[current_section].append(item)
                
                elif ('✓' in line or '⚠️' in line or '❌' in line) and current_section:
                    # Fact check results with symbols
                    if current_section not in analysis_result:
                        analysis_result[current_section] = []
                    analysis_result[current_section].append(line)
        
        except Exception as e:
            logger.warning(f"Failed to parse content response", error=str(e))
            analysis_result['parse_error'] = str(e)
        
        return analysis_result
    
    def _calculate_confidence(self, llm_result: Dict, analysis_type: str) -> float:
        """Calculate confidence score for the content analysis."""
        confidence = 0.82  # Base confidence for content analysis
        
        # Adjust based on analysis type complexity
        if analysis_type == 'fact_checking':
            confidence *= 0.85  # Fact checking requires external verification
        elif analysis_type == 'research_gaps':
            confidence *= 0.88  # Research analysis is somewhat subjective
        elif analysis_type == 'evidence_evaluation':
            confidence *= 0.9   # Evidence evaluation is more objective
        elif analysis_type == 'completeness_check':
            confidence *= 0.92  # Completeness is easier to assess
        
        # Adjust based on cached tokens (Gemini may have less caching)
        cached_ratio = llm_result['usage'].get('cached_tokens', 0) / max(llm_result['usage']['input_tokens'], 1)
        if cached_ratio > 0.3:  # Lower threshold for Gemini
            confidence = min(0.95, confidence * 1.05)
        
        return round(confidence, 2)
    
    async def get_content_stats(self) -> Dict[str, Any]:
        """Get content-specific worker statistics."""
        base_stats = await self.get_worker_stats()
        
        # Add content-specific metrics
        content_stats = {
            **base_stats,
            'max_text_length': self.max_text_length,
            'analysis_types': [
                'content_analysis', 'fact_checking', 'research_gaps',
                'completeness_check', 'evidence_evaluation', 'content_enrichment'
            ],
            'supports_batch': False,  # Content analysis requires individual attention
            'model_used': 'gemini-2.5-flash-lite-preview',
            'long_context_support': True
        }
        
        return content_stats


# Standalone function to run the worker
async def run_content_worker():
    """Run a content worker instance."""
    worker = ContentWorker()
    await worker.initialize()
    await worker.start()


if __name__ == "__main__":
    import asyncio
    asyncio.run(run_content_worker())