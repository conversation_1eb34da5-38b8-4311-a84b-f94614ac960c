import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  BookOpenIcon,
  LinkIcon,
  AcademicCapIcon,
  DocumentTextIcon,
  PlusIcon,
  CheckIcon,
  XMarkIcon,
  SparklesIcon,
  GlobeAltIcon,
  NewspaperIcon,
} from '@heroicons/react/24/outline';

interface Citation {
  id: string;
  title: string;
  authors: string[];
  year: number;
  source: string;
  type: 'journal' | 'book' | 'website' | 'report' | 'article';
  doi?: string;
  url?: string;
  abstract?: string;
  tags?: string[];
  style: 'apa' | 'mla' | 'chicago';
  usageCount: number;
  dateAdded: Date;
}

interface SmartCitationSearchProps {
  isOpen: boolean;
  onClose: () => void;
  onInsertCitation: (citation: Citation) => void;
  position?: { x: number; y: number };
  searchQuery?: string;
}

const mockCitations: Citation[] = [
  {
    id: '1',
    title: 'The Impact of Artificial Intelligence on Modern Writing',
    authors: ['<PERSON>, <PERSON><PERSON>', '<PERSON>, <PERSON>', '<PERSON>, K<PERSON>'],
    year: 2023,
    source: 'Journal of Digital Humanities',
    type: 'journal',
    doi: '10.1234/jdh.2023.001',
    abstract: 'This study examines how AI technologies are transforming the writing process...',
    tags: ['AI', 'writing', 'technology'],
    style: 'apa',
    usageCount: 0,
    dateAdded: new Date(),
  },
  {
    id: '2',
    title: 'Writing in the Digital Age: A Comprehensive Guide',
    authors: ['Davis, R.'],
    year: 2022,
    source: 'Academic Press',
    type: 'book',
    url: 'https://example.com/writing-digital-age',
    abstract: 'A thorough exploration of modern writing techniques and digital tools...',
    tags: ['digital writing', 'education'],
    style: 'mla',
    usageCount: 0,
    dateAdded: new Date(),
  },
  {
    id: '3',
    title: 'The Future of Collaborative Writing Tools',
    authors: ['Wilson, S.', 'Taylor, A.'],
    year: 2023,
    source: 'TechCrunch',
    type: 'article',
    url: 'https://techcrunch.com/future-collaborative-writing',
    abstract: 'An analysis of emerging trends in collaborative writing software...',
    tags: ['collaboration', 'software', 'productivity'],
    style: 'apa',
    usageCount: 0,
    dateAdded: new Date(),
  },
  {
    id: '4',
    title: 'Research Methods in Digital Writing Studies',
    authors: ['Garcia, L.', 'Chen, W.', 'Anderson, P.'],
    year: 2021,
    source: 'Oxford University Press',
    type: 'book',
    doi: '10.1093/oup.2021.456',
    abstract: 'Methodological approaches to studying writing in digital environments...',
    tags: ['research methods', 'digital writing'],
    style: 'chicago',
    usageCount: 0,
    dateAdded: new Date(),
  },
];

const SmartCitationSearch: React.FC<SmartCitationSearchProps> = ({
  isOpen,
  onClose,
  onInsertCitation,
  position,
  searchQuery = '',
}) => {
  const [query, setQuery] = useState(searchQuery);
  const [results, setResults] = useState<Citation[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [recentCitations, setRecentCitations] = useState<Citation[]>([]);
  
  const searchInputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    if (query.trim()) {
      setIsSearching(true);
      // Simulate API search delay
      const timer = setTimeout(() => {
        const filtered = mockCitations.filter(citation =>
          citation.title.toLowerCase().includes(query.toLowerCase()) ||
          citation.authors.some(author => author.toLowerCase().includes(query.toLowerCase())) ||
          citation.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
        );
        setResults(filtered);
        setSelectedIndex(0);
        setIsSearching(false);
      }, 300);
      return () => clearTimeout(timer);
    } else {
      setResults([]);
      setSelectedIndex(0);
    }
  }, [query]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => Math.max(prev - 1, 0));
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (results[selectedIndex]) {
        handleInsertCitation(results[selectedIndex]);
      }
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  const handleInsertCitation = (citation: Citation) => {
    onInsertCitation(citation);
    setRecentCitations(prev => {
      const updated = [citation, ...prev.filter(c => c.id !== citation.id)];
      return updated.slice(0, 5); // Keep only 5 recent citations
    });
    onClose();
  };

  const getTypeIcon = (type: Citation['type']) => {
    switch (type) {
      case 'journal': return AcademicCapIcon;
      case 'book': return BookOpenIcon;
      case 'website': return GlobeAltIcon;
      case 'article': return NewspaperIcon;
      case 'report': return DocumentTextIcon;
      default: return DocumentTextIcon;
    }
  };

  const getTypeColor = (type: Citation['type']) => {
    switch (type) {
      case 'journal': return 'blue';
      case 'book': return 'green';
      case 'website': return 'purple';
      case 'article': return 'orange';
      case 'report': return 'gray';
      default: return 'gray';
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/10 backdrop-blur-sm z-50"
        onClick={onClose}
      >
        <motion.div
          ref={containerRef}
          initial={{ opacity: 0, scale: 0.95, y: -20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: -20 }}
          className="absolute bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-slate-200/50 w-[600px] max-h-[500px] overflow-hidden"
          style={{
            left: position?.x || '50%',
            top: position?.y || '50%',
            transform: position ? 'none' : 'translate(-50%, -50%)',
          }}
          onClick={e => e.stopPropagation()}
        >
          {/* Header */}
          <div className="px-6 py-4 border-b border-slate-200/50 bg-gradient-to-r from-blue-50/50 to-purple-50/50">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                <BookOpenIcon className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-slate-900">Smart Citations</h2>
                <p className="text-sm text-slate-600">Search and insert citations</p>
              </div>
            </div>
          </div>

          {/* Search Input */}
          <div className="p-4 border-b border-slate-200/50">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search by title, author, keywords..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyDown={handleKeyDown}
                className="w-full pl-10 pr-4 py-3 bg-slate-50/50 border border-slate-200 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              />
              {isSearching && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                </div>
              )}
            </div>
          </div>

          {/* Results */}
          <div className="max-h-80 overflow-y-auto">
            {query.trim() ? (
              results.length > 0 ? (
                <div className="p-2">
                  {results.map((citation, index) => (
                    <CitationItem
                      key={citation.id}
                      citation={citation}
                      isSelected={index === selectedIndex}
                      onClick={() => handleInsertCitation(citation)}
                      onMouseEnter={() => setSelectedIndex(index)}
                    />
                  ))}
                </div>
              ) : !isSearching ? (
                <div className="p-8 text-center">
                  <MagnifyingGlassIcon className="w-12 h-12 text-slate-300 mx-auto mb-3" />
                  <h3 className="text-lg font-semibold text-slate-900 mb-2">No results found</h3>
                  <p className="text-slate-600">Try different keywords or check spelling</p>
                  <button className="mt-4 flex items-center mx-auto px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:shadow-md transition-all">
                    <PlusIcon className="w-4 h-4 mr-2" />
                    Add Custom Citation
                  </button>
                </div>
              ) : null
            ) : (
              <div className="p-4">
                {recentCitations.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-sm font-semibold text-slate-700 mb-3">Recent Citations</h3>
                    <div className="space-y-1">
                      {recentCitations.map((citation) => (
                        <CitationItem
                          key={citation.id}
                          citation={citation}
                          isSelected={false}
                          onClick={() => handleInsertCitation(citation)}
                          compact
                        />
                      ))}
                    </div>
                  </div>
                )}
                
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold text-slate-700">Quick Actions</h3>
                  <div className="grid grid-cols-2 gap-3">
                    <button className="flex items-center p-3 bg-slate-50 rounded-xl hover:bg-slate-100 transition-colors">
                      <LinkIcon className="w-5 h-5 text-blue-600 mr-3" />
                      <div className="text-left">
                        <p className="text-sm font-medium text-slate-900">Import from URL</p>
                        <p className="text-xs text-slate-500">Paste DOI or URL</p>
                      </div>
                    </button>
                    <button className="flex items-center p-3 bg-slate-50 rounded-xl hover:bg-slate-100 transition-colors">
                      <PlusIcon className="w-5 h-5 text-green-600 mr-3" />
                      <div className="text-left">
                        <p className="text-sm font-medium text-slate-900">Manual Entry</p>
                        <p className="text-xs text-slate-500">Create custom citation</p>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="px-4 py-3 bg-slate-50/50 border-t border-slate-200/50">
            <div className="flex items-center justify-between text-xs text-slate-500">
              <div className="flex items-center space-x-4">
                <span>↑↓ Navigate</span>
                <span>⏎ Select</span>
                <span>⎋ Close</span>
              </div>
              <div className="flex items-center space-x-2">
                <SparklesIcon className="w-4 h-4" />
                <span>Powered by Smart Search</span>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

const CitationItem: React.FC<{
  citation: Citation;
  isSelected: boolean;
  onClick: () => void;
  onMouseEnter?: () => void;
  compact?: boolean;
}> = ({ citation, isSelected, onClick, onMouseEnter, compact = false }) => {
  const TypeIcon = useRef(getTypeIcon(citation.type)).current;
  const typeColor = getTypeColor(citation.type);

  function getTypeIcon(type: Citation['type']) {
    switch (type) {
      case 'journal': return AcademicCapIcon;
      case 'book': return BookOpenIcon;
      case 'website': return GlobeAltIcon;
      case 'article': return NewspaperIcon;
      case 'report': return DocumentTextIcon;
      default: return DocumentTextIcon;
    }
  }

  function getTypeColor(type: Citation['type']) {
    switch (type) {
      case 'journal': return 'blue';
      case 'book': return 'green';
      case 'website': return 'purple';
      case 'article': return 'orange';
      case 'report': return 'gray';
      default: return 'gray';
    }
  }

  return (
    <motion.button
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      className={`w-full text-left p-4 rounded-xl transition-all duration-200 ${
        isSelected
          ? 'bg-blue-50 border-2 border-blue-200 shadow-sm'
          : 'bg-white hover:bg-slate-50 border-2 border-transparent'
      } ${compact ? 'py-2' : ''}`}
      whileHover={{ scale: 1.01 }}
      whileTap={{ scale: 0.99 }}
    >
      <div className="flex items-start space-x-3">
        <div className={`w-8 h-8 bg-${typeColor}-100 rounded-lg flex items-center justify-center flex-shrink-0`}>
          <TypeIcon className={`w-4 h-4 text-${typeColor}-600`} />
        </div>
        
        <div className="flex-1 min-w-0">
          <h4 className={`font-semibold text-slate-900 line-clamp-2 ${compact ? 'text-sm' : ''}`}>
            {citation.title}
          </h4>
          
          <div className={`mt-1 ${compact ? 'text-xs' : 'text-sm'} text-slate-600`}>
            <p>{citation.authors.join(', ')} ({citation.year})</p>
            <p className="text-slate-500">{citation.source}</p>
          </div>

          {!compact && citation.abstract && (
            <p className="mt-2 text-xs text-slate-500 line-clamp-2">
              {citation.abstract}
            </p>
          )}

          {!compact && citation.tags && (
            <div className="mt-2 flex flex-wrap gap-1">
              {citation.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag}
                  className="px-2 py-1 text-xs bg-slate-100 text-slate-600 rounded-md"
                >
                  {tag}
                </span>
              ))}
            </div>
          )}
        </div>

        <div className="flex-shrink-0">
          {isSelected && (
            <CheckIcon className="w-5 h-5 text-blue-600" />
          )}
        </div>
      </div>
    </motion.button>
  );
};

export default SmartCitationSearch;