"""
Health Worker for Revisionary

Purpose: Specialized worker for comprehensive writing health analysis
- Uses GPT-4.1-mini for multi-dimensional health scoring
- Analyzes 4 core metrics: readability, clarity, voice consistency, brand alignment
- Provides real-time health feedback with actionable suggestions
- Optimized for sub-100ms response times with intelligent caching

Features:
- Comprehensive health metrics calculation
- Real-time issue detection and classification
- Actionable improvement suggestions
- Health trend analysis
- Debounced processing for cost efficiency
"""

import json
import hashlib
from typing import Dict, Any, List, Optional
from datetime import datetime
from uuid import uuid4
import structlog
import re

from workers.base_worker import BaseWorker

logger = structlog.get_logger(__name__)


class HealthWorker(BaseWorker):
    """Worker specialized for writing health analysis."""
    
    def __init__(self, worker_id: Optional[str] = None):
        """Initialize health worker."""
        super().__init__('health', worker_id)
        
        # Health-specific configuration
        self.max_text_length = 2500  # Longer context for comprehensive analysis
        self.cache_ttl = 3600  # 1-hour cache for health results
        self.prompt_templates = self._load_prompt_templates()
        
        # Health metrics configuration
        self.health_metrics = [
            'readability', 'clarity', 'voice_consistency', 
            'brand_alignment'
        ]
        
        logger.info(f"Health worker {self.worker_id} initialized")
    
    def _load_prompt_templates(self) -> Dict[str, str]:
        """Load optimized prompt templates for health analysis."""
        return {
            'comprehensive_health': """Analyze this text for writing health across 4 dimensions. Provide specific scores and actionable feedback.

Text: {text}

Analyze and score (0-100) these metrics:

1. READABILITY
   - Sentence length appropriateness
   - Vocabulary complexity 
   - Reading ease
   - Grade level appropriateness

2. CLARITY  
   - Word choice precision
   - Sentence structure clarity
   - Pronoun reference clarity
   - Active vs passive voice usage

3. VOICE CONSISTENCY
   - Tense consistency throughout
   - Person consistency (1st/2nd/3rd)
   - Tone consistency
   - Formality level consistency

4. BRAND ALIGNMENT
   - Professional tone appropriateness
   - Terminology consistency
   - Style guideline adherence
   - Voice/messaging consistency

Format response as:
SCORES:
Readability: [score]/100
Clarity: [score]/100 
Voice: [score]/100
Brand: [score]/100
Overall: [overall_score]/100

SPECIFIC ISSUES:
- [Issue 1]: Position [start-end] - [brief description] - [suggestion]
- [Issue 2]: Position [start-end] - [brief description] - [suggestion]

DETAILED METRICS:
Readability: Grade level [X.X], Reading ease [XX], Avg sentence length [XX] words
Clarity: Wordiness issues [X], Unclear pronouns [X], Passive voice [XX]%
Voice: Tense consistency [XX]%, Person consistency [XX]%, Tone variation [X]
Brand: Terminology compliance [XX]%, Style adherence [XX]%, Voice match [XX]%

IMPROVEMENT SUGGESTIONS:
1. [Specific actionable suggestion 1]
2. [Specific actionable suggestion 2]
3. [Specific actionable suggestion 3]""",
            
            'quick_health': """Quickly assess writing health for real-time feedback.

Text: {text}

Provide brief health assessment:

QUICK SCORES (0-100):
Overall: [score]
Readability: [score] 
Clarity: [score]
Voice: [score]

TOP ISSUES:
- [Most important issue with suggestion]
- [Second most important issue]

QUICK WINS:
- [Easy improvement 1]
- [Easy improvement 2]""",
            
            'focused_health': """Analyze specific health dimensions based on user focus areas.

Text: {text}
Focus Areas: {focus_areas}

Provide detailed analysis for: {focus_areas}

For each focus area, provide:
- Score (0-100)
- Specific issues found
- Improvement suggestions
- Impact assessment

Format:
[FOCUS_AREA]: [Score]/100
Issues: [specific issues]
Suggestions: [actionable improvements]
Impact: [how this affects reader experience]"""
        }
    
    async def process_job(self, job: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a health analysis job.
        
        Args:
            job: Job data containing text and health analysis options
            
        Returns:
            Dict with comprehensive health analysis results
        """
        job_id = job.get('job_id', 'unknown')
        text = job.get('text', '')
        options = job.get('options', {})
        
        # Validate input
        if not text or not text.strip():
            return {
                'job_id': job_id,
                'success': False,
                'error': 'Empty text provided'
            }
        
        # Check cache first
        text_hash = hashlib.md5(text.encode()).hexdigest()
        cache_key = f"health:{text_hash}:{json.dumps(options, sort_keys=True)}"
        
        cached_result = await self._get_cached_result(cache_key)
        if cached_result:
            logger.info(f"Health job {job_id} served from cache")
            cached_result['job_id'] = job_id
            cached_result['cached'] = True
            return cached_result
        
        # Limit text length for cost control
        original_text = text
        if len(text) > self.max_text_length:
            text = text[:self.max_text_length] + "..."
            logger.info(f"Text truncated for cost control", 
                       original_length=len(original_text),
                       truncated_length=len(text))
        
        try:
            # Determine analysis type
            analysis_type = options.get('analysis_type', 'comprehensive')
            focus_areas = options.get('focus_areas', self.health_metrics)
            
            # Choose appropriate prompt
            if analysis_type == 'quick':
                prompt = self.prompt_templates['quick_health'].format(text=text)
            elif analysis_type == 'focused':
                prompt = self.prompt_templates['focused_health'].format(
                    text=text,
                    focus_areas=', '.join(focus_areas)
                )
            else:
                prompt = self.prompt_templates['comprehensive_health'].format(text=text)
            
            # Get completion from LLM service
            result = await self.llm_service.get_completion(
                agent_type='health',
                prompt=prompt,
                context={
                    'job_id': job_id,
                    'text_length': len(text),
                    'analysis_type': analysis_type,
                    'focus_areas': focus_areas
                }
            )
            
            # Parse the response
            response_text = result['text'].strip()
            health_analysis = self._parse_health_response(response_text, analysis_type, original_text)
            
            # Add metadata
            health_analysis.update({
                'job_id': job_id,
                'analysis_id': str(uuid4()),
                'timestamp': datetime.utcnow().isoformat() + "Z",
                'analysis_type': analysis_type,
                'focus_areas': focus_areas,
                'processing_time': result.get('latency', 0),
                'tokens_used': result['usage']['input_tokens'] + result['usage']['output_tokens'],
                'cached_tokens': result['usage'].get('cached_tokens', 0),
                'model_used': result['model'],
                'success': True
            })
            
            # Calculate text statistics
            health_analysis['text_stats'] = self._calculate_text_stats(original_text)
            
            # Cache the result
            await self._cache_result(cache_key, health_analysis)
            
            # Track cost
            await self.cost_tracker.track_usage(
                model=result['model'],
                usage=result['usage'],
                agent_type='health',
                user_id=job.get('user_id')
            )
            
            logger.info(
                f"Health job {job_id} completed",
                overall_score=health_analysis.get('overall_score', 0),
                issues_found=len(health_analysis.get('issues', [])),
                analysis_type=analysis_type,
                tokens_used=health_analysis['tokens_used'],
                processing_time=health_analysis['processing_time']
            )
            
            return health_analysis
            
        except Exception as e:
            logger.error(f"Health job {job_id} failed", error=str(e), exc_info=True)
            return {
                'job_id': job_id,
                'success': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat() + "Z",
                'analysis_type': analysis_type
            }
    
    def _parse_health_response(self, response: str, analysis_type: str, original_text: str) -> Dict[str, Any]:
        """Parse the health analysis response into structured data."""
        health_data = {
            'overall_score': 75,  # Default fallback
            'metrics': {},
            'issues': [],
            'improvement_suggestions': [],
            'detailed_metrics': {}
        }
        
        try:
            lines = response.split('\n')
            current_section = None
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # Parse scores section
                if 'SCORES:' in line or 'QUICK SCORES' in line:
                    current_section = 'scores'
                    continue
                
                elif 'SPECIFIC ISSUES:' in line or 'TOP ISSUES:' in line:
                    current_section = 'issues'
                    continue
                
                elif 'DETAILED METRICS:' in line:
                    current_section = 'detailed_metrics'
                    continue
                
                elif 'IMPROVEMENT SUGGESTIONS:' in line or 'QUICK WINS:' in line:
                    current_section = 'suggestions'
                    continue
                
                # Parse content based on current section
                if current_section == 'scores':
                    self._parse_scores_line(line, health_data)
                
                elif current_section == 'issues':
                    issue = self._parse_issue_line(line, original_text)
                    if issue:
                        health_data['issues'].append(issue)
                
                elif current_section == 'detailed_metrics':
                    self._parse_detailed_metrics_line(line, health_data)
                
                elif current_section == 'suggestions':
                    suggestion = self._parse_suggestion_line(line)
                    if suggestion:
                        health_data['improvement_suggestions'].append(suggestion)
            
            # Ensure we have all required metrics
            self._ensure_complete_metrics(health_data)
            
        except Exception as e:
            logger.warning(f"Failed to parse health response", error=str(e))
            # Return default health structure
            health_data = self._get_default_health_data()
        
        return health_data
    
    def _parse_scores_line(self, line: str, health_data: Dict[str, Any]):
        """Parse a score line and update health data."""
        # Extract scores like "Readability: 85/100" or "Overall: 82"
        score_patterns = [
            (r'Overall:\s*(\d+)', 'overall_score'),
            (r'Readability:\s*(\d+)', 'readability'),
            (r'Clarity:\s*(\d+)', 'clarity'),
            (r'Voice:\s*(\d+)', 'voice_consistency'),
            (r'Brand:\s*(\d+)', 'brand_alignment')
        ]
        
        for pattern, key in score_patterns:
            match = re.search(pattern, line, re.IGNORECASE)
            if match:
                score = int(match.group(1))
                if key == 'overall_score':
                    health_data['overall_score'] = score
                else:
                    if 'metrics' not in health_data:
                        health_data['metrics'] = {}
                    health_data['metrics'][key] = {'score': score}
    
    def _parse_issue_line(self, line: str, original_text: str) -> Optional[Dict[str, Any]]:
        """Parse an issue line and return structured issue data."""
        # Look for patterns like "- [Issue]: Position [start-end] - [description] - [suggestion]"
        if not line.startswith('-'):
            return None
        
        line = line[1:].strip()  # Remove leading dash
        
        # Try to extract position
        position_match = re.search(r'Position\s*(\d+)-(\d+)', line, re.IGNORECASE)
        if position_match:
            start = int(position_match.group(1))
            end = int(position_match.group(2))
        else:
            # Default position if not specified
            start = 0
            end = min(50, len(original_text))
        
        # Split by common separators to extract parts
        parts = re.split(r'\s*-\s*', line)
        
        issue_type = 'general'
        description = parts[0] if parts else line
        suggestion = parts[-1] if len(parts) > 1 else "Review this section for improvement"
        
        # Try to determine issue type from description
        if any(word in description.lower() for word in ['sentence', 'long', 'short', 'complex']):
            issue_type = 'readability'
        elif any(word in description.lower() for word in ['unclear', 'vague', 'confusing', 'pronoun']):
            issue_type = 'clarity'
        elif any(word in description.lower() for word in ['tense', 'voice', 'tone', 'person']):
            issue_type = 'voice_consistency'
        elif any(word in description.lower() for word in ['brand', 'style', 'terminology']):
            issue_type = 'brand_alignment'
        
        return {
            'id': f"issue_{uuid4().hex[:8]}",
            'type': issue_type,
            'severity': 'medium',  # Default severity
            'position': {'start': start, 'end': end},
            'description': description,
            'suggestion': suggestion,
            'impact': f"May affect {issue_type} of the text"
        }
    
    def _parse_detailed_metrics_line(self, line: str, health_data: Dict[str, Any]):
        """Parse detailed metrics line and update health data."""
        # Look for patterns like "Readability: Grade level 9.2, Reading ease 65"
        if 'readability' in line.lower():
            grade_match = re.search(r'Grade level\s*([\d.]+)', line, re.IGNORECASE)
            ease_match = re.search(r'Reading ease\s*(\d+)', line, re.IGNORECASE)
            length_match = re.search(r'Avg sentence length\s*(\d+)', line, re.IGNORECASE)
            
            if 'readability' not in health_data['metrics']:
                health_data['metrics']['readability'] = {'score': 75}
            
            if grade_match:
                health_data['metrics']['readability']['flesch_kincaid_grade'] = float(grade_match.group(1))
            if ease_match:
                health_data['metrics']['readability']['flesch_reading_ease'] = int(ease_match.group(1))
            if length_match:
                health_data['metrics']['readability']['avg_sentence_length'] = int(length_match.group(1))
        
        elif 'clarity' in line.lower():
            wordiness_match = re.search(r'Wordiness issues\s*(\d+)', line, re.IGNORECASE)
            pronouns_match = re.search(r'Unclear pronouns\s*(\d+)', line, re.IGNORECASE)
            passive_match = re.search(r'Passive voice\s*(\d+)', line, re.IGNORECASE)
            
            if 'clarity' not in health_data['metrics']:
                health_data['metrics']['clarity'] = {'score': 75}
            
            if wordiness_match:
                health_data['metrics']['clarity']['wordiness_issues'] = int(wordiness_match.group(1))
            if pronouns_match:
                health_data['metrics']['clarity']['unclear_pronouns'] = int(pronouns_match.group(1))
            if passive_match:
                health_data['metrics']['clarity']['passive_voice_percentage'] = int(passive_match.group(1))
    
    def _parse_suggestion_line(self, line: str) -> Optional[str]:
        """Parse a suggestion line."""
        if line.startswith(('-', '•', '*')) or re.match(r'^\d+\.', line):
            return line.lstrip('-•* ').lstrip('**********. ')
        return None
    
    def _ensure_complete_metrics(self, health_data: Dict[str, Any]):
        """Ensure all required health metrics are present."""
        required_metrics = {
            'readability': {
                'score': 75,
                'flesch_kincaid_grade': 9.0,
                'flesch_reading_ease': 65,
                'target_audience': 'General Adult'
            },
            'clarity': {
                'score': 80,
                'wordiness_issues': 2,
                'redundancy_count': 1,
                'unclear_pronouns': 1,
                'passive_voice_percentage': 15
            },
            'voice_consistency': {
                'score': 85,
                'tense_consistency': 90,
                'person_consistency': 95,
                'tone_variation': 5,
                'formality_consistency': 80
            },
            'brand_alignment': {
                'score': 75,
                'terminology_compliance': 80,
                'style_guide_adherence': 75,
                'voice_match': 70,
                'messaging_consistency': 80
            }
        }
        
        if 'metrics' not in health_data:
            health_data['metrics'] = {}
        
        for metric, default_values in required_metrics.items():
            if metric not in health_data['metrics']:
                health_data['metrics'][metric] = default_values
            else:
                # Fill in missing sub-metrics
                for key, value in default_values.items():
                    if key not in health_data['metrics'][metric]:
                        health_data['metrics'][metric][key] = value
        
        # Calculate overall score if not present
        if not health_data.get('overall_score'):
            metric_scores = [health_data['metrics'][m]['score'] for m in required_metrics.keys()]
            health_data['overall_score'] = int(sum(metric_scores) / len(metric_scores))
    
    def _get_default_health_data(self) -> Dict[str, Any]:
        """Get default health data structure when parsing fails."""
        default_data = {
            'overall_score': 75,
            'metrics': {},
            'issues': [],
            'improvement_suggestions': [
                "Review text for clarity and readability",
                "Ensure consistent voice and tone",
                "Check for brand alignment compliance"
            ]
        }
        
        self._ensure_complete_metrics(default_data)
        return default_data
    
    def _calculate_text_stats(self, text: str) -> Dict[str, Any]:
        """Calculate basic text statistics."""
        words = text.split()
        sentences = len(re.findall(r'[.!?]+', text))
        paragraphs = len([p for p in text.split('\n\n') if p.strip()])
        
        return {
            'word_count': len(words),
            'character_count': len(text),
            'sentence_count': max(1, sentences),
            'paragraph_count': max(1, paragraphs),
            'avg_words_per_sentence': len(words) / max(1, sentences),
            'avg_characters_per_word': len(text.replace(' ', '')) / max(1, len(words))
        }
    
    async def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached health analysis result."""
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            logger.warning(f"Failed to get cached health result", error=str(e))
        return None
    
    async def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """Cache health analysis result."""
        try:
            # Remove non-serializable fields
            cache_data = {k: v for k, v in result.items() 
                         if k not in ['cached', 'job_id']}
            await self.redis.set(cache_key, json.dumps(cache_data), ttl=self.cache_ttl)
        except Exception as e:
            logger.warning(f"Failed to cache health result", error=str(e))
    
    async def get_health_stats(self) -> Dict[str, Any]:
        """Get health-specific worker statistics."""
        base_stats = await self.get_worker_stats()
        
        # Add health-specific metrics
        health_stats = {
            **base_stats,
            'max_text_length': self.max_text_length,
            'cache_ttl': self.cache_ttl,
            'health_metrics': self.health_metrics,
            'health_metrics_count': len(self.health_metrics),  # 4 metrics
            'analysis_types': ['comprehensive', 'quick', 'focused'],
            'supports_batch': False,  # Health analysis requires individual attention
            'model_used': 'gpt-4.1-mini-2025-04-14'
        }
        
        return health_stats


# Standalone function to run the worker
async def run_health_worker():
    """Run a health worker instance."""
    worker = HealthWorker()
    await worker.initialize()
    await worker.start()


if __name__ == "__main__":
    import asyncio
    asyncio.run(run_health_worker())