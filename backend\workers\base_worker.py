"""
Base Worker for Revisionary

Purpose: Base class for all background workers
- Redis Streams queue processing
- Job lifecycle management
- Error handling and retries
- Performance monitoring

Features:
- Async queue processing loop
- Automatic job acknowledgment
- Graceful error handling
- Batch processing support
- Worker health monitoring
"""

import os
import json
import asyncio
import signal
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import structlog

from core.redis_service import get_redis
from core.llm_service import get_llm_service
from core.cost_tracker import get_cost_tracker
from core.settings import settings

logger = structlog.get_logger(__name__)


class BaseWorker(ABC):
    """Base class for all background workers."""
    
    def __init__(self, worker_type: str, worker_id: Optional[str] = None):
        """
        Initialize base worker.
        
        Args:
            worker_type: Type of worker (grammar, style, structure, content)
            worker_id: Unique ID for this worker instance
        """
        self.worker_type = worker_type
        self.worker_id = worker_id or f"{worker_type}_{os.getpid()}_{datetime.utcnow().strftime('%H%M%S')}"
        
        # Queue configuration
        self.queue_name = f"queue:{worker_type}"
        self.consumer_group = f"{worker_type}_workers"
        self.max_jobs_per_batch = int(os.getenv(f'BATCH_SIZE_{worker_type.upper()}', '1'))
        self.batch_timeout_ms = int(os.getenv('BATCH_TIMEOUT_MS', '500'))
        
        # Worker limits
        self.max_concurrent_jobs = int(os.getenv(f'MAX_WORKERS_{worker_type.upper()}', '1'))
        self.job_timeout = int(os.getenv('WORKER_JOB_TIMEOUT', '30'))  # seconds
        
        # State tracking
        self.is_running = False
        self.jobs_processed = 0
        self.jobs_failed = 0
        self.start_time = None
        self.pending_jobs = []
        
        # Services
        self.redis = None
        self.llm_service = None
        self.cost_tracker = None
        
        # Concurrency control
        self.semaphore = asyncio.Semaphore(self.max_concurrent_jobs)
        
        logger.info(
            f"Worker initialized",
            worker_type=worker_type,
            worker_id=self.worker_id,
            max_concurrent=self.max_concurrent_jobs,
            batch_size=self.max_jobs_per_batch
        )
    
    async def initialize(self):
        """Initialize async resources."""
        self.redis = await get_redis()
        self.llm_service = await get_llm_service()
        self.cost_tracker = await get_cost_tracker()
        
        # Create consumer group if it doesn't exist
        try:
            await self.redis.redis_client.xgroup_create(
                self.queue_name, 
                self.consumer_group, 
                id='0', 
                mkstream=True
            )
            logger.info(f"Created consumer group: {self.consumer_group}")
        except Exception as e:
            if "BUSYGROUP" not in str(e):
                logger.error(f"Failed to create consumer group: {e}")
                raise
        
        logger.info(f"Worker {self.worker_id} initialized")
    
    async def start(self):
        """Start the worker processing loop."""
        if self.is_running:
            logger.warning(f"Worker {self.worker_id} already running")
            return
        
        self.is_running = True
        self.start_time = datetime.utcnow()
        
        logger.info(f"Starting worker {self.worker_id}")
        
        # Set up signal handlers for graceful shutdown
        loop = asyncio.get_event_loop()
        for sig in [signal.SIGTERM, signal.SIGINT]:
            loop.add_signal_handler(sig, self._signal_handler)
        
        try:
            await self._processing_loop()
        except Exception as e:
            logger.error(f"Worker {self.worker_id} crashed", error=str(e), exc_info=True)
            raise
        finally:
            await self.stop()
    
    def _signal_handler(self):
        """Handle shutdown signals."""
        logger.info(f"Received shutdown signal for worker {self.worker_id}")
        self.is_running = False
    
    async def stop(self):
        """Stop the worker gracefully."""
        self.is_running = False
        
        # Wait for current jobs to complete (with timeout)
        timeout = 30  # 30 seconds
        start = datetime.utcnow()
        
        while self.pending_jobs and (datetime.utcnow() - start).total_seconds() < timeout:
            logger.info(f"Waiting for {len(self.pending_jobs)} jobs to complete...")
            await asyncio.sleep(1)
        
        # Log worker statistics
        uptime = (datetime.utcnow() - self.start_time).total_seconds() if self.start_time else 0
        success_rate = ((self.jobs_processed - self.jobs_failed) / max(self.jobs_processed, 1)) * 100
        
        logger.info(
            f"Worker {self.worker_id} stopped",
            uptime_seconds=uptime,
            jobs_processed=self.jobs_processed,
            jobs_failed=self.jobs_failed,
            success_rate=f"{success_rate:.1f}%"
        )
    
    async def _processing_loop(self):
        """Main processing loop for the worker."""
        logger.info(f"Worker {self.worker_id} entering processing loop")
        
        consecutive_empty_reads = 0
        max_empty_reads = 10  # Back off after 10 empty reads
        
        while self.is_running:
            try:
                # Check if we're in emergency mode
                if await self._check_emergency_mode():
                    logger.warning("Emergency mode active, worker pausing")
                    await asyncio.sleep(60)  # Wait 1 minute
                    continue
                
                # Read jobs from stream
                jobs = await self._read_jobs_from_queue()
                
                if jobs:
                    consecutive_empty_reads = 0
                    await self._process_jobs(jobs)
                else:
                    consecutive_empty_reads += 1
                    
                    # Backoff strategy for empty queues
                    if consecutive_empty_reads >= max_empty_reads:
                        sleep_time = min(30, consecutive_empty_reads - max_empty_reads + 1)
                        await asyncio.sleep(sleep_time)
                    else:
                        await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(
                    f"Error in processing loop for worker {self.worker_id}",
                    error=str(e),
                    exc_info=True
                )
                await asyncio.sleep(5)  # Wait before retrying
    
    async def _read_jobs_from_queue(self) -> List[Dict[str, Any]]:
        """Read jobs from Redis stream."""
        try:
            # Read from stream with consumer group
            result = await self.redis.redis_client.xreadgroup(
                groupname=self.consumer_group,
                consumername=self.worker_id,
                streams={self.queue_name: '>'},
                count=self.max_jobs_per_batch,
                block=1000  # Block for 1 second
            )
            
            jobs = []
            if result:
                for stream_name, messages in result:
                    for message_id, fields in messages:
                        try:
                            job_data = json.loads(fields.get('data', '{}'))
                            job_data['message_id'] = message_id
                            job_data['stream_name'] = stream_name
                            jobs.append(job_data)
                        except json.JSONDecodeError as e:
                            logger.error(f"Failed to parse job data", error=str(e))
                            # Acknowledge bad message to remove it
                            await self.redis.redis_client.xack(
                                self.queue_name, 
                                self.consumer_group, 
                                message_id
                            )
            
            return jobs
            
        except Exception as e:
            logger.error(f"Failed to read from queue", error=str(e))
            return []
    
    async def _process_jobs(self, jobs: List[Dict[str, Any]]):
        """Process a batch of jobs."""
        logger.debug(f"Processing {len(jobs)} jobs")
        
        # Check if we can batch process
        if len(jobs) > 1 and await self._can_batch_process(jobs):
            await self._process_job_batch(jobs)
        else:
            # Process jobs individually
            tasks = []
            for job in jobs:
                task = asyncio.create_task(self._process_single_job(job))
                tasks.append(task)
            
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _process_single_job(self, job: Dict[str, Any]):
        """Process a single job with error handling."""
        job_id = job.get('job_id', 'unknown')
        message_id = job.get('message_id')
        
        async with self.semaphore:
            start_time = datetime.utcnow()
            
            try:
                self.pending_jobs.append(job_id)
                
                logger.debug(f"Processing job {job_id}")
                
                # Call the concrete implementation
                result = await self.process_job(job)
                
                # Track success
                processing_time = (datetime.utcnow() - start_time).total_seconds()
                await self._track_job_success(job_id, processing_time)
                
                # Acknowledge job completion
                await self.redis.redis_client.xack(
                    self.queue_name, 
                    self.consumer_group, 
                    message_id
                )
                
                # Publish result if needed
                if result and job.get('response_channel'):
                    await self.redis.publish(job['response_channel'], json.dumps(result))
                
                logger.debug(
                    f"Job {job_id} completed successfully",
                    processing_time=processing_time
                )
                
            except Exception as e:
                # Track failure
                processing_time = (datetime.utcnow() - start_time).total_seconds()
                await self._track_job_failure(job_id, str(e), processing_time)
                
                logger.error(
                    f"Job {job_id} failed",
                    error=str(e),
                    processing_time=processing_time,
                    exc_info=True
                )
                
                # Still acknowledge to prevent reprocessing
                await self.redis.redis_client.xack(
                    self.queue_name, 
                    self.consumer_group, 
                    message_id
                )
                
            finally:
                # Remove from pending jobs
                if job_id in self.pending_jobs:
                    self.pending_jobs.remove(job_id)
    
    @abstractmethod
    async def process_job(self, job: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single job. Must be implemented by concrete workers.
        
        Args:
            job: Job data dictionary
            
        Returns:
            Dict with job results
        """
        pass
    
    async def _can_batch_process(self, jobs: List[Dict[str, Any]]) -> bool:
        """Check if jobs can be batch processed."""
        # Only batch if enabled and jobs are similar
        if not os.getenv('ENABLE_BATCH_PROCESSING', 'false').lower() == 'true':
            return False
        
        if len(jobs) < 2:
            return False
        
        # Check if all jobs are of the same type and can be batched
        first_job_type = jobs[0].get('job_type')
        return all(job.get('job_type') == first_job_type for job in jobs)
    
    async def _process_job_batch(self, jobs: List[Dict[str, Any]]):
        """Process jobs as a batch (to be overridden by concrete workers)."""
        # Default implementation: process individually
        for job in jobs:
            await self._process_single_job(job)
    
    async def _track_job_success(self, job_id: str, processing_time: float):
        """Track successful job completion."""
        self.jobs_processed += 1
        
        # Update Redis metrics
        await self.redis.incr(f"worker:stats:{self.worker_type}:processed")
        await self.redis.incrbyfloat(
            f"worker:stats:{self.worker_type}:total_time", 
            processing_time
        )
        
        # Track average processing time
        avg_key = f"worker:stats:{self.worker_type}:avg_time"
        current_avg = await self.redis.get(avg_key)
        if current_avg:
            new_avg = (float(current_avg) + processing_time) / 2
        else:
            new_avg = processing_time
        await self.redis.set(avg_key, str(new_avg))
    
    async def _track_job_failure(self, job_id: str, error: str, processing_time: float):
        """Track job failure."""
        self.jobs_failed += 1
        
        # Update Redis metrics
        await self.redis.incr(f"worker:stats:{self.worker_type}:failed")
        
        # Store error details
        error_key = f"worker:errors:{self.worker_type}:{datetime.utcnow().strftime('%Y%m%d%H')}"
        error_data = {
            'job_id': job_id,
            'error': error,
            'processing_time': processing_time,
            'timestamp': datetime.utcnow().isoformat(),
            'worker_id': self.worker_id
        }
        await self.redis.lpush(error_key, json.dumps(error_data))
        await self.redis.expire(error_key, 86400)  # Keep for 24 hours
    
    async def _check_emergency_mode(self) -> bool:
        """Check if emergency mode is active."""
        return await self.cost_tracker.is_emergency_mode_active()
    
    async def get_worker_stats(self) -> Dict[str, Any]:
        """Get worker statistics."""
        uptime = (datetime.utcnow() - self.start_time).total_seconds() if self.start_time else 0
        success_rate = ((self.jobs_processed - self.jobs_failed) / max(self.jobs_processed, 1)) * 100
        
        return {
            'worker_id': self.worker_id,
            'worker_type': self.worker_type,
            'is_running': self.is_running,
            'uptime_seconds': uptime,
            'jobs_processed': self.jobs_processed,
            'jobs_failed': self.jobs_failed,
            'success_rate': success_rate,
            'pending_jobs': len(self.pending_jobs),
            'start_time': self.start_time.isoformat() if self.start_time else None
        }