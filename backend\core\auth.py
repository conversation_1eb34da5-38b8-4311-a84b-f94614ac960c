"""
Authentication Service

Purpose: Handles all authentication and authorization functionality including:
- Firebase ID token validation and user extraction
- JWT access token generation and validation
- User profile management and session handling
- Authorization checks and permission validation

Responsibilities:
- Firebase Authentication integration and token validation
- JWT token lifecycle management (generation, validation, refresh)
- User profile creation and management in database
- Session management and token invalidation
- Authorization middleware and permission checks

Used by: API endpoints, authentication middleware, protected routes
Dependencies: Firebase Admin SDK, JWT libraries, database service
"""

from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import os
import jwt
from pydantic import BaseModel
import structlog
import firebase_admin
from firebase_admin import auth as firebase_auth, credentials

# Configure logging
logger = structlog.get_logger(__name__)

class UserClaims(BaseModel):
    """User claims extracted from JWT token."""
    user_id: str
    email: str
    display_name: Optional[str] = None
    subscription_tier: str = "free"
    subscription_status: str = "active"
    expires_at: datetime

class UserProfile(BaseModel):
    """User profile data."""
    id: str
    firebase_uid: str
    email: str
    display_name: Optional[str] = None
    avatar_url: Optional[str] = None
    subscription_tier: str = "free"
    subscription_status: str = "active"
    created_at: datetime
    last_active_at: Optional[datetime] = None
    preferences: Dict[str, Any] = {}

class AuthService:
    """
    Authentication service for handling Firebase integration and JWT tokens.
    """
    
    _initialized = False
    _mock_mode = False
    _firebase_app = None
    
    @classmethod
    async def initialize(cls):
        """Initialize the authentication service."""
        if cls._initialized:
            return
        
        cls._mock_mode = os.getenv("USE_MOCK_AUTH", "true").lower() == "true"
        
        if not cls._mock_mode:
            await cls._initialize_firebase()
        
        cls._initialized = True
        logger.info("AuthService initialized", mock_mode=cls._mock_mode)
    
    @classmethod
    async def _initialize_firebase(cls):
        """Initialize Firebase Admin SDK."""
        try:
            # Check if Firebase is already initialized
            if firebase_admin._apps:
                cls._firebase_app = firebase_admin.get_app()
                return
            
            # Get Firebase configuration from environment
            project_id = os.getenv("FIREBASE_PROJECT_ID")
            private_key_id = os.getenv("FIREBASE_PRIVATE_KEY_ID")
            private_key = os.getenv("FIREBASE_PRIVATE_KEY", "").replace("\\n", "\n")
            client_email = os.getenv("FIREBASE_CLIENT_EMAIL")
            client_id = os.getenv("FIREBASE_CLIENT_ID")
            
            if not all([project_id, private_key_id, private_key, client_email, client_id]):
                raise ValueError("Missing Firebase configuration environment variables")
            
            # Create Firebase credentials
            cred_dict = {
                "type": "service_account",
                "project_id": project_id,
                "private_key_id": private_key_id,
                "private_key": private_key,
                "client_email": client_email,
                "client_id": client_id,
                "auth_uri": os.getenv("FIREBASE_AUTH_URI", "https://accounts.google.com/o/oauth2/auth"),
                "token_uri": os.getenv("FIREBASE_TOKEN_URI", "https://oauth2.googleapis.com/token"),
            }
            
            cred = credentials.Certificate(cred_dict)
            cls._firebase_app = firebase_admin.initialize_app(cred)
            
            logger.info("Firebase Admin SDK initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize Firebase Admin SDK", error=str(e))
            # Fall back to mock mode if Firebase initialization fails
            cls._mock_mode = True
            logger.warning("Falling back to mock authentication mode")
    
    @classmethod
    def is_mock_mode(cls) -> bool:
        """Check if authentication is running in mock mode."""
        return cls._mock_mode
    
    @classmethod
    async def verify_firebase_token(cls, id_token: str) -> UserClaims:
        """
        Verify Firebase ID token and extract user claims.
        
        Args:
            id_token: Firebase ID token to verify
            
        Returns:
            UserClaims: Extracted user claims
            
        Raises:
            ValueError: If token is invalid
        """
        if cls._mock_mode:
            # Return mock user claims for development
            return UserClaims(
                user_id="14285842-26d6-4a48-8b7c-1ac76fa5c488",
                email="<EMAIL>",
                display_name="Mock User",
                subscription_tier="professional",
                subscription_status="active",
                expires_at=datetime.utcnow() + timedelta(hours=1)
            )
        
        try:
            # Verify the Firebase token
            decoded_token = firebase_auth.verify_id_token(id_token)
            
            return UserClaims(
                user_id=decoded_token["uid"],
                email=decoded_token.get("email", ""),
                display_name=decoded_token.get("name"),
                subscription_tier="free",  # Will be updated from database
                subscription_status="active",
                expires_at=datetime.utcnow() + timedelta(hours=1)
            )
            
        except Exception as e:
            logger.error("Firebase token verification failed", error=str(e))
            raise ValueError("Invalid Firebase token")
    
    @classmethod
    async def generate_access_token(cls, user_claims: UserClaims) -> str:
        """
        Generate JWT access token for API authentication.
        
        Args:
            user_claims: User claims to encode in token
            
        Returns:
            str: JWT access token
        """
        if cls._mock_mode:
            return f"mock_access_token_{user_claims.user_id}"
        
        # JWT payload
        payload = {
            "user_id": user_claims.user_id,
            "email": user_claims.email,
            "display_name": user_claims.display_name,
            "subscription_tier": user_claims.subscription_tier,
            "subscription_status": user_claims.subscription_status,
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(hours=int(os.getenv("JWT_EXPIRATION_HOURS", "24")))
        }
        
        # Generate JWT token
        secret_key = os.getenv("JWT_SECRET_KEY", "development-secret-key")
        algorithm = os.getenv("JWT_ALGORITHM", "HS256")
        
        return jwt.encode(payload, secret_key, algorithm=algorithm)
    
    @classmethod
    async def generate_refresh_token(cls, user_claims: UserClaims) -> str:
        """
        Generate refresh token for token renewal.
        
        Args:
            user_claims: User claims to encode in token
            
        Returns:
            str: Refresh token
        """
        if cls._mock_mode:
            return f"mock_refresh_token_{user_claims.user_id}"
        
        # Refresh token payload (longer expiration)
        payload = {
            "user_id": user_claims.user_id,
            "type": "refresh",
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(days=30)  # 30 days
        }
        
        secret_key = os.getenv("JWT_SECRET_KEY", "development-secret-key")
        algorithm = os.getenv("JWT_ALGORITHM", "HS256")
        
        return jwt.encode(payload, secret_key, algorithm=algorithm)
    
    @classmethod
    async def verify_token(cls, token: str) -> UserClaims:
        """
        Verify JWT access token and extract user claims.
        
        Args:
            token: JWT access token to verify
            
        Returns:
            UserClaims: Extracted user claims
            
        Raises:
            ValueError: If token is invalid or expired
        """
        if cls._mock_mode:
            # Return mock user claims for development
            return UserClaims(
                user_id="14285842-26d6-4a48-8b7c-1ac76fa5c488",
                email="<EMAIL>",
                display_name="Mock User",
                subscription_tier="professional",
                subscription_status="active",
                expires_at=datetime.utcnow() + timedelta(hours=1)
            )
        
        try:
            secret_key = os.getenv("JWT_SECRET_KEY", "development-secret-key")
            algorithm = os.getenv("JWT_ALGORITHM", "HS256")
            
            # Decode and verify JWT token
            payload = jwt.decode(token, secret_key, algorithms=[algorithm])
            
            return UserClaims(
                user_id=payload["user_id"],
                email=payload["email"],
                display_name=payload.get("display_name"),
                subscription_tier=payload.get("subscription_tier", "free"),
                subscription_status=payload.get("subscription_status", "active"),
                expires_at=datetime.fromtimestamp(payload["exp"])
            )
            
        except jwt.ExpiredSignatureError:
            raise ValueError("Token has expired")
        except jwt.InvalidTokenError as e:
            logger.error("JWT token validation failed", error=str(e))
            raise ValueError("Invalid token")
    
    @classmethod
    async def verify_refresh_token(cls, refresh_token: str) -> UserClaims:
        """
        Verify refresh token and extract user claims.
        
        Args:
            refresh_token: Refresh token to verify
            
        Returns:
            UserClaims: Extracted user claims
            
        Raises:
            ValueError: If refresh token is invalid or expired
        """
        if cls._mock_mode:
            return UserClaims(
                user_id="14285842-26d6-4a48-8b7c-1ac76fa5c488",
                email="<EMAIL>",
                display_name="Mock User",
                subscription_tier="professional",
                subscription_status="active",
                expires_at=datetime.utcnow() + timedelta(hours=1)
            )
        
        try:
            secret_key = os.getenv("JWT_SECRET_KEY", "development-secret-key")
            algorithm = os.getenv("JWT_ALGORITHM", "HS256")
            
            # Decode and verify refresh token
            payload = jwt.decode(refresh_token, secret_key, algorithms=[algorithm])
            
            if payload.get("type") != "refresh":
                raise ValueError("Invalid refresh token type")
            
            # Get user profile to create new claims
            user_profile = await cls.get_user_profile(payload["user_id"])
            
            return UserClaims(
                user_id=user_profile.id,
                email=user_profile.email,
                display_name=user_profile.display_name,
                subscription_tier=user_profile.subscription_tier,
                subscription_status=user_profile.subscription_status,
                expires_at=datetime.utcnow() + timedelta(hours=1)
            )
            
        except jwt.ExpiredSignatureError:
            raise ValueError("Refresh token has expired")
        except jwt.InvalidTokenError as e:
            logger.error("Refresh token validation failed", error=str(e))
            raise ValueError("Invalid refresh token")
    
    @classmethod
    async def get_or_create_user(cls, user_claims: UserClaims) -> UserProfile:
        """
        Get existing user profile or create new one.
        
        Args:
            user_claims: User claims from Firebase token
            
        Returns:
            UserProfile: User profile data
        """
        if cls._mock_mode:
            return UserProfile(
                id=user_claims.user_id,
                firebase_uid=user_claims.user_id,
                email=user_claims.email,
                display_name=user_claims.display_name,
                subscription_tier=user_claims.subscription_tier,
                subscription_status=user_claims.subscription_status,
                created_at=datetime.utcnow(),
                last_active_at=datetime.utcnow()
            )
        
        # TODO: Implement database operations
        # For now, return mock profile
        return UserProfile(
            id=user_claims.user_id,
            firebase_uid=user_claims.user_id,
            email=user_claims.email,
            display_name=user_claims.display_name,
            subscription_tier="free",
            subscription_status="active",
            created_at=datetime.utcnow(),
            last_active_at=datetime.utcnow()
        )
    
    @classmethod
    async def get_user_profile(cls, user_id: str) -> UserProfile:
        """
        Get user profile by user ID.
        
        Args:
            user_id: User ID to retrieve
            
        Returns:
            UserProfile: User profile data
        """
        if cls._mock_mode:
            return UserProfile(
                id=user_id,
                firebase_uid=user_id,
                email="<EMAIL>",
                display_name="Mock User",
                subscription_tier="professional",
                subscription_status="active",
                created_at=datetime.utcnow(),
                last_active_at=datetime.utcnow(),
                preferences={
                    "theme": "dark",
                    "notifications": True,
                    "language": "en"
                }
            )
        
        # TODO: Implement database lookup
        # For now, return mock profile
        return UserProfile(
            id=user_id,
            firebase_uid=user_id,
            email="<EMAIL>",
            display_name="User",
            subscription_tier="free",
            subscription_status="active",
            created_at=datetime.utcnow(),
            last_active_at=datetime.utcnow()
        )
    
    @classmethod
    async def invalidate_user_tokens(cls, user_id: str) -> bool:
        """
        Invalidate all tokens for a user (logout).
        
        Args:
            user_id: User ID to invalidate tokens for
            
        Returns:
            bool: Success status
        """
        if cls._mock_mode:
            logger.info("Mock token invalidation", user_id=user_id)
            return True
        
        # TODO: Implement token blacklisting
        # For now, just return success
        logger.info("Token invalidation", user_id=user_id)
        return True