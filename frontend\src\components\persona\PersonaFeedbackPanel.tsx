import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  UserGroupIcon,
  SparklesIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  HeartIcon,
  EyeIcon,
  ChartBarIcon,
  LightBulbIcon,
  ArrowPathIcon,
  AdjustmentsHorizontalIcon,
  FaceSmileIcon,
  FaceFrownIcon,
  PlusIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import {
  PersonaFeedback,
  PersonaInsights,
  PersonaReaction,
  ReaderPersona,
} from '../../types/persona';
import { usePersonaStore, usePersonaSelectors } from '../../stores/personaStore';
import PersonaSelector from './PersonaSelector';

interface PersonaFeedbackPanelProps {
  documentId: string;
  documentContent: string;
  isOpen: boolean;
  onClose: () => void;
  onGenerateFeedback?: () => void;
}

const PersonaFeedbackPanel: React.FC<PersonaFeedbackPanelProps> = ({
  documentId,
  documentContent,
  isOpen,
  onClose,
  onGenerateFeedback,
}) => {
  const {
    selectedPersonas,
    generateFeedback,
    generateInsights,
    getFeedbackForDocument,
    getPersona,
    insights,
  } = usePersonaStore();
  
  const selectors = usePersonaSelectors();
  const [activeTab, setActiveTab] = useState<'feedback' | 'insights' | 'consensus'>('feedback');
  const [isGenerating, setIsGenerating] = useState(false);
  const [feedbackData, setFeedbackData] = useState<PersonaFeedback[]>([]);
  const [showPersonaSelector, setShowPersonaSelector] = useState(false);

  // Get feedback for this document
  useEffect(() => {
    const feedback = getFeedbackForDocument(documentId);
    setFeedbackData(feedback);
  }, [documentId, getFeedbackForDocument]);

  const handleGenerateFeedback = async () => {
    if (selectedPersonas.length === 0) {
      alert('Please select at least one persona first');
      return;
    }

    setIsGenerating(true);
    try {
      const feedback = await generateFeedback(documentId, documentContent);
      setFeedbackData(feedback);
      
      // Also generate insights
      await generateInsights(documentId);
      
      onGenerateFeedback?.();
    } catch (error) {
      console.error('Failed to generate feedback:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const getReactionIcon = (reaction: PersonaReaction) => {
    switch (reaction) {
      case 'love': return '😍';
      case 'like': return '😊';
      case 'neutral': return '😐';
      case 'confused': return '😕';
      case 'bored': return '😴';
      case 'annoyed': return '😤';
      case 'offended': return '😠';
      case 'excited': return '🤩';
      case 'moved': return '🥺';
      case 'inspired': return '✨';
      default: return '😐';
    }
  };

  const getReactionColor = (reaction: PersonaReaction) => {
    switch (reaction) {
      case 'love':
      case 'like':
      case 'excited':
      case 'inspired':
        return 'text-green-600 bg-green-100';
      case 'neutral':
        return 'text-yellow-600 bg-yellow-100';
      case 'confused':
      case 'bored':
        return 'text-orange-600 bg-orange-100';
      case 'annoyed':
      case 'offended':
        return 'text-red-600 bg-red-100';
      case 'moved':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getEngagementLevel = (score: number) => {
    if (score >= 85) return { label: 'Highly Engaged', color: 'text-green-600' };
    if (score >= 70) return { label: 'Engaged', color: 'text-blue-600' };
    if (score >= 55) return { label: 'Moderately Engaged', color: 'text-yellow-600' };
    return { label: 'Low Engagement', color: 'text-red-600' };
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black/50 z-[9999] flex items-center justify-center p-4">
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="p-6 border-b border-slate-200 bg-gradient-to-r from-purple-50 to-pink-50">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-slate-900 flex items-center">
                  <UserGroupIcon className="w-6 h-6 mr-2 text-purple-600" />
                  Reader Persona Feedback
                </h2>
                <p className="text-slate-600 mt-1">
                  See how different audiences react to your content
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowPersonaSelector(!showPersonaSelector)}
                  className="flex items-center px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors font-medium"
                >
                  <UserGroupIcon className="w-5 h-5 mr-2" />
                  {showPersonaSelector ? 'View Feedback' : 'Manage Personas'}
                </button>
                {feedbackData.length === 0 && !showPersonaSelector && (
                  <button
                    onClick={handleGenerateFeedback}
                    disabled={isGenerating || selectedPersonas.length === 0}
                    className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-xl hover:bg-purple-700 disabled:opacity-50 transition-colors"
                  >
                    {isGenerating ? (
                      <ArrowPathIcon className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <SparklesIcon className="w-4 h-4 mr-2" />
                    )}
                    {isGenerating ? 'Generating...' : 'Generate Feedback'}
                  </button>
                )}
                <button
                  onClick={onClose}
                  className="p-2 text-slate-600 hover:text-slate-900 transition-colors"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Tabs */}
            {!showPersonaSelector && feedbackData.length > 0 && (
              <div className="flex space-x-1 mt-4 bg-white/60 rounded-xl p-1">
              {[
                { id: 'feedback', label: 'Individual Feedback', icon: UserGroupIcon },
                { id: 'insights', label: 'Audience Insights', icon: LightBulbIcon },
                { id: 'consensus', label: 'Consensus Analysis', icon: ChartBarIcon, tooltip: 'Analyzes feedback from multiple personas to find common patterns, agreements, and differences in how various audiences react to your content' },
              ].map((tab) => {
                const IconComponent = tab.icon;
                return (
                  <div key={tab.id} className="relative group">
                    <button
                      onClick={() => setActiveTab(tab.id as any)}
                      className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                        activeTab === tab.id
                          ? 'bg-white text-purple-700 shadow-sm'
                          : 'text-slate-600 hover:text-slate-900'
                      }`}
                    >
                      <IconComponent className="w-4 h-4 mr-2" />
                      {tab.label}
                      {tab.tooltip && (
                        <InformationCircleIcon className="w-3 h-3 ml-1 text-slate-400" />
                      )}
                    </button>
                    {tab.tooltip && (
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-slate-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap max-w-xs z-50">
                        {tab.tooltip}
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 -mt-1 w-0 h-0 border-4 border-transparent border-t-slate-900" />
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
            )}
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
            {/* Persona Selector */}
            {showPersonaSelector ? (
              <PersonaSelector onClose={() => setShowPersonaSelector(false)} />
            ) : (
              <>
                {/* No Feedback State */}
                {feedbackData.length === 0 && !isGenerating && (
              <div className="text-center py-12">
                <UserGroupIcon className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-slate-900 mb-2">No Feedback Yet</h3>
                <p className="text-slate-600 mb-6">
                  {selectedPersonas.length === 0
                    ? 'Select some personas first, then generate feedback to see how different audiences react to your content.'
                    : `Generate feedback from ${selectedPersonas.length} selected persona${selectedPersonas.length > 1 ? 's' : ''}.`
                  }
                </p>
                {selectedPersonas.length > 0 && (
                  <button
                    onClick={handleGenerateFeedback}
                    className="px-6 py-3 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-colors"
                  >
                    Generate Feedback
                  </button>
                )}
              </div>
            )}

            {/* Loading State */}
            {isGenerating && (
              <div className="text-center py-12">
                <ArrowPathIcon className="w-16 h-16 text-purple-600 mx-auto mb-4 animate-spin" />
                <h3 className="text-lg font-medium text-slate-900 mb-2">Generating Feedback...</h3>
                <p className="text-slate-600">
                  Our AI personas are reading and analyzing your content...
                </p>
              </div>
            )}

            {/* Feedback Content */}
            {feedbackData.length > 0 && (
              <AnimatePresence mode="wait">
                {activeTab === 'feedback' && (
                  <IndividualFeedbackTab
                    feedbackData={feedbackData}
                    getPersona={getPersona}
                    getReactionIcon={getReactionIcon}
                    getReactionColor={getReactionColor}
                    getEngagementLevel={getEngagementLevel}
                  />
                )}
                
                {activeTab === 'insights' && (
                  <AudienceInsightsTab
                    insights={insights}
                    feedbackData={feedbackData}
                    getPersona={getPersona}
                  />
                )}
                
                {activeTab === 'consensus' && (
                  <ConsensusAnalysisTab
                    feedbackData={feedbackData}
                    getPersona={getPersona}
                    getReactionIcon={getReactionIcon}
                  />
                )}
              </AnimatePresence>
            )}
              </>
            )}
          </div>

          {/* Footer */}
          {feedbackData.length > 0 && !showPersonaSelector && (
            <div className="p-6 border-t border-slate-200 bg-slate-50">
              <div className="flex items-center justify-between">
                <div className="text-sm text-slate-600">
                  Feedback from {feedbackData.length} persona{feedbackData.length > 1 ? 's' : ''}
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={handleGenerateFeedback}
                    disabled={isGenerating}
                    className="flex items-center px-4 py-2 text-purple-600 hover:text-purple-700 transition-colors"
                  >
                    <ArrowPathIcon className="w-4 h-4 mr-1" />
                    Regenerate
                  </button>
                  <button
                    onClick={onClose}
                    className="px-4 py-2 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

// Individual Feedback Tab
const IndividualFeedbackTab: React.FC<{
  feedbackData: PersonaFeedback[];
  getPersona: (id: string) => ReaderPersona | undefined;
  getReactionIcon: (reaction: PersonaReaction) => string;
  getReactionColor: (reaction: PersonaReaction) => string;
  getEngagementLevel: (score: number) => { label: string; color: string };
}> = ({ feedbackData, getPersona, getReactionIcon, getReactionColor, getEngagementLevel }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      {feedbackData.map((feedback, index) => {
        const persona = getPersona(feedback.personaId);
        if (!persona) return null;

        const engagement = getEngagementLevel(feedback.engagementScore);

        return (
          <motion.div
            key={feedback.personaId}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white border border-slate-200 rounded-2xl p-6 hover:shadow-md transition-shadow"
          >
            {/* Persona Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className={`w-12 h-12 rounded-full bg-${persona.color}-100 flex items-center justify-center`}>
                  <span className="text-2xl">{getReactionIcon(feedback.overallReaction)}</span>
                </div>
                <div>
                  <h3 className="font-semibold text-slate-900">{persona.name}</h3>
                  <p className="text-sm text-slate-600">{persona.description}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className={`px-2 py-1 text-xs rounded-full ${getReactionColor(feedback.overallReaction)}`}>
                      {feedback.overallReaction}
                    </span>
                    <span className="text-xs text-slate-500">•</span>
                    <span className={`text-xs font-medium ${engagement.color}`}>
                      {engagement.label}
                    </span>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-slate-500">Confidence</div>
                <div className="text-lg font-bold text-slate-900">{feedback.confidence}%</div>
              </div>
            </div>

            {/* Scores */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="p-3 bg-blue-50 rounded-xl">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-blue-700">Engagement</span>
                  <EyeIcon className="w-4 h-4 text-blue-600" />
                </div>
                <div className="text-2xl font-bold text-blue-900 mt-1">
                  {feedback.engagementScore}/100
                </div>
              </div>
              <div className="p-3 bg-green-50 rounded-xl">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-green-700">Comprehension</span>
                  <CheckCircleIcon className="w-4 h-4 text-green-600" />
                </div>
                <div className="text-2xl font-bold text-green-900 mt-1">
                  {feedback.comprehensionScore}/100
                </div>
              </div>
            </div>

            {/* Feedback Comments */}
            <div className="space-y-3">
              {feedback.feedback.map((comment) => (
                <div key={comment.id} className="p-3 bg-slate-50 rounded-xl">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-slate-900">{comment.title}</h4>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      comment.severity === 'high' ? 'bg-red-100 text-red-700' :
                      comment.severity === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-green-100 text-green-700'
                    }`}>
                      {comment.severity}
                    </span>
                  </div>
                  <p className="text-sm text-slate-600">{comment.message}</p>
                  {comment.suggestion && (
                    <div className="mt-2 p-2 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-800">
                        <strong>Suggestion:</strong> {comment.suggestion}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Predictions */}
            <div className="mt-4 p-3 bg-gray-50 rounded-xl">
              <h4 className="font-medium text-slate-900 mb-2">Predictions</h4>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <span className="text-slate-600">Will finish reading:</span>
                  <span className="ml-2 font-medium">{feedback.predictions.willFinishReading}%</span>
                </div>
                <div>
                  <span className="text-slate-600">Would recommend:</span>
                  <span className="ml-2 font-medium">{feedback.predictions.wouldRecommend}%</span>
                </div>
              </div>
            </div>
          </motion.div>
        );
      })}
    </motion.div>
  );
};

// Audience Insights Tab
const AudienceInsightsTab: React.FC<{
  insights: PersonaInsights | null;
  feedbackData: PersonaFeedback[];
  getPersona: (id: string) => ReaderPersona | undefined;
}> = ({ insights, feedbackData, getPersona }) => {
  const averageEngagement = feedbackData.length > 0
    ? feedbackData.reduce((acc, f) => acc + f.engagementScore, 0) / feedbackData.length
    : 0;

  const averageComprehension = feedbackData.length > 0
    ? feedbackData.reduce((acc, f) => acc + f.comprehensionScore, 0) / feedbackData.length
    : 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      {/* Overall Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="p-6 bg-gradient-to-r from-blue-50 to-blue-100 rounded-2xl">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-blue-900">Average Engagement</h3>
            <EyeIcon className="w-5 h-5 text-blue-600" />
          </div>
          <div className="text-3xl font-bold text-blue-900">
            {Math.round(averageEngagement)}/100
          </div>
          <p className="text-sm text-blue-700 mt-2">
            Overall audience engagement level
          </p>
        </div>

        <div className="p-6 bg-gradient-to-r from-green-50 to-green-100 rounded-2xl">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-green-900">Comprehension</h3>
            <CheckCircleIcon className="w-5 h-5 text-green-600" />
          </div>
          <div className="text-3xl font-bold text-green-900">
            {Math.round(averageComprehension)}/100
          </div>
          <p className="text-sm text-green-700 mt-2">
            How well audiences understand
          </p>
        </div>

        <div className="p-6 bg-gradient-to-r from-purple-50 to-purple-100 rounded-2xl">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-purple-900">Cross-Appeal</h3>
            <UserGroupIcon className="w-5 h-5 text-purple-600" />
          </div>
          <div className="text-3xl font-bold text-purple-900">
            {insights?.audienceCompatibility.crossAudienceAppeal || 75}/100
          </div>
          <p className="text-sm text-purple-700 mt-2">
            Appeal across different audiences
          </p>
        </div>
      </div>

      {/* Audience Breakdown */}
      <div className="bg-white border border-slate-200 rounded-2xl p-6">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Audience Breakdown</h3>
        <div className="space-y-4">
          {feedbackData.map((feedback) => {
            const persona = getPersona(feedback.personaId);
            if (!persona) return null;

            return (
              <div key={feedback.personaId} className="flex items-center justify-between p-3 bg-slate-50 rounded-xl">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full bg-${persona.color}-100 flex items-center justify-center`}>
                    <span className="text-xs font-bold text-${persona.color}-700">
                      {persona.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <div className="font-medium text-slate-900">{persona.name}</div>
                    <div className="text-sm text-slate-600">{persona.category}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-slate-900">
                    {feedback.engagementScore}/100
                  </div>
                  <div className="text-xs text-slate-500">engagement</div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Recommendations */}
      <div className="bg-white border border-slate-200 rounded-2xl p-6">
        <h3 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
          <LightBulbIcon className="w-5 h-5 mr-2 text-yellow-500" />
          Optimization Recommendations
        </h3>
        <div className="space-y-3">
          {/* Mock recommendations based on feedback */}
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-xl">
            <div className="flex items-start">
              <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600 mr-3 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-800">Simplify Complex Sentences</h4>
                <p className="text-sm text-yellow-700 mt-1">
                  Academic personas are engaged, but general readers find some sections difficult to follow.
                </p>
              </div>
            </div>
          </div>
          
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-xl">
            <div className="flex items-start">
              <LightBulbIcon className="w-5 h-5 text-blue-600 mr-3 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-800">Add More Examples</h4>
                <p className="text-sm text-blue-700 mt-1">
                  Professional personas want concrete examples to better understand abstract concepts.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Consensus Analysis Tab
const ConsensusAnalysisTab: React.FC<{
  feedbackData: PersonaFeedback[];
  getPersona: (id: string) => ReaderPersona | undefined;
  getReactionIcon: (reaction: PersonaReaction) => string;
}> = ({ feedbackData, getPersona, getReactionIcon }) => {
  // Calculate consensus metrics
  const reactions = feedbackData.map(f => f.overallReaction);
  const reactionCounts = reactions.reduce((acc, reaction) => {
    acc[reaction] = (acc[reaction] || 0) + 1;
    return acc;
  }, {} as Record<PersonaReaction, number>);

  const mostCommonReaction = Object.entries(reactionCounts)
    .sort(([,a], [,b]) => b - a)[0];

  const consensusLevel = mostCommonReaction 
    ? (reactionCounts[mostCommonReaction[0] as PersonaReaction] / feedbackData.length) * 100
    : 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      {/* Consensus Overview */}
      <div className="bg-white border border-slate-200 rounded-2xl p-6">
        <div className="flex items-start justify-between mb-4">
          <h3 className="text-lg font-semibold text-slate-900">Consensus Overview</h3>
          <div className="group relative">
            <InformationCircleIcon className="w-5 h-5 text-slate-400 hover:text-slate-600 cursor-help" />
            <div className="absolute right-0 top-full mt-2 w-64 p-3 bg-slate-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
              Consensus analysis identifies patterns across all persona feedback to help you understand which elements work universally and which are audience-specific.
              <div className="absolute top-0 right-2 -mt-1 w-0 h-0 border-4 border-transparent border-b-slate-900" />
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="text-center">
            <div className="text-6xl mb-2">
              {mostCommonReaction ? getReactionIcon(mostCommonReaction[0] as PersonaReaction) : '😐'}
            </div>
            <div className="text-lg font-semibold text-slate-900">
              Most Common Reaction
            </div>
            <div className="text-sm text-slate-600">
              {mostCommonReaction ? mostCommonReaction[0] : 'neutral'}
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-4xl font-bold text-blue-600 mb-2">
              {Math.round(consensusLevel)}%
            </div>
            <div className="text-lg font-semibold text-slate-900">
              Consensus Level
            </div>
            <div className="text-sm text-slate-600">
              Agreement among personas
            </div>
          </div>
        </div>
      </div>

      {/* Reaction Distribution */}
      <div className="bg-white border border-slate-200 rounded-2xl p-6">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Reaction Distribution</h3>
        <div className="space-y-3">
          {Object.entries(reactionCounts)
            .sort(([,a], [,b]) => b - a)
            .map(([reaction, count]) => {
              const percentage = (count / feedbackData.length) * 100;
              return (
                <div key={reaction} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getReactionIcon(reaction as PersonaReaction)}</span>
                    <span className="font-medium text-slate-900 capitalize">{reaction}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-32 bg-slate-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-slate-600 w-12 text-right">
                      {count}/{feedbackData.length}
                    </span>
                  </div>
                </div>
              );
            })}
        </div>
      </div>

      {/* Areas of Agreement/Disagreement */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-green-50 border border-green-200 rounded-2xl p-6">
          <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center">
            <CheckCircleIcon className="w-5 h-5 mr-2" />
            Universal Strengths
          </h3>
          <div className="space-y-2 text-sm">
            <div className="p-2 bg-green-100 rounded-lg">
              <span className="text-green-800">Clear writing style appreciated by all personas</span>
            </div>
            <div className="p-2 bg-green-100 rounded-lg">
              <span className="text-green-800">Strong opening that engages most readers</span>
            </div>
          </div>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-2xl p-6">
          <h3 className="text-lg font-semibold text-red-900 mb-4 flex items-center">
            <ExclamationTriangleIcon className="w-5 h-5 mr-2" />
            Areas for Improvement
          </h3>
          <div className="space-y-2 text-sm">
            <div className="p-2 bg-red-100 rounded-lg">
              <span className="text-red-800">Technical complexity confuses general readers</span>
            </div>
            <div className="p-2 bg-red-100 rounded-lg">
              <span className="text-red-800">Pacing issues noted by multiple personas</span>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default PersonaFeedbackPanel;