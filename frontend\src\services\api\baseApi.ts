/**
 * Base API Service Configuration
 * 
 * Provides centralized HTTP client configuration with:
 * - Authentication token management
 * - Request/response interceptors
 * - Error handling
 * - Correlation ID tracking
 * - Retry logic for failed requests
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { auth } from '@/services/firebase';
import { config } from '@/config';

// API Configuration from centralized config
const API_BASE_URL = `${config.api.baseUrl}/api/v1`;
const API_TIMEOUT = config.api.timeout;

// Request retry configuration
const MAX_RETRIES = config.api.retryAttempts;
const RETRY_DELAY = config.api.retryDelay;

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  meta?: {
    request_id: string;
    timestamp: string;
  };
}

export interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    request_id: string;
    timestamp: string;
  };
}

class BaseApiService {
  private axiosInstance: AxiosInstance;
  private retryCount = new Map<string, number>();
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private tokenExpiresAt: number | null = null;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: API_BASE_URL,
      timeout: API_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor - Add auth token and correlation ID
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        try {
          // Skip auth for token exchange endpoint
          if (config.url?.includes('/auth/token')) {
            return this.addRequestMetadata(config);
          }

          // Ensure we have a valid access token
          await this.ensureValidAccessToken();
          
          // Add access token if available
          if (this.accessToken) {
            config.headers.Authorization = `Bearer ${this.accessToken}`;
          }

          return this.addRequestMetadata(config);
        } catch (error) {
          console.error('[API Request Error]', error);
          return Promise.reject(error);
        }
      },
      (error) => {
        console.error('[API Request Interceptor Error]', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor - Handle responses and errors
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => {
        const correlationId = response.headers['x-correlation-id'];
        const processingTime = response.headers['x-process-time'];

        // Safe logging - only log metadata, not sensitive data
        console.log(`[API Response] ${response.status} ${response.config.url}`, {
          correlationId,
          processingTime: processingTime ? `${processingTime}ms` : 'unknown',
          status: response.status,
          size: JSON.stringify(response.data || {}).length,
        });

        return response;
      },
      async (error: AxiosError) => {
        const correlationId = error.response?.headers?.['x-correlation-id'];
        
        console.error(`[API Error] ${error.response?.status || 'Network'} ${error.config?.url}`, {
          correlationId,
          error: error.response?.data || error.message,
          config: error.config,
        });

        // Handle token refresh for 401 errors
        if (error.response?.status === 401) {
          const refreshed = await this.handleTokenRefresh();
          if (refreshed && error.config) {
            return this.axiosInstance.request(error.config);
          }
        }

        // Handle retry logic for network errors and 5xx errors
        if (this.shouldRetry(error)) {
          return this.retryRequest(error);
        }

        return Promise.reject(this.formatError(error));
      }
    );
  }

  private generateCorrelationId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private addRequestMetadata(config: any): any {
    // Add correlation ID for request tracking
    const correlationId = this.generateCorrelationId();
    config.headers['X-Correlation-ID'] = correlationId;

    // Add timestamp
    config.headers['X-Request-Time'] = new Date().toISOString();

    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
      correlationId,
      hasAuth: !!config.headers.Authorization,
    });

    return config;
  }

  private async ensureValidAccessToken(): Promise<void> {
    // In dev mode, always use mock token
    if (config.dev.enabled) {
      if (!this.accessToken) {
        this.accessToken = 'dev-mock-token';
        this.tokenExpiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
      }
      return;
    }

    // Check if token is expired or will expire soon (5 minutes buffer)
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000;
    
    if (!this.accessToken || !this.tokenExpiresAt || (this.tokenExpiresAt - now) < fiveMinutes) {
      await this.refreshAccessToken();
    }
  }

  private async refreshAccessToken(): Promise<void> {
    try {
      if (this.refreshToken) {
        // Try to refresh using refresh token
        const response = await this.axiosInstance.post('/auth/refresh', {
          refresh_token: this.refreshToken
        });

        if (response.data.success) {
          this.accessToken = response.data.data.access_token;
          this.tokenExpiresAt = Date.now() + (response.data.data.expires_in * 1000);
          return;
        }
      }

      // If refresh fails or no refresh token, exchange Firebase token
      await this.exchangeFirebaseToken();
    } catch (error) {
      console.error('[Token Refresh Error]', error);
      // Clear tokens and let the request fail for proper error handling
      this.clearTokens();
      throw error;
    }
  }

  private async exchangeFirebaseToken(): Promise<void> {
    // Check if we're in dev mode
    if (config.dev.enabled) {
      // In dev mode, use a mock token
      this.accessToken = 'dev-mock-token';
      this.tokenExpiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
      return;
    }

    if (!auth) {
      throw new Error('Firebase auth not initialized');
    }
    
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('No authenticated Firebase user');
    }

    const firebaseToken = await user.getIdToken();
    const response = await this.axiosInstance.post('/auth/token', {
      firebase_token: firebaseToken
    });

    if (response.data.success) {
      const tokenData = response.data.data;
      this.accessToken = tokenData.access_token;
      this.refreshToken = tokenData.refresh_token;
      this.tokenExpiresAt = Date.now() + (tokenData.expires_in * 1000);
    } else {
      throw new Error('Token exchange failed');
    }
  }

  private clearTokens(): void {
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiresAt = null;
  }

  private async handleTokenRefresh(): Promise<boolean> {
    try {
      await this.refreshAccessToken();
      return true;
    } catch (error) {
      console.error('[Token Refresh Error]', error);
      return false;
    }
  }

  private shouldRetry(error: AxiosError): boolean {
    // Don't retry client errors (4xx) except 401 and 429
    if (error.response?.status && error.response.status >= 400 && error.response.status < 500) {
      return error.response.status === 429; // Rate limit
    }

    // Retry network errors and server errors (5xx)
    return !error.response || (error.response.status >= 500);
  }

  private async retryRequest(error: AxiosError): Promise<any> {
    const requestId = error.config?.headers?.['X-Correlation-ID'] as string;
    const currentRetries = this.retryCount.get(requestId) || 0;

    if (currentRetries >= MAX_RETRIES) {
      this.retryCount.delete(requestId);
      return Promise.reject(this.formatError(error));
    }

    this.retryCount.set(requestId, currentRetries + 1);

    // Calculate delay with exponential backoff
    const delay = RETRY_DELAY * Math.pow(2, currentRetries);
    
    console.log(`[API Retry] Attempt ${currentRetries + 1}/${MAX_RETRIES} for ${error.config?.url} in ${delay}ms`);

    await new Promise(resolve => setTimeout(resolve, delay));

    try {
      const response = await this.axiosInstance.request(error.config!);
      this.retryCount.delete(requestId);
      return response;
    } catch (retryError) {
      return this.retryRequest(retryError as AxiosError);
    }
  }

  private formatError(error: AxiosError): ApiError {
    if (error.response?.data) {
      // Backend returned structured error
      return error.response.data as ApiError;
    }

    // Network or other error
    return {
      success: false,
      error: {
        code: error.code || 'NETWORK_ERROR',
        message: error.message || 'Network request failed',
        details: {
          status: error.response?.status,
          statusText: error.response?.statusText,
        },
      },
    };
  }

  // Public API methods
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.get<ApiResponse<T>>(url, config);
    return response.data;
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.post<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.put<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.patch<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.axiosInstance.delete<ApiResponse<T>>(url, config);
    return response.data;
  }

  // Upload file with progress tracking
  async upload<T = any>(
    url: string, 
    file: File, 
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    const config: AxiosRequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    };

    const response = await this.axiosInstance.post<ApiResponse<T>>(url, formData, config);
    return response.data;
  }

  // Get the axios instance for custom requests
  getAxiosInstance(): AxiosInstance {
    return this.axiosInstance;
  }
}

// Export singleton instance
export const baseApi = new BaseApiService();
export default baseApi;