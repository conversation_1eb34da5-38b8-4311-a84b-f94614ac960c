import React from 'react';
import { motion } from 'framer-motion';
import { useAnalyticsSelectors } from '../../stores/analyticsStore';

interface WritingHeatmapProps {
  className?: string;
}

interface DayData {
  date: Date;
  wordsWritten: number;
  level: 0 | 1 | 2 | 3 | 4; // 0 = no activity, 4 = high activity
}

const WritingHeatmap: React.FC<WritingHeatmapProps> = ({ className = '' }) => {
  const analyticsSelectors = useAnalyticsSelectors();
  
  // Generate last 365 days of data
  const generateHeatmapData = (): DayData[] => {
    const data: DayData[] = [];
    const today = new Date();
    
    for (let i = 364; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      // Get daily stats for this date
      const dayStats = analyticsSelectors.getDayStats(date);
      const wordsWritten = dayStats.wordsWritten;
      
      // Calculate activity level based on words written
      let level: 0 | 1 | 2 | 3 | 4 = 0;
      if (wordsWritten > 0) level = 1;
      if (wordsWritten > 250) level = 2;
      if (wordsWritten > 500) level = 3;
      if (wordsWritten > 1000) level = 4;
      
      data.push({ date, wordsWritten, level });
    }
    
    return data;
  };

  const heatmapData = generateHeatmapData();
  
  // Group data into weeks (7 days each)
  const weeks: DayData[][] = [];
  for (let i = 0; i < heatmapData.length; i += 7) {
    weeks.push(heatmapData.slice(i, i + 7));
  }

  const getColorClass = (level: number): string => {
    switch (level) {
      case 0: return 'bg-slate-100';
      case 1: return 'bg-purple-200';
      case 2: return 'bg-purple-300';
      case 3: return 'bg-purple-400';
      case 4: return 'bg-purple-500';
      default: return 'bg-slate-100';
    }
  };

  const getTooltipText = (day: DayData): string => {
    const dateStr = day.date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
    
    if (day.wordsWritten === 0) {
      return `${dateStr}: No writing activity`;
    }
    
    return `${dateStr}: ${day.wordsWritten.toLocaleString()} words written`;
  };

  const monthLabels = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ];

  const weekdayLabels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className={`bg-white rounded-2xl border border-slate-200 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-slate-900">Writing Activity</h3>
        <div className="flex items-center space-x-2 text-sm text-slate-600">
          <span>Less</span>
          <div className="flex space-x-1">
            {[0, 1, 2, 3, 4].map(level => (
              <div
                key={level}
                className={`w-3 h-3 rounded-sm ${getColorClass(level)}`}
              />
            ))}
          </div>
          <span>More</span>
        </div>
      </div>

      <div className="overflow-x-auto">
        <div className="inline-flex flex-col min-w-full">
          {/* Month labels */}
          <div className="flex mb-2">
            <div className="w-8" /> {/* Space for weekday labels */}
            <div className="flex-1 flex justify-between text-xs text-slate-500">
              {monthLabels.map((month, i) => (
                <span key={month} className="text-center" style={{ width: `${100/12}%` }}>
                  {month}
                </span>
              ))}
            </div>
          </div>

          {/* Heatmap grid */}
          <div className="flex">
            {/* Weekday labels */}
            <div className="flex flex-col justify-around w-8 mr-2">
              {weekdayLabels.map((day, i) => (
                <span key={day} className="text-xs text-slate-500 h-3 flex items-center">
                  {i % 2 === 1 ? day : ''}
                </span>
              ))}
            </div>

            {/* Grid */}
            <div className="flex space-x-1">
              {weeks.map((week, weekIndex) => (
                <div key={weekIndex} className="flex flex-col space-y-1">
                  {week.map((day, dayIndex) => (
                    <motion.div
                      key={`${weekIndex}-${dayIndex}`}
                      className={`w-3 h-3 rounded-sm cursor-pointer ${getColorClass(day.level)} hover:ring-2 hover:ring-purple-300 transition-all duration-200`}
                      title={getTooltipText(day)}
                      whileHover={{ scale: 1.2 }}
                      whileTap={{ scale: 0.9 }}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: (weekIndex * 7 + dayIndex) * 0.002 }}
                    />
                  ))}
                </div>
              ))}
            </div>
          </div>

          {/* Legend */}
          <div className="mt-4 text-xs text-slate-600">
            <p>
              Each square represents a day. Color intensity reflects writing activity.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WritingHeatmap;