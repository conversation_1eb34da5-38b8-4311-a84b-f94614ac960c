# Revisionary Backend Environment Configuration
# Copy this file to .env and update with your actual values

# Environment
ENV=development
DEBUG=true
LOG_LEVEL=INFO

# Database Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-role-key
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.your-project.supabase.co:6543/postgres

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your-redis-password

# Firebase Authentication
FIREBASE_PROJECT_ID=revisionary-prod
FIREBASE_CREDENTIALS_PATH=/path/to/firebase-credentials.json
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Model Configuration - Grammar Agent (GPT-4.1-nano for cost efficiency)
GRAMMAR_MODEL_PROVIDER=openai
GRAMMAR_MODEL_NAME=gpt-4.1-nano-2025-04-14
GRAMMAR_MODEL_MAX_TOKENS=200
GRAMMAR_MODEL_COST_PER_1K_INPUT=0.0001
GRAMMAR_MODEL_COST_PER_1K_OUTPUT=0.0004

# Model Configuration - Style Agent (GPT-4.1-mini for balanced cost/quality)
STYLE_MODEL_PROVIDER=openai
STYLE_MODEL_NAME=gpt-4.1-mini-2025-04-14
STYLE_MODEL_MAX_TOKENS=400
STYLE_MODEL_COST_PER_1K_INPUT=0.0004
STYLE_MODEL_COST_PER_1K_OUTPUT=0.0016

# Model Configuration - Structure Agent (GPT-4.1 for complex analysis)
STRUCTURE_MODEL_PROVIDER=openai
STRUCTURE_MODEL_NAME=gpt-4.1-2025-04-14
STRUCTURE_MODEL_MAX_TOKENS=800
STRUCTURE_MODEL_COST_PER_1K_INPUT=0.002
STRUCTURE_MODEL_COST_PER_1K_OUTPUT=0.008

# Model Configuration - Content Agent (Gemini 2.5 Flash-Lite for long context)
CONTENT_MODEL_PROVIDER=google
CONTENT_MODEL_NAME=gemini-2.5-flash-lite-preview
CONTENT_MODEL_MAX_TOKENS=1500
CONTENT_MODEL_COST_PER_1K_INPUT=0.0001
CONTENT_MODEL_COST_PER_1K_OUTPUT=0.0004
CONTENT_MODEL_CONTEXT_WINDOW=1000000

# Model Configuration - Health Agent (GPT-4.1-mini for comprehensive health analysis)
HEALTH_MODEL_PROVIDER=openai
HEALTH_MODEL_NAME=gpt-4.1-mini-2025-04-14
HEALTH_MODEL_MAX_TOKENS=800
HEALTH_MODEL_COST_PER_1K_INPUT=0.0004
HEALTH_MODEL_COST_PER_1K_OUTPUT=0.0016

# LLM API Keys
OPENAI_API_KEY=sk-your-openai-api-key
GOOGLE_AI_API_KEY=your-google-ai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# Caching Configuration
ENABLE_OPENAI_CACHING=true
OPENAI_CACHE_DISCOUNT=0.75
ENABLE_GEMINI_CACHING=true
GEMINI_CACHE_TTL_MINUTES=60

# Cost Controls
MAX_TOKEN_COST_MONTH=50
ALERT_THRESHOLD_5MIN=1
FORCE_MODEL_DOWNGRADE=false
DISABLE_EXPENSIVE_MODELS=false

# Concurrency & Rate Limits
MAX_CONCURRENT_LLM_CALLS=5
MAX_WORKERS_GRAMMAR=3
MAX_WORKERS_STYLE=2
MAX_WORKERS_STRUCTURE=1
MAX_WORKERS_CONTENT=1
MAX_WORKERS_HEALTH=2

# Queue Configuration (Redis Streams for budget mode)
QUEUE_STRATEGY=redis_streams
DISABLE_CLOUD_TASKS=true

# API Server Configuration
API_HOST=127.0.0.1
API_PORT=8000
API_WORKERS=1
API_TIMEOUT_KEEP_ALIVE=5
API_TIMEOUT_GRACEFUL_SHUTDOWN=10

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173"]

# Mock Settings (for development/testing)
USE_MOCK_DATA=false
USE_MOCK_LLM=false
USE_MOCK_AUTH=false
USE_MOCK_REDIS=false

# File Upload Settings
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=["txt", "md", "docx", "pdf"]

# Monitoring & Observability
SENTRY_DSN=your-sentry-dsn
PROMETHEUS_ENABLED=false
PROMETHEUS_PORT=9090

# Emergency Cost Controls (activate when approaching budget)
EMERGENCY_MODE=false
EMERGENCY_CACHE_TTL=14400
EMERGENCY_MAX_TOKENS=100
EMERGENCY_DISABLE_STRUCTURE_AGENT=false
EMERGENCY_DISABLE_CONTENT_AGENT=false

# Worker Scaling Thresholds
WORKER_SCALE_UP_THRESHOLD=50
WORKER_SCALE_DOWN_THRESHOLD=10
WORKER_MAX_IDLE_TIME=300

# Model Context Thresholds (when to switch to more powerful models)
MODEL_CONTEXT_THRESHOLD_GRAMMAR=400
MODEL_CONTEXT_THRESHOLD_STYLE=1000
MODEL_CONTEXT_THRESHOLD_STRUCTURE=4000
MODEL_CONTEXT_THRESHOLD_CONTENT=6000

# Batch Processing Configuration
ENABLE_BATCH_PROCESSING=true
BATCH_SIZE_GRAMMAR=5
BATCH_SIZE_STYLE=3
BATCH_TIMEOUT_MS=500

# Cache TTL Settings (in seconds)
CACHE_TTL_SHORT=1800    # 30 minutes
CACHE_TTL_MEDIUM=7200   # 2 hours
CACHE_TTL_LONG=86400    # 24 hours

# Development Only
TEST_MODE=false
MOCK_RESPONSE_DELAY_MS=100