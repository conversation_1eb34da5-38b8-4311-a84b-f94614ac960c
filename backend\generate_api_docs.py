#!/usr/bin/env python3
"""
API Documentation Generator for Revisionary Backend

Purpose: Generate comprehensive API documentation including:
- OpenAPI/Swagger JSON export
- Human-readable documentation with examples
- API endpoint coverage analysis
- Model schema documentation

This script creates both machine-readable and human-readable documentation
for frontend developers and API consumers.
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime

# Add backend to path
backend_path = Path(__file__).parent
sys.path.insert(0, str(backend_path))

# Set up mock environment for documentation generation
os.environ.setdefault('USE_MOCK_DATA', 'true')
os.environ.setdefault('USE_MOCK_LLM', 'true')
os.environ.setdefault('USE_MOCK_AUTH', 'true')
os.environ.setdefault('USE_MOCK_REDIS', 'true')
os.environ.setdefault('SUPABASE_URL', 'http://mock-supabase')
os.environ.setdefault('SUPABASE_KEY', 'mock-key')
os.environ.setdefault('DATABASE_URL', 'postgresql://mock:mock@localhost/mock')
os.environ.setdefault('REDIS_URL', 'redis://localhost:6379')

def generate_openapi_spec():
    """Generate OpenAPI specification JSON."""
    print("🔧 Generating OpenAPI specification...")
    
    try:
        from api.main import create_app
        
        # Create FastAPI app
        app = create_app()
        
        # Get OpenAPI schema
        openapi_schema = app.openapi()
        
        # Save to file
        docs_dir = backend_path / "docs"
        docs_dir.mkdir(exist_ok=True)
        
        openapi_file = docs_dir / "openapi.json"
        with open(openapi_file, 'w') as f:
            json.dump(openapi_schema, f, indent=2)
        
        print(f"✅ OpenAPI spec saved to {openapi_file}")
        return openapi_schema
        
    except Exception as e:
        print(f"❌ Error generating OpenAPI spec: {e}")
        return None

def analyze_api_coverage(openapi_schema: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze API endpoint coverage."""
    print("📊 Analyzing API coverage...")
    
    paths = openapi_schema.get('paths', {})
    
    coverage_stats = {
        'total_endpoints': 0,
        'endpoints_by_method': {},
        'endpoints_by_tag': {},
        'endpoints_with_auth': 0,
        'endpoints_with_examples': 0,
        'coverage_by_resource': {}
    }
    
    for path, methods in paths.items():
        for method, details in methods.items():
            if method in ['get', 'post', 'put', 'patch', 'delete']:
                coverage_stats['total_endpoints'] += 1
                
                # Count by method
                method_upper = method.upper()
                coverage_stats['endpoints_by_method'][method_upper] = coverage_stats['endpoints_by_method'].get(method_upper, 0) + 1
                
                # Count by tag
                tags = details.get('tags', ['untagged'])
                for tag in tags:
                    coverage_stats['endpoints_by_tag'][tag] = coverage_stats['endpoints_by_tag'].get(tag, 0) + 1
                
                # Check for authentication
                if 'security' in details:
                    coverage_stats['endpoints_with_auth'] += 1
                
                # Check for examples
                if 'examples' in details.get('requestBody', {}).get('content', {}).get('application/json', {}):
                    coverage_stats['endpoints_with_examples'] += 1
    
    # Resource coverage
    resources = ['auth', 'documents', 'blocks', 'agents', 'personas', 'health']
    for resource in resources:
        resource_endpoints = [path for path in paths.keys() if f"/{resource}" in path]
        coverage_stats['coverage_by_resource'][resource] = len(resource_endpoints)
    
    print(f"✅ Found {coverage_stats['total_endpoints']} total endpoints")
    return coverage_stats

def generate_human_readable_docs(openapi_schema: Dict[str, Any], coverage_stats: Dict[str, Any]):
    """Generate human-readable documentation."""
    print("📝 Generating human-readable documentation...")
    
    docs_content = f"""# Revisionary API Documentation

**Version:** {openapi_schema.get('info', {}).get('version', '1.0.0')}  
**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}

{openapi_schema.get('info', {}).get('description', 'AI-powered writing assistant backend API')}

## API Coverage Summary

- **Total Endpoints:** {coverage_stats['total_endpoints']}
- **Authenticated Endpoints:** {coverage_stats['endpoints_with_auth']}
- **Endpoints with Examples:** {coverage_stats['endpoints_with_examples']}

### Endpoints by HTTP Method
"""
    
    for method, count in coverage_stats['endpoints_by_method'].items():
        docs_content += f"- **{method}:** {count} endpoints\n"
    
    docs_content += "\n### Endpoints by Resource\n"
    for resource, count in coverage_stats['coverage_by_resource'].items():
        docs_content += f"- **{resource.title()}:** {count} endpoints\n"
    
    # Add endpoint documentation
    docs_content += "\n## API Endpoints\n\n"
    
    paths = openapi_schema.get('paths', {})
    components = openapi_schema.get('components', {})
    
    # Group by tag
    endpoints_by_tag = {}
    for path, methods in paths.items():
        for method, details in methods.items():
            if method in ['get', 'post', 'put', 'patch', 'delete']:
                tags = details.get('tags', ['untagged'])
                for tag in tags:
                    if tag not in endpoints_by_tag:
                        endpoints_by_tag[tag] = []
                    endpoints_by_tag[tag].append({
                        'path': path,
                        'method': method.upper(),
                        'details': details
                    })
    
    # Generate documentation for each tag
    for tag, endpoints in endpoints_by_tag.items():
        docs_content += f"### {tag.title()}\n\n"
        
        for endpoint in endpoints:
            path = endpoint['path']
            method = endpoint['method']
            details = endpoint['details']
            
            docs_content += f"#### `{method} {path}`\n\n"
            docs_content += f"**Description:** {details.get('summary', 'No description')}\n\n"
            
            # Parameters
            if 'parameters' in details:
                docs_content += "**Parameters:**\n"
                for param in details['parameters']:
                    required = " (required)" if param.get('required', False) else ""
                    docs_content += f"- `{param['name']}` ({param.get('in', 'query')}){required}: {param.get('description', 'No description')}\n"
                docs_content += "\n"
            
            # Request body
            if 'requestBody' in details:
                docs_content += "**Request Body:**\n"
                content = details['requestBody'].get('content', {})
                if 'application/json' in content:
                    schema_ref = content['application/json'].get('schema', {})
                    if '$ref' in schema_ref:
                        model_name = schema_ref['$ref'].split('/')[-1]
                        docs_content += f"- Content-Type: `application/json`\n"
                        docs_content += f"- Schema: `{model_name}`\n\n"
            
            # Responses
            docs_content += "**Responses:**\n"
            responses = details.get('responses', {})
            for status_code, response_details in responses.items():
                docs_content += f"- `{status_code}`: {response_details.get('description', 'No description')}\n"
            docs_content += "\n---\n\n"
    
    # Add schema documentation
    docs_content += "## Data Models\n\n"
    schemas = components.get('schemas', {})
    for schema_name, schema_details in schemas.items():
        docs_content += f"### {schema_name}\n\n"
        docs_content += f"**Type:** {schema_details.get('type', 'object')}\n\n"
        
        if 'description' in schema_details:
            docs_content += f"**Description:** {schema_details['description']}\n\n"
        
        if 'properties' in schema_details:
            docs_content += "**Properties:**\n"
            properties = schema_details['properties']
            required_fields = schema_details.get('required', [])
            
            for prop_name, prop_details in properties.items():
                required_text = " (required)" if prop_name in required_fields else ""
                prop_type = prop_details.get('type', 'unknown')
                description = prop_details.get('description', 'No description')
                docs_content += f"- `{prop_name}` ({prop_type}){required_text}: {description}\n"
        
        docs_content += "\n---\n\n"
    
    # Add example requests
    docs_content += """## Example Requests

### Authentication
```bash
# Get user profile
curl -X GET "http://localhost:8000/api/v1/auth/me" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Health Analysis
```bash
# Analyze text health
curl -X POST "http://localhost:8000/api/v1/health/analyze" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d '{
    "text": "This is sample text to analyze for writing health.",
    "analysis_type": "comprehensive"
  }'
```

### Document Management
```bash
# Create a new document
curl -X POST "http://localhost:8000/api/v1/documents" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d '{
    "title": "My New Document",
    "type": "creative",
    "content": "Initial document content..."
  }'
```

### Persona Feedback
```bash
# Request multi-persona feedback
curl -X POST "http://localhost:8000/api/v1/personas/multi-feedback" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d '{
    "text": "Text to analyze",
    "persona_ids": ["persona-1", "persona-2"],
    "options": {
      "include_cross_analysis": true
    }
  }'
```

## Error Handling

All endpoints return errors in the following format:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {}
  }
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `422` - Unprocessable Entity (validation error)
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

## Rate Limiting

- **Free tier:** 100 requests per hour
- **Professional:** 1,000 requests per hour  
- **Studio:** 10,000 requests per hour
- **Enterprise:** Unlimited

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit per window
- `X-RateLimit-Remaining`: Requests remaining in current window
- `X-RateLimit-Reset`: Timestamp when window resets

## Authentication

The API uses Firebase JWT tokens for authentication. Include the token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

Tokens can be obtained through Firebase Authentication on the frontend.
"""
    
    # Save documentation
    docs_file = backend_path / "docs" / "api_documentation.md"
    with open(docs_file, 'w') as f:
        f.write(docs_content)
    
    print(f"✅ Human-readable docs saved to {docs_file}")

def generate_postman_collection(openapi_schema: Dict[str, Any]):
    """Generate Postman collection for API testing."""
    print("📮 Generating Postman collection...")
    
    collection = {
        "info": {
            "name": "Revisionary API",
            "description": openapi_schema.get('info', {}).get('description', ''),
            "version": openapi_schema.get('info', {}).get('version', '1.0.0'),
            "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
        },
        "auth": {
            "type": "bearer",
            "bearer": [
                {
                    "key": "token",
                    "value": "{{jwt_token}}",
                    "type": "string"
                }
            ]
        },
        "variable": [
            {
                "key": "base_url",
                "value": "http://localhost:8000",
                "type": "string"
            },
            {
                "key": "jwt_token",
                "value": "YOUR_JWT_TOKEN_HERE",
                "type": "string"
            }
        ],
        "item": []
    }
    
    # Group items by tag
    paths = openapi_schema.get('paths', {})
    folders = {}
    
    for path, methods in paths.items():
        for method, details in methods.items():
            if method in ['get', 'post', 'put', 'patch', 'delete']:
                tags = details.get('tags', ['untagged'])
                tag = tags[0] if tags else 'untagged'
                
                if tag not in folders:
                    folders[tag] = {
                        "name": tag.title(),
                        "item": []
                    }
                
                # Create request
                request_item = {
                    "name": details.get('summary', f"{method.upper()} {path}"),
                    "request": {
                        "method": method.upper(),
                        "header": [
                            {
                                "key": "Content-Type",
                                "value": "application/json",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{base_url}}" + path,
                            "host": ["{{base_url}}"],
                            "path": path.strip('/').split('/')
                        }
                    }
                }
                
                # Add request body for POST/PUT/PATCH
                if method in ['post', 'put', 'patch'] and 'requestBody' in details:
                    request_item["request"]["body"] = {
                        "mode": "raw",
                        "raw": "{\n  // Add request body here\n}",
                        "options": {
                            "raw": {
                                "language": "json"
                            }
                        }
                    }
                
                folders[tag]["item"].append(request_item)
    
    # Add folders to collection
    for folder in folders.values():
        collection["item"].append(folder)
    
    # Save collection
    collection_file = backend_path / "docs" / "revisionary_api.postman_collection.json"
    with open(collection_file, 'w') as f:
        json.dump(collection, f, indent=2)
    
    print(f"✅ Postman collection saved to {collection_file}")

def main():
    """Main documentation generation function."""
    print("🚀 REVISIONARY API DOCUMENTATION GENERATOR")
    print("=" * 80)
    print("Generating comprehensive API documentation...")
    print()
    
    # Generate OpenAPI specification
    openapi_schema = generate_openapi_spec()
    if not openapi_schema:
        print("❌ Failed to generate OpenAPI schema")
        return False
    
    # Analyze coverage
    coverage_stats = analyze_api_coverage(openapi_schema)
    
    # Generate human-readable documentation
    generate_human_readable_docs(openapi_schema, coverage_stats)
    
    # Generate Postman collection
    generate_postman_collection(openapi_schema)
    
    print("\n" + "=" * 80)
    print("🎉 API DOCUMENTATION GENERATION COMPLETE!")
    print("=" * 80)
    print("\nGenerated files:")
    print("  📄 docs/openapi.json - OpenAPI/Swagger specification")
    print("  📖 docs/api_documentation.md - Human-readable documentation")
    print("  📮 docs/revisionary_api.postman_collection.json - Postman collection")
    print("\nDocumentation Summary:")
    print(f"  ✅ {coverage_stats['total_endpoints']} total API endpoints documented")
    print(f"  ✅ {len(coverage_stats['endpoints_by_tag'])} API resource groups")
    print(f"  ✅ {coverage_stats['endpoints_with_auth']} authenticated endpoints")
    print("\n🔗 View interactive docs at: http://localhost:8000/docs")
    print("🔗 View ReDoc at: http://localhost:8000/redoc")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)