"""
Revisionary Backend Configuration System

Purpose: Centralized configuration management using Pydantic Settings
- Environment variable validation and type conversion
- Default values and validation rules
- Supabase, Redis, and LLM API configuration
- Development vs production settings management

Features:
- Type-safe configuration loading from environment variables
- Validation of required settings
- Default values for development
- Structured configuration for all services
"""

import os
from typing import List, Optional, Dict
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Main application settings."""
    
    # Environment
    env: str = Field("development", env="ENV")
    debug: bool = Field(False, env="DEBUG")
    log_level: str = Field("INFO", env="LOG_LEVEL")
    
    # Database settings
    supabase_url: str = Field(..., env="SUPABASE_URL")
    supabase_key: str = Field(..., env="SUPABASE_KEY") 
    supabase_service_key: str = Field("PLACEHOLDER_SERVICE_KEY_FROM_DASHBOARD", env="SUPABASE_SERVICE_KEY")
    database_url: str = Field(..., env="DATABASE_URL")
    
    # Database connection pool settings - Optimized for Supabase
    database_connection_pool_size: int = Field(10, env="DATABASE_CONNECTION_POOL_SIZE")
    database_connection_timeout: int = Field(30, env="DATABASE_CONNECTION_TIMEOUT")
    database_command_timeout: int = Field(60, env="DATABASE_COMMAND_TIMEOUT")
    
    # Redis settings - Optimized for performance
    redis_url: str = Field("redis://localhost:6379/0", env="REDIS_URL")
    redis_password: Optional[str] = Field(None, env="REDIS_PASSWORD")
    redis_db: int = Field(0, env="REDIS_DB")
    redis_connection_pool_size: int = Field(50, env="REDIS_CONNECTION_POOL_SIZE")  # Increased pool size
    redis_connection_timeout: int = Field(3, env="REDIS_CONNECTION_TIMEOUT")  # Faster timeout
    redis_socket_keepalive: bool = Field(True, env="REDIS_SOCKET_KEEPALIVE")  # Keep connections alive
    redis_socket_keepalive_options: Dict[str, int] = Field(
        default_factory=lambda: {"TCP_KEEPINTVL": 1, "TCP_KEEPCNT": 3, "TCP_KEEPIDLE": 1},
        env="REDIS_SOCKET_KEEPALIVE_OPTIONS"
    )
    
    # Auth settings
    firebase_project_id: str = Field("revisionary-prod", env="FIREBASE_PROJECT_ID")
    firebase_credentials_path: Optional[str] = Field(None, env="FIREBASE_CREDENTIALS_PATH")
    jwt_secret_key: str = Field("development-secret-key-change-in-production", env="JWT_SECRET_KEY")
    jwt_algorithm: str = Field("HS256", env="JWT_ALGORITHM")
    jwt_expiration_hours: int = Field(24, env="JWT_EXPIRATION_HOURS")
    
    # LLM API Keys
    openai_api_key: str = Field("PLACEHOLDER_OPENAI_API_KEY", env="OPENAI_API_KEY")
    google_ai_api_key: str = Field("PLACEHOLDER_GOOGLE_AI_API_KEY", env="GOOGLE_AI_API_KEY")
    anthropic_api_key: str = Field("PLACEHOLDER_ANTHROPIC_API_KEY", env="ANTHROPIC_API_KEY")
    
    # Cost Management
    max_token_cost_month: float = Field(50.0, env="MAX_TOKEN_COST_MONTH")
    alert_threshold_5min: float = Field(1.0, env="ALERT_THRESHOLD_5MIN")
    
    # Cache Settings - Optimized for better performance
    llm_cache_ttl_hours: int = Field(4, env="LLM_CACHE_TTL_HOURS")  # Increased for better hit rates
    emergency_cache_ttl_hours: int = Field(8, env="EMERGENCY_CACHE_TTL_HOURS")  # Longer fallback cache
    health_cache_ttl_minutes: int = Field(30, env="HEALTH_CACHE_TTL_MINUTES")  # Short-term health cache
    document_cache_ttl_minutes: int = Field(15, env="DOCUMENT_CACHE_TTL_MINUTES")  # Document context cache
    redis_max_memory_policy: str = Field("allkeys-lru", env="REDIS_MAX_MEMORY_POLICY")  # LRU eviction
    
    # Grammar Agent Model Configuration
    grammar_model_provider: str = Field("openai", env="GRAMMAR_MODEL_PROVIDER")
    grammar_model_name: str = Field("gpt-4.1-nano-2025-04-14", env="GRAMMAR_MODEL_NAME")
    grammar_model_max_tokens: int = Field(200, env="GRAMMAR_MODEL_MAX_TOKENS")
    grammar_model_cost_per_1k_input: float = Field(0.0001, env="GRAMMAR_MODEL_COST_PER_1K_INPUT")
    grammar_model_cost_per_1k_output: float = Field(0.0004, env="GRAMMAR_MODEL_COST_PER_1K_OUTPUT")
    
    # Style Agent Model Configuration
    style_model_provider: str = Field("openai", env="STYLE_MODEL_PROVIDER")
    style_model_name: str = Field("gpt-4.1-mini-2025-04-14", env="STYLE_MODEL_NAME")
    style_model_max_tokens: int = Field(500, env="STYLE_MODEL_MAX_TOKENS")
    style_model_cost_per_1k_input: float = Field(0.0004, env="STYLE_MODEL_COST_PER_1K_INPUT")
    style_model_cost_per_1k_output: float = Field(0.0016, env="STYLE_MODEL_COST_PER_1K_OUTPUT")
    
    # Structure Agent Model Configuration
    structure_model_provider: str = Field("openai", env="STRUCTURE_MODEL_PROVIDER")
    structure_model_name: str = Field("gpt-4.1-mini-2025-04-14", env="STRUCTURE_MODEL_NAME")
    structure_model_max_tokens: int = Field(800, env="STRUCTURE_MODEL_MAX_TOKENS")
    structure_model_cost_per_1k_input: float = Field(0.0004, env="STRUCTURE_MODEL_COST_PER_1K_INPUT")
    structure_model_cost_per_1k_output: float = Field(0.0016, env="STRUCTURE_MODEL_COST_PER_1K_OUTPUT")
    
    # Content Agent Model Configuration (uses Gemini for longer context)
    content_model_provider: str = Field("google", env="CONTENT_MODEL_PROVIDER")
    content_model_name: str = Field("gemini-2.5-flash-lite-preview", env="CONTENT_MODEL_NAME")
    content_model_max_tokens: int = Field(2000, env="CONTENT_MODEL_MAX_TOKENS")
    content_model_cost_per_1k_input: float = Field(0.0001, env="CONTENT_MODEL_COST_PER_1K_INPUT")
    content_model_cost_per_1k_output: float = Field(0.0004, env="CONTENT_MODEL_COST_PER_1K_OUTPUT")
    
    # Health Agent Model Configuration (uses GPT-4.1-mini for comprehensive analysis)
    health_model_provider: str = Field("openai", env="HEALTH_MODEL_PROVIDER")
    health_model_name: str = Field("gpt-4.1-mini-2025-04-14", env="HEALTH_MODEL_NAME")
    health_model_max_tokens: int = Field(800, env="HEALTH_MODEL_MAX_TOKENS")
    health_model_cost_per_1k_input: float = Field(0.0004, env="HEALTH_MODEL_COST_PER_1K_INPUT")
    health_model_cost_per_1k_output: float = Field(0.0016, env="HEALTH_MODEL_COST_PER_1K_OUTPUT")
    
    # Worker Configuration
    max_workers_grammar: int = Field(3, env="MAX_WORKERS_GRAMMAR")
    max_workers_style: int = Field(2, env="MAX_WORKERS_STYLE")
    max_workers_structure: int = Field(1, env="MAX_WORKERS_STRUCTURE")
    max_workers_content: int = Field(1, env="MAX_WORKERS_CONTENT")
    max_workers_health: int = Field(2, env="MAX_WORKERS_HEALTH")
    
    # Queue Configuration
    worker_scale_up_threshold: int = Field(50, env="WORKER_SCALE_UP_THRESHOLD")
    worker_scale_down_threshold: int = Field(10, env="WORKER_SCALE_DOWN_THRESHOLD")
    worker_job_timeout: int = Field(30, env="WORKER_JOB_TIMEOUT")
    job_max_retries: int = Field(3, env="JOB_MAX_RETRIES")
    batch_timeout_ms: int = Field(500, env="BATCH_TIMEOUT_MS")
    enable_batch_processing: bool = Field(True, env="ENABLE_BATCH_PROCESSING")
    
    # Batch Size Configuration
    batch_size_grammar: int = Field(5, env="BATCH_SIZE_GRAMMAR")
    batch_size_style: int = Field(3, env="BATCH_SIZE_STYLE")
    batch_size_structure: int = Field(1, env="BATCH_SIZE_STRUCTURE")
    batch_size_content: int = Field(1, env="BATCH_SIZE_CONTENT")
    batch_size_health: int = Field(1, env="BATCH_SIZE_HEALTH")
    
    # API settings
    api_host: str = Field("127.0.0.1", env="API_HOST")
    api_port: int = Field(8000, env="API_PORT")
    api_workers: int = Field(1, env="API_WORKERS")
    api_timeout_keep_alive: int = Field(5, env="API_TIMEOUT_KEEP_ALIVE")
    api_timeout_graceful_shutdown: int = Field(10, env="API_TIMEOUT_GRACEFUL_SHUTDOWN")
    cors_origins: List[str] = Field(
        ["http://localhost:3000", "http://localhost:5173"],
        env="CORS_ORIGINS"
    )
    
    # Mock settings
    use_mock_data: bool = Field(False, env="USE_MOCK_DATA")
    use_mock_llm: bool = Field(False, env="USE_MOCK_LLM")
    use_mock_auth: bool = Field(False, env="USE_MOCK_AUTH")
    use_mock_redis: bool = Field(False, env="USE_MOCK_REDIS")
    
    # File upload settings
    max_file_size_mb: int = Field(10, env="MAX_FILE_SIZE_MB")
    allowed_file_types: List[str] = Field(
        ["txt", "md", "docx", "pdf"],
        env="ALLOWED_FILE_TYPES"
    )
    
    # Monitoring
    sentry_dsn: Optional[str] = Field(None, env="SENTRY_DSN")
    prometheus_enabled: bool = Field(False, env="PROMETHEUS_ENABLED")
    prometheus_port: int = Field(9090, env="PROMETHEUS_PORT")
    
    # Computed properties for backward compatibility
    @property
    def database(self):
        return type('DatabaseSettings', (), {
            'supabase_url': self.supabase_url,
            'supabase_key': self.supabase_key,
            'supabase_service_key': self.supabase_service_key,
            'database_url': self.database_url,
            'connection_pool_size': self.database_connection_pool_size,
            'connection_timeout': self.database_connection_timeout,
            'command_timeout': self.database_command_timeout,
        })()
    
    @property
    def redis(self):
        return type('RedisSettings', (), {
            'redis_url': self.redis_url,
            'redis_password': self.redis_password,
            'redis_db': self.redis_db,
            'connection_pool_size': self.redis_connection_pool_size,
            'connection_timeout': self.redis_connection_timeout,
        })()
    
    @property
    def auth(self):
        return type('AuthSettings', (), {
            'firebase_project_id': self.firebase_project_id,
            'firebase_credentials_path': self.firebase_credentials_path,
            'jwt_secret_key': self.jwt_secret_key,
            'jwt_algorithm': self.jwt_algorithm,
            'jwt_expiration_hours': self.jwt_expiration_hours,
        })()
    
    @property
    def api(self):
        return type('APISettings', (), {
            'host': self.api_host,
            'port': self.api_port,
            'cors_origins': self.cors_origins,
            'cors_credentials': True,
            'cors_methods': ["*"],
            'cors_headers': ["*"],
        })()
    
    @property
    def mock(self):
        return type('MockSettings', (), {
            'use_mock_data': self.use_mock_data,
            'use_mock_llm': self.use_mock_llm,
            'use_mock_auth': self.use_mock_auth,
            'use_mock_redis': self.use_mock_redis,
        })()
    
    @property
    def cost_management(self):
        return type('CostSettings', (), {
            'max_token_cost_month': self.max_token_cost_month,
            'alert_threshold_5min': self.alert_threshold_5min,
            'llm_cache_ttl_hours': self.llm_cache_ttl_hours,
            'emergency_cache_ttl_hours': self.emergency_cache_ttl_hours,
        })()
    
    @property
    def llm_models(self):
        return type('LLMModelSettings', (), {
            'grammar': type('GrammarModel', (), {
                'provider': self.grammar_model_provider,
                'name': self.grammar_model_name,
                'max_tokens': self.grammar_model_max_tokens,
                'cost_per_1k_input': self.grammar_model_cost_per_1k_input,
                'cost_per_1k_output': self.grammar_model_cost_per_1k_output,
            })(),
            'style': type('StyleModel', (), {
                'provider': self.style_model_provider,
                'name': self.style_model_name,
                'max_tokens': self.style_model_max_tokens,
                'cost_per_1k_input': self.style_model_cost_per_1k_input,
                'cost_per_1k_output': self.style_model_cost_per_1k_output,
            })(),
            'structure': type('StructureModel', (), {
                'provider': self.structure_model_provider,
                'name': self.structure_model_name,
                'max_tokens': self.structure_model_max_tokens,
                'cost_per_1k_input': self.structure_model_cost_per_1k_input,
                'cost_per_1k_output': self.structure_model_cost_per_1k_output,
            })(),
            'content': type('ContentModel', (), {
                'provider': self.content_model_provider,
                'name': self.content_model_name,
                'max_tokens': self.content_model_max_tokens,
                'cost_per_1k_input': self.content_model_cost_per_1k_input,
                'cost_per_1k_output': self.content_model_cost_per_1k_output,
            })(),
            'health': type('HealthModel', (), {
                'provider': self.health_model_provider,
                'name': self.health_model_name,
                'max_tokens': self.health_model_max_tokens,
                'cost_per_1k_input': self.health_model_cost_per_1k_input,
                'cost_per_1k_output': self.health_model_cost_per_1k_output,
            })(),
        })()
    
    @property
    def workers(self):
        return type('WorkerSettings', (), {
            'max_workers': {
                'grammar': self.max_workers_grammar,
                'style': self.max_workers_style,
                'structure': self.max_workers_structure,
                'content': self.max_workers_content,
                'health': self.max_workers_health,
            },
            'scale_up_threshold': self.worker_scale_up_threshold,
            'scale_down_threshold': self.worker_scale_down_threshold,
            'job_timeout': self.worker_job_timeout,
            'max_retries': self.job_max_retries,
            'batch_timeout_ms': self.batch_timeout_ms,
            'enable_batch_processing': self.enable_batch_processing,
            'batch_sizes': {
                'grammar': self.batch_size_grammar,
                'style': self.batch_size_style,
                'structure': self.batch_size_structure,
                'content': self.batch_size_content,
                'health': self.batch_size_health,
            }
        })()
    
    @validator('cors_origins', pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            # Handle string representation of list
            if v.startswith('[') and v.endswith(']'):
                import json
                return json.loads(v.replace("'", '"'))
            return [v]
        return v
    
    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
def get_settings() -> Settings:
    """Get application settings instance."""
    return Settings()


# Create settings instance
settings = get_settings()