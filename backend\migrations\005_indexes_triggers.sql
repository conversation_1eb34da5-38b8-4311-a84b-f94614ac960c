-- Revisionary Database Indexes, Triggers, and Functions
-- Part 5 of the schema migration

-- =================================================================
-- INDEXES
-- =================================================================

-- Users indexes
CREATE INDEX idx_users_firebase_uid ON users(firebase_uid);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_subscription ON users(subscription_tier, subscription_status);
CREATE INDEX idx_users_last_active ON users(last_active_at) WHERE deleted_at IS NULL;

-- Documents indexes
CREATE INDEX idx_documents_owner ON documents(owner_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_documents_type ON documents(type) WHERE deleted_at IS NULL;
CREATE INDEX idx_documents_status ON documents(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_documents_updated ON documents(updated_at DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_documents_tags ON documents USING gin(tags);
CREATE INDEX idx_documents_metadata ON documents USING gin(metadata);
CREATE INDEX idx_documents_search ON documents USING gin(
    to_tsvector('english', title || ' ' || COALESCE(description, ''))
);

-- Blocks indexes
CREATE UNIQUE INDEX idx_blocks_document_position ON blocks(document_id, parent_id, position) 
    WHERE deleted_at IS NULL;
CREATE INDEX idx_blocks_parent ON blocks(parent_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_blocks_path ON blocks USING gist(path) WHERE deleted_at IS NULL;
CREATE INDEX idx_blocks_type ON blocks(type) WHERE deleted_at IS NULL;
CREATE INDEX idx_blocks_hash ON blocks(content_hash);
CREATE INDEX idx_blocks_updated ON blocks(updated_at DESC);
CREATE INDEX idx_blocks_content_search ON blocks USING gin(
    to_tsvector('english', content)
) WHERE content IS NOT NULL AND deleted_at IS NULL;

-- Versions indexes
CREATE INDEX idx_versions_block ON versions(block_id, version_number DESC);
CREATE INDEX idx_versions_author ON versions(author_id, created_at DESC);
CREATE INDEX idx_versions_created ON versions(created_at DESC);
CREATE INDEX idx_versions_change_type ON versions(change_type);

-- Suggestions indexes
CREATE INDEX idx_suggestions_block_status ON suggestions(block_id, status);
CREATE INDEX idx_suggestions_agent_severity ON suggestions(agent_type, severity);
CREATE INDEX idx_suggestions_created ON suggestions(created_at DESC);
CREATE INDEX idx_suggestions_expires ON suggestions(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_suggestions_hash ON suggestions(original_hash);
CREATE INDEX idx_suggestions_position ON suggestions USING gin(position);
CREATE INDEX idx_suggestions_pending ON suggestions(block_id) WHERE status = 'pending';
CREATE INDEX idx_suggestions_active ON suggestions(block_id, created_at DESC) 
    WHERE status IN ('pending', 'accepted');

-- Summaries indexes
CREATE INDEX idx_summaries_block_level ON summaries(block_id, level);
CREATE INDEX idx_summaries_expires ON summaries(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_summaries_hash ON summaries(content_hash);
CREATE INDEX idx_summaries_embedding ON summaries 
    USING ivfflat (embedding vector_cosine_ops)
    WITH (lists = 100);

-- Collaborations indexes
CREATE INDEX idx_collaborations_document ON collaborations(document_id, status);
CREATE INDEX idx_collaborations_user ON collaborations(user_id, status);
CREATE INDEX idx_collaborations_expires ON collaborations(expires_at) WHERE expires_at IS NOT NULL;

-- Comments indexes
CREATE INDEX idx_comments_block ON comments(block_id, created_at DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_comments_author ON comments(author_id, created_at DESC) WHERE deleted_at IS NULL;
CREATE INDEX idx_comments_parent ON comments(parent_id) WHERE parent_id IS NOT NULL;
CREATE INDEX idx_comments_status ON comments(status) WHERE deleted_at IS NULL;

-- Analytics indexes
CREATE INDEX idx_document_scores_document ON document_scores(document_id, created_at DESC);
CREATE INDEX idx_document_scores_user ON document_scores(user_id, created_at DESC);
CREATE INDEX idx_document_scores_type_score ON document_scores(document_type, total_score DESC);
CREATE INDEX idx_document_scores_date ON document_scores(created_at DESC);

CREATE INDEX idx_user_writing_stats_user_date ON user_writing_stats(user_id, date DESC);
CREATE INDEX idx_user_writing_stats_type ON user_writing_stats(document_type, date DESC);
CREATE INDEX idx_user_writing_stats_score ON user_writing_stats(average_score DESC);

CREATE INDEX idx_writing_achievements_user ON writing_achievements(user_id, earned_at DESC);
CREATE INDEX idx_writing_achievements_type ON writing_achievements(achievement_type, achievement_level);
CREATE INDEX idx_writing_achievements_earned ON writing_achievements(earned_at DESC);

CREATE INDEX idx_writing_challenges_user_date ON writing_challenges(user_id, assigned_date DESC);
CREATE INDEX idx_writing_challenges_type ON writing_challenges(challenge_type);
CREATE INDEX idx_writing_challenges_completed ON writing_challenges(completed_at) 
    WHERE completed_at IS NOT NULL;

CREATE UNIQUE INDEX idx_scoring_algorithms_active ON scoring_algorithms(is_active) 
    WHERE is_active = TRUE;

CREATE INDEX idx_usage_events_user_date ON usage_events(user_id, created_at DESC);
CREATE INDEX idx_usage_events_document ON usage_events(document_id, created_at DESC);
CREATE INDEX idx_usage_events_type ON usage_events(event_type, created_at DESC);
CREATE INDEX idx_usage_events_tokens ON usage_events(tokens_used) WHERE tokens_used > 0;

CREATE INDEX idx_token_usage_daily_user_date ON token_usage_daily(user_id, date DESC);
CREATE INDEX idx_token_usage_daily_date ON token_usage_daily(date DESC);

-- Persona indexes
CREATE INDEX idx_personas_user ON personas(user_id) WHERE is_active = TRUE;
CREATE INDEX idx_personas_type ON personas(type) WHERE is_active = TRUE;
CREATE INDEX idx_personas_template ON personas(is_template) WHERE is_template = TRUE;
CREATE INDEX idx_personas_usage ON personas(usage_count DESC);
CREATE INDEX idx_personas_effectiveness ON personas(effectiveness_score DESC) WHERE effectiveness_score IS NOT NULL;

CREATE INDEX idx_persona_feedback_block ON persona_feedback(block_id, created_at DESC);
CREATE INDEX idx_persona_feedback_persona ON persona_feedback(persona_id, created_at DESC);
CREATE INDEX idx_persona_feedback_session ON persona_feedback(session_id);
CREATE INDEX idx_persona_feedback_score ON persona_feedback(overall_score DESC);

CREATE INDEX idx_audience_analysis_document ON audience_analysis(document_id, created_at DESC);
CREATE INDEX idx_audience_analysis_expires ON audience_analysis(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_audience_analysis_score ON audience_analysis(overall_appeal_score DESC);

-- Agent indexes
CREATE INDEX idx_custom_agents_user ON custom_agents(user_id) WHERE is_active = TRUE;
CREATE INDEX idx_custom_agents_type ON custom_agents(type) WHERE is_active = TRUE;
CREATE INDEX idx_custom_agents_priority ON custom_agents(priority DESC) WHERE is_active = TRUE;
CREATE INDEX idx_custom_agents_template ON custom_agents(is_template) WHERE is_template = TRUE;
CREATE INDEX idx_custom_agents_last_used ON custom_agents(last_used_at DESC) WHERE last_used_at IS NOT NULL;

CREATE INDEX idx_agent_style_rules_agent ON agent_style_rules(agent_id, priority DESC) WHERE is_enabled = TRUE;
CREATE INDEX idx_agent_style_rules_category ON agent_style_rules(category) WHERE is_enabled = TRUE;

CREATE INDEX idx_agent_usage_stats_agent_date ON agent_usage_stats(agent_id, date DESC);
CREATE INDEX idx_agent_usage_stats_user_date ON agent_usage_stats(user_id, date DESC);
CREATE INDEX idx_agent_usage_stats_date ON agent_usage_stats(date DESC);

-- Health indexes
CREATE INDEX idx_health_metrics_block ON health_metrics(block_id, created_at DESC);
CREATE INDEX idx_health_metrics_user ON health_metrics(user_id, created_at DESC);
CREATE INDEX idx_health_metrics_overall_score ON health_metrics(overall_score DESC);
CREATE INDEX idx_health_metrics_created ON health_metrics(created_at DESC);

CREATE INDEX idx_health_issues_block ON health_issues(block_id, severity, created_at DESC);
CREATE INDEX idx_health_issues_type ON health_issues(issue_type, severity);
CREATE INDEX idx_health_issues_unresolved ON health_issues(block_id) WHERE is_resolved = FALSE;
CREATE INDEX idx_health_issues_severity ON health_issues(severity, created_at DESC);

-- Entity indexes
CREATE INDEX idx_entities_document ON entities(document_id, entity_type);
CREATE INDEX idx_entities_user ON entities(user_id, created_at DESC);
CREATE INDEX idx_entities_name ON entities USING gin(to_tsvector('english', name));
CREATE INDEX idx_entities_aliases ON entities USING gin(aliases);
CREATE INDEX idx_entities_importance ON entities(importance_score DESC);
CREATE INDEX idx_entities_mentions ON entities(mention_count DESC);

CREATE INDEX idx_entity_relationships_source ON entity_relationships(source_entity_id, relationship_type);
CREATE INDEX idx_entity_relationships_target ON entity_relationships(target_entity_id, relationship_type);
CREATE INDEX idx_entity_relationships_strength ON entity_relationships(strength DESC);
CREATE UNIQUE INDEX idx_entity_relationships_unique ON entity_relationships(source_entity_id, target_entity_id, relationship_type);

CREATE INDEX idx_consistency_violations_document ON consistency_violations(document_id, severity, created_at DESC);
CREATE INDEX idx_consistency_violations_entity ON consistency_violations(entity_id, severity);
CREATE INDEX idx_consistency_violations_unresolved ON consistency_violations(document_id) WHERE is_resolved = FALSE;
CREATE INDEX idx_consistency_violations_type ON consistency_violations(violation_type, severity);

-- Citation indexes
CREATE INDEX idx_citations_document ON citations(document_id, created_at DESC);
CREATE INDEX idx_citations_block ON citations(block_id);
CREATE INDEX idx_citations_key ON citations(citation_key);
CREATE INDEX idx_citations_type ON citations(citation_type);
CREATE INDEX idx_citations_authors ON citations USING gin(authors);
CREATE INDEX idx_citations_title_search ON citations USING gin(to_tsvector('english', title));
CREATE INDEX idx_citations_unverified ON citations(document_id) WHERE is_verified = FALSE;

CREATE INDEX idx_reference_library_user ON reference_library(user_id, created_at DESC);
CREATE INDEX idx_reference_library_type ON reference_library(citation_type);
CREATE INDEX idx_reference_library_authors ON reference_library USING gin(authors);
CREATE INDEX idx_reference_library_tags ON reference_library USING gin(tags);
CREATE INDEX idx_reference_library_title_search ON reference_library USING gin(to_tsvector('english', title));
CREATE INDEX idx_reference_library_favorites ON reference_library(user_id) WHERE is_favorite = TRUE;
CREATE INDEX idx_reference_library_usage ON reference_library(usage_count DESC);

-- Export and template indexes
CREATE INDEX idx_export_jobs_user ON export_jobs(user_id, created_at DESC);
CREATE INDEX idx_export_jobs_document ON export_jobs(document_id, created_at DESC);
CREATE INDEX idx_export_jobs_status ON export_jobs(status);
CREATE INDEX idx_export_jobs_expires ON export_jobs(expires_at) WHERE expires_at IS NOT NULL;

CREATE INDEX idx_templates_category ON templates(category) WHERE is_public = TRUE;
CREATE INDEX idx_templates_type ON templates(document_type) WHERE is_public = TRUE;
CREATE INDEX idx_templates_created_by ON templates(created_by);
CREATE INDEX idx_templates_usage ON templates(usage_count DESC) WHERE is_public = TRUE;
CREATE INDEX idx_templates_rating ON templates(rating DESC) WHERE is_public = TRUE AND rating IS NOT NULL;
CREATE INDEX idx_templates_tags ON templates USING gin(tags);

-- =================================================================
-- FUNCTIONS AND TRIGGERS
-- =================================================================

-- Function to update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update block path for hierarchical structure
CREATE OR REPLACE FUNCTION update_block_path()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.parent_id IS NULL THEN
        -- Replace hyphens with underscores for valid ltree format
        NEW.path = REPLACE(NEW.id::text, '-', '_')::ltree;
        NEW.depth = 0;
    ELSE
        SELECT path || REPLACE(NEW.id::text, '-', '_'), depth + 1
        INTO NEW.path, NEW.depth
        FROM blocks
        WHERE id = NEW.parent_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update document statistics
CREATE OR REPLACE FUNCTION update_document_stats()
RETURNS TRIGGER AS $$
DECLARE
    doc_id UUID;
    total_words INTEGER;
    total_chars INTEGER;
BEGIN
    -- Get document ID from the block
    IF TG_OP = 'DELETE' THEN
        doc_id := OLD.document_id;
    ELSE
        doc_id := NEW.document_id;
    END IF;
    
    -- Calculate totals
    SELECT 
        COALESCE(SUM(word_count), 0),
        COALESCE(SUM(content_length), 0)
    INTO total_words, total_chars
    FROM blocks 
    WHERE document_id = doc_id 
    AND deleted_at IS NULL 
    AND type = 'paragraph';
    
    -- Update document
    UPDATE documents 
    SET 
        word_count = total_words,
        character_count = total_chars,
        updated_at = NOW()
    WHERE id = doc_id;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to generate content hash
CREATE OR REPLACE FUNCTION generate_content_hash()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.content IS NOT NULL THEN
        NEW.content_hash = encode(sha256(NEW.content::bytea), 'hex');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to expire stale suggestions
CREATE OR REPLACE FUNCTION expire_stale_suggestions()
RETURNS TRIGGER AS $$
BEGIN
    -- Mark suggestions as stale when block content changes
    UPDATE suggestions 
    SET 
        status = 'stale',
        resolved_at = NOW()
    WHERE 
        block_id = NEW.id 
        AND status = 'pending'
        AND original_hash != NEW.content_hash;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to tables with updated_at column
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blocks_updated_at BEFORE UPDATE ON blocks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_collaborations_updated_at BEFORE UPDATE ON collaborations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_comments_updated_at BEFORE UPDATE ON comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_templates_updated_at BEFORE UPDATE ON templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_writing_stats_updated_at BEFORE UPDATE ON user_writing_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_personas_updated_at BEFORE UPDATE ON personas
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_custom_agents_updated_at BEFORE UPDATE ON custom_agents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_style_rules_updated_at BEFORE UPDATE ON agent_style_rules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_usage_stats_updated_at BEFORE UPDATE ON agent_usage_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_entities_updated_at BEFORE UPDATE ON entities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_entity_relationships_updated_at BEFORE UPDATE ON entity_relationships
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_citations_updated_at BEFORE UPDATE ON citations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reference_library_updated_at BEFORE UPDATE ON reference_library
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Hierarchical structure triggers
CREATE TRIGGER trigger_update_block_path
    BEFORE INSERT OR UPDATE OF parent_id
    ON blocks
    FOR EACH ROW
    EXECUTE FUNCTION update_block_path();

-- Document statistics triggers
CREATE TRIGGER update_document_stats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON blocks
    FOR EACH ROW EXECUTE FUNCTION update_document_stats();

-- Content hash triggers
CREATE TRIGGER generate_content_hash_trigger
    BEFORE INSERT OR UPDATE OF content ON blocks
    FOR EACH ROW EXECUTE FUNCTION generate_content_hash();

CREATE TRIGGER generate_version_hash_trigger
    BEFORE INSERT ON versions
    FOR EACH ROW EXECUTE FUNCTION generate_content_hash();

-- Expire stale suggestions trigger
CREATE TRIGGER expire_stale_suggestions_trigger
    AFTER UPDATE OF content_hash ON blocks
    FOR EACH ROW EXECUTE FUNCTION expire_stale_suggestions();