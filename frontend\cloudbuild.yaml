steps:
  # Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/revisionary-frontend:$BUILD_ID',
      '-t', 'gcr.io/$PROJECT_ID/revisionary-frontend:latest',
      '.'
    ]
    dir: 'frontend'

  # Push the Docker image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'push',
      'gcr.io/$PROJECT_ID/revisionary-frontend:$BUILD_ID'
    ]

  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'push',
      'gcr.io/$PROJECT_ID/revisionary-frontend:latest'
    ]

  # Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args: [
      'run', 'deploy', 'revisionary-frontend',
      '--image', 'gcr.io/$PROJECT_ID/revisionary-frontend:$BUILD_ID',
      '--platform', 'managed',
      '--region', 'us-central1',
      '--allow-unauthenticated',
      '--port', '8080',
      '--memory', '512Mi',
      '--cpu', '1',
      '--max-instances', '10',
      '--set-env-vars', 'NODE_ENV=production'
    ]

# Make images available for a reasonable time
images:
  - 'gcr.io/$PROJECT_ID/revisionary-frontend:$BUILD_ID'
  - 'gcr.io/$PROJECT_ID/revisionary-frontend:latest'

# Options for the build
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'