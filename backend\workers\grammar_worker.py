"""
Grammar Worker for Revisionary

Purpose: Specialized worker for grammar checking and correction
- Uses GPT-4.1-nano for cost efficiency
- Focuses on spelling, punctuation, grammar, syntax
- Supports batch processing for efficiency
- Minimal token usage with targeted prompts

Features:
- Real-time grammar error detection
- Batch processing for multiple texts
- Cost-optimized prompts
- Grammar rule explanations
- Context-aware corrections
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import structlog

from workers.base_worker import BaseWorker

logger = structlog.get_logger(__name__)


class GrammarWorker(BaseWorker):
    """Worker specialized for grammar checking and correction."""
    
    def __init__(self, worker_id: Optional[str] = None):
        """Initialize grammar worker."""
        super().__init__('grammar', worker_id)
        
        # Grammar-specific configuration
        self.max_text_length = 1000  # Limit text length for cost control
        self.prompt_templates = self._load_prompt_templates()
        
        logger.info(f"Grammar worker {self.worker_id} initialized")
    
    def _load_prompt_templates(self) -> Dict[str, str]:
        """Load optimized prompt templates for grammar checking."""
        return {
            'grammar_check': """Fix ONLY grammar, spelling, and punctuation errors. Return the corrected text.

Text: {text}

Corrected:""",
            
            'grammar_check_with_explanations': """Fix grammar, spelling, and punctuation errors. List changes made.

Text: {text}

Corrected text:
[corrected text here]

Changes made:
[list of changes]""",
            
            'batch_grammar_check': """Fix grammar, spelling, and punctuation errors for each numbered text. Return numbered corrections.

{batch_texts}

Corrections:""",
            
            'minimal_check': """Fix errors: {text}"""
        }
    
    async def process_job(self, job: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a grammar checking job.
        
        Args:
            job: Job data containing text and options
            
        Returns:
            Dict with corrected text and suggestions
        """
        job_id = job.get('job_id', 'unknown')
        text = job.get('text', '')
        options = job.get('options', {})
        
        # Validate input
        if not text or not text.strip():
            return {
                'job_id': job_id,
                'success': False,
                'error': 'Empty text provided'
            }
        
        # Limit text length for cost control
        original_text = text
        if len(text) > self.max_text_length:
            text = text[:self.max_text_length] + "..."
            logger.info(f"Text truncated for cost control", 
                       original_length=len(original_text),
                       truncated_length=len(text))
        
        try:
            # Choose prompt based on options
            include_explanations = options.get('include_explanations', False)
            prompt_type = 'grammar_check_with_explanations' if include_explanations else 'grammar_check'
            
            # Build prompt
            prompt = self.prompt_templates[prompt_type].format(text=text)
            
            # Get completion from LLM service
            result = await self.llm_service.get_completion(
                agent_type='grammar',
                prompt=prompt,
                context={
                    'job_id': job_id,
                    'text_length': len(text),
                    'include_explanations': include_explanations
                }
            )
            
            # Parse the response
            response_text = result['text'].strip()
            corrected_text, explanations = self._parse_grammar_response(
                response_text, 
                include_explanations
            )
            
            # Track cost
            await self.cost_tracker.track_usage(
                model=result['model'],
                usage=result['usage'],
                agent_type='grammar',
                user_id=job.get('user_id')
            )
            
            # Build result
            grammar_result = {
                'job_id': job_id,
                'success': True,
                'original_text': original_text,
                'corrected_text': corrected_text,
                'has_changes': corrected_text != text,
                'processing_time': result.get('latency', 0),
                'tokens_used': result['usage']['input_tokens'] + result['usage']['output_tokens'],
                'cached_tokens': result['usage'].get('cached_tokens', 0),
                'model_used': result['model']
            }
            
            # Add explanations if requested
            if include_explanations and explanations:
                grammar_result['explanations'] = explanations
            
            # Calculate confidence score based on model response
            grammar_result['confidence'] = self._calculate_confidence(result, text, corrected_text)
            
            logger.info(
                f"Grammar job {job_id} completed",
                has_changes=grammar_result['has_changes'],
                tokens_used=grammar_result['tokens_used'],
                processing_time=grammar_result['processing_time']
            )
            
            return grammar_result
            
        except Exception as e:
            logger.error(f"Grammar job {job_id} failed", error=str(e), exc_info=True)
            return {
                'job_id': job_id,
                'success': False,
                'error': str(e),
                'original_text': original_text
            }
    
    async def _process_job_batch(self, jobs: List[Dict[str, Any]]):
        """Process multiple grammar jobs in a single API call."""
        job_ids = [job.get('job_id', 'unknown') for job in jobs]
        
        logger.info(f"Batch processing {len(jobs)} grammar jobs", job_ids=job_ids)
        
        try:
            # Prepare batch text
            batch_texts = []
            for i, job in enumerate(jobs):
                text = job.get('text', '')[:500]  # Limit each text in batch
                batch_texts.append(f"{i+1}. {text}")
            
            # Build batch prompt
            batch_text_str = '\n'.join(batch_texts)
            prompt = self.prompt_templates['batch_grammar_check'].format(
                batch_texts=batch_text_str
            )
            
            # Get completion
            result = await self.llm_service.get_completion(
                agent_type='grammar',
                prompt=prompt,
                context={
                    'batch': True,
                    'job_count': len(jobs),
                    'batch_ids': job_ids
                }
            )
            
            # Parse batch response
            batch_results = self._parse_batch_response(result['text'], jobs)
            
            # Track cost for batch
            await self.cost_tracker.track_usage(
                model=result['model'],
                usage=result['usage'],
                agent_type='grammar',
                user_id=jobs[0].get('user_id') if jobs else None
            )
            
            # Acknowledge all jobs and publish results
            for job, batch_result in zip(jobs, batch_results):
                message_id = job.get('message_id')
                if message_id:
                    await self.redis.redis_client.xack(
                        self.queue_name,
                        self.consumer_group,
                        message_id
                    )
                
                # Publish result if needed
                if job.get('response_channel'):
                    await self.redis.publish(
                        job['response_channel'], 
                        json.dumps(batch_result)
                    )
            
            logger.info(f"Batch grammar processing completed", 
                       jobs_processed=len(batch_results),
                       total_tokens=result['usage']['input_tokens'] + result['usage']['output_tokens'])
            
        except Exception as e:
            logger.error(f"Batch grammar processing failed", error=str(e), exc_info=True)
            
            # Process jobs individually as fallback
            for job in jobs:
                await self._process_single_job(job)
    
    def _parse_grammar_response(self, response: str, include_explanations: bool) -> tuple[str, Optional[List[str]]]:
        """Parse the grammar checking response."""
        if not include_explanations:
            return response.strip(), None
        
        # Split response into corrected text and explanations
        parts = response.split('Changes made:', 1)
        
        if len(parts) == 2:
            corrected_text = parts[0].replace('Corrected text:', '').strip()
            explanations_text = parts[1].strip()
            
            # Parse explanations
            explanations = []
            for line in explanations_text.split('\n'):
                line = line.strip()
                if line and not line.startswith('[') and not line.endswith(']'):
                    explanations.append(line)
            
            return corrected_text, explanations if explanations else None
        else:
            return response.strip(), None
    
    def _parse_batch_response(self, response: str, jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Parse batch response and create individual results."""
        results = []
        
        # Split response by numbers (1., 2., etc.)
        corrections = []
        lines = response.split('\n')
        current_correction = ""
        
        for line in lines:
            line = line.strip()
            if line and any(line.startswith(f"{i}.") for i in range(1, len(jobs) + 1)):
                if current_correction:
                    corrections.append(current_correction.strip())
                current_correction = line[2:].strip()  # Remove number prefix
            elif current_correction:
                current_correction += " " + line
        
        # Add the last correction
        if current_correction:
            corrections.append(current_correction.strip())
        
        # Create results for each job
        for i, job in enumerate(jobs):
            original_text = job.get('text', '')
            corrected_text = corrections[i] if i < len(corrections) else original_text
            
            result = {
                'job_id': job.get('job_id', 'unknown'),
                'success': True,
                'original_text': original_text,
                'corrected_text': corrected_text,
                'has_changes': corrected_text != original_text[:500],  # Compare with truncated
                'processing_time': 0,  # Batch processing time divided later
                'batch_processed': True
            }
            
            results.append(result)
        
        return results
    
    def _calculate_confidence(self, llm_result: Dict, original: str, corrected: str) -> float:
        """Calculate confidence score for the grammar correction."""
        # Base confidence on various factors
        confidence = 0.8  # Base confidence
        
        # Adjust based on whether changes were made
        if original == corrected:
            confidence = 0.95  # High confidence if no changes needed
        else:
            # Lower confidence if many changes were made
            change_ratio = abs(len(corrected) - len(original)) / len(original)
            if change_ratio > 0.5:
                confidence *= 0.7  # Major changes, lower confidence
            elif change_ratio > 0.2:
                confidence *= 0.85  # Moderate changes
        
        # Adjust based on cached tokens (higher confidence for cached results)
        cached_ratio = llm_result['usage'].get('cached_tokens', 0) / max(llm_result['usage']['input_tokens'], 1)
        if cached_ratio > 0.5:
            confidence = min(0.98, confidence * 1.1)
        
        return round(confidence, 2)
    
    async def get_grammar_stats(self) -> Dict[str, Any]:
        """Get grammar-specific worker statistics."""
        base_stats = await self.get_worker_stats()
        
        # Add grammar-specific metrics
        grammar_stats = {
            **base_stats,
            'max_text_length': self.max_text_length,
            'prompt_templates': list(self.prompt_templates.keys()),
            'supports_batch': True,
            'model_used': 'gpt-4.1-nano-2025-04-14'
        }
        
        return grammar_stats


# Standalone function to run the worker
async def run_grammar_worker():
    """Run a grammar worker instance."""
    worker = GrammarWorker()
    await worker.initialize()
    await worker.start()


if __name__ == "__main__":
    import asyncio
    asyncio.run(run_grammar_worker())