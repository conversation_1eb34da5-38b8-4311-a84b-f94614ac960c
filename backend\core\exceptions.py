"""
Custom Exception Classes for Revisionary Backend

Purpose: Provides specific exception types for better error handling and debugging
- Domain-specific exceptions with structured error codes
- Validation exceptions with field-level error details  
- Service-specific exceptions for external API failures
- HTTP-friendly exception classes with proper status codes

Features:
- Structured error codes for frontend error handling
- Validation error details for form feedback
- Service availability exceptions with retry logic
- Authentication and authorization specific exceptions
"""

from typing import Dict, List, Any, Optional
from fastapi import HTTPEx<PERSON>, status


class RevisionaryException(Exception):
    """Base exception class for all Revisionary-specific errors."""
    
    def __init__(
        self, 
        message: str, 
        error_code: str = "GENERIC_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(RevisionaryException):
    """Exception for data validation errors."""
    
    def __init__(
        self, 
        message: str = "Validation failed", 
        field_errors: Optional[Dict[str, List[str]]] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.field_errors = field_errors or {}
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details={**(details or {}), "field_errors": self.field_errors}
        )


class AuthenticationException(RevisionaryException):
    """Exception for authentication failures."""
    
    def __init__(self, message: str = "Authentication failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            details=details
        )


class AuthorizationException(RevisionaryException):
    """Exception for authorization failures."""
    
    def __init__(self, message: str = "Access denied", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR", 
            details=details
        )


class ResourceNotFoundException(RevisionaryException):
    """Exception for when a requested resource is not found."""
    
    def __init__(
        self, 
        resource_type: str, 
        resource_id: str, 
        message: Optional[str] = None
    ):
        self.resource_type = resource_type
        self.resource_id = resource_id
        
        if not message:
            message = f"{resource_type} with ID '{resource_id}' not found"
            
        super().__init__(
            message=message,
            error_code="RESOURCE_NOT_FOUND",
            details={
                "resource_type": resource_type,
                "resource_id": resource_id
            }
        )


class DuplicateResourceException(RevisionaryException):
    """Exception for when trying to create a resource that already exists."""
    
    def __init__(
        self, 
        resource_type: str, 
        identifier: str,
        identifier_field: str = "id",
        message: Optional[str] = None
    ):
        self.resource_type = resource_type
        self.identifier = identifier
        self.identifier_field = identifier_field
        
        if not message:
            message = f"{resource_type} with {identifier_field} '{identifier}' already exists"
            
        super().__init__(
            message=message,
            error_code="DUPLICATE_RESOURCE",
            details={
                "resource_type": resource_type,
                "identifier": identifier,
                "identifier_field": identifier_field
            }
        )


class ServiceUnavailableException(RevisionaryException):
    """Exception for when an external service is unavailable."""
    
    def __init__(
        self, 
        service_name: str, 
        message: Optional[str] = None,
        retry_after: Optional[int] = None
    ):
        self.service_name = service_name
        self.retry_after = retry_after
        
        if not message:
            message = f"{service_name} service is currently unavailable"
            
        details = {"service_name": service_name}
        if retry_after:
            details["retry_after_seconds"] = retry_after
            
        super().__init__(
            message=message,
            error_code="SERVICE_UNAVAILABLE",
            details=details
        )


class RateLimitException(RevisionaryException):
    """Exception for rate limit violations."""
    
    def __init__(
        self, 
        limit: int, 
        window_seconds: int, 
        retry_after: Optional[int] = None
    ):
        self.limit = limit
        self.window_seconds = window_seconds
        self.retry_after = retry_after
        
        message = f"Rate limit exceeded: {limit} requests per {window_seconds} seconds"
        
        details = {
            "limit": limit,
            "window_seconds": window_seconds
        }
        if retry_after:
            details["retry_after_seconds"] = retry_after
            
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_EXCEEDED",
            details=details
        )


class DatabaseException(RevisionaryException):
    """Exception for database operation failures."""
    
    def __init__(
        self, 
        operation: str, 
        message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.operation = operation
        
        if not message:
            message = f"Database {operation} operation failed"
            
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            details={**(details or {}), "operation": operation}
        )


class LLMServiceException(RevisionaryException):
    """Exception for LLM service failures."""
    
    def __init__(
        self, 
        provider: str, 
        model: str, 
        message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.provider = provider
        self.model = model
        
        if not message:
            message = f"LLM service error with {provider} {model}"
            
        super().__init__(
            message=message,
            error_code="LLM_SERVICE_ERROR",
            details={**(details or {}), "provider": provider, "model": model}
        )


class CitationException(RevisionaryException):
    """Exception for citation processing failures."""
    
    def __init__(
        self, 
        citation_id: str, 
        operation: str, 
        message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.citation_id = citation_id
        self.operation = operation
        
        if not message:
            message = f"Citation {operation} failed for citation {citation_id}"
            
        super().__init__(
            message=message,
            error_code="CITATION_ERROR",
            details={
                **(details or {}), 
                "citation_id": citation_id,
                "operation": operation
            }
        )


class ConfigurationException(RevisionaryException):
    """Exception for configuration errors."""
    
    def __init__(
        self, 
        config_key: str, 
        message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.config_key = config_key
        
        if not message:
            message = f"Configuration error for key '{config_key}'"
            
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            details={**(details or {}), "config_key": config_key}
        )


# HTTP Exception Mappers
def to_http_exception(exc: RevisionaryException) -> HTTPException:
    """Convert custom exceptions to HTTP exceptions."""
    
    # Map error codes to HTTP status codes
    status_code_mapping = {
        "VALIDATION_ERROR": status.HTTP_400_BAD_REQUEST,
        "AUTHENTICATION_ERROR": status.HTTP_401_UNAUTHORIZED,
        "AUTHORIZATION_ERROR": status.HTTP_403_FORBIDDEN,
        "RESOURCE_NOT_FOUND": status.HTTP_404_NOT_FOUND,
        "DUPLICATE_RESOURCE": status.HTTP_409_CONFLICT,
        "RATE_LIMIT_EXCEEDED": status.HTTP_429_TOO_MANY_REQUESTS,
        "SERVICE_UNAVAILABLE": status.HTTP_503_SERVICE_UNAVAILABLE,
        "DATABASE_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
        "LLM_SERVICE_ERROR": status.HTTP_502_BAD_GATEWAY,
        "CITATION_ERROR": status.HTTP_422_UNPROCESSABLE_ENTITY,
        "CONFIGURATION_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
        "GENERIC_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR
    }
    
    status_code = status_code_mapping.get(exc.error_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    # Create structured error response
    error_response = {
        "success": False,
        "error": {
            "code": exc.error_code,
            "message": exc.message,
            "details": exc.details
        }
    }
    
    # Add retry-after header for rate limits and service unavailable
    headers = {}
    if exc.error_code in ["RATE_LIMIT_EXCEEDED", "SERVICE_UNAVAILABLE"]:
        retry_after = exc.details.get("retry_after_seconds")
        if retry_after:
            headers["Retry-After"] = str(retry_after)
    
    return HTTPException(
        status_code=status_code,
        detail=error_response,
        headers=headers if headers else None
    )


# Validation helpers
def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> None:
    """Validate that all required fields are present and not empty."""
    field_errors = {}
    
    for field in required_fields:
        value = data.get(field)
        if value is None:
            field_errors[field] = [f"{field} is required"]
        elif isinstance(value, str) and not value.strip():
            field_errors[field] = [f"{field} cannot be empty"]
        elif isinstance(value, list) and len(value) == 0:
            field_errors[field] = [f"{field} cannot be empty"]
    
    if field_errors:
        raise ValidationException(
            message="Required field validation failed",
            field_errors=field_errors
        )


def validate_field_length(
    data: Dict[str, Any], 
    field_name: str, 
    min_length: Optional[int] = None,
    max_length: Optional[int] = None
) -> None:
    """Validate field length constraints."""
    value = data.get(field_name)
    if value is None:
        return
        
    if not isinstance(value, str):
        return
        
    errors = []
    if min_length and len(value) < min_length:
        errors.append(f"{field_name} must be at least {min_length} characters")
    
    if max_length and len(value) > max_length:
        errors.append(f"{field_name} must be no more than {max_length} characters")
    
    if errors:
        raise ValidationException(
            message=f"Field length validation failed for {field_name}",
            field_errors={field_name: errors}
        )