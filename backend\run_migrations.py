#!/usr/bin/env python3
"""
Revisionary Database Migration Runner

This script runs all database migrations in the correct order.
It can be used for initial setup or to apply new migrations.

Usage:
    python run_migrations.py --env development
    python run_migrations.py --env production --confirm
"""

import asyncio
import os
import sys
import argparse
from pathlib import Path
from typing import List
import asyncpg
import structlog
from dotenv import load_dotenv

# Add backend to path for imports
sys.path.append(str(Path(__file__).parent))

from core.settings import settings

logger = structlog.get_logger(__name__)


class MigrationRunner:
    """Database migration runner for Revisionary."""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.migrations_dir = Path(__file__).parent / "migrations"
        
    async def get_connection(self) -> asyncpg.Connection:
        """Get database connection."""
        # Disable prepared statements for Supabase transaction pooler
        return await asyncpg.connect(self.database_url, statement_cache_size=0)
    
    async def create_migrations_table(self, conn: asyncpg.Connection) -> None:
        """Create migrations tracking table if it doesn't exist."""
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS schema_migrations (
                version VARCHAR(255) PRIMARY KEY,
                executed_at TIMESTAMPTZ DEFAULT NOW()
            );
        """)
        logger.info("Migrations table ready")
    
    async def get_applied_migrations(self, conn: asyncpg.Connection) -> List[str]:
        """Get list of already applied migrations."""
        rows = await conn.fetch("SELECT version FROM schema_migrations ORDER BY version")
        return [row['version'] for row in rows]
    
    async def mark_migration_applied(self, conn: asyncpg.Connection, version: str) -> None:
        """Mark a migration as applied."""
        await conn.execute(
            "INSERT INTO schema_migrations (version) VALUES ($1)",
            version
        )
    
    def get_migration_files(self) -> List[Path]:
        """Get sorted list of migration files."""
        migration_files = list(self.migrations_dir.glob("*.sql"))
        return sorted(migration_files)
    
    async def run_migration_file(self, conn: asyncpg.Connection, file_path: Path) -> None:
        """Run a single migration file."""
        logger.info("Running migration", file=file_path.name)
        
        # Read migration file
        with open(file_path, 'r', encoding='utf-8') as f:
            migration_sql = f.read()
        
        logger.info("Migration file loaded", file=file_path.name, size_chars=len(migration_sql))
        
        # Execute migration
        try:
            logger.info("Executing migration SQL", file=file_path.name)
            await conn.execute(migration_sql)
            logger.info("Migration completed successfully", file=file_path.name)
        except Exception as e:
            logger.error("Migration failed", file=file_path.name, error=str(e))
            raise
    
    async def run_migrations(self, dry_run: bool = False) -> None:
        """Run all pending migrations."""
        logger.info("Starting migration process", dry_run=dry_run)
        
        conn = await self.get_connection()
        try:
            # Create migrations table
            await self.create_migrations_table(conn)
            
            # Get applied migrations
            applied_migrations = await self.get_applied_migrations(conn)
            logger.info("Found applied migrations", count=len(applied_migrations))
            
            # Get migration files
            migration_files = self.get_migration_files()
            logger.info("Found migration files", count=len(migration_files))
            
            if not migration_files:
                logger.warning("No migration files found")
                return
            
            # Run pending migrations
            pending_count = 0
            
            for migration_file in migration_files:
                version = migration_file.stem  # filename without extension
                
                if version in applied_migrations:
                    logger.debug("Skipping applied migration", version=version)
                    continue
                
                if dry_run:
                    logger.info("Would run migration", version=version)
                    pending_count += 1
                else:
                    # Run migration in transaction
                    async with conn.transaction():
                        await self.run_migration_file(conn, migration_file)
                        await self.mark_migration_applied(conn, version)
                    
                    pending_count += 1
                    logger.info("Applied migration", version=version)
            
            if pending_count == 0:
                logger.info("No pending migrations")
            else:
                action = "would apply" if dry_run else "applied"
                logger.info(f"Migration process complete - {action} {pending_count} migrations")
                
        finally:
            await conn.close()
    
    async def check_connection(self) -> bool:
        """Test database connection."""
        try:
            conn = await self.get_connection()
            await conn.fetchval("SELECT 1")
            await conn.close()
            logger.info("Database connection successful")
            return True
        except Exception as e:
            logger.error("Database connection failed", error=str(e))
            return False


async def main():
    """Main migration runner."""
    parser = argparse.ArgumentParser(description="Run Revisionary database migrations")
    parser.add_argument(
        "--env",
        choices=["development", "staging", "production"],
        default="development",
        help="Environment to run migrations against"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what migrations would be run without executing them"
    )
    parser.add_argument(
        "--confirm",
        action="store_true",
        help="Required for production environment"
    )
    parser.add_argument(
        "--database-url",
        help="Override database URL from environment"
    )
    
    args = parser.parse_args()
    
    # Load environment variables
    if args.env == "development":
        load_dotenv(".env")
    elif args.env == "staging":
        load_dotenv(".env.staging")
    elif args.env == "production":
        load_dotenv(".env.production")
    
    # Production safety check
    if args.env == "production" and not args.confirm:
        logger.error("Production migrations require --confirm flag")
        sys.exit(1)
    
    # Get database URL
    database_url = args.database_url or settings.database.database_url
    
    if not database_url:
        logger.error("No database URL provided")
        sys.exit(1)
    
    # Initialize migration runner
    runner = MigrationRunner(database_url)
    
    # Test connection
    if not await runner.check_connection():
        logger.error("Cannot connect to database")
        sys.exit(1)
    
    # Confirm production run
    if args.env == "production" and not args.dry_run:
        response = input(f"Are you sure you want to run migrations against PRODUCTION? Type 'yes' to continue: ")
        if response.lower() != 'yes':
            logger.info("Migration cancelled")
            sys.exit(0)
    
    # Run migrations
    try:
        await runner.run_migrations(dry_run=args.dry_run)
        logger.info("Migration process completed successfully")
    except Exception as e:
        logger.error("Migration process failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    # Configure logging
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    asyncio.run(main())