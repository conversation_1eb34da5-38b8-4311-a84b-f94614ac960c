import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import {
  XMarkIcon,
  PlusIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  InformationCircleIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';
import { CustomAgent, AgentCapability, PredefinedRules, AgentTool } from '../../types/agent';
import { useAgentStore } from '../../stores/agentStore';

interface AgentEditorProps {
  agent?: CustomAgent | null;
  onClose: () => void;
  onSave?: (agent: CustomAgent) => void;
}

const defaultCapabilities: AgentCapability[] = [
  {
    id: 'edit',
    action: 'edit',
    name: 'Edit Content',
    description: 'Make corrections and improvements to existing text',
    enabled: true,
  },
  {
    id: 'delete',
    action: 'delete',
    name: 'Delete Content',
    description: 'Remove redundant or unnecessary content',
    enabled: false,
  },
  {
    id: 'add',
    action: 'add',
    name: 'Add Content',
    description: 'Suggest missing information or content',
    enabled: false,
  },
  {
    id: 'restructure',
    action: 'restructure',
    name: 'Restructure',
    description: 'Reorganize content for better flow and organization',
    enabled: false,
  },
  {
    id: 'find-duplicates',
    action: 'find-duplicates',
    name: 'Find Duplicates',
    description: 'Identify repetitive or duplicate content',
    enabled: false,
  },
  {
    id: 'fact-check',
    action: 'fact-check',
    name: 'Fact Check',
    description: 'Verify accuracy of factual claims',
    enabled: false,
  },
  {
    id: 'tone-adjust',
    action: 'tone-adjust',
    name: 'Tone Adjustment',
    description: 'Modify voice and tone to match requirements',
    enabled: false,
  },
];

const defaultPredefinedRules: PredefinedRules = {
  oxfordComma: true,
  sentenceLength: {
    max: 30,
    preferred: 20,
  },
  passiveVoice: {
    maxPercentage: 10,
  },
  paragraphLength: {
    maxSentences: 5,
  },
  vocabulary: {
    preferredTerms: {},
    avoidTerms: [],
  },
};

const availableColors = [
  { name: 'Blue', value: 'blue' },
  { name: 'Green', value: 'green' },
  { name: 'Purple', value: 'purple' },
  { name: 'Orange', value: 'orange' },
  { name: 'Pink', value: 'pink' },
  { name: 'Red', value: 'red' },
  { name: 'Yellow', value: 'yellow' },
  { name: 'Indigo', value: 'indigo' },
  { name: 'Teal', value: 'teal' },
  { name: 'Cyan', value: 'cyan' },
];

// Available tools based on agent specialty
const availableTools: AgentTool[] = [
  // Grammar tools
  {
    id: 'grammar-check',
    name: 'Grammar Checker',
    description: 'Detect and fix grammatical errors',
    category: 'grammar',
    enabled: false,
  },
  {
    id: 'punctuation-analysis',
    name: 'Punctuation Analysis',
    description: 'Check punctuation usage and consistency',
    category: 'grammar',
    enabled: false,
  },
  {
    id: 'spelling-check',
    name: 'Spelling Checker',
    description: 'Identify and correct spelling mistakes',
    category: 'grammar',
    enabled: false,
  },
  // Style tools
  {
    id: 'tone-analyzer',
    name: 'Tone Analyzer',
    description: 'Analyze and adjust writing tone',
    category: 'style',
    enabled: false,
  },
  {
    id: 'readability-scorer',
    name: 'Readability Scorer',
    description: 'Assess text complexity and readability',
    category: 'style',
    enabled: false,
  },
  {
    id: 'voice-consistency',
    name: 'Voice Consistency',
    description: 'Ensure consistent voice throughout',
    category: 'style',
    enabled: false,
  },
  // Structure tools
  {
    id: 'structure-analyzer',
    name: 'Structure Analyzer',
    description: 'Evaluate document organization',
    category: 'structure',
    enabled: false,
  },
  {
    id: 'flow-checker',
    name: 'Flow Checker',
    description: 'Assess logical flow and transitions',
    category: 'structure',
    enabled: false,
  },
  {
    id: 'outline-generator',
    name: 'Outline Generator',
    description: 'Create document outlines',
    category: 'structure',
    enabled: false,
  },
  // Content tools
  {
    id: 'completeness-checker',
    name: 'Completeness Checker',
    description: 'Identify missing content or gaps',
    category: 'content',
    enabled: false,
  },
  {
    id: 'relevance-analyzer',
    name: 'Relevance Analyzer',
    description: 'Assess content relevance to topic',
    category: 'content',
    enabled: false,
  },
  {
    id: 'depth-assessor',
    name: 'Depth Assessor',
    description: 'Evaluate content depth and detail',
    category: 'content',
    enabled: false,
  },
  // Verification tools
  {
    id: 'fact-verifier',
    name: 'Fact Verifier',
    description: 'Verify factual claims and statements',
    category: 'verification',
    enabled: false,
  },
  {
    id: 'source-checker',
    name: 'Source Checker',
    description: 'Validate sources and references',
    category: 'verification',
    enabled: false,
  },
  {
    id: 'claim-analyzer',
    name: 'Claim Analyzer',
    description: 'Analyze unsupported claims',
    category: 'verification',
    enabled: false,
  },
  // Optimization tools
  {
    id: 'duplicate-detector',
    name: 'Duplicate Detector',
    description: 'Find repetitive or duplicate content',
    category: 'optimization',
    enabled: false,
  },
  {
    id: 'conciseness-optimizer',
    name: 'Conciseness Optimizer',
    description: 'Suggest more concise phrasing',
    category: 'optimization',
    enabled: false,
  },
  {
    id: 'redundancy-eliminator',
    name: 'Redundancy Eliminator',
    description: 'Remove unnecessary repetition',
    category: 'optimization',
    enabled: false,
  },
];

const AgentEditor: React.FC<AgentEditorProps> = ({ agent, onClose, onSave }) => {
  const { createAgent, updateAgent } = useAgentStore();
  const isEditing = !!agent;

  // Form state
  const [name, setName] = useState(agent?.name || '');
  const [description, setDescription] = useState(agent?.description || '');
  const [specialty, setSpecialty] = useState(agent?.specialty || '');
  const [color, setColor] = useState(agent?.color || 'blue');
  const [systemPrompt, setSystemPrompt] = useState(agent?.systemPrompt || '');
  const [customInstructions, setCustomInstructions] = useState(agent?.customInstructions || '');
  const [capabilities, setCapabilities] = useState<AgentCapability[]>(
    agent?.capabilities || defaultCapabilities
  );
  const [tools, setTools] = useState<AgentTool[]>(
    agent?.tools || availableTools.map(tool => ({ ...tool }))
  );
  const [predefinedRules] = useState<PredefinedRules>(
    agent?.predefinedRules || defaultPredefinedRules
  );
  const [tags, setTags] = useState<string[]>(agent?.tags || []);
  const [newTag, setNewTag] = useState('');
  const [priority, setPriority] = useState(agent?.priority || 10);

  // UI state
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) newErrors.name = 'Agent name is required';
    if (!description.trim()) newErrors.description = 'Description is required';
    if (!specialty.trim()) newErrors.specialty = 'Specialty is required';
    if (!systemPrompt.trim()) newErrors.systemPrompt = 'System prompt is required';
    if (capabilities.filter(c => c.enabled).length === 0) {
      newErrors.capabilities = 'At least one action must be enabled';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!validateForm()) return;

    const agentData = {
      name,
      description,
      specialty,
      color,
      systemPrompt,
      customInstructions,
      capabilities,
      tools: tools.filter(t => t.enabled), // Only save enabled tools
      styleRules: [], // Will be managed by StyleRuleEditor
      predefinedRules,
      tags,
      priority,
      isActive: true,
      isBuiltIn: false,
    };

    if (isEditing && agent) {
      updateAgent(agent.id, agentData);
    } else {
      createAgent(agentData);
    }

    if (onSave) {
      onSave({
        ...agentData,
        id: agent?.id || '',
        createdAt: agent?.createdAt || new Date(),
        updatedAt: new Date(),
      });
    }
    onClose();
  };

  const toggleCapability = (capId: string) => {
    setCapabilities(caps =>
      caps.map(cap =>
        cap.id === capId ? { ...cap, enabled: !cap.enabled } : cap
      )
    );
  };

  const toggleTool = (toolId: string) => {
    setTools(tools =>
      tools.map(tool =>
        tool.id === toolId ? { ...tool, enabled: !tool.enabled } : tool
      )
    );
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag));
  };

  const modalContent = (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[9999]">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col"
      >
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            {isEditing ? 'Edit Agent' : 'Create New Agent'}
          </h2>
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-500 rounded"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Agent Name *
                </label>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className={`input-field ${errors.name ? 'border-red-300' : ''}`}
                  placeholder="e.g., Academic Editor"
                />
                {errors.name && (
                  <p className="text-xs text-red-600 mt-1">{errors.name}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description *
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className={`input-field min-h-[80px] ${errors.description ? 'border-red-300' : ''}`}
                  placeholder="Describe what this agent does..."
                />
                {errors.description && (
                  <p className="text-xs text-red-600 mt-1">{errors.description}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Specialty *
                </label>
                <input
                  type="text"
                  value={specialty}
                  onChange={(e) => setSpecialty(e.target.value)}
                  className={`input-field ${errors.specialty ? 'border-red-300' : ''}`}
                  placeholder="e.g., Academic Writing"
                />
                {errors.specialty && (
                  <p className="text-xs text-red-600 mt-1">{errors.specialty}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Color Theme
                </label>
                <div className="grid grid-cols-5 gap-2">
                  {availableColors.map((colorOption) => (
                    <button
                      key={colorOption.value}
                      onClick={() => setColor(colorOption.value)}
                      className={`h-10 rounded-lg border-2 transition-all ${
                        color === colorOption.value
                          ? 'border-gray-900 scale-110'
                          : 'border-gray-300 hover:border-gray-400'
                      } bg-${colorOption.value}-500`}
                      title={colorOption.name}
                    />
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tags
                </label>
                <div className="flex items-center gap-2 mb-2">
                  <input
                    type="text"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    className="input-field flex-1"
                    placeholder="Add a tag..."
                  />
                  <button
                    onClick={addTag}
                    className="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg"
                  >
                    <PlusIcon className="w-4 h-4" />
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700"
                    >
                      #{tag}
                      <button
                        onClick={() => removeTag(tag)}
                        className="ml-1 text-gray-400 hover:text-gray-500"
                      >
                        <XMarkIcon className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Agent Actions & Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Agent Actions</h3>
              <p className="text-sm text-gray-600 mb-4">
                What can this agent do to improve content?
              </p>

              <div className="space-y-2">
                {capabilities.map((capability) => (
                  <label
                    key={capability.id}
                    className="flex items-start p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                  >
                    <input
                      type="checkbox"
                      checked={capability.enabled}
                      onChange={() => toggleCapability(capability.id)}
                      className="mt-0.5 rounded"
                    />
                    <div className="ml-3 flex-1">
                      <div className="font-medium text-sm text-gray-900">
                        {capability.name}
                      </div>
                      <div className="text-xs text-gray-500">
                        {capability.description}
                      </div>
                    </div>
                  </label>
                ))}
                {errors.capabilities && (
                  <p className="text-xs text-red-600">{errors.capabilities}</p>
                )}
                <p className="text-xs text-gray-500 mt-2">
                  These determine what types of changes the agent can make to your document.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Priority (1-100)
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={priority}
                  onChange={(e) => setPriority(Number(e.target.value))}
                  className="input-field"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Higher priority agents appear first in the list
                </p>
              </div>
            </div>
          </div>

          {/* System Prompt & Instructions */}
          <div className="mt-6 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                System Prompt *
              </label>
              <textarea
                value={systemPrompt}
                onChange={(e) => setSystemPrompt(e.target.value)}
                className={`input-field min-h-[120px] font-mono text-sm ${
                  errors.systemPrompt ? 'border-red-300' : ''
                }`}
                placeholder="You are a [role]. Your focus is on [specific tasks]. You should [behavioral instructions]..."
              />
              {errors.systemPrompt && (
                <p className="text-xs text-red-600 mt-1">{errors.systemPrompt}</p>
              )}
              <p className="text-xs text-gray-500 mt-1">
                This defines the agent's core behavior and expertise
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Custom Instructions
              </label>
              <textarea
                value={customInstructions}
                onChange={(e) => setCustomInstructions(e.target.value)}
                className="input-field min-h-[100px]"
                placeholder="Additional specific instructions for this agent..."
              />
              <p className="text-xs text-gray-500 mt-1">
                Optional additional guidance for the agent's behavior
              </p>
            </div>
          </div>

          {/* Analysis Tools */}
          <div className="mt-6">
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center text-sm font-medium text-gray-700 hover:text-gray-900"
            >
              {showAdvanced ? (
                <ChevronUpIcon className="w-4 h-4 mr-1" />
              ) : (
                <ChevronDownIcon className="w-4 h-4 mr-1" />
              )}
              Analysis Tools
            </button>
            <p className="text-xs text-gray-500 mt-1">
              How does this agent analyze content to determine what actions to take?
            </p>

            <AnimatePresence>
              {showAdvanced && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="mt-4 space-y-4 overflow-hidden"
                >
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <InformationCircleIcon className="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                      <div className="text-sm text-blue-800">
                        <p className="font-medium mb-1">How It Works</p>
                        <p className="text-xs mb-2">
                          <strong>Analysis Tools</strong> help the agent detect issues in content (e.g., Grammar Checker finds errors).
                        </p>
                        <p className="text-xs">
                          <strong>Agent Actions</strong> determine what the agent can do about those issues (e.g., Edit to fix errors).
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Group tools by category */}
                  <div className="space-y-6">
                    {(['grammar', 'style', 'structure', 'content', 'verification', 'optimization'] as const).map((category) => {
                      const categoryTools = tools.filter(t => t.category === category);
                      const enabledCount = categoryTools.filter(t => t.enabled).length;
                      
                      return (
                        <div key={category} className="space-y-2">
                          <h4 className="text-sm font-medium text-gray-900 capitalize flex items-center justify-between">
                            {category} Analysis
                            {enabledCount > 0 && (
                              <span className="text-xs text-gray-500 font-normal">
                                {enabledCount} of {categoryTools.length} enabled
                              </span>
                            )}
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            {categoryTools.map((tool) => (
                              <label
                                key={tool.id}
                                className="flex items-start p-2 border rounded-lg cursor-pointer hover:bg-gray-50"
                              >
                                <input
                                  type="checkbox"
                                  checked={tool.enabled}
                                  onChange={() => toggleTool(tool.id)}
                                  className="mt-0.5 rounded"
                                />
                                <div className="ml-3 flex-1">
                                  <div className="font-medium text-xs text-gray-900">
                                    {tool.name}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {tool.description}
                                  </div>
                                </div>
                              </label>
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
          <div className="flex items-center text-xs text-gray-500">
            <SparklesIcon className="w-4 h-4 mr-1" />
            {isEditing ? 'Editing existing agent' : 'Creating new agent'}
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-700"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="btn-primary"
            >
              {isEditing ? 'Save Changes' : 'Create Agent'}
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  );

  // Render the modal using a portal to bypass stacking context issues
  return createPortal(modalContent, document.body);
};

export default AgentEditor;