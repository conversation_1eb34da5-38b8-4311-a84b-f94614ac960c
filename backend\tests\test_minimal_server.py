"""
Pytest tests for the ultra-minimal test server.
"""
import httpx
import pytest
from fastapi import FastAPI

# Create the most basic FastAPI app possible
app = FastAPI(
    title="Ultra Minimal Test",
    docs_url="/docs",  # Enable docs
    redoc_url=None
)

@app.get("/")
def read_root():
    return {"message": "Hello World"}

@app.get("/health")
def health():
    return {"status": "ok"}

@app.get("/test")
def test():
    return {"test": "passed", "server": "minimal"}

@pytest.fixture(scope="module")
def client():
    with httpx.Client(app=app, base_url="http://test") as client:
        yield client

def test_health_check(client):
    """Test the /health endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}

def test_test_endpoint(client):
    """Test the /test endpoint."""
    response = client.get("/test")
    assert response.status_code == 200
    assert response.json() == {"test": "passed", "server": "minimal"}

def test_docs_endpoint(client):
    """Test that the /docs endpoint is enabled."""
    response = client.get("/docs")
    assert response.status_code == 200
    assert "<title>Ultra Minimal Test - Swagger UI</title>" in response.text