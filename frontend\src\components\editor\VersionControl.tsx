import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ClockIcon,
  CodeBracketIcon,
  EyeIcon,
  ArrowUturnLeftIcon,
  PlusIcon,
  DocumentDuplicateIcon,
  TagIcon,
  CalendarIcon,
  UserIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  XMarkIcon,
  ArrowsRightLeftIcon,
  CheckIcon,
} from '@heroicons/react/24/outline';

interface Checkpoint {
  id: string;
  timestamp: Date;
  message?: string;
  wordCount: number;
  author: string;
  isAutoSave: boolean;
  changes?: {
    added: number;
    removed: number;
  };
}

interface DraftVersion {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  lastModified: Date;
  wordCount: number;
  author: string;
  isActive: boolean;
  checkpoints: Checkpoint[];
  parentVersionId?: string;
}

interface VersionControlProps {
  isOpen: boolean;
  onClose: () => void;
  currentVersion: DraftVersion;
  versions: DraftVersion[];
  onCreateVersion: (name: string, description?: string) => void;
  onSwitchVersion: (versionId: string) => void;
  onMergeVersions: (sourceId: string, targetId: string) => void;
  onRestoreCheckpoint: (checkpointId: string) => void;
  onCreateCheckpoint: (message?: string) => void;
  selectedCheckpoint?: string;
  onPreviewCheckpoint: (checkpointId: string) => void;
}

const VersionControl: React.FC<VersionControlProps> = ({
  isOpen,
  onClose,
  currentVersion,
  versions,
  onCreateVersion,
  onSwitchVersion,
  onMergeVersions,
  onRestoreCheckpoint,
  onCreateCheckpoint,
  selectedCheckpoint,
  onPreviewCheckpoint,
}) => {
  const [activeTab, setActiveTab] = useState<'versions' | 'history'>('versions');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newVersionName, setNewVersionName] = useState('');
  const [newVersionDescription, setNewVersionDescription] = useState('');
  const [timelinePosition, setTimelinePosition] = useState(100);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const allCheckpoints = currentVersion.checkpoints.sort(
    (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
  );

  const handleCreateVersion = () => {
    if (newVersionName.trim()) {
      onCreateVersion(newVersionName.trim(), newVersionDescription.trim() || undefined);
      setNewVersionName('');
      setNewVersionDescription('');
      setShowCreateForm(false);
    }
  };

  const handleTimelineChange = (position: number) => {
    setTimelinePosition(position);
    const checkpointIndex = Math.floor((position / 100) * (allCheckpoints.length - 1));
    const checkpoint = allCheckpoints[checkpointIndex];
    if (checkpoint) {
      onPreviewCheckpoint(checkpoint.id);
      setIsPreviewMode(position < 100);
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-[9998]"
            onClick={onClose}
          />

          {/* Panel */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            className="fixed inset-0 flex items-center justify-center z-[9999] p-8"
          >
            <div className="w-full max-w-4xl max-h-[90vh] bg-white/95 backdrop-blur-xl border border-slate-200/50 rounded-2xl shadow-2xl flex flex-col overflow-hidden">
            {/* Header */}
            <div className="px-6 py-4 border-b border-slate-200/50 bg-gradient-to-r from-blue-50/50 to-green-50/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-xl flex items-center justify-center">
                    <ClockIcon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg font-bold text-slate-900">Version Control</h2>
                    <p className="text-sm text-slate-600">
                      Current: {currentVersion.name} • {currentVersion.checkpoints.length} checkpoints
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {isPreviewMode && (
                    <div className="flex items-center space-x-2 px-3 py-1.5 bg-amber-100 text-amber-800 rounded-lg text-sm font-medium">
                      <EyeIcon className="w-4 h-4" />
                      Preview Mode
                      <button
                        onClick={() => {
                          setTimelinePosition(100);
                          setIsPreviewMode(false);
                        }}
                        className="ml-2 p-1 hover:bg-amber-200 rounded"
                      >
                        <XMarkIcon className="w-3 h-3" />
                      </button>
                    </div>
                  )}
                  
                  <button
                    onClick={onClose}
                    className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Tabs */}
              <div className="flex mt-4 bg-white/60 rounded-xl p-1">
                <button
                  onClick={() => setActiveTab('versions')}
                  className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg text-sm font-semibold transition-all ${
                    activeTab === 'versions'
                      ? 'bg-white text-slate-900 shadow-sm'
                      : 'text-slate-600 hover:text-slate-900'
                  }`}
                >
                  <CodeBracketIcon className="w-4 h-4 mr-2" />
                  Draft Versions ({versions.length})
                </button>
                <button
                  onClick={() => setActiveTab('history')}
                  className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg text-sm font-semibold transition-all ${
                    activeTab === 'history'
                      ? 'bg-white text-slate-900 shadow-sm'
                      : 'text-slate-600 hover:text-slate-900'
                  }`}
                >
                  <ClockIcon className="w-4 h-4 mr-2" />
                  History ({allCheckpoints.length})
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-hidden">
              <AnimatePresence mode="wait">
                {activeTab === 'versions' ? (
                  <motion.div
                    key="versions"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="h-full p-6"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-slate-900">Draft Versions</h3>
                      <button
                        onClick={() => setShowCreateForm(true)}
                        className="flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-green-500 text-white rounded-lg font-medium hover:shadow-md transition-all"
                      >
                        <PlusIcon className="w-4 h-4 mr-2" />
                        New Version
                      </button>
                    </div>

                    {/* Create Version Form */}
                    <AnimatePresence>
                      {showCreateForm && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="mb-6 p-4 bg-slate-50 rounded-xl border border-slate-200"
                        >
                          <div className="space-y-3">
                            <div>
                              <label className="block text-sm font-medium text-slate-700 mb-1">
                                Version Name *
                              </label>
                              <input
                                type="text"
                                value={newVersionName}
                                onChange={(e) => setNewVersionName(e.target.value)}
                                placeholder="e.g., Chapter 3 Revision, Alternative Ending"
                                className="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-slate-700 mb-1">
                                Description (optional)
                              </label>
                              <textarea
                                value={newVersionDescription}
                                onChange={(e) => setNewVersionDescription(e.target.value)}
                                placeholder="What changes are you exploring in this version?"
                                rows={2}
                                className="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                              />
                            </div>
                            <div className="flex space-x-2">
                              <button
                                onClick={handleCreateVersion}
                                disabled={!newVersionName.trim()}
                                className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium disabled:bg-slate-400 hover:bg-blue-700 transition-colors"
                              >
                                Create Version
                              </button>
                              <button
                                onClick={() => setShowCreateForm(false)}
                                className="px-4 py-2 bg-slate-200 text-slate-700 rounded-lg text-sm font-medium hover:bg-slate-300 transition-colors"
                              >
                                Cancel
                              </button>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>

                    {/* Versions List */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-64 overflow-y-auto">
                      {versions.map((version, index) => (
                        <motion.div
                          key={version.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className={`p-4 rounded-xl border-2 transition-all duration-200 hover:shadow-md ${
                            version.isActive
                              ? 'border-blue-200 bg-blue-50/50'
                              : 'border-slate-200 bg-white hover:border-slate-300'
                          }`}
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center space-x-2">
                              <div className={`w-3 h-3 rounded-full ${
                                version.isActive ? 'bg-blue-500' : 'bg-slate-300'
                              }`} />
                              <h4 className="font-semibold text-slate-900">{version.name}</h4>
                            </div>
                            {version.isActive && (
                              <span className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full font-medium">
                                Active
                              </span>
                            )}
                          </div>

                          {version.description && (
                            <p className="text-sm text-slate-600 mb-3 line-clamp-2">
                              {version.description}
                            </p>
                          )}

                          <div className="space-y-2 text-xs text-slate-500">
                            <div className="flex items-center space-x-2">
                              <CalendarIcon className="w-4 h-4" />
                              <span>Created {formatTimeAgo(version.createdAt)}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <DocumentDuplicateIcon className="w-4 h-4" />
                              <span>{version.wordCount} words</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <UserIcon className="w-4 h-4" />
                              <span>{version.author}</span>
                            </div>
                          </div>

                          <div className="mt-4 flex space-x-2">
                            {!version.isActive && (
                              <button
                                onClick={() => onSwitchVersion(version.id)}
                                className="flex-1 px-3 py-1.5 bg-blue-600 text-white rounded-lg text-xs font-medium hover:bg-blue-700 transition-colors"
                              >
                                Switch
                              </button>
                            )}
                            <button
                              onClick={() => onMergeVersions(version.id, currentVersion.id)}
                              className="flex-1 px-3 py-1.5 bg-slate-200 text-slate-700 rounded-lg text-xs font-medium hover:bg-slate-300 transition-colors"
                            >
                              <ArrowsRightLeftIcon className="w-3 h-3 inline mr-1" />
                              Merge
                            </button>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="history"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="h-full p-6"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-slate-900">Version History</h3>
                      <button
                        onClick={() => onCreateCheckpoint()}
                        className="flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-lg font-medium hover:shadow-md transition-all"
                      >
                        <TagIcon className="w-4 h-4 mr-2" />
                        Save Checkpoint
                      </button>
                    </div>

                    {/* History Timeline Slider */}
                    <div className="mb-6 p-4 bg-gradient-to-r from-slate-50 to-blue-50 rounded-xl">
                      <div className="flex items-center justify-between mb-3">
                        <span className="text-sm font-medium text-slate-700">Time Machine</span>
                        <span className="text-xs text-slate-500">
                          {isPreviewMode ? 'Previewing' : 'Current'} • {allCheckpoints.length} checkpoints
                        </span>
                      </div>
                      
                      <div className="relative">
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={timelinePosition}
                          onChange={(e) => handleTimelineChange(Number(e.target.value))}
                          className="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer"
                          style={{
                            background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${timelinePosition}%, #e2e8f0 ${timelinePosition}%, #e2e8f0 100%)`
                          }}
                        />
                        <div className="flex justify-between text-xs text-slate-500 mt-2">
                          <span>Earliest</span>
                          <span>Latest</span>
                        </div>
                      </div>

                      {isPreviewMode && (
                        <div className="mt-3 flex space-x-2">
                          <button
                            onClick={() => {
                              const checkpointIndex = Math.floor((timelinePosition / 100) * (allCheckpoints.length - 1));
                              const checkpoint = allCheckpoints[checkpointIndex];
                              if (checkpoint) {
                                onRestoreCheckpoint(checkpoint.id);
                                setTimelinePosition(100);
                                setIsPreviewMode(false);
                              }
                            }}
                            className="flex items-center px-3 py-1.5 bg-green-600 text-white rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
                          >
                            <ArrowUturnLeftIcon className="w-4 h-4 mr-1" />
                            Restore This Version
                          </button>
                          <button
                            onClick={() => {
                              setTimelinePosition(100);
                              setIsPreviewMode(false);
                            }}
                            className="px-3 py-1.5 bg-slate-200 text-slate-700 rounded-lg text-sm font-medium hover:bg-slate-300 transition-colors"
                          >
                            Cancel Preview
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Checkpoints List */}
                    <div className="max-h-40 overflow-y-auto space-y-2">
                      {allCheckpoints.map((checkpoint, index) => (
                        <motion.div
                          key={checkpoint.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className={`flex items-center p-3 rounded-lg border transition-all duration-200 hover:shadow-sm ${
                            selectedCheckpoint === checkpoint.id
                              ? 'border-blue-200 bg-blue-50'
                              : 'border-slate-200 bg-white hover:border-slate-300'
                          }`}
                        >
                          <div className="flex-1">
                            <div className="flex items-center space-x-3">
                              <div className={`w-2 h-2 rounded-full ${
                                checkpoint.isAutoSave ? 'bg-slate-400' : 'bg-green-500'
                              }`} />
                              <span className="text-sm font-medium text-slate-900">
                                {checkpoint.message || (checkpoint.isAutoSave ? 'Auto-save' : 'Manual checkpoint')}
                              </span>
                              <span className="text-xs text-slate-500">
                                {formatTimeAgo(checkpoint.timestamp)}
                              </span>
                            </div>
                            <div className="mt-1 flex items-center space-x-4 text-xs text-slate-500">
                              <span>{checkpoint.wordCount} words</span>
                              {checkpoint.changes && (
                                <span>
                                  <span className="text-green-600">+{checkpoint.changes.added}</span>
                                  {' '}
                                  <span className="text-red-600">-{checkpoint.changes.removed}</span>
                                </span>
                              )}
                              <span>{checkpoint.author}</span>
                            </div>
                          </div>
                          
                          <div className="flex space-x-2">
                            <button
                              onClick={() => onPreviewCheckpoint(checkpoint.id)}
                              className="p-1.5 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
                              title="Preview"
                            >
                              <EyeIcon className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => onRestoreCheckpoint(checkpoint.id)}
                              className="p-1.5 text-green-600 hover:bg-green-100 rounded-lg transition-colors"
                              title="Restore"
                            >
                              <ArrowUturnLeftIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default VersionControl;