"""
Persona Management API Endpoints

Purpose: Provides REST API endpoints for managing reader personas including:
- Creating, reading, updating, and deleting personas
- Requesting single and multi-persona feedback
- Managing persona templates and configurations
- Analytics and effectiveness tracking

Responsibilities:
- Persona CRUD operations via HTTP endpoints
- Persona feedback request handling
- Multi-persona feedback aggregation
- Cross-audience analysis
- Response formatting and error handling
- Request validation and authentication

Used by: Frontend persona management UI, feedback requests
Dependencies: core.database, workers.persona_worker, core.auth
"""

from fastapi import APIRouter, HTTPException, Depends, Query, BackgroundTasks
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from uuid import UUID, uuid4
from datetime import datetime
import structlog
import asyncio

from api.auth import get_current_user
from core.auth import UserClaims
from core.database import get_database
from core.queue_manager import QueueManager

# Configure logging
logger = structlog.get_logger(__name__)

# Create router
router = APIRouter()

# Pydantic Models
class PersonaCreate(BaseModel):
    """Request model for creating a persona."""
    feedback_style: Dict[str, Any] = Field(default_factory=dict)
    is_template: bool = Field(default=False)

class PersonaUpdate(BaseModel):
    """Request model for updating a persona."""
    feedback_style: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

class PersonaFeedbackRequest(BaseModel):
    """Request model for single persona feedback."""
    block_id: Optional[UUID] = None
    text: str = Field(..., min_length=1)
    context: Optional[Dict[str, Any]] = Field(default_factory=dict)
    analysis_type: str = Field(default="standard", description="standard, quick, comprehensive")

class MultiFeedbackRequest(BaseModel):
    """Request model for multi-persona feedback."""
    block_id: Optional[UUID] = None
    persona_ids: List[str] = Field(..., min_items=1, max_items=10)
    text: str = Field(..., min_length=1)
    context: Optional[Dict[str, Any]] = Field(default_factory=dict)
    options: Optional[Dict[str, Any]] = Field(default_factory=dict)
    include_cross_analysis: bool = Field(default=True)

class PersonaTemplateCustomization(BaseModel):
    """Request model for customizing persona templates."""
    customizations: Dict[str, Any] = Field(default_factory=dict)

# Response Models
class PersonaResponse(BaseModel):
    """Response model for persona data."""
    success: bool
    data: Dict[str, Any]

class PersonaListResponse(BaseModel):
    """Response model for persona list."""
    success: bool
    data: List[Dict[str, Any]]
    meta: Dict[str, Any]

class FeedbackResponse(BaseModel):
    """Response model for persona feedback."""
    success: bool
    data: Dict[str, Any]

class MultiFeedbackResponse(BaseModel):
    """Response model for multi-persona feedback."""
    success: bool
    data: Dict[str, Any]

# API Endpoints

@router.get("/", response_model=PersonaListResponse)
async def list_personas(
    current_user: UserClaims = Depends(get_current_user),
    limit: int = Query(default=50, le=100),
    offset: int = Query(default=0, ge=0),
    persona_type: Optional[str] = Query(default=None),
    is_active: Optional[bool] = Query(default=True),
    include_templates: bool = Query(default=False)
):
    """
    List all personas for the authenticated user.
    
    Args:
        current_user: Authenticated user from JWT token
        limit: Maximum number of personas to return
        offset: Number of personas to skip for pagination
        persona_type: Filter by persona type (academic, professional, creative, personal)
        is_active: Filter by active status
        include_templates: Include persona templates in results
    
    Returns:
        PersonaListResponse: List of personas with metadata
    """
    try:
        logger.info("Listing personas", user_id=current_user.user_id, limit=limit, offset=offset)
        
        db = await get_database()
        
        # Build query - only select columns that exist in the actual schema
        query = """
            SELECT 
                id, feedback_style, is_active, is_template,
                usage_count, effectiveness_score, created_at, updated_at
            FROM personas
            WHERE 1=1
        """
        params = []
        param_count = 0
        
        # Add filters only for columns that exist
        if is_active is not None:
            param_count += 1
            query += f" AND is_active = ${param_count}"
            params.append(is_active)
        
        if not include_templates:
            query += f" AND (is_template = FALSE OR is_template IS NULL)"
        
        # Add pagination
        query += f" ORDER BY usage_count DESC, created_at DESC LIMIT ${param_count + 1} OFFSET ${param_count + 2}"
        params.extend([limit, offset])
        
        # Execute query
        async with db.get_connection() as conn:
            rows = await conn.fetch(query, *params)
            
            # Get total count for pagination
            count_query = """
                SELECT COUNT(*) FROM personas 
                WHERE 1=1
            """
            count_params = []
            
            if is_active is not None:
                count_query += " AND is_active = $1"
                count_params.append(is_active)
            
            total_count = await conn.fetchval(count_query, *count_params) if count_params else await conn.fetchval(count_query)
        
        # Format response
        personas = []
        for row in rows:
            persona = dict(row)
            # Convert datetime to ISO string
            if persona.get('created_at'):
                persona['created_at'] = persona['created_at'].isoformat() + 'Z'
            if persona.get('updated_at'):
                persona['updated_at'] = persona['updated_at'].isoformat() + 'Z'
            personas.append(persona)
        
        return PersonaListResponse(
            success=True,
            data=personas,
            meta={
                "total": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": offset + limit < total_count
            }
        )
        
    except Exception as e:
        logger.error("Error listing personas", error=str(e), user_id=current_user.user_id)
        raise HTTPException(status_code=500, detail=f"Error listing personas: {str(e)}")


@router.post("/", response_model=PersonaResponse)
async def create_persona(
    persona: PersonaCreate,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Create a new persona for the authenticated user.
    
    Args:
        persona: Persona creation data
        current_user: Authenticated user from JWT token
    
    Returns:
        PersonaResponse: Created persona data
    """
    try:
        logger.info("Creating persona", user_id=current_user.user_id, name=persona.name)
        
        db = await get_database()
        persona_id = uuid4()
        
        # Insert persona - only use columns that exist in actual schema
        query = """
            INSERT INTO personas (
                id, feedback_style, is_active, is_template,
                usage_count, effectiveness_score, created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8
            ) RETURNING *
        """
        
        async with db.get_connection() as conn:
            row = await conn.fetchrow(
                query,
                persona_id,
                persona.feedback_style,
                True,  # is_active
                persona.is_template,
                0,  # usage_count
                0.0,  # effectiveness_score
                datetime.utcnow(),
                datetime.utcnow()
            )
        
        # Format response
        created_persona = dict(row)
        if created_persona.get('created_at'):
            created_persona['created_at'] = created_persona['created_at'].isoformat() + 'Z'
        if created_persona.get('updated_at'):
            created_persona['updated_at'] = created_persona['updated_at'].isoformat() + 'Z'
        
        logger.info("Persona created successfully", persona_id=str(persona_id))
        
        return PersonaResponse(
            success=True,
            data=created_persona
        )
        
    except Exception as e:
        logger.error("Error creating persona", error=str(e), user_id=current_user.user_id)
        raise HTTPException(status_code=500, detail=f"Error creating persona: {str(e)}")


@router.get("/{persona_id}", response_model=PersonaResponse)
async def get_persona(
    persona_id: UUID,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Get a specific persona by ID.
    
    Args:
        persona_id: UUID of the persona to retrieve
        current_user: Authenticated user from JWT token
    
    Returns:
        PersonaResponse: Persona data
    """
    try:
        logger.info("Getting persona", persona_id=str(persona_id), user_id=current_user.user_id)
        
        db = await get_database()
        
        query = """
            SELECT 
                id, feedback_style, is_active, is_template,
                usage_count, effectiveness_score, created_at, updated_at
            FROM personas 
            WHERE id = $1
        """
        
        async with db.get_connection() as conn:
            row = await conn.fetchrow(query, persona_id)
        
        if not row:
            raise HTTPException(status_code=404, detail="Persona not found")
        
        # Format response
        persona = dict(row)
        if persona.get('created_at'):
            persona['created_at'] = persona['created_at'].isoformat() + 'Z'
        if persona.get('updated_at'):
            persona['updated_at'] = persona['updated_at'].isoformat() + 'Z'
        
        return PersonaResponse(
            success=True,
            data=persona
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error getting persona", error=str(e), persona_id=str(persona_id))
        raise HTTPException(status_code=500, detail=f"Error getting persona: {str(e)}")


@router.put("/{persona_id}", response_model=PersonaResponse)
async def update_persona(
    persona_id: UUID,
    updates: PersonaUpdate,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Update a persona.
    
    Args:
        persona_id: UUID of the persona to update
        updates: Persona update data
        current_user: Authenticated user from JWT token
    
    Returns:
        PersonaResponse: Updated persona data
    """
    try:
        logger.info("Updating persona", persona_id=str(persona_id), user_id=current_user.user_id)
        
        db = await get_database()
        
        # Build update query dynamically
        update_fields = []
        params = [persona_id]
        param_count = 1
        
        for field, value in updates.dict(exclude_unset=True).items():
            if value is not None:
                param_count += 1
                update_fields.append(f"{field} = ${param_count}")
                params.append(value)
        
        if not update_fields:
            raise HTTPException(status_code=400, detail="No fields to update")
        
        # Add updated_at
        param_count += 1
        update_fields.append(f"updated_at = ${param_count}")
        params.append(datetime.utcnow())
        
        query = f"""
            UPDATE personas 
            SET {', '.join(update_fields)}
            WHERE id = $1
            RETURNING *
        """
        
        async with db.get_connection() as conn:
            row = await conn.fetchrow(query, *params)
        
        if not row:
            raise HTTPException(status_code=404, detail="Persona not found")
        
        # Format response
        updated_persona = dict(row)
        if updated_persona.get('created_at'):
            updated_persona['created_at'] = updated_persona['created_at'].isoformat() + 'Z'
        if updated_persona.get('updated_at'):
            updated_persona['updated_at'] = updated_persona['updated_at'].isoformat() + 'Z'
        
        logger.info("Persona updated successfully", persona_id=str(persona_id))
        
        return PersonaResponse(
            success=True,
            data=updated_persona
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error updating persona", error=str(e), persona_id=str(persona_id))
        raise HTTPException(status_code=500, detail=f"Error updating persona: {str(e)}")


@router.delete("/{persona_id}", response_model=PersonaResponse)
async def delete_persona(
    persona_id: UUID,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Delete (soft delete) a persona.
    
    Args:
        persona_id: UUID of the persona to delete
        current_user: Authenticated user from JWT token
    
    Returns:
        PersonaResponse: Confirmation of deletion
    """
    try:
        logger.info("Deleting persona", persona_id=str(persona_id), user_id=current_user.user_id)
        
        db = await get_database()
        
        # Hard delete since deleted_at column doesn't exist
        query = """
            DELETE FROM personas 
            WHERE id = $1
            RETURNING id
        """
        
        async with db.get_connection() as conn:
            row = await conn.fetchrow(query, persona_id)
        
        if not row:
            raise HTTPException(status_code=404, detail="Persona not found")
        
        logger.info("Persona deleted successfully", persona_id=str(persona_id))
        
        return PersonaResponse(
            success=True,
            data={
                "id": str(row['id']),
                "message": "Persona deleted successfully"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error deleting persona", error=str(e), persona_id=str(persona_id))
        raise HTTPException(status_code=500, detail=f"Error deleting persona: {str(e)}")


@router.post("/{persona_id}/feedback", response_model=FeedbackResponse)
async def request_persona_feedback(
    persona_id: UUID,
    request: PersonaFeedbackRequest,
    background_tasks: BackgroundTasks,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Request feedback from a specific persona.
    
    Args:
        persona_id: UUID of the persona to get feedback from
        request: Feedback request data
        background_tasks: Background task handler
        current_user: Authenticated user from JWT token
    
    Returns:
        FeedbackResponse: Persona feedback data
    """
    try:
        logger.info("Requesting persona feedback", persona_id=str(persona_id), user_id=current_user.user_id)
        
        db = await get_database()
        
        # Verify persona exists
        persona_query = """
            SELECT id, feedback_style, usage_count
            FROM personas 
            WHERE id = $1 AND is_active = TRUE
        """
        
        async with db.get_connection() as conn:
            persona = await conn.fetchrow(persona_query, persona_id)
        
        if not persona:
            raise HTTPException(status_code=404, detail="Persona not found or inactive")
        
        # For now, return mock feedback data that matches the database structure
        # TODO: Implement actual persona worker
        session_id = uuid4()
        feedback_id = uuid4()
        
        # Mock feedback based on persona characteristics
        mock_feedback = {
            "feedback_id": str(feedback_id),
            "persona_id": str(persona_id),
            "session_id": str(session_id),
            "persona_name": f"Persona {str(persona_id)[:8]}",
            "persona_type": "general",
            "comprehension_score": 0.85 + (hash(str(persona_id)) % 15) / 100,  # 0.85-1.0
            "engagement_score": 0.80 + (hash(str(persona_id) + "eng") % 20) / 100,  # 0.80-1.0
            "emotional_score": 0.75 + (hash(str(persona_id) + "emo") % 25) / 100,  # 0.75-1.0
            "style_score": 0.82 + (hash(str(persona_id) + "style") % 18) / 100,  # 0.82-1.0
            "overall_score": 0.83,
            "feedback_text": "This content shows good understanding for the target audience. The writing style aligns well with general readability expectations.",
            "specific_issues": [
                "Consider adjusting tone for target audience",
                "Some technical terms may need clarification"
            ],
            "suggestions": [
                "Add more context for complex concepts",
                "Consider breaking up longer paragraphs",
                "Include more engaging examples"
            ],
            "confidence": 0.89,
            "processing_time_ms": 2150,
            "tokens_used": 125,
            "created_at": datetime.utcnow().isoformat() + 'Z'
        }
        
        # Update usage count
        async with db.get_connection() as conn:
            await conn.execute(
                "UPDATE personas SET usage_count = usage_count + 1 WHERE id = $1",
                persona_id
            )
        
        logger.info("Persona feedback generated", persona_id=str(persona_id), feedback_id=str(feedback_id))
        
        return FeedbackResponse(
            success=True,
            data=mock_feedback
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error requesting persona feedback", error=str(e), persona_id=str(persona_id))
        raise HTTPException(status_code=500, detail=f"Error requesting persona feedback: {str(e)}")


@router.post("/multi-feedback", response_model=MultiFeedbackResponse)
async def request_multi_persona_feedback(
    request: MultiFeedbackRequest,
    background_tasks: BackgroundTasks,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Request feedback from multiple personas simultaneously.
    
    Args:
        request: Multi-feedback request data
        background_tasks: Background task handler
        current_user: Authenticated user from JWT token
    
    Returns:
        MultiFeedbackResponse: Aggregated multi-persona feedback
    """
    try:
        logger.info("Requesting multi-persona feedback", 
                   persona_count=len(request.persona_ids), 
                   user_id=current_user.user_id)
        
        db = await get_database()
        
        # Verify all personas exist
        personas_query = """
            SELECT id, feedback_style
            FROM personas 
            WHERE id = ANY($1) AND is_active = TRUE
        """
        
        # Convert string persona_ids to UUIDs for database query
        persona_uuids = [UUID(pid) for pid in request.persona_ids]
        
        async with db.get_connection() as conn:
            personas = await conn.fetch(personas_query, persona_uuids)
        
        if len(personas) != len(request.persona_ids):
            raise HTTPException(status_code=404, detail="One or more personas not found or inactive")
        
        # For now, generate mock feedback for each persona
        # TODO: Implement actual multi-persona worker
        session_id = uuid4()
        feedback_results = []
        
        for persona in personas:
            feedback_id = uuid4()
            feedback = {
                "feedback_id": str(feedback_id),
                "persona_id": str(persona['id']),
                "persona_name": f"Persona {str(persona['id'])[:8]}",
                "persona_type": "general",
                "comprehension_score": 0.80 + (hash(str(persona['id'])) % 20) / 100,
                "engagement_score": 0.75 + (hash(str(persona['id']) + "eng") % 25) / 100,
                "emotional_score": 0.70 + (hash(str(persona['id']) + "emo") % 30) / 100,
                "style_score": 0.78 + (hash(str(persona['id']) + "style") % 22) / 100,
                "overall_score": 0.80,
                "feedback_text": "From this persona's perspective, this content demonstrates solid understanding and engagement.",
                "specific_issues": [
                    "Could benefit from persona-specific improvements",
                    "Some areas need clarification for this audience"
                ],
                "suggestions": [
                    "Adapt tone for target readers",
                    "Include more relevant examples",
                    "Consider audience expertise level"
                ],
                "confidence": 0.85 + (hash(str(persona['id']) + "conf") % 15) / 100
            }
            feedback_results.append(feedback)
        
        # Generate cross-audience analysis if requested
        cross_analysis = None
        if request.include_cross_analysis:
            cross_analysis = {
                "overall_appeal_score": 0.82,
                "consistency_score": 0.88,
                "conflicts": [
                    {
                        "type": "tone_mismatch",
                        "description": "Professional and creative personas have different tone preferences",
                        "affected_personas": [p['persona_name'] for p in feedback_results[:2]],
                        "severity": "medium"
                    }
                ],
                "optimization_suggestions": [
                    {
                        "suggestion": "Balance formal and creative elements",
                        "impact": "Improves appeal across different audience types",
                        "priority": "high"
                    },
                    {
                        "suggestion": "Add audience-specific sections",
                        "impact": "Addresses diverse reader needs",
                        "priority": "medium"
                    }
                ],
                "audience_alignment": {
                    "primary_audience": "mixed",
                    "secondary_audiences": ["general" for p in personas],
                    "compatibility_score": 0.85
                }
            }
        
        # Update usage counts
        async with db.get_connection() as conn:
            await conn.execute(
                "UPDATE personas SET usage_count = usage_count + 1 WHERE id = ANY($1)",
                persona_uuids
            )
        
        response_data = {
            "session_id": str(session_id),
            "feedback": feedback_results,
            "personas_analyzed": len(feedback_results),
            "processing_time_ms": 3200,
            "created_at": datetime.utcnow().isoformat() + 'Z'
        }
        
        if cross_analysis:
            response_data["cross_audience_analysis"] = cross_analysis
        
        logger.info("Multi-persona feedback generated", 
                   session_id=str(session_id), 
                   persona_count=len(feedback_results))
        
        return MultiFeedbackResponse(
            success=True,
            data=response_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error requesting multi-persona feedback", error=str(e))
        raise HTTPException(status_code=500, detail=f"Error requesting multi-persona feedback: {str(e)}")


@router.get("/templates", response_model=PersonaListResponse)
async def get_persona_templates(
    current_user: UserClaims = Depends(get_current_user),
    persona_type: Optional[str] = Query(default=None)
):
    """
    Get available persona templates.
    
    Args:
        current_user: Authenticated user from JWT token
        persona_type: Filter by persona type
    
    Returns:
        PersonaListResponse: List of persona templates
    """
    try:
        logger.info("Getting persona templates", user_id=current_user.user_id)
        
        db = await get_database()
        
        # Get public templates
        query = """
            SELECT 
                id, feedback_style, effectiveness_score,
                created_at, updated_at
            FROM personas 
            WHERE is_template = TRUE
        """
        params = []
        
        query += " ORDER BY effectiveness_score DESC, name ASC"
        
        async with db.get_connection() as conn:
            rows = await conn.fetch(query, *params)
        
        # Format response
        templates = []
        for row in rows:
            template = dict(row)
            # Remove user-specific fields and convert datetime
            template.pop('user_id', None)
            if template.get('created_at'):
                template['created_at'] = template['created_at'].isoformat() + 'Z'
            if template.get('updated_at'):
                template['updated_at'] = template['updated_at'].isoformat() + 'Z'
            templates.append(template)
        
        return PersonaListResponse(
            success=True,
            data=templates,
            meta={
                "total": len(templates),
                "template_count": len(templates)
            }
        )
        
    except Exception as e:
        logger.error("Error getting persona templates", error=str(e))
        raise HTTPException(status_code=500, detail=f"Error getting persona templates: {str(e)}")


@router.post("/templates/{template_id}/instantiate", response_model=PersonaResponse)
async def create_persona_from_template(
    template_id: UUID,
    customization: PersonaTemplateCustomization,
    current_user: UserClaims = Depends(get_current_user)
):
    """
    Create a new persona from a template.
    
    Args:
        template_id: UUID of the template to use
        customization: Template customization data
        current_user: Authenticated user from JWT token
    
    Returns:
        PersonaResponse: Created persona data
    """
    try:
        logger.info("Creating persona from template", 
                   template_id=str(template_id), 
                   user_id=current_user.user_id)
        
        db = await get_database()
        
        # Get template
        template_query = """
            SELECT feedback_style
            FROM personas 
            WHERE id = $1 AND is_template = TRUE
        """
        
        async with db.get_connection() as conn:
            template = await conn.fetchrow(template_query, template_id)
        
        if not template:
            raise HTTPException(status_code=404, detail="Template not found")
        
        # Apply customizations to template feedback_style
        template_feedback_style = dict(template['feedback_style']) if template['feedback_style'] else {}
        
        # Merge customizations
        for key, value in customization.customizations.items():
            if key == 'feedback_style' and isinstance(value, dict):
                template_feedback_style.update(value)
        
        # Create new persona
        persona_id = uuid4()
        insert_query = """
            INSERT INTO personas (
                id, feedback_style, is_active, is_template,
                usage_count, effectiveness_score, created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8
            ) RETURNING *
        """
        
        async with db.get_connection() as conn:
            row = await conn.fetchrow(
                insert_query,
                persona_id,
                template_feedback_style,
                True,  # is_active
                False,  # is_template
                0,  # usage_count
                0.0,  # effectiveness_score
                datetime.utcnow(),
                datetime.utcnow()
            )
        
        # Format response
        created_persona = dict(row)
        if created_persona.get('created_at'):
            created_persona['created_at'] = created_persona['created_at'].isoformat() + 'Z'
        if created_persona.get('updated_at'):
            created_persona['updated_at'] = created_persona['updated_at'].isoformat() + 'Z'
        
        logger.info("Persona created from template", 
                   persona_id=str(persona_id), 
                   template_id=str(template_id))
        
        return PersonaResponse(
            success=True,
            data=created_persona
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error creating persona from template", 
                   error=str(e), 
                   template_id=str(template_id))
        raise HTTPException(status_code=500, detail=f"Error creating persona from template: {str(e)}")