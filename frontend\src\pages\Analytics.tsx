import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ChartBarIcon,
  TrophyIcon,
  FireIcon,
  ClockIcon,
  DocumentTextIcon,
  SparklesIcon,
  CalendarDaysIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  PlusIcon,
  Cog6ToothIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon,
} from '@heroicons/react/24/outline';
import { useAnalyticsStore, useAnalyticsSelectors } from '../stores/analyticsStore';
import { DailyStats, WritingGoal, Achievement } from '../types/analytics';
import WritingHeatmap from '../components/analytics/WritingHeatmap';
import ProgressChart from '../components/analytics/ProgressChart';
import WritingGoalsManager from '../components/analytics/WritingGoalsManager';
import AchievementNotification from '../components/analytics/AchievementNotification';

const Analytics: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('week');
  const [activeTab, setActiveTab] = useState<'overview' | 'goals' | 'insights'>('overview');
  const [mounted, setMounted] = useState(false);
  const [newAchievement, setNewAchievement] = useState<Achievement | null>(null);

  const store = useAnalyticsStore();
  const selectors = useAnalyticsSelectors();

  useEffect(() => {
    setMounted(true);
    // Trigger analytics calculations on load
    store.calculateInsights();
    store.updateStreak();
    store.checkAchievements();
  }, []);

  // Listen for new achievements
  useEffect(() => {
    const recentAchievements = selectors.getRecentAchievements();
    const latestAchievement = recentAchievements[0];
    
    if (latestAchievement && latestAchievement.unlockedAt && 
        Date.now() - latestAchievement.unlockedAt.getTime() < 5000) {
      setNewAchievement(latestAchievement);
    }
  }, [store.achievements]);

  const todayStats = selectors.getTodayStats();
  const weekStats = selectors.getWeekStats();
  const monthStats = selectors.getMonthStats();
  const activeGoals = selectors.getActiveGoals();
  const recentAchievements = selectors.getRecentAchievements();

  // Calculate trends
  const calculateTrend = (current: number, previous: number) => {
    if (previous === 0) return { change: 0, trend: 'stable' as const };
    const change = ((current - previous) / previous) * 100;
    return {
      change: Math.abs(change),
      trend: change > 0 ? 'up' as const : change < 0 ? 'down' as const : 'stable' as const,
    };
  };

  // Prepare chart data
  const getChartData = () => {
    const stats = selectedPeriod === 'week' ? weekStats : 
                  selectedPeriod === 'month' ? monthStats : 
                  monthStats; // For now, use month data for year as well

    return stats.map(stat => ({
      date: stat.date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      }),
      value: stat.wordsWritten,
      label: `${stat.wordsWritten} words`
    }));
  };

  const getTimeChartData = () => {
    const stats = selectedPeriod === 'week' ? weekStats : 
                  selectedPeriod === 'month' ? monthStats : 
                  monthStats;

    return stats.map(stat => ({
      date: stat.date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      }),
      value: stat.timeSpent,
      label: `${stat.timeSpent} minutes`
    }));
  };

  // Weekly totals for comparison
  const thisWeekWords = weekStats.reduce((sum, day) => sum + day.wordsWritten, 0);
  const thisWeekTime = weekStats.reduce((sum, day) => sum + day.timeSpent, 0);

  // Mock previous week data for trend calculation
  const lastWeekWords = Math.floor(thisWeekWords * (0.8 + Math.random() * 0.4));
  const lastWeekTime = Math.floor(thisWeekTime * (0.8 + Math.random() * 0.4));

  const wordsTrend = calculateTrend(thisWeekWords, lastWeekWords);
  const timeTrend = calculateTrend(thisWeekTime, lastWeekTime);

  const statCards = [
    {
      title: 'Words This Week',
      value: thisWeekWords.toLocaleString(),
      icon: DocumentTextIcon,
      trend: wordsTrend,
      color: 'purple',
    },
    {
      title: 'Writing Time',
      value: `${thisWeekTime}m`,
      icon: ClockIcon,
      trend: timeTrend,
      color: 'blue',
    },
    {
      title: 'Current Streak',
      value: `${store.writingStreak.current} days`,
      icon: FireIcon,
      trend: { change: 0, trend: 'stable' as const },
      color: 'orange',
    },
    {
      title: 'AI Acceptance',
      value: `${store.aiUsageStats.acceptanceRate.toFixed(1)}%`,
      icon: SparklesIcon,
      trend: { change: 0, trend: 'stable' as const },
      color: 'green',
    },
  ];

  const handleExportData = () => {
    const data = {
      dailyStats: store.dailyStats,
      writingStreak: store.writingStreak,
      goals: store.goals,
      achievements: store.achievements,
      aiUsageStats: store.aiUsageStats,
      exportedAt: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `revisionary-analytics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: ChartBarIcon },
    { id: 'goals', label: 'Goals', icon: TrophyIcon },
    { id: 'insights', label: 'Insights', icon: SparklesIcon },
  ];

  return (
    <div className="mobile-full-height bg-gradient-to-br from-slate-50 via-white to-purple-50/30">
      {/* Achievement Notification */}
      <AchievementNotification
        achievement={newAchievement}
        onDismiss={() => setNewAchievement(null)}
      />

      <div className="relative z-10 max-w-7xl mx-auto mobile-px sm:px-8 lg:px-10 py-4 lg:py-8 lg:pl-80">
        {/* Header */}
        <div className={`mb-6 lg:mb-8 transform transition-all duration-1000 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl lg:text-4xl font-bold text-slate-900 mb-2">Analytics</h1>
              <p className="text-base lg:text-lg text-slate-600">Track your writing progress and insights</p>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleExportData}
                className="inline-flex items-center px-4 py-2 text-slate-600 bg-white border border-slate-300 rounded-xl hover:bg-slate-50 transition-colors"
              >
                <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
                Export Data
              </button>
              <button className="inline-flex items-center px-4 py-2 text-slate-600 bg-white border border-slate-300 rounded-xl hover:bg-slate-50 transition-colors">
                <Cog6ToothIcon className="w-4 h-4 mr-2" />
                Settings
              </button>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className={`mb-6 lg:mb-8 transform transition-all duration-1000 delay-200 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <div className="flex space-x-1 bg-white rounded-xl lg:rounded-2xl p-1 lg:p-2 border border-slate-200 shadow-sm overflow-x-auto">
            {tabs.map((tab) => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center mobile-px lg:px-6 mobile-py lg:py-3 rounded-lg lg:rounded-xl font-medium transition-all duration-200 whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'bg-purple-100 text-purple-700 shadow-sm'
                      : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
                  }`}
                >
                  <IconComponent className="w-4 h-4 lg:w-5 lg:h-5 mr-1 lg:mr-2" />
                  <span className="text-sm lg:text-base">{tab.label}</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Tab Content */}
        <div className="space-y-8">
          {activeTab === 'overview' && (
            <>
              {/* Stats Cards */}
              <div className={`mobile-grid-2 md:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 transform transition-all duration-1000 delay-400 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
                {statCards.map((stat, index) => {
                  const IconComponent = stat.icon;
                  return (
                    <motion.div
                      key={stat.title}
                      className="bg-white rounded-2xl border border-slate-200 p-6 shadow-sm hover:shadow-md transition-shadow"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 + 0.5 }}
                    >
                      <div className="flex items-center justify-between mb-4">
                        <div className={`p-3 rounded-xl bg-${stat.color}-100`}>
                          <IconComponent className={`w-6 h-6 text-${stat.color}-600`} />
                        </div>
                        <div className={`flex items-center space-x-1 text-sm ${
                          stat.trend.trend === 'up' ? 'text-green-600' :
                          stat.trend.trend === 'down' ? 'text-red-600' : 'text-slate-500'
                        }`}>
                          {stat.trend.trend === 'up' && <ArrowTrendingUpIcon className="w-4 h-4" />}
                          {stat.trend.trend === 'down' && <ArrowTrendingDownIcon className="w-4 h-4" />}
                          <span>{stat.trend.change.toFixed(1)}%</span>
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-slate-600 mb-1">{stat.title}</div>
                        <div className="text-2xl font-bold text-slate-900">{stat.value}</div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>

              {/* Period Selector */}
              <div className={`flex justify-center transform transition-all duration-1000 delay-600 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
                <div className="flex space-x-1 bg-white rounded-xl p-1 border border-slate-200">
                  {['week', 'month', 'year'].map((period) => (
                    <button
                      key={period}
                      onClick={() => setSelectedPeriod(period as any)}
                      className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 capitalize ${
                        selectedPeriod === period
                          ? 'bg-purple-100 text-purple-700'
                          : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
                      }`}
                    >
                      {period}
                    </button>
                  ))}
                </div>
              </div>

              {/* Charts */}
              <div className={`grid grid-cols-1 lg:grid-cols-2 gap-8 transform transition-all duration-1000 delay-800 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
                <ProgressChart
                  data={getChartData()}
                  title="Words Written"
                  color="purple"
                  type="line"
                  unit=" words"
                  showTrend={true}
                />
                <ProgressChart
                  data={getTimeChartData()}
                  title="Writing Time"
                  color="blue"
                  type="bar"
                  unit=" min"
                  showTrend={true}
                />
              </div>

              {/* Writing Heatmap */}
              <div className={`transform transition-all duration-1000 delay-1000 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
                <WritingHeatmap />
              </div>

              {/* Active Goals Preview */}
              {activeGoals.length > 0 && (
                <div className={`transform transition-all duration-1000 delay-1200 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
                  <div className="bg-white rounded-2xl border border-slate-200 p-8">
                    <div className="flex items-center justify-between mb-6">
                      <h2 className="text-xl font-bold text-slate-900">Active Goals</h2>
                      <button
                        onClick={() => setActiveTab('goals')}
                        className="text-purple-600 hover:text-purple-700 font-medium"
                      >
                        View All Goals
                      </button>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {activeGoals.slice(0, 3).map((goal) => {
                        const progress = Math.min((goal.current / goal.target) * 100, 100);
                        return (
                          <div key={goal.id} className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl border border-purple-200">
                            <div className="flex items-center justify-between mb-2">
                              <h3 className="font-semibold text-slate-900 capitalize text-sm">
                                {goal.type.replace('_', ' ')}
                              </h3>
                              <span className="text-xs text-slate-600">
                                {goal.current.toLocaleString()}/{goal.target.toLocaleString()}
                              </span>
                            </div>
                            <div className="w-full bg-white/60 rounded-full h-2 mb-2">
                              <div
                                className="h-2 bg-purple-500 rounded-full transition-all duration-500"
                                style={{ width: `${progress}%` }}
                              />
                            </div>
                            <p className="text-xs text-purple-700 font-medium">
                              {progress.toFixed(1)}% complete
                            </p>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          {activeTab === 'goals' && (
            <div className={`transform transition-all duration-1000 delay-400 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <WritingGoalsManager />
            </div>
          )}

          {activeTab === 'insights' && (
            <div className={`transform transition-all duration-1000 delay-400 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Productivity Insights */}
                <div className="bg-white rounded-2xl border border-slate-200 p-8">
                  <h2 className="text-xl font-bold text-slate-900 mb-6">Productivity Insights</h2>
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-xl">
                      <h3 className="font-semibold text-blue-900 mb-2">Peak Writing Hours</h3>
                      <p className="text-blue-700 text-sm">
                        Your most productive writing time is between 9 AM and 11 AM, with an average of 45% more words written.
                      </p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-xl">
                      <h3 className="font-semibold text-green-900 mb-2">Consistency Score</h3>
                      <p className="text-green-700 text-sm">
                        You've written {weekStats.filter(day => day.wordsWritten > 0).length} out of 7 days this week. Great consistency!
                      </p>
                    </div>
                    <div className="p-4 bg-purple-50 rounded-xl">
                      <h3 className="font-semibold text-purple-900 mb-2">AI Collaboration</h3>
                      <p className="text-purple-700 text-sm">
                        You accept {store.aiUsageStats.acceptanceRate.toFixed(0)}% of AI suggestions, showing effective AI collaboration.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Recent Achievements */}
                <div className="bg-white rounded-2xl border border-slate-200 p-8">
                  <h2 className="text-xl font-bold text-slate-900 mb-6">Recent Achievements</h2>
                  {recentAchievements.length > 0 ? (
                    <div className="space-y-4">
                      {recentAchievements.slice(0, 5).map((achievement) => {
                        const rarityColors = {
                          common: 'bg-slate-50 border-slate-200',
                          rare: 'bg-blue-50 border-blue-200',
                          epic: 'bg-purple-50 border-purple-200',
                          legendary: 'bg-yellow-50 border-yellow-200',
                        };
                        
                        return (
                          <div
                            key={achievement.id}
                            className={`p-4 rounded-xl border ${rarityColors[achievement.rarity]}`}
                          >
                            <div className="flex items-center space-x-3">
                              <span className="text-2xl">{achievement.icon}</span>
                              <div className="flex-1">
                                <h3 className="font-semibold text-slate-900">{achievement.title}</h3>
                                <p className="text-sm text-slate-600">{achievement.description}</p>
                                {achievement.unlockedAt && (
                                  <p className="text-xs text-slate-500 mt-1">
                                    Unlocked {achievement.unlockedAt.toLocaleDateString()}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <TrophyIcon className="w-12 h-12 mx-auto text-slate-300 mb-3" />
                      <p className="text-slate-500">No achievements yet. Keep writing to unlock your first achievement!</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Analytics;