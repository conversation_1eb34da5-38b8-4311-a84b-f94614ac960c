import React, { useState } from 'react';
import {
  SparklesIcon,
  PencilSquareIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  ArrowPathIcon,
  BoltIcon,
  ChatBubbleBottomCenterTextIcon,
  DocumentTextIcon,
  HashtagIcon,
  ChatBubbleLeftIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';
import DocumentOutline from './DocumentOutline';
import CommentsPanel from './CommentsPanel';

interface AISuggestion {
  id: string;
  type: 'grammar' | 'style' | 'content' | 'structure';
  severity: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  originalText: string;
  suggestedText: string;
  line: number;
  isStale?: boolean;
}

interface Comment {
  id: string;
  text: string;
  author: {
    name: string;
    avatar?: string;
    email: string;
  };
  createdAt: Date;
  line?: number;
  resolved: boolean;
  replies: Comment[];
}

interface AIPanelProps {
  suggestions: AISuggestion[];
  onAcceptSuggestion: (id: string) => void;
  onDismissSuggestion: (id: string) => void;
  onRerunSuggestion: (id: string) => void;
  onGenerateContent: () => void;
  isGenerating: boolean;
  documentContent: string;
  onNavigateToLine: (line: number) => void;
  activeTab: 'ai' | 'outline' | 'comments';
  onTabChange: (tab: 'ai' | 'outline' | 'comments') => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
  comments: Comment[];
  onResolveComment: (commentId: string) => void;
  onReplyToComment: (commentId: string, text: string) => void;
}

const suggestionTypeConfig = {
  grammar: {
    color: 'blue',
    icon: CheckCircleIcon,
    label: 'Grammar',
  },
  style: {
    color: 'green',
    icon: PencilSquareIcon,
    label: 'Style',
  },
  content: {
    color: 'purple',
    icon: DocumentTextIcon,
    label: 'Content',
  },
  structure: {
    color: 'orange',
    icon: ChatBubbleBottomCenterTextIcon,
    label: 'Structure',
  },
};

const severityConfig = {
  low: { priority: 'Low', bgColor: 'bg-slate-50', textColor: 'text-slate-600' },
  medium: { priority: 'Medium', bgColor: 'bg-yellow-50', textColor: 'text-yellow-700' },
  high: { priority: 'High', bgColor: 'bg-red-50', textColor: 'text-red-700' },
};

const AIPanel: React.FC<AIPanelProps> = ({
  suggestions,
  onAcceptSuggestion,
  onDismissSuggestion,
  onRerunSuggestion,
  onGenerateContent,
  isGenerating,
  documentContent,
  onNavigateToLine,
  activeTab,
  onTabChange,
  isCollapsed = false,
  onToggleCollapse,
  comments,
  onResolveComment,
  onReplyToComment,
}) => {


  const tabs = [
    { 
      id: 'ai', 
      label: 'AI', 
      icon: SparklesIcon,
      count: suggestions.length,
      color: 'text-purple-600' 
    },
    { 
      id: 'outline', 
      label: 'Outline', 
      icon: HashtagIcon,
      count: null,
      color: 'text-blue-600' 
    },
    { 
      id: 'comments', 
      label: 'Comments', 
      icon: ChatBubbleLeftIcon,
      count: comments.filter(c => !c.resolved).length,
      color: 'text-green-600' 
    },
  ];

  const handleAddComment = (text: string, line?: number) => {
    console.log('Adding comment:', text, line);
    // Implementation for adding comment would be passed as another prop
  };

  return (
    <div className="w-96 bg-gradient-to-b from-slate-50/80 to-white/80 backdrop-blur-xl border-l border-slate-200/50 flex flex-col relative z-50">
      {/* Toggle button - ALWAYS VISIBLE - LARGER */}
      <motion.button
        onClick={onToggleCollapse || (() => {})}
        className="absolute bottom-4 left-4 w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg shadow-xl flex items-center justify-center z-[60] group"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        <ChevronRightIcon className="w-5 h-5 text-white" />
        <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg" />
      </motion.button>

      {/* Header */}
      <div className="px-4 py-4 border-b border-slate-200/50">
        {/* Tabs */}
        <div className="flex bg-slate-100/80 rounded-xl p-1">
          {tabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id as any)}
                className={`flex-1 flex items-center justify-center px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-white text-slate-900 shadow-sm'
                    : 'text-slate-600 hover:text-slate-900'
                }`}
              >
                <IconComponent className={`w-4 h-4 mr-1 ${activeTab === tab.id ? tab.color : 'text-slate-500'}`} />
                {tab.label}
                {tab.count !== null && tab.count > 0 && (
                  <span className={`ml-2 px-1.5 py-0.5 text-xs font-bold rounded-full ${
                    activeTab === tab.id
                      ? 'bg-slate-100 text-slate-700'
                      : 'bg-slate-200 text-slate-600'
                  }`}>
                    {tab.count}
                  </span>
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <AnimatePresence mode="wait">
          {activeTab === 'ai' ? (
            <motion.div
              key="ai"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="h-full overflow-y-auto"
            >
              {suggestions.length > 0 ? (
                <div className="p-4 space-y-4">
                  {suggestions.map((suggestion, index) => (
                    <SuggestionCard
                      key={suggestion.id}
                      suggestion={suggestion}
                      index={index}
                      onAccept={() => onAcceptSuggestion(suggestion.id)}
                      onDismiss={() => onDismissSuggestion(suggestion.id)}
                      onRerun={() => onRerunSuggestion(suggestion.id)}
                    />
                  ))}
                </div>
              ) : (
                <div className="p-6">
                  <GenerateContent onGenerate={onGenerateContent} isGenerating={isGenerating} />
                </div>
              )}
            </motion.div>
          ) : activeTab === 'outline' ? (
            <motion.div
              key="outline"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="h-full overflow-y-auto p-4"
            >
              <DocumentOutline 
                content={documentContent} 
                onNavigate={onNavigateToLine}
              />
            </motion.div>
          ) : (
            <motion.div
              key="comments"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="h-full overflow-y-auto p-4"
            >
              <CommentsPanel
                comments={comments}
                onAddComment={handleAddComment}
                onResolveComment={onResolveComment}
                onReplyToComment={onReplyToComment}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

const SuggestionCard: React.FC<{
  suggestion: AISuggestion;
  index: number;
  onAccept: () => void;
  onDismiss: () => void;
  onRerun: () => void;
}> = ({ suggestion, index, onAccept, onDismiss, onRerun }) => {
  const config = suggestionTypeConfig[suggestion.type];
  const severity = severityConfig[suggestion.severity];
  const IconComponent = config.icon;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className={`relative overflow-hidden rounded-2xl border shadow-sm transition-all duration-200 hover:shadow-md ${
        suggestion.isStale
          ? 'bg-yellow-50/50 border-yellow-200'
          : 'bg-white border-slate-200'
      }`}
    >
      {suggestion.isStale && (
        <div className="absolute top-0 left-0 right-0 bg-yellow-400 text-yellow-900 text-xs font-semibold px-4 py-2 flex items-center">
          <ExclamationTriangleIcon className="w-4 h-4 mr-2" />
          Text changed - suggestion may be outdated
          <button
            onClick={onRerun}
            className="ml-auto flex items-center text-yellow-800 hover:text-yellow-900"
          >
            <ArrowPathIcon className="w-4 h-4 mr-1" />
            Re-run
          </button>
        </div>
      )}

      <div className={`p-4 ${suggestion.isStale ? 'pt-16' : ''}`}>
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className={`w-8 h-8 bg-${config.color}-100 rounded-xl flex items-center justify-center`}>
              <IconComponent className={`w-4 h-4 text-${config.color}-600`} />
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <span className={`text-sm font-semibold text-${config.color}-700`}>
                  {config.label}
                </span>
                <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${severity.bgColor} ${severity.textColor}`}>
                  {severity.priority}
                </span>
              </div>
              <span className="text-xs text-slate-500">Line {suggestion.line}</span>
            </div>
          </div>
          <button
            onClick={onDismiss}
            className="p-1 text-slate-400 hover:text-slate-600 transition-colors"
          >
            <XMarkIcon className="w-4 h-4" />
          </button>
        </div>

        {/* Content */}
        <div className="space-y-3">
          <p className="text-sm text-slate-900 font-medium">{suggestion.title}</p>
          <p className="text-sm text-slate-600">{suggestion.description}</p>

          {/* Text comparison */}
          <div className="space-y-2">
            <div className="p-3 bg-red-50 border border-red-200 rounded-xl">
              <p className="text-xs font-semibold text-red-700 mb-1">Original</p>
              <p className="text-sm text-red-900">{suggestion.originalText}</p>
            </div>
            <div className="p-3 bg-green-50 border border-green-200 rounded-xl">
              <p className="text-xs font-semibold text-green-700 mb-1">Suggested</p>
              <p className="text-sm text-green-900">{suggestion.suggestedText}</p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-2 pt-2">
            <motion.button
              onClick={onAccept}
              disabled={suggestion.isStale}
              className={`flex-1 px-4 py-2 text-sm font-semibold rounded-xl transition-all duration-200 ${
                suggestion.isStale
                  ? 'bg-slate-100 text-slate-400 cursor-not-allowed'
                  : `bg-${config.color}-600 text-white hover:bg-${config.color}-700 shadow-sm hover:shadow-md`
              }`}
              whileHover={!suggestion.isStale ? { scale: 1.02 } : {}}
              whileTap={!suggestion.isStale ? { scale: 0.98 } : {}}
            >
              Accept
            </motion.button>
            <motion.button
              onClick={onDismiss}
              className="px-4 py-2 text-sm font-semibold text-slate-600 bg-slate-100 hover:bg-slate-200 rounded-xl transition-colors"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Dismiss
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const GenerateContent: React.FC<{
  onGenerate: () => void;
  isGenerating: boolean;
}> = ({ onGenerate, isGenerating }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-slate-900 mb-3">Generate Content</h3>
        <p className="text-sm text-slate-600 mb-6">
          Let AI help you write, expand, or improve your content.
        </p>
      </div>

      <div className="space-y-4">
        <motion.button
          onClick={onGenerate}
          disabled={isGenerating}
          className={`w-full flex items-center justify-center px-6 py-4 rounded-2xl text-white font-semibold transition-all duration-200 ${
            isGenerating
              ? 'bg-slate-400 cursor-not-allowed'
              : 'bg-gradient-to-r from-purple-600 to-pink-600 hover:shadow-lg hover:shadow-purple-500/25'
          }`}
          whileHover={!isGenerating ? { scale: 1.02 } : {}}
          whileTap={!isGenerating ? { scale: 0.98 } : {}}
        >
          {isGenerating ? (
            <>
              <ArrowPathIcon className="w-5 h-5 mr-3 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <BoltIcon className="w-5 h-5 mr-3" />
              Generate Content
            </>
          )}
        </motion.button>

        <div className="grid grid-cols-1 gap-3">
          <button className="flex items-center p-4 text-left bg-white border border-slate-200 rounded-2xl hover:border-purple-300 hover:shadow-sm transition-all duration-200">
            <PencilSquareIcon className="w-5 h-5 text-purple-600 mr-3" />
            <div>
              <p className="text-sm font-semibold text-slate-900">Improve Writing</p>
              <p className="text-xs text-slate-500">Enhance clarity and flow</p>
            </div>
          </button>
          
          <button className="flex items-center p-4 text-left bg-white border border-slate-200 rounded-2xl hover:border-purple-300 hover:shadow-sm transition-all duration-200">
            <CheckCircleIcon className="w-5 h-5 text-green-600 mr-3" />
            <div>
              <p className="text-sm font-semibold text-slate-900">Check Grammar</p>
              <p className="text-xs text-slate-500">Fix grammar and spelling</p>
            </div>
          </button>

          <button className="flex items-center p-4 text-left bg-white border border-slate-200 rounded-2xl hover:border-purple-300 hover:shadow-sm transition-all duration-200">
            <DocumentTextIcon className="w-5 h-5 text-blue-600 mr-3" />
            <div>
              <p className="text-sm font-semibold text-slate-900">Expand Ideas</p>
              <p className="text-xs text-slate-500">Add more detail and depth</p>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default AIPanel;