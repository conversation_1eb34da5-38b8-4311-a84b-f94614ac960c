/**
 * WebSocket Service
 * 
 * Handles real-time communication with the backend including:
 * - Document collaboration
 * - Live health analysis
 * - Persona feedback updates
 * - Agent execution status
 * - System notifications
 */

import { io, Socket } from 'socket.io-client';
import { config } from '@/config';

export interface DocumentCollaborationEvent {
  type: 'block_updated' | 'block_created' | 'block_deleted' | 'cursor_moved';
  document_id: string;
  block_id: string;
  user_id: string;
  user_name: string;
  data: any;
  timestamp: number;
}

export interface HealthAnalysisEvent {
  type: 'analysis_complete' | 'analysis_started' | 'issues_detected';
  block_id: string;
  document_id: string;
  health_metrics?: any;
  issues?: any[];
  timestamp: number;
}

export interface PersonaFeedbackEvent {
  type: 'feedback_complete' | 'feedback_started' | 'multi_feedback_complete';
  persona_id?: string;
  block_id: string;
  document_id: string;
  feedback?: any;
  multi_feedback?: any;
  timestamp: number;
}

export interface AgentExecutionEvent {
  type: 'execution_started' | 'execution_complete' | 'execution_failed' | 'batch_complete';
  agent_id?: string;
  execution_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  results?: any;
  error?: string;
  timestamp: number;
}

export interface SystemNotificationEvent {
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  action?: {
    label: string;
    callback: () => void;
  };
  duration?: number;
  timestamp: number;
}

export interface UserPresence {
  user_id: string;
  user_name: string;
  avatar_url?: string;
  document_id: string;
  cursor_position?: {
    block_id: string;
    position: number;
  };
  last_seen: number;
  is_typing?: boolean;
}

type WebSocketEventType = 
  | 'document_collaboration'
  | 'health_analysis'
  | 'persona_feedback'
  | 'agent_execution'
  | 'system_notification'
  | 'user_presence';

type WebSocketEventData = 
  | DocumentCollaborationEvent
  | HealthAnalysisEvent
  | PersonaFeedbackEvent
  | AgentExecutionEvent
  | SystemNotificationEvent
  | UserPresence;

type EventHandler<T = WebSocketEventData> = (data: T) => void;

class WebSocketService {
  private socket: Socket | null = null;
  private eventHandlers = new Map<string, Set<EventHandler>>();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = config.websocket.reconnectAttempts;
  private reconnectDelay = config.websocket.reconnectDelay;
  private isConnecting = false;
  private currentUserId: string | null = null;
  private currentDocumentId: string | null = null;

  constructor() {
    this.setupEventHandlers();
  }

  /**
   * Connect to WebSocket server
   */
  async connect(userId: string, authToken: string): Promise<void> {
    if (this.socket?.connected || this.isConnecting) {
      console.log('[WebSocket] Already connected or connecting');
      return;
    }

    this.isConnecting = true;
    this.currentUserId = userId;

    try {
      const wsUrl = config.api.wsUrl;
      
      this.socket = io(wsUrl, {
        auth: {
          token: authToken
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true
      });

      await this.waitForConnection();
      this.setupSocketEventHandlers();
      this.reconnectAttempts = 0;
      
      console.log('[WebSocket] Connected successfully');
    } catch (error) {
      console.error('[WebSocket] Connection failed:', error);
      this.isConnecting = false;
      throw error;
    } finally {
      this.isConnecting = false;
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.currentUserId = null;
      this.currentDocumentId = null;
      console.log('[WebSocket] Disconnected');
    }
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Join a document room for collaboration
   */
  joinDocument(documentId: string): void {
    if (!this.socket || !this.socket.connected) {
      console.warn('[WebSocket] Cannot join document - not connected');
      return;
    }

    // Leave current document if any
    if (this.currentDocumentId && this.currentDocumentId !== documentId) {
      this.leaveDocument();
    }

    this.currentDocumentId = documentId;
    this.socket.emit('join_document', {
      document_id: documentId,
      user_id: this.currentUserId
    });

    console.log(`[WebSocket] Joined document: ${documentId}`);
  }

  /**
   * Leave current document room
   */
  leaveDocument(): void {
    if (!this.socket || !this.currentDocumentId) {
      return;
    }

    this.socket.emit('leave_document', {
      document_id: this.currentDocumentId,
      user_id: this.currentUserId
    });

    console.log(`[WebSocket] Left document: ${this.currentDocumentId}`);
    this.currentDocumentId = null;
  }

  /**
   * Send block update for real-time collaboration
   */
  sendBlockUpdate(blockId: string, content: string, cursorPosition?: number): void {
    if (!this.socket || !this.currentDocumentId) {
      console.warn('[WebSocket] Cannot send block update - not in a document');
      return;
    }

    this.socket.emit('block_update', {
      document_id: this.currentDocumentId,
      block_id: blockId,
      content,
      cursor_position: cursorPosition,
      user_id: this.currentUserId,
      timestamp: Date.now()
    });
  }

  /**
   * Send cursor position update
   */
  sendCursorUpdate(blockId: string, position: number): void {
    if (!this.socket || !this.currentDocumentId) {
      return;
    }

    this.socket.emit('cursor_update', {
      document_id: this.currentDocumentId,
      block_id: blockId,
      position,
      user_id: this.currentUserId,
      timestamp: Date.now()
    });
  }

  /**
   * Send typing indicator
   */
  sendTypingIndicator(blockId: string, isTyping: boolean): void {
    if (!this.socket || !this.currentDocumentId) {
      return;
    }

    this.socket.emit('typing_indicator', {
      document_id: this.currentDocumentId,
      block_id: blockId,
      is_typing: isTyping,
      user_id: this.currentUserId,
      timestamp: Date.now()
    });
  }

  /**
   * Request live health analysis
   */
  requestHealthAnalysis(
    blockId: string, 
    text: string, 
    analysisType: string = 'realtime',
    preferences: Record<string, any> = {}
  ): void {
    if (!this.socket || !this.currentDocumentId) {
      console.warn('[WebSocket] Cannot request health analysis - not connected or no document');
      return;
    }

    this.socket.emit('request_health_analysis', {
      document_id: this.currentDocumentId,
      block_id: blockId,
      text,
      analysis_type: analysisType,
      preferences,
      user_id: this.currentUserId,
      timestamp: Date.now()
    });
  }

  /**
   * Request live persona feedback
   */
  requestPersonaFeedback(
    blockId: string, 
    personaIds: string[], 
    text: string,
    context: Record<string, any> = {},
    options: Record<string, any> = {}
  ): void {
    if (!this.socket || !this.currentDocumentId) {
      console.warn('[WebSocket] Cannot request persona feedback - not connected or no document');
      return;
    }

    this.socket.emit('request_persona_feedback', {
      document_id: this.currentDocumentId,
      block_id: blockId,
      persona_ids: personaIds,
      text,
      context,
      options,
      user_id: this.currentUserId,
      timestamp: Date.now()
    });
  }

  /**
   * Subscribe to WebSocket events
   */
  on<T extends WebSocketEventData>(eventType: WebSocketEventType, handler: EventHandler<T>): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set());
    }
    this.eventHandlers.get(eventType)!.add(handler as EventHandler);
  }

  /**
   * Unsubscribe from WebSocket events
   */
  off<T extends WebSocketEventData>(eventType: WebSocketEventType, handler: EventHandler<T>): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.delete(handler as EventHandler);
    }
  }

  /**
   * Emit event to registered handlers
   */
  private emit(eventType: WebSocketEventType, data: WebSocketEventData): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`[WebSocket] Error in event handler for ${eventType}:`, error);
        }
      });
    }
  }

  /**
   * Wait for socket connection
   */
  private waitForConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not initialized'));
        return;
      }

      const timeout: number = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, 10000);

      this.socket.on('connect', () => {
        clearTimeout(timeout);
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  /**
   * Setup socket event handlers
   */
  private setupSocketEventHandlers(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('[WebSocket] Connected to server');
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', (reason) => {
      console.log('[WebSocket] Disconnected:', reason);
      if (reason === 'io server disconnect') {
        // Server disconnected, attempt to reconnect
        this.attemptReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('[WebSocket] Connection error:', error);
      this.attemptReconnect();
    });

    // Document collaboration events
    this.socket.on('block_updated', (data: DocumentCollaborationEvent) => {
      this.emit('document_collaboration', data);
    });

    this.socket.on('block_created', (data: DocumentCollaborationEvent) => {
      this.emit('document_collaboration', data);
    });

    this.socket.on('block_deleted', (data: DocumentCollaborationEvent) => {
      this.emit('document_collaboration', data);
    });

    this.socket.on('cursor_moved', (data: DocumentCollaborationEvent) => {
      this.emit('document_collaboration', data);
    });

    // User presence events (corrected to single presence_update event)
    this.socket.on('presence_update', (data: UserPresence) => {
      this.emit('user_presence', data);
    });

    this.socket.on('typing_update', (data: UserPresence) => {
      this.emit('user_presence', data);
    });

    // Health analysis events (corrected event names)
    this.socket.on('health_metrics_update', (data: HealthAnalysisEvent) => {
      this.emit('health_analysis', data);
    });

    this.socket.on('health_issues_detected', (data: HealthAnalysisEvent) => {
      this.emit('health_analysis', data);
    });

    // Persona feedback events (corrected event names)
    this.socket.on('persona_feedback_result', (data: PersonaFeedbackEvent) => {
      this.emit('persona_feedback', data);
    });

    // Agent execution events
    this.socket.on('agent_execution_started', (data: AgentExecutionEvent) => {
      this.emit('agent_execution', data);
    });

    this.socket.on('agent_execution_complete', (data: AgentExecutionEvent) => {
      this.emit('agent_execution', data);
    });

    this.socket.on('agent_execution_failed', (data: AgentExecutionEvent) => {
      this.emit('agent_execution', data);
    });

    this.socket.on('agent_batch_complete', (data: AgentExecutionEvent) => {
      this.emit('agent_execution', data);
    });

    // System notifications
    this.socket.on('system_notification', (data: SystemNotificationEvent) => {
      this.emit('system_notification', data);
    });

    // Error handling
    this.socket.on('error', (error) => {
      console.error('[WebSocket] Socket error:', error);
      this.emit('system_notification', {
        type: 'error',
        title: 'Connection Error',
        message: 'WebSocket connection error occurred',
        timestamp: Date.now()
      });
    });
  }

  /**
   * Setup application event handlers
   */
  private setupEventHandlers(): void {
    // Handle page visibility change
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // Page is hidden, reduce activity
        if (this.socket && this.currentDocumentId) {
          this.socket.emit('user_away', {
            document_id: this.currentDocumentId,
            user_id: this.currentUserId
          });
        }
      } else {
        // Page is visible, resume activity
        if (this.socket && this.currentDocumentId) {
          this.socket.emit('user_active', {
            document_id: this.currentDocumentId,
            user_id: this.currentUserId
          });
        }
      }
    });

    // Handle beforeunload to gracefully disconnect
    window.addEventListener('beforeunload', () => {
      if (this.socket && this.currentDocumentId) {
        this.leaveDocument();
      }
      this.disconnect();
    });
  }

  /**
   * Attempt to reconnect with exponential backoff
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('[WebSocket] Max reconnection attempts reached');
      this.emit('system_notification', {
        type: 'error',
        title: 'Connection Lost',
        message: 'Unable to reconnect to server. Please refresh the page.',
        timestamp: Date.now()
      });
      return;
    }

    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts);
    this.reconnectAttempts++;

    console.log(`[WebSocket] Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);

    setTimeout(() => {
      if (this.socket && !this.socket.connected && this.currentUserId) {
        // Get auth token from storage or auth service
        const authToken = localStorage.getItem('auth_token') || '';
        this.connect(this.currentUserId, authToken)
          .catch(error => {
            console.error('[WebSocket] Reconnection failed:', error);
          });
      }
    }, delay);
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): {
    connected: boolean;
    userId: string | null;
    documentId: string | null;
    reconnectAttempts: number;
  } {
    return {
      connected: this.isConnected(),
      userId: this.currentUserId,
      documentId: this.currentDocumentId,
      reconnectAttempts: this.reconnectAttempts
    };
  }

  /**
   * Force reconnection
   */
  forceReconnect(): void {
    this.disconnect();
    this.reconnectAttempts = 0;
    
    if (this.currentUserId) {
      const authToken = localStorage.getItem('auth_token') || '';
      this.connect(this.currentUserId, authToken)
        .catch(error => {
          console.error('[WebSocket] Force reconnection failed:', error);
        });
    }
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();
export default websocketService;