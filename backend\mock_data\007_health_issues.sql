-- Revisionary Mock Data - Part 7: Health Issues
-- Specific problems identified in documents

DO $$
DECLARE
    -- Get user IDs from firebase_uid (from 001_core_data.sql)
    sarah_id UUID;
    alex_id UUID;
    kim_id UUID;
    maya_id UUID;
    james_id UUID;
    
    -- Get document IDs by title (from 001_core_data.sql)
    sarah_climate_doc_id UUID;
    alex_cafe_doc_id UUID;
    kim_neural_doc_id UUID;
    maya_chap1_doc_id UUID;
    james_q4_doc_id UUID;
    
    -- Get block IDs for health references
    sarah_climate_para1_id UUID;
    alex_cafe_para1_id UUID;
    kim_neural_para_id UUID;
    maya_chap1_para1_id UUID;
    james_q4_para_id UUID;
    
    -- Health metric IDs for health_issues references
    sarah_climate_health_id UUID;
    alex_cafe_health_id UUID;
    kim_neural_health_id UUID;
    maya_chap1_health_id UUID;
    james_q4_health_id UUID;
    
BEGIN
    -- Get user IDs from existing users
    SELECT id INTO sarah_id FROM users WHERE firebase_uid = 'firebase_user_001' LIMIT 1;
    SELECT id INTO alex_id FROM users WHERE firebase_uid = 'firebase_user_002' LIMIT 1;
    SELECT id INTO kim_id FROM users WHERE firebase_uid = 'firebase_user_003' LIMIT 1;
    SELECT id INTO maya_id FROM users WHERE firebase_uid = 'firebase_user_004' LIMIT 1;
    SELECT id INTO james_id FROM users WHERE firebase_uid = 'firebase_user_005' LIMIT 1;
    
    -- Get document IDs from existing documents
    SELECT id INTO sarah_climate_doc_id FROM documents WHERE title = 'Climate Change Research Paper' LIMIT 1;
    SELECT id INTO alex_cafe_doc_id FROM documents WHERE title = 'The Midnight Café' LIMIT 1;
    SELECT id INTO kim_neural_doc_id FROM documents WHERE title = 'Neural Networks in Medical Diagnosis' LIMIT 1;
    SELECT id INTO maya_chap1_doc_id FROM documents WHERE title = 'Chronicles of Aetheria: Chapter 1' LIMIT 1;
    SELECT id INTO james_q4_doc_id FROM documents WHERE title = 'Q4 Marketing Strategy Report' LIMIT 1;
    
    -- Get block IDs for health and entity references
    SELECT id INTO sarah_climate_para1_id FROM blocks WHERE content LIKE 'Climate change represents one of the most pressing%' AND document_id = sarah_climate_doc_id LIMIT 1;
    SELECT id INTO alex_cafe_para1_id FROM blocks WHERE content LIKE 'Elena had walked past the corner%' AND document_id = alex_cafe_doc_id LIMIT 1;
    SELECT id INTO kim_neural_para_id FROM blocks WHERE content LIKE 'The integration of artificial neural networks%' AND document_id = kim_neural_doc_id LIMIT 1;
    SELECT id INTO maya_chap1_para1_id FROM blocks WHERE content LIKE 'The ancient crown of Aetheria lay in fragments%' AND document_id = maya_chap1_doc_id LIMIT 1;
    SELECT id INTO james_q4_para_id FROM blocks WHERE content LIKE 'Q4 2024 demonstrated exceptional growth%' AND document_id = james_q4_doc_id LIMIT 1;

    -- Get health metric IDs for health_issues references
    SELECT id INTO sarah_climate_health_id FROM health_metrics WHERE block_id = sarah_climate_para1_id LIMIT 1;
    SELECT id INTO alex_cafe_health_id FROM health_metrics WHERE block_id = alex_cafe_para1_id LIMIT 1;
    SELECT id INTO kim_neural_health_id FROM health_metrics WHERE block_id = kim_neural_para_id LIMIT 1;
    SELECT id INTO maya_chap1_health_id FROM health_metrics WHERE block_id = maya_chap1_para1_id LIMIT 1;
    SELECT id INTO james_q4_health_id FROM health_metrics WHERE block_id = james_q4_para_id LIMIT 1;

    -- =================================================================
    -- HEALTH ISSUES - Specific problems identified in documents
    -- =================================================================
    
    INSERT INTO health_issues (health_metric_id, block_id, issue_type, severity, category, description, position, suggested_fix, is_resolved, confidence) 
    VALUES
    (sarah_climate_health_id, sarah_climate_para1_id, 'readability', 'medium', 'sentence_length',
     'Sentence length averaging 28 words may challenge general readers', '{"start": 0, "end": 150}'::jsonb, 
     'Consider breaking complex sentences into shorter, more digestible segments while maintaining academic rigor', false, 0.78),
    
    (sarah_climate_health_id, sarah_climate_para1_id, 'clarity', 'low', 'terminology',
     'Technical term "anthropogenic" used without definition', '{"start": 85, "end": 98}'::jsonb,
     'Add brief definition in parentheses: "anthropogenic (human-caused)"', false, 0.85),
    
    (alex_cafe_health_id, alex_cafe_para1_id, 'voice', 'low', 'perspective_consistency',
     'Narrative perspective shifts slightly in third paragraph', '{"start": 200, "end": 350}'::jsonb,
     'Maintain consistent third-person limited perspective throughout', true, 0.72),
    
    (kim_neural_health_id, kim_neural_para_id, 'clarity', 'medium', 'methodology_presentation',
     'Methodology section could benefit from visual flowchart', '{"start": 150, "end": 400}'::jsonb,
     'Consider adding a process diagram to illustrate the neural network training pipeline', false, 0.81),
    
    (maya_chap1_health_id, maya_chap1_para1_id, 'voice', 'low', 'terminology_density',
     'Fantasy terminology density peaks in opening paragraph', '{"start": 0, "end": 200}'::jsonb,
     'Balance world-building exposition with accessible language for reader onboarding', false, 0.69),
    
    (james_q4_health_id, james_q4_para_id, 'brand', 'low', 'formatting_consistency',
     'Metric presentation style varies across sections', '{"start": 100, "end": 300}'::jsonb,
     'Standardize percentage and currency formatting throughout document', true, 0.88);

END $$;