"""
WebSocket Server for Revisionary

Purpose: Real-time communication and collaboration features
- Socket.io server with Firebase authentication
- Document collaboration (presence, cursors, auto-save)
- Real-time health analysis notifications
- Agent processing status updates

Features:
- Authenticated socket connections
- Document room management
- Real-time presence tracking
- Live cursor synchronization
- Auto-save coordination
- Agent processing notifications
"""

import asyncio
import json
from typing import Dict, Set, Optional, Any, List
from datetime import datetime
import structlog
import socketio

from core.auth import verify_firebase_token
from core.redis_service import get_redis
from core.settings import settings

logger = structlog.get_logger(__name__)


class RealtimeServer:
    """WebSocket server for real-time features."""
    
    def __init__(self):
        """Initialize the Socket.io server."""
        # Create Socket.io server with CORS support
        self.sio = socketio.AsyncServer(
            cors_allowed_origins=settings.cors_origins,
            cors_credentials=True,
            logger=False,  # Use our structured logger instead
            engineio_logger=False
        )
        
        # Connection tracking
        self.connected_users: Dict[str, Set[str]] = {}  # user_id -> set of session_ids
        self.user_sessions: Dict[str, Dict[str, Any]] = {}  # session_id -> user_info
        self.document_rooms: Dict[str, Set[str]] = {}  # doc_id -> set of session_ids
        self.cursor_positions: Dict[str, Dict[str, Any]] = {}  # session_id -> cursor_data
        
        # Redis for pub/sub
        self.redis = None
        
        # Register event handlers
        self._register_handlers()
        
        logger.info("Realtime server initialized")
    
    async def initialize(self):
        """Initialize async resources."""
        self.redis = await get_redis()
        
        # Start background tasks
        asyncio.create_task(self._redis_subscriber())
        asyncio.create_task(self._cleanup_inactive_sessions())
        
        logger.info("Realtime server async resources initialized")
    
    def _register_handlers(self):
        """Register Socket.io event handlers."""
        
        @self.sio.event
        async def connect(sid, environ, auth):
            """Handle client connection with authentication."""
            try:
                # Verify Firebase token
                if not auth or 'token' not in auth:
                    logger.warning("Connection rejected: missing auth token", sid=sid)
                    return False
                
                user_claims = await verify_firebase_token(auth['token'])
                if not user_claims:
                    logger.warning("Connection rejected: invalid token", sid=sid)
                    return False
                
                user_id = user_claims['uid']
                
                # Store session info
                self.user_sessions[sid] = {
                    'user_id': user_id,
                    'email': user_claims.get('email'),
                    'connected_at': datetime.utcnow().isoformat(),
                    'last_activity': datetime.utcnow().isoformat()
                }
                
                # Track user connections
                if user_id not in self.connected_users:
                    self.connected_users[user_id] = set()
                self.connected_users[user_id].add(sid)
                
                logger.info(
                    "Client connected",
                    sid=sid,
                    user_id=user_id,
                    email=user_claims.get('email')
                )
                
                # Send connection confirmation
                await self.sio.emit('connected', {
                    'status': 'connected',
                    'user_id': user_id,
                    'server_time': datetime.utcnow().isoformat()
                }, room=sid)
                
                return True
                
            except Exception as e:
                logger.error("Connection error", error=str(e), sid=sid)
                return False
        
        @self.sio.event
        async def disconnect(sid):
            """Handle client disconnection."""
            try:
                if sid in self.user_sessions:
                    user_info = self.user_sessions[sid]
                    user_id = user_info['user_id']
                    
                    # Remove from user connections
                    if user_id in self.connected_users:
                        self.connected_users[user_id].discard(sid)
                        if not self.connected_users[user_id]:
                            del self.connected_users[user_id]
                    
                    # Leave all document rooms
                    for doc_id, sessions in self.document_rooms.items():
                        if sid in sessions:
                            sessions.discard(sid)
                            await self._broadcast_presence_update(doc_id)
                    
                    # Clean up cursor position
                    if sid in self.cursor_positions:
                        del self.cursor_positions[sid]
                    
                    # Remove session
                    del self.user_sessions[sid]
                    
                    logger.info(
                        "Client disconnected",
                        sid=sid,
                        user_id=user_id
                    )
                
            except Exception as e:
                logger.error("Disconnect error", error=str(e), sid=sid)
        
        @self.sio.event
        async def join_document(sid, data):
            """Join a document room for collaboration."""
            try:
                if sid not in self.user_sessions:
                    await self.sio.emit('error', {'message': 'Not authenticated'}, room=sid)
                    return
                
                doc_id = data.get('document_id')
                if not doc_id:
                    await self.sio.emit('error', {'message': 'document_id required'}, room=sid)
                    return
                
                # Join document room
                await self.sio.enter_room(sid, f"doc:{doc_id}")
                
                # Track in document rooms
                if doc_id not in self.document_rooms:
                    self.document_rooms[doc_id] = set()
                self.document_rooms[doc_id].add(sid)
                
                user_info = self.user_sessions[sid]
                
                logger.info(
                    "User joined document",
                    sid=sid,
                    user_id=user_info['user_id'],
                    document_id=doc_id
                )
                
                # Send join confirmation
                await self.sio.emit('document_joined', {
                    'document_id': doc_id,
                    'joined_at': datetime.utcnow().isoformat()
                }, room=sid)
                
                # Broadcast presence update
                await self._broadcast_presence_update(doc_id)
                
            except Exception as e:
                logger.error("Join document error", error=str(e), sid=sid)
                await self.sio.emit('error', {'message': 'Failed to join document'}, room=sid)
        
        @self.sio.event
        async def leave_document(sid, data):
            """Leave a document room."""
            try:
                doc_id = data.get('document_id')
                if not doc_id:
                    return
                
                # Leave document room
                await self.sio.leave_room(sid, f"doc:{doc_id}")
                
                # Remove from tracking
                if doc_id in self.document_rooms:
                    self.document_rooms[doc_id].discard(sid)
                    if not self.document_rooms[doc_id]:
                        del self.document_rooms[doc_id]
                
                # Remove cursor position for this document
                if sid in self.cursor_positions:
                    cursor_data = self.cursor_positions[sid]
                    if cursor_data.get('document_id') == doc_id:
                        del self.cursor_positions[sid]
                
                logger.info("User left document", sid=sid, document_id=doc_id)
                
                # Broadcast presence update
                await self._broadcast_presence_update(doc_id)
                
            except Exception as e:
                logger.error("Leave document error", error=str(e), sid=sid)
        
        @self.sio.event
        async def cursor_update(sid, data):
            """Handle cursor position updates."""
            try:
                if sid not in self.user_sessions:
                    return
                
                doc_id = data.get('document_id')
                block_id = data.get('block_id')
                position = data.get('position')
                
                if not doc_id or position is None:
                    return
                
                user_info = self.user_sessions[sid]
                
                # Store cursor position
                self.cursor_positions[sid] = {
                    'document_id': doc_id,
                    'block_id': block_id,
                    'position': position,
                    'user_id': user_info['user_id'],
                    'user_email': user_info['email'],
                    'timestamp': datetime.utcnow().isoformat()
                }
                
                # Broadcast to other users in the document
                await self.sio.emit('cursor_moved', {
                    'user_id': user_info['user_id'],
                    'user_email': user_info['email'],
                    'document_id': doc_id,
                    'block_id': block_id,
                    'position': position,
                    'timestamp': datetime.utcnow().isoformat()
                }, room=f"doc:{doc_id}", skip_sid=sid)
                
            except Exception as e:
                logger.error("Cursor update error", error=str(e), sid=sid)
        
        @self.sio.event
        async def block_update(sid, data):
            """Handle block content updates."""
            try:
                if sid not in self.user_sessions:
                    return
                
                doc_id = data.get('document_id')
                changes = data.get('changes')
                
                if not doc_id or not changes:
                    return
                
                user_info = self.user_sessions[sid]
                
                # Broadcast changes to other users
                await self.sio.emit('document_updated', {
                    'document_id': doc_id,
                    'changes': changes,
                    'user_id': user_info['user_id'],
                    'timestamp': datetime.utcnow().isoformat()
                }, room=f"doc:{doc_id}", skip_sid=sid)
                
                # Trigger auto-save after a delay
                await self._schedule_auto_save(doc_id, user_info['user_id'])
                
            except Exception as e:
                logger.error("Block update error", error=str(e), sid=sid)
        
        @self.sio.event
        async def request_analysis(sid, data):
            """Request AI analysis for text."""
            try:
                if sid not in self.user_sessions:
                    await self.sio.emit('error', {'message': 'Not authenticated'}, room=sid)
                    return
                
                doc_id = data.get('document_id')
                block_id = data.get('block_id')
                text = data.get('text')
                agent_types = data.get('agent_types', ['grammar'])
                
                if not all([doc_id, block_id, text]):
                    await self.sio.emit('error', {'message': 'Missing required fields'}, room=sid)
                    return
                
                user_info = self.user_sessions[sid]
                
                # Create analysis job
                job_data = {
                    'document_id': doc_id,
                    'block_id': block_id,
                    'text': text,
                    'user_id': user_info['user_id'],
                    'response_channel': f"ai:suggestions:{sid}"
                }
                
                # Enqueue analysis jobs
                from core.queue_manager import get_queue_manager
                queue_manager = await get_queue_manager()
                
                job_ids = await queue_manager.enqueue_multi_agent_job(
                    agent_types=agent_types,
                    job_data=job_data,
                    user_tier='professional',  # TODO: Get from user profile
                    user_id=user_info['user_id']
                )
                
                # Send acknowledgment
                await self.sio.emit('analysis_started', {
                    'job_ids': job_ids,
                    'agent_types': agent_types,
                    'started_at': datetime.utcnow().isoformat()
                }, room=sid)
                
                logger.info(
                    "Analysis requested",
                    user_id=user_info['user_id'],
                    document_id=doc_id,
                    block_id=block_id,
                    agent_types=agent_types,
                    job_ids=job_ids
                )
                
            except Exception as e:
                logger.error("Request analysis error", error=str(e), sid=sid)
                await self.sio.emit('error', {'message': 'Failed to start analysis'}, room=sid)
        
        @self.sio.event
        async def request_health_analysis(sid, data):
            """Request real-time health analysis for text with debouncing."""
            try:
                if sid not in self.user_sessions:
                    await self.sio.emit('error', {'message': 'Not authenticated'}, room=sid)
                    return
                
                doc_id = data.get('document_id')
                block_id = data.get('block_id')
                text = data.get('text')
                analysis_type = data.get('analysis_type', 'quick')
                preferences = data.get('preferences', {})
                
                if not all([doc_id, text]):
                    await self.sio.emit('error', {'message': 'document_id and text required'}, room=sid)
                    return
                
                user_info = self.user_sessions[sid]
                
                # Use debounced health service for real-time analysis
                from core.debounced_health_service import get_debounced_health_service
                debounced_service = await get_debounced_health_service()
                
                # Determine priority based on analysis type and real-time nature
                priority = 3 if analysis_type == 'comprehensive' else 2  # High priority for WebSocket
                
                # Request debounced analysis with WebSocket session
                request_id = await debounced_service.request_health_analysis(
                    text=text,
                    user_id=user_info['user_id'],
                    user_tier='professional',  # TODO: Get from user profile
                    document_id=doc_id,
                    block_id=block_id,
                    analysis_type=analysis_type,
                    preferences=preferences,
                    priority=priority,
                    websocket_session=sid  # This will handle the response automatically
                )
                
                # Send acknowledgment
                await self.sio.emit('health_analysis_started', {
                    'request_id': request_id,
                    'document_id': doc_id,
                    'block_id': block_id,
                    'analysis_type': analysis_type,
                    'debounced': True,
                    'started_at': datetime.utcnow().isoformat()
                }, room=sid)
                
                logger.info(
                    "Debounced health analysis requested via WebSocket",
                    user_id=user_info['user_id'],
                    document_id=doc_id,
                    block_id=block_id,
                    analysis_type=analysis_type,
                    request_id=request_id,
                    priority=priority
                )
                
            except Exception as e:
                logger.error("Request health analysis error", error=str(e), sid=sid)
                await self.sio.emit('error', {'message': 'Failed to start health analysis'}, room=sid)
        
        @self.sio.event
        async def request_persona_feedback(sid, data):
            """Request persona feedback for text."""
            try:
                if sid not in self.user_sessions:
                    await self.sio.emit('error', {'message': 'Not authenticated'}, room=sid)
                    return
                
                doc_id = data.get('document_id')
                block_id = data.get('block_id')
                text = data.get('text')
                persona_ids = data.get('persona_ids', [])
                context = data.get('context', {})
                options = data.get('options', {})
                
                if not all([doc_id, text]) or not persona_ids:
                    await self.sio.emit('error', {'message': 'document_id, text, and persona_ids required'}, room=sid)
                    return
                
                user_info = self.user_sessions[sid]
                
                # Create persona feedback job
                job_data = {
                    'text': text,
                    'personas': persona_ids,
                    'analysis_type': 'multi' if len(persona_ids) > 1 else 'single',
                    'context': context,
                    'options': options,
                    'document_id': doc_id,
                    'block_id': block_id,
                    'user_id': user_info['user_id'],
                    'response_channel': f"persona:feedback:{sid}"
                }
                
                # Enqueue persona job
                from core.queue_manager import get_queue_manager
                queue_manager = await get_queue_manager()
                
                job_id = await queue_manager.enqueue_job(
                    worker_type='persona',
                    job_data=job_data,
                    priority=2,  # High priority for WebSocket
                    user_id=user_info['user_id']
                )
                
                # Send acknowledgment
                await self.sio.emit('persona_feedback_started', {
                    'job_id': job_id,
                    'persona_count': len(persona_ids),
                    'analysis_type': job_data['analysis_type'],
                    'document_id': doc_id,
                    'block_id': block_id,
                    'started_at': datetime.utcnow().isoformat()
                }, room=sid)
                
                logger.info(
                    "Persona feedback requested via WebSocket",
                    user_id=user_info['user_id'],
                    document_id=doc_id,
                    block_id=block_id,
                    persona_count=len(persona_ids),
                    job_id=job_id
                )
                
            except Exception as e:
                logger.error("Request persona feedback error", error=str(e), sid=sid)
                await self.sio.emit('error', {'message': 'Failed to start persona feedback'}, room=sid)
        
        @self.sio.event
        async def typing_indicator(sid, data):
            """Handle typing indicator updates."""
            try:
                if sid not in self.user_sessions:
                    return
                
                doc_id = data.get('document_id')
                block_id = data.get('block_id')
                is_typing = data.get('is_typing', False)
                
                if not all([doc_id, block_id]):
                    return
                
                user_info = self.user_sessions[sid]
                
                # Broadcast typing status to other users in the document
                await self.sio.emit('typing_update', {
                    'document_id': doc_id,
                    'block_id': block_id,
                    'user_id': user_info['user_id'],
                    'email': user_info['email'],
                    'is_typing': is_typing,
                    'timestamp': datetime.utcnow().isoformat()
                }, room=f"doc:{doc_id}", skip_sid=sid)
                
                logger.debug(
                    "Typing indicator updated",
                    user_id=user_info['user_id'],
                    document_id=doc_id,
                    block_id=block_id,
                    is_typing=is_typing
                )
                
            except Exception as e:
                logger.error("Typing indicator error", error=str(e), sid=sid)
    
    async def _broadcast_presence_update(self, doc_id: str):
        """Broadcast presence update to document room."""
        try:
            if doc_id not in self.document_rooms:
                return
            
            # Get active users in document
            active_users = []
            for sid in self.document_rooms[doc_id]:
                if sid in self.user_sessions:
                    user_info = self.user_sessions[sid]
                    cursor_data = self.cursor_positions.get(sid, {})
                    
                    active_users.append({
                        'user_id': user_info['user_id'],
                        'email': user_info['email'],
                        'connected_at': user_info['connected_at'],
                        'cursor_position': cursor_data.get('position')
                    })
            
            # Broadcast to document room
            await self.sio.emit('presence_update', {
                'document_id': doc_id,
                'active_users': active_users,
                'total_users': len(active_users),
                'timestamp': datetime.utcnow().isoformat()
            }, room=f"doc:{doc_id}")
            
        except Exception as e:
            logger.error("Broadcast presence error", error=str(e), document_id=doc_id)
    
    async def _schedule_auto_save(self, doc_id: str, user_id: str):
        """Schedule auto-save for a document."""
        try:
            # Use Redis to coordinate auto-save
            auto_save_key = f"autosave:{doc_id}"
            
            # Set auto-save flag with 3 second TTL
            await self.redis.set(auto_save_key, user_id, ttl=3)
            
            # Wait for the TTL to expire, then trigger save
            await asyncio.sleep(3.5)
            
            # Check if the flag still exists (no new changes)
            saved_by = await self.redis.get(auto_save_key)
            if not saved_by:
                # Trigger auto-save
                await self.sio.emit('auto_save_trigger', {
                    'document_id': doc_id,
                    'triggered_at': datetime.utcnow().isoformat()
                }, room=f"doc:{doc_id}")
                
                logger.info("Auto-save triggered", document_id=doc_id, user_id=user_id)
            
        except Exception as e:
            logger.error("Auto-save schedule error", error=str(e), document_id=doc_id)
    
    async def _redis_subscriber(self):
        """Subscribe to Redis pub/sub channels for AI results and document updates."""
        try:
            # Subscribe to AI suggestion channels, persona feedback, and document update channels
            pubsub = self.redis.redis_client.pubsub()
            await pubsub.subscribe("ai:suggestions:*", "persona:feedback:*", "document:updates:*")
            
            logger.info("Redis subscriber started for AI suggestions and document updates")
            
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    try:
                        channel = message['channel'].decode()
                        data = json.loads(message['data'])
                        
                        # Handle AI suggestions
                        if channel.startswith('ai:suggestions:'):
                            session_id = channel.split(':')[-1]
                            
                            # Forward to specific client
                            await self.sio.emit('analysis_result', data, room=session_id)
                        
                        # Handle persona feedback results
                        elif channel.startswith('persona:feedback:'):
                            session_id = channel.split(':')[-1]
                            
                            # Forward persona feedback to specific client
                            await self.sio.emit('persona_feedback_result', data, room=session_id)
                            
                            logger.debug(
                                "Persona feedback forwarded to client",
                                session_id=session_id,
                                analysis_type=data.get('analysis_type'),
                                persona_count=data.get('personas_analyzed', 1)
                            )
                        
                        # Handle document updates (including health metrics)
                        elif channel.startswith('document:updates:'):
                            doc_id = channel.split(':')[-1]
                            
                            # Forward to all users in the document room
                            event_type = data.get('type')
                            
                            if event_type == 'health_update':
                                # Send health-specific event
                                await self.sio.emit('health_metrics_update', {
                                    'document_id': doc_id,
                                    'block_id': data.get('block_id'),
                                    'health_data': data.get('health_data'),
                                    'timestamp': data.get('timestamp')
                                }, room=f"doc:{doc_id}")
                                
                                logger.debug(
                                    "Health metrics forwarded to document room",
                                    document_id=doc_id,
                                    block_id=data.get('block_id'),
                                    overall_score=data.get('health_data', {}).get('overall_score')
                                )
                            else:
                                # Send generic document update
                                await self.sio.emit('document_update', {
                                    'document_id': doc_id,
                                    'update_data': data,
                                    'timestamp': data.get('timestamp', datetime.utcnow().isoformat())
                                }, room=f"doc:{doc_id}")
                            
                    except Exception as e:
                        logger.error("Message processing error", error=str(e), channel=channel)
            
        except Exception as e:
            logger.error("Redis subscriber error", error=str(e))
    
    async def _cleanup_inactive_sessions(self):
        """Clean up inactive sessions periodically."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                
                current_time = datetime.utcnow()
                inactive_sessions = []
                
                for sid, user_info in self.user_sessions.items():
                    last_activity = datetime.fromisoformat(user_info['last_activity'])
                    if (current_time - last_activity).total_seconds() > 3600:  # 1 hour
                        inactive_sessions.append(sid)
                
                for sid in inactive_sessions:
                    await self.sio.disconnect(sid)
                    logger.info("Cleaned up inactive session", sid=sid)
                
            except Exception as e:
                logger.error("Session cleanup error", error=str(e))
    
    async def get_server_stats(self) -> Dict[str, Any]:
        """Get server statistics."""
        return {
            'connected_users': len(self.connected_users),
            'total_sessions': len(self.user_sessions),
            'active_documents': len(self.document_rooms),
            'total_document_sessions': sum(len(sessions) for sessions in self.document_rooms.values()),
            'cursor_positions': len(self.cursor_positions),
            'timestamp': datetime.utcnow().isoformat()
        }


# Global server instance
_realtime_server: Optional[RealtimeServer] = None


async def get_realtime_server() -> RealtimeServer:
    """Get or create realtime server instance."""
    global _realtime_server
    if _realtime_server is None:
        _realtime_server = RealtimeServer()
        await _realtime_server.initialize()
    return _realtime_server


# For standalone running
async def run_websocket_server():
    """Run the WebSocket server standalone."""
    import uvicorn
    from fastapi import FastAPI
    
    # Create FastAPI app with Socket.io
    app = FastAPI(title="Revisionary WebSocket Server")
    
    # Get server instance
    server = await get_realtime_server()
    
    # Mount Socket.io to FastAPI
    app.mount("/socket.io", socketio.ASGIApp(server.sio))
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        stats = await server.get_server_stats()
        return {"status": "healthy", "stats": stats}
    
    # Run server
    config = uvicorn.Config(
        app=app,
        host=settings.api_host,
        port=settings.api_port + 1,  # Use different port for WebSocket
        log_level=settings.log_level.lower()
    )
    
    server_instance = uvicorn.Server(config)
    await server_instance.serve()


if __name__ == "__main__":
    asyncio.run(run_websocket_server())