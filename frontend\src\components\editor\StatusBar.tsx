import React, { useMemo } from 'react';
import {
  ClockIcon,
  DocumentTextIcon,
  CommandLineIcon,
  EyeIcon,
  SparklesIcon,
  ChartBarIcon,
  FireIcon,
  TrophyIcon,
} from '@heroicons/react/24/outline';
import { useAnalyticsStore, useAnalyticsSelectors } from '../../stores/analyticsStore';

interface StatusBarProps {
  content: string;
  isAutoSaving: boolean;
  lastSaved: Date | null;
  readingTime: number;
  focusMode: boolean;
  onToggleFocusMode: () => void;
  writingHealthScore?: number;
  documentId?: string;
}

const StatusBar: React.FC<StatusBarProps> = ({
  content,
  isAutoSaving,
  lastSaved,
  readingTime,
  focusMode,
  onToggleFocusMode,
  writingHealthScore,
  documentId,
}) => {
  // Analytics hooks
  const analyticsStore = useAnalyticsStore();
  const analyticsSelectors = useAnalyticsSelectors();
  
  // Get analytics data
  const todayStats = analyticsSelectors.getTodayStats();
  const currentSession = documentId ? analyticsSelectors.getCurrentSession(documentId) : null;
  const stats = useMemo(() => {
    const text = content.trim();
    const words = text ? text.split(/\s+/).filter(Boolean).length : 0;
    const characters = text.length;
    const charactersNoSpaces = text.replace(/\s/g, '').length;
    const paragraphs = text ? text.split(/\n\s*\n/).filter(Boolean).length : 0;
    const lines = content.split('\n').length;

    return {
      words,
      characters,
      charactersNoSpaces,
      paragraphs,
      lines,
    };
  }, [content]);

  const getTimeAgo = (date: Date | null) => {
    if (!date) return 'Never saved';
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="h-6 px-4 flex items-center justify-between border-t border-slate-200/50 backdrop-blur-xl bg-white/80 text-xs text-slate-600">
      {/* Left section - Document stats */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-1">
          <DocumentTextIcon className="w-3 h-3" />
          <span className="font-medium">{stats.words} words</span>
        </div>
        
        <div className="flex items-center space-x-1">
          <span>{stats.characters} characters</span>
        </div>
        
        <div className="flex items-center space-x-1">
          <span>{stats.paragraphs} paragraphs</span>
        </div>
        
        <div className="flex items-center space-x-1">
          <ClockIcon className="w-3 h-3" />
          <span>{readingTime} min read</span>
        </div>

        {/* Writing Health Score */}
        {writingHealthScore !== undefined && (
          <div className="flex items-center space-x-1">
            <span className="text-slate-500">Writing Health</span>
            <span className={`font-semibold ${
              writingHealthScore >= 85 ? 'text-green-600' :
              writingHealthScore >= 70 ? 'text-yellow-600' : 'text-red-600'
            }`}>
              {Math.round(writingHealthScore)}/100
            </span>
            {writingHealthScore >= 85 ? (
              <span className="text-green-600">✓</span>
            ) : (
              <span className="text-slate-400">○</span>
            )}
          </div>
        )}

        {/* Save status */}
        <div className="flex items-center space-x-1">
          {isAutoSaving ? (
            <>
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
              <span className="text-purple-600">Saving...</span>
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Saved {getTimeAgo(lastSaved)}</span>
            </>
          )}
        </div>
      </div>

      {/* Right section - Analytics insights and shortcuts */}
      <div className="flex items-center space-x-4">
        {/* Today's word count */}
        {todayStats.wordsWritten > 0 && (
          <div className="flex items-center space-x-1 text-slate-500">
            <ChartBarIcon className="w-3 h-3" />
            <span>{todayStats.wordsWritten} today</span>
          </div>
        )}

        {/* Writing streak */}
        {analyticsStore.writingStreak.current > 0 && (
          <div className="flex items-center space-x-1 text-slate-500">
            <FireIcon className="w-3 h-3 text-orange-500" />
            <span className="text-orange-600 font-medium">{analyticsStore.writingStreak.current} day streak</span>
          </div>
        )}

        {/* Session time (if active session) */}
        {currentSession && (
          <div className="flex items-center space-x-1 text-slate-500">
            <ClockIcon className="w-3 h-3" />
            <span>
              {Math.floor((Date.now() - currentSession.startTime.getTime()) / 60000)}m session
            </span>
          </div>
        )}

        {/* Focus mode toggle */}
        <button
          onClick={onToggleFocusMode}
          className={`flex items-center space-x-1 px-2 py-1 rounded transition-colors ${
            focusMode
              ? 'bg-purple-100 text-purple-700'
              : 'hover:bg-slate-100 text-slate-600'
          }`}
        >
          <EyeIcon className="w-3 h-3" />
          <span>Focus</span>
        </button>

        {/* AI indicator with acceptance rate */}
        <div className="flex items-center space-x-1 text-slate-500">
          <SparklesIcon className="w-3 h-3" />
          <span>
            AI {analyticsStore.aiUsageStats.acceptanceRate > 0 
              ? `${analyticsStore.aiUsageStats.acceptanceRate.toFixed(0)}%` 
              : 'Ready'}
          </span>
        </div>

        {/* Keyboard shortcut hint */}
        <div className="flex items-center space-x-1 text-slate-500">
          <div className="flex items-center space-x-1">
            <CommandLineIcon className="w-3 h-3" />
            <span>⌘K</span>
          </div>
          <span>→ Commands</span>
        </div>
      </div>
    </div>
  );
};

export default StatusBar;