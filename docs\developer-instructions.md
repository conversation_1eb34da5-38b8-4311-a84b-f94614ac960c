# AI Writing Assistant – Agent Orchestration Blueprint (v1 · Pre‑Launch)

> **Scope:** Grammar, Style, Structure, Content agents only.
> **Run‑time Store:** *Local Redis* (single‑node) inside the API VM.
> **Post‑launch work:** factuality/consistency, plagiarism, PII/compliance agents.

---

## 1 · Purpose

*Give every engineer a single reference that explains how user‑initiated requests are routed to the right agent(s), how the agents run **in parallel**, and how results are merged into the UI.*

---

## 2 · Agent Catalogue (Launch Set)

| Agent     | ID          | What It Does                                    | Typical Context                  |
| --------- | ----------- | ----------------------------------------------- | -------------------------------- |
| Grammar   | `grammar`   | Fix spelling, punctuation, agreement            | Sentence ±1                      |
| Style     | `style`     | Improve diction, tone, cliché removal           | Paragraph + neighbour            |
| Structure | `structure` | Check flow, transitions, paragraph order        | Section + sibling summaries      |
| Content   | `content`   | Spot logic gaps, strengthen argument/story beat | Section + doc summary (≤6 k tok) |

---

## 3 · Runtime Orchestration Layers

```text
┌──────────────────┐   REST   ┌───────────────────┐   enqueue   ┌──────────────┐
│ Front‑End (React)│ ───────▶ │  API Gateway      │ ───────────▶ │ Cloud Tasks  │
└─────────┬────────┘          └─────────┬─────────┘             └────┬─────────┘
          │ context build                 │ pull                    │ pub  
┌─────────▼────────┐   Redis (L1 cache)    └─────────────┐  Redis (PUB/SUB)  │
│Context Builder   │◀───────────────────────────────────┘                │
└─────────┬────────┘                                                ┌───▼────────┐
          │                                                          │Aggregator  │
┌─────────▼────────┐  parallel jobs                                  └───┬────────┘
│ Agent Worker(4)  │ grammar | style | structure | content  ◀───────────┘
└──────────────────┘  (GPT‑4.1 nano/mini/‑)                             SSE → FE
```

* **Local Redis** inside the same VPC – 4 GB RAM, AOF 1 s, exposes port 6379 internally.
* **Cloud Tasks** rate‑limits calls & balances queue depth.
* Workers are stateless Cloud Run containers (one per agent type) and can scale 0⇢N.

---

## 4 · Activation Matrix & Routing Logic

| User Intent          | Agents Triggered                   | Routing Rules                                           |
| -------------------- | ---------------------------------- | ------------------------------------------------------- |
| `rewrite‑sentence`   | grammar, style                     | Sentence length < 400 tok → **GPT‑4.1 nano**; else mini |
| `improve‑paragraph`  | grammar, style, structure          | Context ≤ 1 k tok → mini; else full 4.1                 |
| `strengthen‑section` | grammar, style, structure, content | If context ≤6 k tok → GPT‑4.1; else Gemini 2.5 Flash    |

#### Rule Engine (pseudo‑code)

```python
agents = intent_to_agents[intent]
for a in agents:
    ctx = build_context(span, a)
    model = pick_model(a, len(ctx))
    enqueue_job(doc_id, span, a, ctx, model)
```

#### Parallel vs Sequential?

* **Parallel by default:** the 4 launch agents have no dependency chain; aggregator streams results as each arrives.
* **Sequential override:** if content agent needs structure output (future), we flag `depends_on:["structure"]` in the job payload.

---

## 5 · Sequence Diagram (Mermaid)

```mermaid
sequenceDiagram
    participant FE as Front‑End
    participant API as Gateway
    participant CTX as Context Builder
    participant Q as Cloud Tasks
    participant G as Grammar Worker
    participant S as Style Worker
    participant St as Structure Worker
    participant C as Content Worker
    participant AG as Aggregator

    FE->>API: POST /refine {span,intent}
    API->>CTX: build_context()
    CTX-->>API: 4 ctx payloads
    API->>Q: enqueue 4 jobs (parallel)
    Q->>G: grammar
    Q->>S: style
    Q->>St: structure
    Q->>C: content
    G-->>AG: suggestion
    S-->>AG: suggestion
    St-->>AG: suggestion
    C-->>AG: suggestion
    AG-->>FE: SSE stream (patch UI)
```

---

## 6 · Context Envelope Rules

```python
if agent=="grammar":  ctx = get_sentences(span,±1)
elif agent=="style":   ctx = get_paragraph(span).merge_neighbors(1)
elif agent=="structure":
    sec = get_section(span)
    ctx = sec.text + sec.child_summaries
else:  # content
    sec = get_section(span)
    ctx = sec.text + get_doc_summary(max_tok=6000)
```

---

## 7 · Data Stores

| Store                                | Purpose                                                             | Plan                                                                           |
| ------------------------------------ | ------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| **Redis (local VM)**                 | Hot cache (prompt → suggestion), PUB/SUB signalling, token counters | docker compose /systemd; 4 GB RAM, AOF every 1 s; monitored via Redis Exporter |
| **Supabase (PostgreSQL + pgvector)** | Document bodies, hierarchical summaries, user prefs                 | Launch free → paid tier                                                        |
| **Cloud Storage**                    | Immutable snapshots & AOF backups                                   | Nearline class                                                                 |

---

## 8 · Timing Targets (p95)

| Stage                   | Target |
| ----------------------- | ------ |
| FE → API ACK            | 60 ms  |
| API → Jobs queued       | 120 ms |
| Worker inference (nano) | 400 ms |
| First suggestion to FE  | 900 ms |
| 4‑agent full return     | ≤2 s   |

---

## 9 · Staleness & Conflict Handling

| Event                                                        | Action                                                                | Data Path                                      | UX Effect                                            |
| ------------------------------------------------------------ | --------------------------------------------------------------------- | ---------------------------------------------- | ---------------------------------------------------- |
| **User edits a span that overlaps an unresolved suggestion** | Gateway publishes `invalidate:{issue_id}` to Redis channel            | Redis SUB → Aggregator → FE                    | Fade‑out annotation; mark comment "stale"            |
| **Agent returns suggestion for a span already changed**      | Worker sets key `suggestion:ID` with TTL = 30 sec & flag `stale:true` | Aggregator ignores; Metrics record wasted call | No duplicate / conflicting bubble                    |
| **Multiple agents flag same tokens**                         | Aggregator merges labels; highest‑severity wins order                 | `conflict_map` held in Redis                   | Single merged comment with tabs per agent            |
| **Sentence rewrite accepted**                                | Context Builder re‑summarises paragraph → bumps `p‑sum` vector        | Upward cache‑invalidations via keyspace events | Structure/Content agents see fresh context next call |

**Implementation note:** every suggestion stores `text_hash` of its original span; if the current text hash differs, UI shows ⚠️ **Stale** badge and agent re‑run button.

---

## 10 · Line‑By‑Line Review Heuristics

To avoid "wall of red," the Review mode runs **density‑aware sampling**:

1. Walk manuscript sentences; compute `issue_score = Σ(agent_confidence × severity_weight)`.
2. Flag top‑K sentences per 400‑word window (K = min(3, ⌈log2(sentences)⌉)).
3. Always include the **first** and **last** sentence of a paragraph if any issue ≥ `high` severity.

Parameter table (tunable):

| Severity | Weight | Grammar Threshold | Content Threshold |
| -------- | ------ | ----------------- | ----------------- |
| critical | 3      | always show       | always show       |
| high     | 2      | score >0.6        | score >0.5        |
| medium   | 1      | window quota      | window quota      |
| low      | 0.5    | hide by default   | hide by default   |

User can toggle **"Show All Minor Issues"** to reveal hidden low‑severity flags.

---

## 11 · Post‑Launch Items (parked)

1. Factuality & Consistency agent (needs retrieval & evidence store).
2. Plagiarism agent (external corpus).
3. Compliance/PII agent.
    · Post‑Launch Items (parked)
4. Factuality & Consistency agent (needs retrieval & evidence store).
5. Plagiarism agent (external corpus).
6. Compliance/PII agent.


# AI Writing Assistant – Senior-Dev Build Guide

*(“Green-field” document — **does not** overwrite the existing Orchestration Blueprint)*

---

## 0 · Purpose & Scope

You will implement the **launch-phase editor back-end** supporting four agents (Grammar, Style, Structure, Content) with user-initiated requests, local Redis for hot state, Firebase for auth, and **optionally** Supabase (PostgreSQL) for durable storage.
This guide lists the stack, install steps, runtime contracts, conflict-resolution logic, and how to keep the design extensible for future agents.

---

## 1 · Environment Setup

| Layer             | Choice                             | Why                                 | Install / Config                                                             |
| ----------------- | ---------------------------------- | ----------------------------------- | ---------------------------------------------------------------------------- |
| OS                | Ubuntu 22.04 LTS                   | VM-friendly, latest libs            | Provision GCE e2-standard-4 (4 vCPU/16 GB)                                   |
| Auth              | **Firebase Auth (email + Google)** | Already used on FE, SDK handles JWT | Service acct JSON in Secret Mgr; set `FIREBASE_PROJECT_ID`                   |
| Hot store         | **Redis 7 (local)**                | <1 ms look-ups, pub/sub, counters   | `apt install redis`; `appendonly yes`; `requirepass` via env                 |
| Durable doc store | **Supabase (production)**          | SQL, RLS, changefeeds               | Production: Create project, grab `SUPABASE_URL/KEY`. Local dev: see §1.1 below |
| Object snapshots  | GCS “ai-docs” bucket               | cheap immutable backups             | `gsutil mb -c nearline gs://ai-docs`                                         |

### 1.1 Database Policy by Environment

**Production & Staging**: Supabase PostgreSQL is mandatory (outline sidebar requires SQL hierarchical queries).

**Local Development**: Two options:
```bash
# Option A: Use Supabase (recommended)
export USE_SUPABASE=true
export DATABASE_URL="postgresql://..."

# Option B: SQLite fallback (limited features)
export USE_SUPABASE=false  
export DATABASE_URL="sqlite:///./revisionary.db"
# Note: Outline sidebar will be disabled, some features unavailable
```

---

## 2 · Repo & Branch Strategy

```
repo/
 ├ backend/
 │   ├ api/            # FastAPI app
 │   ├ workers/        # Agent containers
 │   ├ scripts/        # one-shot admin
 │   └ tests/
 ├ infra/              # Terraform for VM, firewall, GCS
 └ docs/               # Architecture + ADRs
```

* `main` = stable; `dev/*` feature branches; PRs require CI green + code-owner.
* Secrets via **`docker-compose.override.yml`** in local dev, Secret Manager in prod.

---

## 3 · Local Redis Configuration

```bash
sudo apt install redis-server
sudo nano /etc/redis/redis.conf
  # --- tweaks ---
  requirepass ${REDIS_PASS}
  appendonly yes
  maxmemory 2gb
  maxmemory-policy allkeys-lfu
  notify-keyspace-events Exg
sudo systemctl restart redis
```

*Ports:* expose **6379** only on internal VPC; use UFW to drop external traffic.
*Monitoring:* `redis_exporter` on **:9121** → Prometheus; alert on `used_memory >1.8 GB`.

---

## 4 · Agent Worker Images

| Agent     | Docker image            | Default model | RAM / CPU    |
| --------- | ----------------------- | ------------- | ------------ |
| Grammar   | `aiwa/worker-grammar`   | GPT-4.1 nano  | 256 MB / 0.2 |
| Style     | `aiwa/worker-style`     | GPT-4.1 mini  | 512 MB / 0.3 |
| Structure | `aiwa/worker-structure` | GPT-4.1       | 1 GB / 0.5   |
| Content   | `aiwa/worker-content`   | GPT-4.1       | 2 GB / 1     |

Each worker pulls jobs from **Redis Streams** (`XREADGROUP`) so you can scale with `docker compose up --scale worker-style=3`.

---

## 5 · Routing & Parallelism

```python
def handle_request(payload):
    span = payload["span"]
    intent = payload["intent"]           # rewrite / improve / analyse
    agents = INTENT_TABLE[intent]        # e.g. ["grammar","style"]
    for a in agents:
        ctx = build_context(span,a)
        model = pick_model(a,len(ctx))
        redis.xadd("jobs", {
            "doc": payload["doc_id"],
            "span": json.dumps(span),
            "agent": a,
            "model": model,
            "ctx": ctx,
        })
```

*Jobs are independent* → workers run in parallel; the **Aggregator** subscribes to `results:{doc_id}` channel and streams the first arriving suggestion back to the FE via SSE.

---

## 6 · Conflict-Handling Pipeline

| Trigger                                                          | Redis Action                                                                      | FE Response                                            |
| ---------------------------------------------------------------- | --------------------------------------------------------------------------------- | ------------------------------------------------------ |
| User edits text that changes `text_hash` of an active suggestion | `PUBLISH stale <issue_id>`                                                        | Badge “stale” → collapses comment                      |
| Worker finishes job but text has changed                         | set `suggestion:{id}` `stale=1` (TTL 30 s)                                        | Aggregator ignores                                     |
| Two agents mark same tokens                                      | Aggregator merges; keeps highest-priority label                                   | Single comment w/ tabs                                 |
| Accept fix                                                       | FE sends `PATCH /doc/{id}`; API updates text, recalcs hash, invalidates summaries | Structure/Content jobs auto re-run via key-space event |

**Hash method:** `sha256(normalise(span_text))` stored with each suggestion.

---

## 7 · Redis-Only vs Supabase

| Requirement                       | Redis-only   | +Supabase               |
| --------------------------------- | ------------ | ----------------------- |
| Millisecond pub/sub               | ✔️           | ✔️ (needs side-channel) |
| Durable document history          | ❌ (AOF only) | ✔️ (`versions` table)   |
| Row-level security / multi-tenant | ❌            | ✔️                      |
| Structured analytics queries      | ❌            | ✔️ (SQL)                |
| Cost (hobby)                      | ≈ \$27/mo VM | \$25–45/mo + VM         |

**Decision rule:**
*Start with Redis-only* if you just need hot suggestions and are OK storing the canonical manuscript in GCS.
Add Supabase when you need SQL joins (billing, team analytics) or row-level security.

---

## 8 · Context Builder & Caching

*Paragraph summaries* (`p-sum:*`) and *section summaries* (`s-sum:*`) cached in Redis 60 min.
Use **LRU** within each worker to dodge Redis hits:

```python
@lru_cache(maxsize=2048)
def get_paragraph_summary(pid): ...
```

---

## 9 · Firebase Guard

```python
from firebase_admin import auth
def verify_token(id_token):
    try:
        return auth.verify_id_token(id_token, check_revoked=True)
    except auth.InvalidIdTokenError:
        raise HTTPException(401,"Invalid Firebase token")
```

In FastAPI, declare a dependency so every route has `user_id` in `request.state.user`.

---

## 10 · Future Agent Plug-in

1. Add metadata in `AGENT_REGISTRY.json`.
2. Build Dockerfile in `workers/newagent/`.
3. Extend `INTENT_TABLE`.
4. Workers auto-register on startup:
   `redis.sadd("agents:active", agent_id)`.

---

## 11 · Dev-to-Prod Checklist

* [ ] Terraform - provision VM, firewall, GCS bucket, Secret Mgr.
* [ ] GitHub Actions: build & push worker images, restart containers via SSH.
* [ ] Prometheus + Grafana dashboards (Redis, worker CPU, model latency).
* [ ] Alertmanager: p95 latency >2 s or Redis used\_memory >1.8 GB.
* [ ] Daily cron dump Redis AOF to `gs://ai-docs/backups/%Y%m%d.aof`.

---

## 12 · Call-outs & Gotchas

1. **Redis persistence** – AOF every 1 s doubles write traffic; size \~3 GB/mo → watch disk.
2. **Firebase rate limits** – cache verified JWT (`kid`) in Redis for 60 min.
3. **Token accounting** – each worker `INCRBY token_count:<user>`; throttle at plan limit.
4. **CORS** – expose `/sse` on gateway with `Access-Control-Allow-Headers: Authorization`.
5. **LMOps** – keep model routing in env files so we can switch 4.1→4.2 without redeploy.

---

### That’s the green-field build spec

Copy this document into your dev handbook; it co-exists with the earlier “Orchestration Blueprint” and covers install → run → extend while highlighting conflict management and the Redis-vs-Supabase decision.


### ⬇️ **Add-On Document** — “Rendering & Navigating the Manuscript”

*(Attach this as ***docs/editor-storage-indexing.md*** alongside the earlier Build Guide. It augments—not overwrites—the guide.)*

---

## 1 · Why Manuscript ≠ One Big Blob

Our API must:

1. **Paint the editor fast** (sub-200 ms first paint even for 100 k-word novels).
2. **Render an outline sidebar** (chapters / sections).
3. **Address text precisely** (line/para offsets for suggestions & diffs).
4. **Support concurrent edits** without full-doc re-writes.

That means we need a **block-level storage model + outline index** rather than a single Markdown file.

---

## 2 · Storage Model (PostgreSQL/Supabase)

*(Redis = hot cache only)*

| Table       | Purpose                              | Key Columns                                    |           |                                    |
| ----------- | ------------------------------------ | ---------------------------------------------- | --------- | ---------------------------------- |
| `documents` | metadata row per doc                 | `id, owner_id, title, created_at`              |           |                                    |
| `blocks`    | **canonical text nodes**             | \`id, doc\_id, parent\_id, idx, type('chapter' | 'section' | 'para'), text, hash\`              |
| `versions`  | immutable snapshots                  | `block_id, version_no, text, author_id, ts`    |           |                                    |
| `summaries` | cached embeddings / 64-tok summaries | \`block\_id, level('para'                      | 'section' | 'doc'), summary\_text, embedding\` |

*Indexes:* `(doc_id, parent_id, idx)` for fast sibling scans; GIN on `to_tsvector('english', text)` for full-text jump-to search.

---

## 3 · Real-Time Editor API

### 3.1 Routes

```
GET  /doc/{id}/outline           -> list of {block_id, type, text (trim), idx}
GET  /block/{id}                 -> full text + suggestions
PATCH /block/{id}                -> {text, cursor_pos, client_hash}
POST  /block/{id}/suggestions    -> {agent_mask, intent}
SSE   /doc/{id}/events           -> delta stream (CRDT ops, suggestion patches)
```

### 3.2 Payload Example

```jsonc
// PATCH /block/987
{
  "text": "Sarah hesitated at the threshold...",
  "cursor_pos": 132,
  "client_hash": "a1f5c..."
}
```

*Backend verifies `client_hash` vs `blocks.hash` → rejects if stale (409) or auto-merges small overlaps.*

---

## 4 · Outline Generation

* On **save** or **bulk import**:

  1. Parse Markdown/Docx headings → create/update `blocks` rows of type `chapter|section`.
  2. Attach paragraph nodes underneath in natural order.
  3. Generate outline JSON and cache it in Redis (`outline:{doc_id}` TTL=10 min).

* FE calls `/doc/{id}/outline` → if Redis miss, API streams DB rows and rebuilds cache.

---

## 5 · Editor Rendering Strategy (FE)

1. **Virtualised list** (react-window) renders \~30 blocks in DOM; off-screen blocks lazy-load.
2. When user clicks a chapter, UI scrolls to the first paragraph of that `block_id`.
3. Suggestions anchor via `data-block="987"` attributes—no offset drift when other sections expand.

---

## 6 · Diff & Conflict Logic (block level)

| Scenario                                           | Resolution                                                                                                     |
| -------------------------------------------------- | -------------------------------------------------------------------------------------------------------------- |
| Two users edit same **paragraph**                  | OT merge (character diff) if edit distance <25 %; else second save returns 409 → FE prompts to merge manually. |
| User edits paragraph that has **stale suggestion** | Redis pub `stale:<suggestion_id>` → FE greys it out.                                                           |
| Structural edit (split/merge paragraphs)           | FE emits `OPER` ops over SSE; backend updates `blocks` rows inside a txn, bumps parent section hash.           |

---

## 7 · Redis Hot Paths

| Key pattern                                     | Value                           | TTL                                |
| ----------------------------------------------- | ------------------------------- | ---------------------------------- |
| `outline:{doc}`                                 | JSON outline                    | 10 min                             |
| `block:{id}`                                    | para JSON + suggestions         | 30 min                             |
| `p-sum:{id}`                                    | 64-tok summary                  | 60 min                             |
| Streams `jobs`, `results:{doc}`, `events:{doc}` | agent jobs / replies / CRDT ops | infinite (trim after 10 k entries) |

*(Redis only accelerates; PostgreSQL remains source-of-truth.)*

---

## 8 · LLM Context Windows (per request)

| Agent     | Context builder source                                |
| --------- | ----------------------------------------------------- |
| Grammar   | The single **block text** + ±1 sibling paragraph      |
| Style     | Parent section text (concat sibling blocks, ≤1 k tok) |
| Structure | All section blocks + section summary (≤4 k tok)       |
| Content   | Section blocks + **doc summary** (≤6 k tok)           |

Outline data lets the builder jump to siblings/parent in O(1) with `parent_id`.

---

## 9 · To Deploy without Supabase?

*Impossible* to deliver outline sidebar or track per-paragraph versions if you only have Redis.
Redis is volatile & key-value—great for caching, not for hierarchical state.
So **yes**, you need **a durable relational store** (Supabase Postgres, AlloyDB, or CloudSQL).
Minimum footprint:

```sql
CREATE TABLE documents  (...);
CREATE TABLE blocks     (...);
```

Disk cost for 1 M paragraphs ≈ 0.6 GB.

---

## 10 · Tasks for Senior Dev

1. **Provision Postgres** (Supabase or CloudSQL) + apply `schema.sql`.
2. Implement outline import script (`scripts/import_doc.py`).
3. Finish FastAPI CRUD for blocks + SSE events.
4. Integrate react-window and `/outline` in the FE.
5. Wire conflict-detection (hash check) on `PATCH /block`.
6. Confirm Redis keys trim correctly, add Grafana panels (`outline hits`, `block hits`).
7. Unit-test diff/merge on paragraph splits & merges.

---

### Deliverable

1. **Editor loads 100 k-word doc in <400 ms first paint** (outline + first screen).
2. Highlight → “Rewrite” hits correct agent with section context.
3. Concurrent edits by two browsers auto-merge or error per policy.
4. Outline sidebar updates if a new `## Heading` is added and saved.

---

This new document answers the “how do we showcase text & navigate chapters” gap while clarifying why “Redis + durable SQL” is the minimal combo. Merge it into the repo’s `docs/` folder—no existing file is overwritten.

Created an **additional file** (it doesn’t overwrite anything already written):

> **docs/cost-optimization-playbook.md**

---

## 🔑  What’s Inside

| Section                                                | Why it matters                                                                                                                     |                                                    |
| ------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------- |
| **1 · Budget Targets**                                 | Hard monthly caps for infra (\$150) and LLM tokens (\$0.10 / active-user hr).                                                      |                                                    |
| **2 · VM & Redis Sizing Cheatsheet**                   | Shows which GCE machine types hit sweet-spot price/perf and how to keep Redis under 2 GB RAM.                                      |                                                    |
| **3 · PostgreSQL (Supabase / Cloud SQL) Minimisation** | Tier-selection matrix, autovacuum tweaks, and nightly \`pg\_dump                                                                   | gzip\` offload to Nearline to avoid storage creep. |
| **4 · Worker Autoscaling Rules**                       | Docker Compose + `--scale` heuristics for dev; production Cloud-Run concurrency & min-instances settings so we never pay for idle. |                                                    |
| **5 · Token-Spend Guardrails**                         | `token_count:<user>` Redis counters, user-plan limits, and how to return HTTP 429 throttle warnings.                               |                                                    |
| **6 · Caching ROI Table**                              | Exact Redis TTLs vs hit-rates; explains why 64-tok paragraph summaries save \~60 % tokens for structure/content agents.            |                                                    |
| **7 · Monitoring & Alerting**                          | Prometheus queries + alert thresholds: `redis_used_memory > 85 %`, `gcp_vm_cpu > 65 % 5-min`, `llm_cost_5m > $1`, etc.             |                                                    |
| **8 · Monthly Cost Checklist**                         | One-pager the dev runs on the first business day of the month (check GCP billing export, Supabase credit burn, OpenAI spend JSON). |                                                    |

---

### Quick Glimpse – Section 1 Excerpt

```markdown
### 1 · Budget Targets

| Line-item             | Cap ($/mo) | Enforcement                                   |
|-----------------------|-----------:|-----------------------------------------------|
| **GCE VM** (e2-std-4) |    ≤ 55    | Pre-emptible OK for workers; 30 GB SSD only   |
| **OpenAI + Gemini**   |    ≤ 50    | `env MAX_TOKEN_COST_MONTH=50`; Redis metering |
| **Supabase** (Launch) |    ≤ 30    | Free → Launch tier; watch disk at 2 GB        |
| **Back-ups & logs**   |    ≤ 10    | Nearline + 30-day retention                   |
| **Misc (egress)**     |     ≈ 5    | Cloud Build cache, Prometheus external        |
| **Total**             |  **≤150**  | alert if Google Billing export day-to-date > cap |
```

---

### How to Use

1. **Pull** the new file from `docs/cost-optimization-playbook.md`.
2. Skim Sections 2-8 for step-by-step knobs: instance reservations, min-instance = 0 on non-critical workers, Redis `maxmemory-policy allkeys-lfu`, etc.
3. Wire the **token guardrails** into the `worker-base` class before launching staging.

This keeps the build guide + storage/indexing doc untouched while giving the senior dev a crystal-clear, cost-first operating manual.


