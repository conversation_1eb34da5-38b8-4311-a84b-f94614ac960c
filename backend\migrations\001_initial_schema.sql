-- Revisionary Database Schema - Initial Migration
-- This script creates the complete database schema for Revisionary
-- Run this in Supabase SQL Editor or via migration tools

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "ltree";
CREATE EXTENSION IF NOT EXISTS "vector";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create all enums first
CREATE TYPE subscription_tier_enum AS ENUM ('free', 'professional', 'studio', 'enterprise');
CREATE TYPE subscription_status_enum AS ENUM ('active', 'canceled', 'past_due', 'paused');
CREATE TYPE document_type_enum AS ENUM ('creative', 'academic', 'professional', 'general', 'technical', 'legal');
CREATE TYPE document_status_enum AS ENUM ('draft', 'editing', 'review', 'published', 'archived');
CREATE TYPE block_type_enum AS ENUM ('document', 'chapter', 'section', 'subsection', 'paragraph', 'heading', 'list', 'quote', 'code');
CREATE TYPE version_change_type_enum AS ENUM ('create', 'edit', 'ai_suggestion', 'collaboration', 'import', 'restore');
CREATE TYPE agent_type_enum AS ENUM ('grammar', 'style', 'structure', 'content', 'research', 'citation', 'technical', 'creative');
CREATE TYPE suggestion_severity_enum AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE suggestion_status_enum AS ENUM ('pending', 'accepted', 'rejected', 'stale', 'expired');
CREATE TYPE suggestion_feedback_enum AS ENUM ('helpful', 'not_helpful', 'incorrect');
CREATE TYPE summary_level_enum AS ENUM ('paragraph', 'section', 'chapter', 'document');
CREATE TYPE collaboration_role_enum AS ENUM ('viewer', 'commenter', 'editor', 'admin');
CREATE TYPE collaboration_status_enum AS ENUM ('pending', 'active', 'declined', 'revoked');
CREATE TYPE comment_type_enum AS ENUM ('general', 'suggestion', 'question', 'issue');
CREATE TYPE comment_status_enum AS ENUM ('open', 'resolved', 'archived');
CREATE TYPE achievement_type_enum AS ENUM ('grammar_master', 'style_savant', 'structure_specialist', 'content_creator', 'rising_star', 'consistent_improver', 'perfect_score', 'prolific_writer', 'academic_scholar', 'creative_genius', 'professional_powerhouse', 'technical_expert');
CREATE TYPE achievement_level_enum AS ENUM ('bronze', 'silver', 'gold', 'platinum');
CREATE TYPE challenge_type_enum AS ENUM ('grammar_perfection', 'style_improvement', 'structure_mastery', 'content_quality', 'word_count', 'vocabulary_diversity', 'readability_target', 'error_reduction');
CREATE TYPE challenge_difficulty_enum AS ENUM ('easy', 'medium', 'hard', 'expert');
CREATE TYPE persona_type_enum AS ENUM ('academic', 'professional', 'creative', 'personal', 'technical', 'cultural', 'age_specific', 'accessibility');
CREATE TYPE agent_capability_enum AS ENUM ('edit', 'add', 'delete', 'restructure', 'expand', 'condense', 'find_duplicates', 'fact_check', 'consistency_check', 'citation_validation', 'tone_adjustment', 'voice_consistency', 'readability_optimization');
CREATE TYPE style_rule_category_enum AS ENUM ('grammar', 'punctuation', 'capitalization', 'formatting', 'style_preferences', 'vocabulary', 'sentence_structure', 'tone');
CREATE TYPE health_issue_type_enum AS ENUM ('readability', 'clarity', 'voice', 'inclusivity', 'brand', 'grammar', 'style');
CREATE TYPE health_issue_severity_enum AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE entity_type_enum AS ENUM ('character', 'location', 'organization', 'concept', 'object', 'event', 'theme');
CREATE TYPE relationship_type_enum AS ENUM ('family', 'friendship', 'professional', 'romantic', 'antagonistic', 'hierarchical', 'geographic', 'temporal', 'causal', 'thematic');
CREATE TYPE violation_type_enum AS ENUM ('attribute_conflict', 'timeline_conflict', 'location_conflict', 'relationship_conflict', 'personality_conflict', 'physical_impossibility');
CREATE TYPE consistency_severity_enum AS ENUM ('minor', 'moderate', 'major', 'critical');
CREATE TYPE citation_type_enum AS ENUM ('book', 'journal_article', 'conference_paper', 'thesis', 'report', 'website', 'news_article', 'blog_post', 'video', 'podcast', 'other');
CREATE TYPE citation_style_enum AS ENUM ('apa', 'mla', 'chicago', 'harvard', 'ieee', 'vancouver');
CREATE TYPE export_format_enum AS ENUM ('pdf', 'docx', 'markdown', 'html', 'epub', 'latex');
CREATE TYPE export_status_enum AS ENUM ('pending', 'processing', 'completed', 'failed', 'expired');
CREATE TYPE template_category_enum AS ENUM ('novel', 'short_story', 'screenplay', 'research_paper', 'essay', 'business_plan', 'report', 'letter', 'blog_post', 'article');

-- 1. Users Table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    firebase_uid VARCHAR(128) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    avatar_url TEXT,
    subscription_tier subscription_tier_enum NOT NULL DEFAULT 'free',
    subscription_status subscription_status_enum NOT NULL DEFAULT 'active',
    subscription_period_start TIMESTAMPTZ,
    subscription_period_end TIMESTAMPTZ,
    onboarding_completed BOOLEAN DEFAULT FALSE,
    preferences JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_active_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT users_email_valid CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT users_preferences_valid CHECK (jsonb_typeof(preferences) = 'object')
);

-- 2. Documents Table
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    type document_type_enum NOT NULL,
    status document_status_enum DEFAULT 'draft',
    description TEXT,
    word_count INTEGER DEFAULT 0,
    character_count INTEGER DEFAULT 0,
    version INTEGER DEFAULT 1,
    is_template BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    language VARCHAR(10) DEFAULT 'en',
    metadata JSONB DEFAULT '{}'::jsonb,
    settings JSONB DEFAULT '{}'::jsonb,
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    published_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ,
    
    -- Computed columns
    slug VARCHAR(255) GENERATED ALWAYS AS (
        LOWER(REGEXP_REPLACE(title, '[^a-zA-Z0-9]+', '-', 'g'))
    ) STORED,
    
    -- Constraints
    CONSTRAINT documents_title_not_empty CHECK (LENGTH(TRIM(title)) > 0),
    CONSTRAINT documents_word_count_positive CHECK (word_count >= 0),
    CONSTRAINT documents_character_count_positive CHECK (character_count >= 0),
    CONSTRAINT documents_metadata_valid CHECK (jsonb_typeof(metadata) = 'object'),
    CONSTRAINT documents_settings_valid CHECK (jsonb_typeof(settings) = 'object')
);

-- 3. Blocks Table (Hierarchical Document Structure)
CREATE TABLE blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    type block_type_enum NOT NULL,
    position INTEGER NOT NULL,
    depth INTEGER DEFAULT 0,
    path LTREE, -- Materialized path for efficient tree queries
    content TEXT,
    content_hash VARCHAR(64), -- SHA256 hash for conflict detection
    content_length INTEGER GENERATED ALWAYS AS (LENGTH(content)) STORED,
    word_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT blocks_position_positive CHECK (position >= 0),
    CONSTRAINT blocks_depth_positive CHECK (depth >= 0),
    CONSTRAINT blocks_word_count_positive CHECK (word_count >= 0),
    CONSTRAINT blocks_content_hash_valid CHECK (content_hash ~ '^[a-f0-9]{64}$'),
    CONSTRAINT blocks_metadata_valid CHECK (jsonb_typeof(metadata) = 'object'),
    
    -- Tree structure constraints
    CONSTRAINT blocks_parent_different CHECK (id != parent_id),
    CONSTRAINT blocks_root_no_parent CHECK (
        (type = 'document' AND parent_id IS NULL) OR 
        (type != 'document' AND parent_id IS NOT NULL)
    )
);

-- 4. Versions Table (Document History)
CREATE TABLE versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    content TEXT NOT NULL,
    content_hash VARCHAR(64) NOT NULL,
    word_count INTEGER DEFAULT 0,
    author_id UUID REFERENCES users(id),
    change_summary TEXT,
    change_type version_change_type_enum DEFAULT 'edit',
    diff_ops JSONB, -- JSON patch operations
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT versions_version_number_positive CHECK (version_number > 0),
    CONSTRAINT versions_word_count_positive CHECK (word_count >= 0),
    CONSTRAINT versions_content_hash_valid CHECK (content_hash ~ '^[a-f0-9]{64}$'),
    CONSTRAINT versions_diff_ops_valid CHECK (
        diff_ops IS NULL OR jsonb_typeof(diff_ops) = 'array'
    ),
    
    UNIQUE(block_id, version_number)
);

-- 5. Suggestions Table (AI Suggestions)
CREATE TABLE suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    agent_type agent_type_enum NOT NULL,
    severity suggestion_severity_enum NOT NULL,
    category VARCHAR(50),
    original_text TEXT NOT NULL,
    suggested_text TEXT NOT NULL,
    explanation TEXT,
    confidence DECIMAL(3,2) CHECK (confidence >= 0 AND confidence <= 1),
    position JSONB NOT NULL, -- {start: int, end: int, line: int}
    original_hash VARCHAR(64) NOT NULL, -- Hash of the original block content
    status suggestion_status_enum DEFAULT 'pending',
    user_feedback suggestion_feedback_enum,
    feedback_note TEXT,
    tokens_used INTEGER DEFAULT 0,
    model_used VARCHAR(50),
    processing_time_ms INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    resolved_at TIMESTAMPTZ,
    resolved_by UUID REFERENCES users(id),
    expires_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT suggestions_position_valid CHECK (
        jsonb_typeof(position) = 'object' AND
        position ? 'start' AND position ? 'end'
    ),
    CONSTRAINT suggestions_original_hash_valid CHECK (original_hash ~ '^[a-f0-9]{64}$'),
    CONSTRAINT suggestions_tokens_positive CHECK (tokens_used >= 0),
    CONSTRAINT suggestions_processing_time_positive CHECK (processing_time_ms >= 0),
    CONSTRAINT suggestions_confidence_range CHECK (confidence IS NULL OR (confidence >= 0 AND confidence <= 1))
);

-- 6. Summaries Table (Cached AI Summaries)
CREATE TABLE summaries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    level summary_level_enum NOT NULL,
    summary_text TEXT NOT NULL,
    key_points TEXT[],
    embedding vector(1536), -- OpenAI embedding dimension
    token_count INTEGER DEFAULT 0,
    model_used VARCHAR(50),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    content_hash VARCHAR(64) NOT NULL, -- Hash of original content
    
    -- Constraints
    CONSTRAINT summaries_token_count_positive CHECK (token_count >= 0),
    CONSTRAINT summaries_content_hash_valid CHECK (content_hash ~ '^[a-f0-9]{64}$'),
    CONSTRAINT summaries_summary_not_empty CHECK (LENGTH(TRIM(summary_text)) > 0),
    
    UNIQUE(block_id, level, content_hash)
);

-- 7. Collaborations Table
CREATE TABLE collaborations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role collaboration_role_enum NOT NULL,
    status collaboration_status_enum DEFAULT 'pending',
    invited_by UUID REFERENCES users(id),
    permissions JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT collaborations_permissions_valid CHECK (jsonb_typeof(permissions) = 'object'),
    
    UNIQUE(document_id, user_id)
);

-- 8. Comments Table
CREATE TABLE comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
    author_id UUID REFERENCES users(id),
    parent_id UUID REFERENCES comments(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    position JSONB, -- {start: int, end: int} for inline comments
    type comment_type_enum DEFAULT 'general',
    status comment_status_enum DEFAULT 'open',
    resolved_by UUID REFERENCES users(id),
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT comments_content_not_empty CHECK (LENGTH(TRIM(content)) > 0),
    CONSTRAINT comments_position_valid CHECK (
        position IS NULL OR (
            jsonb_typeof(position) = 'object' AND
            position ? 'start' AND position ? 'end'
        )
    ),
    CONSTRAINT comments_parent_different CHECK (id != parent_id)
);

-- 9. Document Scores Table
CREATE TABLE document_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    total_score DECIMAL(5,2) NOT NULL,
    grammar_score DECIMAL(5,2) NOT NULL,
    style_score DECIMAL(5,2) NOT NULL,
    structure_score DECIMAL(5,2) NOT NULL,
    content_score DECIMAL(5,2) NOT NULL,
    word_count INTEGER NOT NULL,
    document_type document_type_enum NOT NULL,
    detailed_breakdown JSONB NOT NULL,
    improvement_suggestions TEXT[],
    scoring_version VARCHAR(10) DEFAULT '1.0',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT document_scores_total_range CHECK (total_score >= 0 AND total_score <= 100),
    CONSTRAINT document_scores_component_range CHECK (
        grammar_score >= 0 AND grammar_score <= 25 AND
        style_score >= 0 AND style_score <= 25 AND
        structure_score >= 0 AND structure_score <= 25 AND
        content_score >= 0 AND content_score <= 25
    ),
    CONSTRAINT document_scores_word_count_positive CHECK (word_count > 0)
);

-- Continue with remaining tables...
-- This file is getting long, so I'll split it into multiple files for better organization.