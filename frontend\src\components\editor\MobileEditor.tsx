import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  SparklesIcon,
  ChatBubbleBottomCenterTextIcon,
  DocumentTextIcon,
  Cog6ToothIcon,
  ArrowLeftIcon,
  BoltIcon,
  ListBulletIcon,
  EyeIcon,
  ShareIcon,
  XMarkIcon,
  CheckIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import {
  BoldIcon,
  ItalicIcon,
  UnderlineIcon,
  LinkIcon
} from '@heroicons/react/24/solid';

interface AISuggestion {
  id: string;
  type: string;
  text: string;
  original: string;
  suggested: string;
}

interface Comment {
  id: string;
  author: string;
  text: string;
  timestamp: string;
  resolved: boolean;
}

interface OutlineItem {
  id: string;
  title: string;
  level: number;
  wordCount: number;
}

interface MobileEditorProps {
  documentId?: string;
  initialContent?: string;
  onSave?: (content: string) => void;
  onBack?: () => void;
  suggestions?: AISuggestion[];
  comments?: Comment[];
  outline?: OutlineItem[];
  onAcceptSuggestion?: (id: string) => void;
  onDismissSuggestion?: (id: string) => void;
  onResolveComment?: (commentId: string) => void;
  onReplyToComment?: (commentId: string, text: string) => void;
}

type BottomSheetType = 'ai' | 'comments' | 'outline' | 'settings' | null;

const MobileEditor: React.FC<MobileEditorProps> = ({
  documentId,
  initialContent = '',
  onSave,
  onBack,
  suggestions = [],
  comments = [],
  outline = [],
  onAcceptSuggestion,
  onDismissSuggestion,
  onResolveComment,
  onReplyToComment,
}) => {
  const [content, setContent] = useState(initialContent);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [activeBottomSheet, setActiveBottomSheet] = useState<BottomSheetType>(null);
  const [wordCount, setWordCount] = useState(0);
  const [selectedText, setSelectedText] = useState('');
  const [showFormatBar, setShowFormatBar] = useState(false);
  const editorRef = useRef<HTMLDivElement>(null);


  // Handle keyboard visibility
  useEffect(() => {
    const handleResize = () => {
      const visualViewport = window.visualViewport;
      if (visualViewport) {
        const heightDiff = window.innerHeight - visualViewport.height;
        setIsKeyboardVisible(heightDiff > 150);
      }
    };

    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleResize);
      return () => window.visualViewport?.removeEventListener('resize', handleResize);
    }
  }, []);

  // Update word count
  useEffect(() => {
    const words = content.trim().split(/\\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
  }, [content]);

  // Handle text selection
  const handleTextSelection = () => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim()) {
      setSelectedText(selection.toString());
      setShowFormatBar(true);
    } else {
      setSelectedText('');
      setShowFormatBar(false);
    }
  };

  const formatText = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    editorRef.current?.focus();
  };

  const insertAISuggestion = (suggestion: AISuggestion) => {
    // Replace selected text or insert at cursor
    const newContent = content.replace(suggestion.original, suggestion.suggested);
    setContent(newContent);
    setActiveBottomSheet(null);
  };

  const BottomSheet: React.FC<{ type: BottomSheetType; children: React.ReactNode }> = ({ 
    type, 
    children 
  }) => (
    <AnimatePresence>
      {activeBottomSheet === type && (
        <>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-mobile-modal"
            onClick={() => setActiveBottomSheet(null)}
          />
          <motion.div
            initial={{ y: '100%' }}
            animate={{ y: 0 }}
            exit={{ y: '100%' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            className="mobile-bottom-sheet bg-white rounded-t-3xl shadow-2xl z-mobile-modal"
          >
            <div className="w-12 h-1 bg-slate-300 rounded-full mx-auto mt-3 mb-4" />
            {children}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );

  return (
    <div className="mobile-full-height bg-white flex flex-col mobile-safe-area">
      {/* Mobile Header */}
      <header className="flex items-center justify-between p-4 border-b border-slate-200 bg-white z-10">
        <div className="flex items-center space-x-3">
          <button
            onClick={onBack}
            className="mobile-icon-btn text-slate-600 hover:text-slate-900 hover:bg-slate-100"
          >
            <ArrowLeftIcon className="w-6 h-6" />
          </button>
          <div>
            <h1 className="font-semibold text-slate-900 text-lg">Document</h1>
            <p className="text-xs text-slate-500">{wordCount} words</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button className="mobile-icon-btn text-slate-600 hover:text-slate-900 hover:bg-slate-100">
            <EyeIcon className="w-5 h-5" />
          </button>
          <button className="mobile-icon-btn text-slate-600 hover:text-slate-900 hover:bg-slate-100">
            <ShareIcon className="w-5 h-5" />
          </button>
          <button 
            onClick={() => onSave?.(content)}
            className="mobile-btn bg-purple-600 text-white rounded-lg px-4 py-2 text-sm font-medium"
          >
            Save
          </button>
        </div>
      </header>

      {/* Main Editor */}
      <div className="flex-1 relative overflow-hidden">
        <div
          ref={editorRef}
          contentEditable
          className="w-full h-full p-4 text-base leading-relaxed text-slate-900 focus:outline-none resize-none overflow-y-auto"
          style={{ 
            minHeight: isKeyboardVisible ? 'auto' : '100%',
            paddingBottom: isKeyboardVisible ? '60px' : '120px' // Space for bottom toolbar
          }}
          onInput={(e) => setContent((e.target as HTMLDivElement).innerText)}
          onMouseUp={handleTextSelection}
          onTouchEnd={handleTextSelection}
          suppressContentEditableWarning
        >
          {content || (
            <div className="text-slate-400 italic">
              Start writing your document...
            </div>
          )}
        </div>

        {/* Floating Format Bar */}
        <AnimatePresence>
          {showFormatBar && selectedText && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-slate-800 text-white rounded-xl px-4 py-2 flex items-center space-x-3 shadow-lg z-20"
            >
              <button onClick={() => formatText('bold')} className="p-1">
                <BoldIcon className="w-4 h-4" />
              </button>
              <button onClick={() => formatText('italic')} className="p-1">
                <ItalicIcon className="w-4 h-4" />
              </button>
              <button onClick={() => formatText('underline')} className="p-1">
                <UnderlineIcon className="w-4 h-4" />
              </button>
              <div className="w-px h-4 bg-slate-600" />
              <button onClick={() => setShowFormatBar(false)} className="p-1">
                <XMarkIcon className="w-4 h-4" />
              </button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Floating AI Button */}
        <button
          onClick={() => setActiveBottomSheet('ai')}
          className="fixed bottom-24 right-4 w-14 h-14 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full shadow-lg flex items-center justify-center z-20"
        >
          <SparklesIcon className="w-6 h-6" />
        </button>
      </div>

      {/* Bottom Navigation */}
      {!isKeyboardVisible && (
        <nav className="border-t border-slate-200 bg-white px-4 py-2 safe-bottom">
          <div className="flex items-center justify-around">
            <button
              onClick={() => setActiveBottomSheet('ai')}
              className="flex flex-col items-center mobile-icon-btn text-slate-600 hover:text-purple-600"
            >
              <BoltIcon className="w-5 h-5 mb-1" />
              <span className="text-xs">AI</span>
            </button>
            
            <button
              onClick={() => setActiveBottomSheet('comments')}
              className="flex flex-col items-center mobile-icon-btn text-slate-600 hover:text-purple-600 relative"
            >
              <ChatBubbleBottomCenterTextIcon className="w-5 h-5 mb-1" />
              <span className="text-xs">Comments</span>
              {comments.filter(c => !c.resolved).length > 0 && (
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-xs font-bold text-white">
                    {comments.filter(c => !c.resolved).length}
                  </span>
                </div>
              )}
            </button>
            
            <button
              onClick={() => setActiveBottomSheet('outline')}
              className="flex flex-col items-center mobile-icon-btn text-slate-600 hover:text-purple-600"
            >
              <ListBulletIcon className="w-5 h-5 mb-1" />
              <span className="text-xs">Outline</span>
            </button>
            
            <button
              onClick={() => setActiveBottomSheet('settings')}
              className="flex flex-col items-center mobile-icon-btn text-slate-600 hover:text-purple-600"
            >
              <Cog6ToothIcon className="w-5 h-5 mb-1" />
              <span className="text-xs">Settings</span>
            </button>
          </div>
        </nav>
      )}

      {/* AI Suggestions Bottom Sheet */}
      <BottomSheet type="ai">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-slate-900">AI Suggestions</h2>
            <button
              onClick={() => setActiveBottomSheet(null)}
              className="mobile-icon-btn text-slate-600 hover:text-slate-900"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>
          
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {suggestions.map((suggestion) => (
              <div
                key={suggestion.id}
                className="p-4 bg-slate-50 rounded-xl border border-slate-200"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${
                      suggestion.type === 'style' ? 'bg-blue-500' : 
                      suggestion.type === 'grammar' ? 'bg-red-500' : 'bg-green-500'
                    }`} />
                    <span className="text-sm font-medium text-slate-700 capitalize">
                      {suggestion.type}
                    </span>
                  </div>
                  <button
                    onClick={() => {
                      insertAISuggestion(suggestion);
                      onAcceptSuggestion?.(suggestion.id);
                    }}
                    className="flex items-center space-x-1 px-3 py-1 bg-purple-600 text-white text-sm rounded-lg"
                  >
                    <CheckIcon className="w-4 h-4" />
                    <span>Apply</span>
                  </button>
                </div>
                
                <p className="text-sm text-slate-600 mb-3">{suggestion.text}</p>
                
                <div className="space-y-2">
                  <div>
                    <span className="text-xs font-medium text-red-600">Original:</span>
                    <p className="text-sm text-slate-900 bg-red-50 p-2 rounded">{suggestion.original}</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-green-600">Suggested:</span>
                    <p className="text-sm text-slate-900 bg-green-50 p-2 rounded">{suggestion.suggested}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </BottomSheet>

      {/* Comments Bottom Sheet */}
      <BottomSheet type="comments">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-slate-900">Comments</h2>
            <button
              onClick={() => setActiveBottomSheet(null)}
              className="mobile-icon-btn text-slate-600 hover:text-slate-900"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>
          
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {comments.map((comment) => (
              <div
                key={comment.id}
                className="p-4 bg-slate-50 rounded-xl border border-slate-200"
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-white">
                        {comment.author.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium text-slate-900 text-sm">{comment.author}</p>
                      <p className="text-xs text-slate-500">{comment.timestamp}</p>
                    </div>
                  </div>
                  {!comment.resolved && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                  )}
                </div>
                <p className="text-sm text-slate-700">{comment.text}</p>
              </div>
            ))}
            
            <button className="w-full flex items-center justify-center space-x-2 p-3 border-2 border-dashed border-slate-300 rounded-xl text-slate-600 hover:border-purple-400 hover:text-purple-600 transition-colors">
              <PlusIcon className="w-5 h-5" />
              <span>Add comment</span>
            </button>
          </div>
        </div>
      </BottomSheet>

      {/* Outline Bottom Sheet */}
      <BottomSheet type="outline">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-slate-900">Document Outline</h2>
            <button
              onClick={() => setActiveBottomSheet(null)}
              className="mobile-icon-btn text-slate-600 hover:text-slate-900"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>
          
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {outline.map((item) => (
              <button
                key={item.id}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-slate-50 rounded-lg transition-colors"
                style={{ paddingLeft: `${item.level * 16 + 12}px` }}
              >
                <div>
                  <p className="font-medium text-slate-900">{item.title}</p>
                  <p className="text-xs text-slate-500">{item.wordCount} words</p>
                </div>
                <div className={`w-1 h-8 rounded ${
                  item.level === 1 ? 'bg-purple-500' : 'bg-blue-400'
                }`} />
              </button>
            ))}
          </div>
        </div>
      </BottomSheet>

      {/* Settings Bottom Sheet */}
      <BottomSheet type="settings">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-slate-900">Settings</h2>
            <button
              onClick={() => setActiveBottomSheet(null)}
              className="mobile-icon-btn text-slate-600 hover:text-slate-900"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>
          
          <div className="space-y-4 max-h-96 overflow-y-auto">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-slate-700">Auto-save</span>
                <button className="w-12 h-6 bg-purple-600 rounded-full relative">
                  <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5 transition-transform" />
                </button>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-slate-700">AI suggestions</span>
                <button className="w-12 h-6 bg-purple-600 rounded-full relative">
                  <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5 transition-transform" />
                </button>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-slate-700">Focus mode</span>
                <button className="w-12 h-6 bg-slate-300 rounded-full relative">
                  <div className="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5 transition-transform" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </BottomSheet>
    </div>
  );
};

export default MobileEditor;